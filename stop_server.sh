#!/bin/bash

# ========================================
# miragemakers AI 系统停止脚本
# ========================================

echo "🛑 停止 miragemakers AI 系统..."

# 停止Django服务器
echo "🔧 停止Django服务器..."
pkill -f "manage.py runserver" 2>/dev/null || true
kill $(lsof -ti:8000) 2>/dev/null || true

# 停止Next.js前端
echo "🎨 停止Next.js前端..."
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "next-server" 2>/dev/null || true
kill $(lsof -ti:3000) 2>/dev/null || true

# 停止Celery Worker
echo "⚙️  停止Celery Worker..."
pkill -f "celery.*worker" 2>/dev/null || true

# 清理文件
echo "🧹 清理临时文件..."
rm -f /tmp/celery.pid /tmp/celery.log /tmp/django.log /tmp/frontend.log 2>/dev/null || true

# 等待进程完全停止
sleep 2

# 检查是否还有相关进程在运行
if pgrep -f "manage.py runserver" >/dev/null 2>&1; then
    echo "⚠️  强制停止Django进程..."
    pkill -9 -f "manage.py runserver" 2>/dev/null || true
fi

if pgrep -f "npm run dev" >/dev/null 2>&1; then
    echo "⚠️  强制停止Next.js进程..."
    pkill -9 -f "npm run dev" 2>/dev/null || true
fi

if pgrep -f "celery.*worker" >/dev/null 2>&1; then
    echo "⚠️  强制停止Celery进程..."
    pkill -9 -f "celery.*worker" 2>/dev/null || true
fi

# 可选：停止Redis（通常保持运行）
# echo "🔄 停止Redis服务..."
# brew services stop redis 2>/dev/null || true

# 可选：停止MySQL（通常保持运行）
# echo "🗄️  停止MySQL服务..."
# if command -v brew >/dev/null 2>&1; then
#     brew services stop mysql 2>/dev/null || true
# else
#     sudo systemctl stop mysql 2>/dev/null || sudo service mysql stop 2>/dev/null || true
# fi

echo "✅ 所有服务已停止"
echo ""
echo "💡 注意：Redis和MySQL服务保持运行状态"
echo "💡 如需停止它们，请手动执行："
echo "   Redis: brew services stop redis"
echo "   MySQL: brew services stop mysql"
echo ""
echo "📄 查看服务日志:"
echo "   应用服务: tail /tmp/{celery,django,frontend}.log"
echo "   Redis: brew services list | grep redis 或 redis-cli info"
echo "   MySQL: brew services list | grep mysql 或 tail /opt/homebrew/var/mysql/*.err"