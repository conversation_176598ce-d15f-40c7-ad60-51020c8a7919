#!/usr/bin/env python3
import os
import asyncio
import uuid

from app.utils.env_loader import load_env_file
from app.config.settings    import settings
from app.models             import RouteRequest
from app.router             import QwenRouter

# 1. 加载 .env 中所有环境变量
load_env_file()

async def main():
    # —— 构造路由请求 —— 
    prompt   = "视频编辑：把猫变成黑色"
    video_url = (
        "https://ugen.oss-cn-beijing.aliyuncs.com/tmp_imgs/tmpba3nvpcc.mp4"
        "?OSSAccessKeyId=LTAI5tE3G5dKW4XgYNQTV62q&Expires=1747757300"
        "&Signature=mRu%2FeBI9X81%2BQxkgBXaPq5a%2BQsY%3D"
    )

    # 如果有图片或视频附件，就放到 attachments 里
    attachments = [{"type": "video", "url": video_url}]

    route_req = RouteRequest(
        session_id=str(uuid.uuid4()),
        user_id=   str(uuid.uuid4()),
        utterance=prompt,
        attachments=attachments,
    )

    # 2. 路由：拿到 plan
    router = QwenRouter()
    plan = await router.route(route_req)
    print("== JobPlan ==\n", plan, "\n")

    # 3. 执行 plan，得到结果
    result = await router.execute(plan)

    # 4. 打印日志和最终输出
    print("== 执行日志 ==")
    for line in result.get("logs", []):
        print(line)

    print("\n== 最终结果 ==")
    if result.get("video_url"):
        print("视频 URL：", result["video_url"])
    if result.get("image_url"):
        print("图片 URL：", result["image_url"])
    if result.get("error"):
        print("错误信息：", result["error"])

if __name__ == "__main__":
    asyncio.run(main())
