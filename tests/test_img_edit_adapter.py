import asyncio
from app.adapters.img_edit_wanx import edit_image, ImgEditError
from app.config.settings import settings

async def test_img_edit_run():
    # 设置密钥和 URL（如未使用 .env）
    settings.img_edit_api_key = "sk-44c96a761fb740a0888b183edf89f526"
    settings.img_edit_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis"
    settings.img_edit_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"

    image_url = "http://wanx.alicdn.com/material/20250318/stylization_all_1.jpeg"
    prompt = "转换成法国绘本风格"

    try:
        result = await edit_image(image_url=image_url, prompt=prompt)
        print("图像编辑成功：", result)
    except ImgEditError as e:
        print("图像编辑失败：", e)

if __name__ == "__main__":
    asyncio.run(test_img_edit_run())