import asyncio
from app.adapters.image_gen import generate_image, ImageGenError
from app.config.settings import settings

async def test_image_gen():
    # settings.image_gen_api_key = "sk-44c96a761fb740a0888b183edf89f526"
    # settings.image_gen_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis"
    # settings.image_gen_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"

    prompt = "一间有着精致窗户的花店，漂亮的木质门，摆放着花朵"

    try:
        result = await generate_image(prompt)
        print("文生图成功：", result)
    except ImageGenError as e:
        print("文生图失败：", e)

if __name__ == "__main__":
    asyncio.run(test_image_gen())