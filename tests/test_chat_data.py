#!/usr/bin/env python3
"""
使用真实聊天数据进行端到端测试。

此脚本会加载指定目录中的所有聊天会话JSON文件，
针对每种API版本（'auto'和'max'）运行完整的Agent图，
并将每个会话的详细结果保存到指定的输出目录中。
"""

import os
import sys
import json
import asyncio
import argparse
import dataclasses
from pathlib import Path
from datetime import datetime
from enum import Enum

# 调整路径以从项目根目录导入模块
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from app.config.settings import settings
from app.core.graph import VisualGenerationGraph

class DataclassJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，用于处理dataclass和Enum对象"""
    def default(self, o):
        if dataclasses.is_dataclass(o):
            return dataclasses.asdict(o)
        if isinstance(o, Enum):
            return o.value
        return super().default(o)

def setup_arg_parser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="运行聊天会话测试并保存结果。",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--input-dir",
        type=str,
        default="data/session_json/chat",
        help="包含输入聊天会话JSON文件的目录。"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default=f"data/output/{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        help="用于保存测试结果的目录。"
    )
    parser.add_argument(
        "--file",
        type=str,
        default=None,
        help="指定要测试的单个JSON文件名（位于输入目录中）。"
    )
    parser.add_argument(
        "--skip-execute",
        action="store_true",
        help="如果设置，将跳过工具执行阶段，只进行到计划生成。"
    )
    return parser.parse_args()

async def run_test_case(conversation: list, api_version: str, skip_execute: bool) -> dict:
    """通过图运行单个测试用例"""
    # 每次测试都创建一个新的图实例以避免状态污染
    graph = VisualGenerationGraph()
    
    # 默认跳过token检查（用于离线测试）
    graph.skip_token_check = True
    print(f"  🔧 离线测试模式：跳过token检查")
    
    # 临时设置当前测试的API版本
    original_api_version = settings.api_version
    settings.api_version = api_version
    
    try:
        # 运行完整的对话流程
        result_summary = graph.run_with_conversation(
            conversation=conversation, 
            skip_tool_execution=skip_execute
        )
        return result_summary
    finally:
        # 恢复原始设置
        settings.api_version = original_api_version

async def main():
    """主执行函数"""
    args = setup_arg_parser()

    input_path = Path(args.input_dir)
    output_path = Path(args.output_dir)

    print(f"▶️  输入目录: {input_path.resolve()}")
    print(f"▶️  输出目录: {output_path.resolve()}")

    # 尝试创建输出目录，失败时继续运行但只输出到终端
    save_to_file = True
    try:
        output_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 输出目录创建成功")
    except Exception as e:
        print(f"⚠️  无法创建输出目录: {e}")
        print(f"📺 将直接在终端输出测试结果，不保存到文件")
        save_to_file = False

    if args.file:
        file_path = input_path / args.file
        if not file_path.is_file():
            print(f"❌ 指定的测试文件不存在: {file_path}")
            return
        test_files = [file_path]
        print(f"🎯 指定了单个测试文件: {args.file}")
    else:
        test_files = sorted(list(input_path.glob("*.json")))
        if not test_files:
            print(f"⚠️ 在目录 {input_path} 中未找到测试文件。")
            return
        print(f"找到了 {len(test_files)} 个测试文件。")

    for file_path in test_files:
        print(f"\n processing test file: {file_path.name}")
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                conversation = json.load(f)
            except json.JSONDecodeError as e:
                print(f"  ❌ 解析JSON失败: {e}")
                continue

        # 使用当前配置的API版本
        api_version = settings.api_version
        print(f"  ▶️  测试API版本: '{api_version}'")

        result = await run_test_case(conversation, api_version, args.skip_execute)
        
        # 准备输出数据
        output_data = {
            "source_file": file_path.name,
            "tested_api_version": api_version,
            "timestamp": datetime.now().isoformat(),
            "result": result
        }

        # 根据情况保存结果或输出到终端
        if save_to_file:
            # 将结果保存到新的JSON文件
            output_filename = f"{file_path.stem}_{api_version}.json"
            output_filepath = output_path / output_filename
            
            try:
                with open(output_filepath, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2, cls=DataclassJSONEncoder)
                print(f"    ✅ 结果已保存到: {output_filepath}")
            except Exception as e:
                print(f"    ❌ 保存结果失败: {e}")
                print(f"    📺 改为终端输出:")
                print(json.dumps(output_data, ensure_ascii=False, indent=2, cls=DataclassJSONEncoder))
        else:
            # 直接在终端输出结果
            print(f"    📺 测试结果 ({file_path.stem}_{api_version}):")
            print(json.dumps(output_data, ensure_ascii=False, indent=2, cls=DataclassJSONEncoder))
            print(f"    {'='*50}")

    print("\n🎉 所有测试已完成！")

if __name__ == "__main__":
    asyncio.run(main()) 