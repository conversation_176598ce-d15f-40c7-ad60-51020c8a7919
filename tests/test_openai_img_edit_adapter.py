#!/usr/bin/env python3
import os
import asyncio
import pytest
from pathlib import Path
from app.adapters.img_edit_openai import edit_image, OpenAIImageEditError
from app.utils.env_loader import load_env_file

load_env_file()

# 测试用远程图片 URL，请确保可访问
VALID_IMAGE_URL = "https://cdn.translate.alibaba.com/r/wanx-demo-1.png"
# 示例编辑指令
PROMPT = "把猫变成蜥蜴。"

@pytest.mark.asyncio
async def test_openai_img_edit_v2_success(tmp_path):
    """成功场景：设置有效 OPENAI_API_KEY，编辑图片后返回本地文件路径"""

    result = await edit_image(VALID_IMAGE_URL, PROMPT)

    # 校验返回结构
    assert isinstance(result, dict)
    assert result.get("prompt") == PROMPT
    assert result.get("task_status") == "SUCCEEDED"
    out_path = result.get("image_url")
    assert isinstance(out_path, str)
    # 文件应已写入本地
    file = Path(out_path)
    assert file.exists(), f"输出文件未找到: {out_path}"
    # 清理临时文件
    try:
        file.unlink()
    except Exception:
        pass

@pytest.mark.asyncio
async def test_openai_img_edit_v2_no_key():
    """失败场景：未设置 OPENAI_API_KEY，应抛出异常"""
    os.environ.pop("OPENAI_API_KEY", None)
    with pytest.raises(OpenAIImageEditError):
        await edit_image(VALID_IMAGE_URL, PROMPT)

@pytest.mark.asyncio
async def test_openai_img_edit_v2_invalid_url():
    """失败场景：无效图片 URL，应抛出异常"""
    os.environ["OPENAI_API_KEY"] = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    invalid_url = "https://example.com/nonexistent.png"
    with pytest.raises(OpenAIImageEditError):
        await edit_image(invalid_url, PROMPT)

if __name__ == "__main__":
    asyncio.run(test_openai_img_edit_v2_success(None))
    # asyncio.run(test_openai_img_edit_v2_no_key())
    # asyncio.run(test_openai_img_edit_v2_invalid_url())
