import asyncio
import pytest
from app.adapters.video_keyframe import extract_keyframe, VideoKeyframeError
import os
from pathlib import Path
from dotenv import load_dotenv

project_root = Path(__file__).parent.parent
env_path = project_root / ".env"
load_dotenv(dotenv_path=env_path)

# 有些 SDK 要求 OPENAI_API_KEY 环境变量，我们同步一下
if os.getenv("DASHSCOPE_API_KEY") and not os.getenv("OPENAI_API_KEY"):
    os.environ["OPENAI_API_KEY"] = os.environ["DASHSCOPE_API_KEY"]
    
# 可替换为你自己的有效视频 URL
VALID_VIDEO_URL = "https://ugen.oss-cn-beijing.aliyuncs.com/tmp_imgs/tmpba3nvpcc.mp4?OSSAccessKeyId=LTAI5tE3G5dKW4XgYNQTV62q&Expires=1747757300&Signature=mRu%2FeBI9X81%2BQxkgBXaPq5a%2BQsY%3D"

# 一个不可访问或格式不对的视频 URL，用于测试失败分支
INVALID_VIDEO_URL = "https://example.com/nonexistent_video.mp4"


async def _run_extract(url: str):
    """内部辅助：调用 extract_keyframe 并返回结果或抛出异常。"""
    return await extract_keyframe(url)


@pytest.mark.asyncio
async def test_valid_video_keyframe_extraction():
    """测试：有效视频 URL 应成功返回 image_url。"""
    result = await _run_extract(VALID_VIDEO_URL)
    assert isinstance(result, dict), "返回值应为 dict"
    assert result.get("task_status") == "SUCCEEDED"
    assert "image_url" in result and result["image_url"].startswith("http"), \
        "返回结果中应包含有效的 image_url"
    print("✅ 关键帧提取成功：", result)


@pytest.mark.asyncio
async def test_invalid_video_keyframe_extraction():
    """测试：无效视频 URL 应抛出 VideoKeyframeError。"""
    with pytest.raises(VideoKeyframeError):
        await _run_extract(INVALID_VIDEO_URL)
    print("❌ 关键帧提取失败：Invalid URL case")


if __name__ == "__main__":
    # 运行单文件测试
    asyncio.run(test_valid_video_keyframe_extraction())
    # asyncio.run(test_invalid_video_keyframe_extraction())
