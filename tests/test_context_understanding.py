#!/usr/bin/env python3
"""
测试上下文理解功能
验证历史信息抽取、参考信息获取、兜底逻辑等
"""

import requests
import time

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpass123"

def test_context_understanding():
    """测试上下文理解功能"""
    
    print("🔍 开始测试上下文理解功能...")
    
    # 1. 注册测试用户
    print("\n1️⃣ 注册测试用户...")
    register_data = {
        "name": "Context Test User",
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/register/", json=register_data)
    print(f"注册响应: {response.status_code}, {response.json()}")
    
    # 2. 登录获取token
    print("\n2️⃣ 登录获取token...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data)
    if response.status_code == 200:
        token = response.json().get('token')
        print(f"登录成功，token: {token[:20]}...")
    else:
        print(f"登录失败: {response.json()}")
        return
    
    headers = {"Authorization": f"Token {token}"}
    
    # 3. 创建新会话
    print("\n3️⃣ 创建新会话...")
    session_data = {"title": "上下文理解测试"}
    response = requests.post(f"{BASE_URL}/api/chat/sessions/create/", 
                           json=session_data, headers=headers)
    session_id = response.json()['session']['id']
    print(f"会话ID: {session_id}")
    
    # 4. 模拟生成图片（第一轮对话）
    print("\n4️⃣ 第一轮对话：生成图片...")
    form_data = {
        'prompt': '生成一只可爱的小狗图片',
        'mode': 'image_gen',
        'session_id': session_id
    }
    
    response = requests.post(f"{BASE_URL}/api/generate/", data=form_data, headers=headers)
    print(f"第一轮响应: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"响应内容: {list(result.keys())}")
        
        # 等待任务完成
        if 'task_id' in result:
            print("等待图片生成...")
            time.sleep(5)
    
    # 5. 测试上下文理解（第二轮对话）
    print("\n5️⃣ 第二轮对话：测试上下文理解...")
    form_data = {
        'prompt': '把这个小狗变成视频，让它跑起来',
        'mode': 'img2video',
        'session_id': session_id
    }
    
    response = requests.post(f"{BASE_URL}/api/generate/", data=form_data, headers=headers)
    print(f"第二轮响应: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"响应内容: {result}")
        
        # 检查是否正确识别了上下文
        if 'Please provide the reference image' in str(result):
            print("❌ 上下文理解失败：需要提供参考图片")
        else:
            print("✅ 上下文理解成功：识别到了历史图片")
    
    # 6. 测试无参考媒体的兜底逻辑
    print("\n6️⃣ 测试兜底逻辑...")
    form_data = {
        'prompt': '编辑图片，增加一些花朵',
        'mode': 'img_edit',
        'session_id': session_id
    }
    
    response = requests.post(f"{BASE_URL}/api/generate/", data=form_data, headers=headers)
    print(f"兜底测试响应: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"兜底响应: {result}")
    
    # 7. 查看会话历史
    print("\n7️⃣ 查看会话历史...")
    response = requests.get(f"{BASE_URL}/api/chat/sessions/{session_id}/messages/", 
                          headers=headers)
    if response.status_code == 200:
        messages = response.json()['messages']
        print(f"历史消息数量: {len(messages)}")
        for i, msg in enumerate(messages):
            print(f"  {i+1}. [{msg['type']}] {msg['content'][:50]}...")
            if msg.get('image_url'):
                print(f"      图片: {msg['image_url'][:50]}...")
            if msg.get('video_url'):
                print(f"      视频: {msg['video_url'][:50]}...")
    
    print("\n🎉 上下文理解功能测试完成！")

if __name__ == "__main__":
    test_context_understanding() 