#!/usr/bin/env python
import os
import sys
import django
from pathlib import Path

# 设置Django环境
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings
from core.email_utils import send_password_reset_email
from core.models import User
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_email_configuration():
    """测试当前邮件配置"""
    print("=== 邮件配置测试 ===")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"USE_MSMTP: {getattr(settings, 'USE_MSMTP', 'Not set')}")
    print(f"MSMTP_PATH: {getattr(settings, 'EMAIL_MSMTP_PATH', 'Not set')}")
    print(f"MSMTP_ACCOUNT: {getattr(settings, 'EMAIL_MSMTP_ACCOUNT', 'Not set')}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print()

def test_simple_email():
    """测试简单邮件发送"""
    print("=== 测试简单邮件发送 ===")
    try:
        result = send_mail(
            subject='miragemakers AI 邮件测试',
            message='这是一封测试邮件，用于验证系统邮件功能是否正常。',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False,
        )
        print(f"简单邮件发送结果: {result}")
        return True
    except Exception as e:
        print(f"简单邮件发送失败: {str(e)}")
        return False

def test_password_reset_function():
    """测试密码重置功能"""
    print("=== 测试密码重置邮件功能 ===")

    # 查找或创建测试用户
    test_email = '<EMAIL>'
    try:
        user = User.objects.get(email=test_email)
        print(f"找到测试用户: {user.name} ({user.email})")
    except User.DoesNotExist:
        print(f"创建测试用户: {test_email}")
        user = User.objects.create(
            username=test_email,
            email=test_email,
            name='测试用户',
            is_email_verified=True
        )

    # 测试密码重置邮件发送
    try:
        result = send_password_reset_email(user, '123456', 'zh')
        print(f"密码重置邮件发送结果: {result}")
        return result
    except Exception as e:
        print(f"密码重置邮件发送失败: {str(e)}")
        return False

def check_msmtp_logs():
    """检查msmtp日志"""
    print("=== 检查msmtp日志 ===")
    try:
        import subprocess
        result = subprocess.run(['tail', '-5', os.path.expanduser('~/.msmtp.log')], 
                              capture_output=True, text=True)
        print("最近5条msmtp日志:")
        print(result.stdout)
    except Exception as e:
        print(f"无法读取msmtp日志: {str(e)}")

if __name__ == "__main__":
    print("开始邮件功能测试...")
    print()

    test_email_configuration()

    simple_email_ok = test_simple_email()
    print()

    reset_email_ok = test_password_reset_function()
    print()

    check_msmtp_logs()
    print()

    print("=== 测试结果总结 ===")
    print(f"简单邮件发送: {'✅ 成功' if simple_email_ok else '❌ 失败'}")
    print(f"密码重置邮件: {'✅ 成功' if reset_email_ok else '❌ 失败'}")

    if simple_email_ok and reset_email_ok:
        print("\n🎉 邮件功能测试通过！请检查您的邮箱。")
    else:
        print("\n⚠️ 邮件功能存在问题，请检查配置。") 
