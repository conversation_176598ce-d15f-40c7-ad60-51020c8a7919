import asyncio
from app.adapters.img2video_wx import image_to_video, Img2VideoError
from app.config.settings import settings
from app.utils.env_loader import load_env_file

load_env_file()

async def test_img2video_run():
    # 手动配置（如未使用 .env）
    settings.img2video_api_key = "sk-44c96a761fb740a0888b183edf89f526"
    # settings.img2video_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis"
    # settings.video_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"
    
    prompt = "一只猫在草地上奔跑"
    img_url = "https://cdn.translate.alibaba.com/r/wanx-demo-1.png"

    try:
        result = await image_to_video(prompt, img_url)
        print("图生视频生成成功：", result)
    except Img2VideoError as e:
        print("图生视频生成失败：", e)

if __name__ == "__main__":
    asyncio.run(test_img2video_run())