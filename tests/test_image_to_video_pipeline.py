# tests/test_image_to_video_pipeline.py
import asyncio
from app.adapters.image_gen import generate_image, ImageGenError
from app.adapters.img_edit_wanx import edit_image, ImgEditError
from app.adapters.img2video_wx import image_to_video, Img2VideoError
from app.config.settings import settings

async def run_image_to_video_pipeline():
    # === 0. 设置密钥和 URL ===
    # settings.image_gen_api_key = "sk-xxx"
    # settings.image_gen_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis"
    # settings.image_gen_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"

    # settings.img_edit_api_key = "sk-xxx"
    # settings.img_edit_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis"
    # settings.img_edit_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"

    # settings.img2video_api_key = "sk-xxx"
    # settings.img2video_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis"
    # settings.video_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"

    try:
        # === 1. 文生图 ===
        print("\n== 步骤 1: 文生图 ==")
        gen_result = await generate_image("一只白猫在月球上奔跑", poll_interval=5)
        print("文生图结果：", gen_result)
        gen_image_url = gen_result["image_url"]

        # === 2. 图像编辑 ===
        print("\n== 步骤 2: 图像编辑 ==")
        edit_result = await edit_image(
            image_url=gen_image_url,
            prompt="一只黑猫在月球上奔跑", 
            poll_interval=5
        )
        print("图像编辑结果：", edit_result)
        edited_image_url = edit_result["image_url"]

        # === 3. 图生视频 ===
        print("\n== 步骤 3: 图生视频 ==")
        video_result = await image_to_video(
            prompt="一只黑猫在火星上奔跑",
            img_url=edited_image_url, 
            poll_interval=5
        )
        print("图生视频结果：", video_result)

    except (ImageGenError, ImgEditError, Img2VideoError) as e:
        print("任务失败：", e)

if __name__ == "__main__":
    asyncio.run(run_image_to_video_pipeline())
