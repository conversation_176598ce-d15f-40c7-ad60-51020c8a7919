#!/usr/bin/env bash

# 用法：
# ./query_video_task.sh 86ecf553-d340-4e21-xxxxxxxxx

# ========= 设置 DashScope API Key =========
DASHSCOPE_API_KEY="sk-44c96a761fb740a0888b183edf89f526"

# ========= 检查参数 =========
if [ -z "$1" ]; then
  echo "❌ 请输入 task_id 作为参数，例如："
  echo "   ./query_video_task.sh 86ecf553-d340-4e21-xxxxxxxxx"
  exit 1
fi

TASK_ID="$1"

# ========= 发送请求并输出 =========
echo "📡 正在查询任务状态：$TASK_ID"

curl -s -X GET \
  --header "Authorization: Bearer $DASHSCOPE_API_KEY" \
  "https://dashscope.aliyuncs.com/api/v1/tasks/$TASK_ID" | jq .