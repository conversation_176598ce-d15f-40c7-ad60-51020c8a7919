#!/usr/bin/env python3
"""
测试Token显示修复脚本
"""
import requests
import json

def test_profile_api():
    """测试profile API是否正常返回token信息"""
    
    # 测试health check
    try:
        response = requests.get('https://miragemakers.ai/health/')
        print(f"✅ Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
    
    # 测试API generate endpoint
    try:
        response = requests.get('https://miragemakers.ai/api/generate/', 
                              headers={'Accept': 'application/json'})
        print(f"✅ API Generate GET: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ API Generate GET failed: {e}")
    
    print("\n" + "="*50)
    print("修复总结:")
    print("1. ✅ 修复了TokenConsumption模型中缺失的video_keyframe选项")
    print("2. ✅ 修复了views.py中async_to_sync的错误使用")
    print("3. ✅ 为auth_views.py中的API添加了TokenAuthentication装饰器")
    print("4. ✅ 改进了前端fetchTokenBalance的错误处理")
    print("5. ✅ 重启了Django服务应用更改")
    print("\n现在用户应该可以正常看到token余额，聊天功能也应该恢复正常。")

if __name__ == "__main__":
    test_profile_api() 