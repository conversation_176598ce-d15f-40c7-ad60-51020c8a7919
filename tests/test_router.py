#!/usr/bin/env python3
# test_router.py

import os, sys
import argparse
import asyncio
import json
from uuid import uuid4
from inspect import iscoroutine

from app.config.settings import settings
from app.utils.env_loader import load_env_file

load_env_file()

from app.router import QwenRouter
from app.models import RouteRequest, JobPlan


async def process_single_file(input_path: str, output_path: str, skip_execute: bool = False):
    """
    从单个 JSON 文件中读取整个会话历史（最后一条就是本次请求），
    调用 router.route，并将结果写回到 output_path。
    如果 route() 返回 JobPlan 且 skip_execute=False，则再调用 execute() 并保存 execute() 的结果。
    
    支持两种JSON格式：
    1. 直接的消息数组: [{"role": "user", "content": "..."}, ...]
    2. 包含session信息的对象: {"session_id": "...", "messages": [...]}
    """
    # 读取整个 JSON
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 处理两种不同的JSON格式
    if isinstance(data, list):
        # 格式1: 直接的消息数组
        session = data
    elif isinstance(data, dict) and "messages" in data:
        # 格式2: 包含session信息的对象
        session = data["messages"]
    else:
        raise ValueError(f"不支持的JSON格式: {input_path}")

    # 取出历史消息（除了最后一条）和当前请求（最后一条）
    history = session[:-1]
    last_entry = session[-1]
    print("history = ", history)
    print("last_entry = ", last_entry)

    # 构造当前请求的 RouteRequest
    attachments = []
    
    # 处理两种不同的附件格式
    if "attachments" in last_entry:
        # 新格式：直接使用attachments数组
        attachments = last_entry.get("attachments", [])
    else:
        # 旧格式：从image_url和video_url构造attachments
        if last_entry.get("image_url") and last_entry["image_url"].strip():
            attachments.append({"type": "image", "url": last_entry["image_url"]})
        if last_entry.get("video_url") and last_entry["video_url"].strip():
            attachments.append({"type": "video", "url": last_entry["video_url"]})

    route_req = RouteRequest(
        session_id=uuid4(),
        user_id=str(uuid4()),
        utterance=last_entry["content"],
        attachments=attachments
    )

    # 初始化 Router 并注入历史
    router = QwenRouter(history=history)

    # 调用 route() 并获取结果
    route_result = await router.route(route_req) 
    print("---> route_result = ", route_result)

    final_output = None
    
    # 检查是否是直接的响应字典（例如，闲聊、错误、或多参考检测）
    if isinstance(route_result, dict) and 'response' in route_result:
        print("\n收到直接响应，正在处理...")
        response_value = route_result['response']
        # 检查响应是否是一个需要等待的协程
        if iscoroutine(response_value):
            print("响应是一个协程，正在等待结果...")
            final_output = {"response": await response_value}
        else:
            final_output = route_result
        print("最终响应: ", final_output)
    # 根据 skip_execute flag 决定是否执行 execute()
    elif isinstance(route_result, JobPlan) and not skip_execute:
        print("\n=== 调用 execute ===")
        execute_result = await router.execute(route_result)
        print("execute 返回：", json.dumps(execute_result, ensure_ascii=False, indent=2))
        final_output = execute_result
    elif isinstance(route_result, JobPlan) and skip_execute:
        print("\n=== 跳过 execute（由 --skip-execute flag 控制）===")
        # 返回 JobPlan 的基本信息用于测试
        final_output = {
            "skipped_execute": True,
            "route_result_type": "JobPlan",
            "nodes_count": len(route_result.nodes),
            "nodes_info": [
                {
                    "id": node.id,
                    "tool": node.tool,
                    "inputs": node.inputs,
                    "depends_on": node.depends_on
                }
                for node in route_result.nodes
            ]
        }
    else:
        print("\n不是 JobPlan，跳过 execute")
        final_output = route_result

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    # 保存最终输出到 JSON 文件
    with open(output_path, 'w', encoding='utf-8') as f_out:
        json.dump(final_output, f_out, ensure_ascii=False, indent=2)

    print(f"Processed '{os.path.basename(input_path)}' -> '{os.path.basename(output_path)}'")



async def main():
    """
    Useage:
    # process single file
    python test_router.py data/session_json/debug/multi_round.json data/session_json/output_intent/multi_round.json --skip-execute
    
    # process all files in a folder
    python tests/test_router.py data/session_json/input data/session_json/output_intent --skip-execute
    """
    # 1. 读取文件
    # 2. 调用 router.route
    # 3. 调用 router.execute
    # 4. 保存结果
    
    parser = argparse.ArgumentParser(description="处理 QwenRouter 请求")
    parser.add_argument("input_path", help="输入路径：可以是JSON文件路径或包含JSON文件的文件夹路径")
    parser.add_argument("output_path", help="输出路径：如果输入是文件则为输出文件路径，如果输入是文件夹则为输出文件夹路径")
    parser.add_argument("--skip-execute", action="store_true", 
                       help="跳过 router.execute() 调用，仅测试 route() 功能（节省时间和费用）")
    args = parser.parse_args()

    input_path = args.input_path
    output_path = args.output_path
    skip_execute = args.skip_execute

    if not os.path.exists(input_path):
        print(f"输入路径不存在: {input_path}")
        sys.exit(1)

    # 提前创建输出目录，避免在异步函数中执行同步I/O
    if os.path.isdir(input_path):
        os.makedirs(output_path, exist_ok=True)
    else:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

    if skip_execute:
        print("⚠️  使用 --skip-execute 模式，将跳过所有 execute() 调用")

    tasks = []
    # 判断输入路径是文件还是文件夹
    if os.path.isfile(input_path):
        # 处理单个文件
        if not input_path.lower().endswith(".json"):
            print(f"输入文件不是JSON格式: {input_path}")
            sys.exit(1)
        
        tasks.append(process_single_file(input_path, output_path, skip_execute))
    
    elif os.path.isdir(input_path):
        # 处理文件夹下所有JSON文件
        json_files = [f for f in os.listdir(input_path) if f.lower().endswith(".json")]
        
        if not json_files:
            print(f"输入文件夹中没有找到JSON文件: {input_path}")
            sys.exit(1)
        
        print(f"找到 {len(json_files)} 个JSON文件")
        
        for filename in json_files:
            input_file_path = os.path.join(input_path, filename)
            output_file_path = os.path.join(output_path, filename)
            tasks.append(process_single_file(input_file_path, output_file_path, skip_execute))
    
    else:
        print(f"输入路径既不是文件也不是文件夹: {input_path}")
        sys.exit(1)

    # 逐个串行执行任务，方便调试和查看日志
    for task in tasks:
        try:
            await task
        except Exception as e:
            print(f"一个任务执行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())