import pytest
import httpx
from app.adapters.video_gen import generate_video, VideoGenError
from app.config.settings import settings

@pytest.mark.asyncio
async def test_generate_video_success(monkeypatch):
    prompt = "测试视频"
    fake_resp = {"output": {"task_id": "abc123", "task_status": "RUNNING"}}

    class MockResponse:
        def raise_for_status(self):
            pass
        def json(self):
            return fake_resp

    class MockClient:
        async def __aenter__(self): return self
        async def __aexit__(self, exc_type, exc, tb): pass
        async def post(self, *args, **kwargs): return MockResponse()

    monkeypatch.setattr(httpx, "AsyncClient", lambda: MockClient())
    # 设置测试用的 URL 和 Key
    settings.video_gen_api_key = "test_key"
    settings.video_gen_base_url = "http://example.com/api"  

    result = await generate_video(prompt, size="640*480", duration=5)
    assert result == {"prompt": prompt, "task_id": "abc123", "task_status": "RUNNING"}

@pytest.mark.asyncio
async def test_generate_video_http_error(monkeypatch):
    prompt = "测试视频"

    class MockResponseError:
        def raise_for_status(self):
            raise httpx.HTTPStatusError("错误", request=None, response=None)

    class MockClientError:
        async def __aenter__(self): return self
        async def __aexit__(self, exc_type, exc, tb): pass
        async def post(self, *args, **kwargs): return MockResponseError()

    monkeypatch.setattr(httpx, "AsyncClient", lambda: MockClientError())
    settings.video_gen_api_key = "test_key"
    settings.video_gen_base_url = "http://example.com/api"

    with pytest.raises(VideoGenError):
        await generate_video(prompt)