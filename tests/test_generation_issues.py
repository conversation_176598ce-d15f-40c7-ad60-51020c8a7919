#!/usr/bin/env python3
"""
诊断图生图、图生视频、文生视频问题
"""

import os
import sys
import django
import requests
import argparse
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
django.setup()

from celery.result import AsyncResult
from core.models import ChatSession, ChatMessage, User
from celery import Celery

# Celery配置
app = Celery('generator')
app.config_from_object('django.conf:settings', namespace='CELERY')

# API配置
BASE_URL = "http://127.0.0.1:8000"
TOKEN = "563fdcef462b287e4f0783fdab217e8437c88739"
SESSION_ID = "8a15a530-0d3a-414d-8ac8-443c5805398e"

def check_celery_tasks():
    """检查所有正在运行的Celery任务"""
    print("🔍 检查Celery任务状态...")
    
    # 检查active tasks
    from celery import current_app
    inspect = current_app.control.inspect()
    
    active_tasks = inspect.active()
    if active_tasks:
        print("📋 当前活跃任务:")
        for worker, tasks in active_tasks.items():
            print(f"  Worker: {worker}")
            for task in tasks:
                task_id = task['id']
                task_name = task['name']
                print(f"    🔄 {task_name} - ID: {task_id}")
                
                # 检查任务结果
                result = AsyncResult(task_id)
                print(f"       状态: {result.status}")
                if result.status == 'SUCCESS':
                    print(f"       结果: {result.result}")
                elif result.status == 'FAILURE':
                    print(f"       错误: {result.result}")
    else:
        print("✅ 当前没有活跃任务")

def check_recent_messages():
    """检查最近的消息，查找生成任务"""
    print("\n📝 检查最近的生成请求...")
    
    try:
        user = User.objects.get(email='<EMAIL>')
        # 尝试两种UUID格式
        normalized_id = '8a15a5300d3a414d8ac8443c5805398e'
        try:
            session = ChatSession.objects.get(
                id=normalized_id, user=user
            )
        except ChatSession.DoesNotExist:
            session = ChatSession.objects.get(
                id=SESSION_ID, user=user
            )
        
        # 获取最近24小时的消息
        yesterday = datetime.now() - timedelta(days=1)
        messages = ChatMessage.objects.filter(
            session=session,
            created_at__gte=yesterday
        ).order_by('-created_at')
        
        print(f"📊 最近24小时内共有 {len(messages)} 条消息")
        
        generation_requests = []
        for msg in messages:
            if (msg.type == 'user' and 
                any(keyword in msg.content for keyword in 
                    ['生成', '变成', '图片', '视频'])):
                generation_requests.append(msg)
                print(f"  🎯 生成请求: {msg.content}")
                print(f"     时间: {msg.created_at}")
                print(f"     有图片: {'是' if msg.image_url else '否'}")
                print(f"     有视频: {'是' if msg.video_url else '否'}")
                
                # 检查是否有对应的AI回复
                next_msg = ChatMessage.objects.filter(
                    session=session,
                    type='assistant',
                    created_at__gt=msg.created_at
                ).first()
                
                if next_msg:
                    print(f"     AI回复: {next_msg.content}")
                    print(f"     回复图片: {'是' if next_msg.image_url else '否'}")
                    print(f"     回复视频: {'是' if next_msg.video_url else '否'}")
                else:
                    print("     ❌ 缺少AI回复")
                print()
        
        return generation_requests
        
    except Exception as e:
        print(f"❌ 检查消息失败: {e}")
        return []

def test_text_to_image():
    """测试文生图功能"""
    print("🖼️ 测试文生图功能...")
    
    response = requests.post(
        f"{BASE_URL}/api/generate",
        headers={"Authorization": f"Token {TOKEN}"},
        data={
            "prompt": "生成一张测试图片 - 蓝天白云",
            "mode": "text2img",
            "session_id": SESSION_ID
        },
        timeout=90
    )
    
    if response.status_code == 200:
        data = response.json()
        image_url = data.get('image_url')
        if image_url:
            print(f"✅ 文生图成功: {image_url[:80]}...")
            return True
        else:
            print(f"❌ 文生图返回格式错误: {data}")
            return False
    else:
        print(f"❌ 文生图失败: {response.status_code} - {response.text}")
        return False

def test_image_to_image():
    """测试图生图功能"""
    print("\n🔄 测试图生图功能...")
    
    # 创建一个测试图片
    test_image_content = b"fake_image_data_for_testing"
    
    files = {
        'media': ('test.jpg', test_image_content, 'image/jpeg')
    }
    
    data = {
        "prompt": "把这张图片变成油画风格",
        "mode": "img2img",
        "session_id": SESSION_ID
    }
    
    response = requests.post(
        f"{BASE_URL}/api/generate",
        headers={"Authorization": f"Token {TOKEN}"},
        data=data,
        files=files,
        timeout=90
    )
    
    if response.status_code == 200:
        result = response.json()
        image_url = result.get('image_url')
        if image_url:
            print(f"✅ 图生图成功: {image_url[:80]}...")
            return True
        else:
            print(f"❌ 图生图返回格式错误: {result}")
            return False
    else:
        print(f"❌ 图生图失败: {response.status_code} - {response.text}")
        return False

def test_text_to_video():
    """测试文生视频功能"""
    print("\n🎬 测试文生视频功能...")
    
    response = requests.post(
        f"{BASE_URL}/api/generate",
        headers={"Authorization": f"Token {TOKEN}"},
        data={
            "prompt": "生成一段小猫玩耍的视频",
            "mode": "text2video",
            "session_id": SESSION_ID
        },
        timeout=150  # 视频生成需要更长时间
    )
    
    if response.status_code == 200:
        data = response.json()
        video_url = data.get('video_url')
        status = data.get('status')
        
        if video_url:
            print(f"✅ 文生视频成功: {video_url[:80]}...")
            return True
        elif status == 'processing':
            print(f"⏳ 文生视频处理中，任务ID: {data.get('task_id')}")
            return 'processing'
        else:
            print(f"❓ 文生视频状态未知: {data}")
            return False
    else:
        print(f"❌ 文生视频失败: {response.status_code} - {response.text}")
        return False

def test_image_to_video():
    """测试图生视频功能"""
    print("\n🎥 测试图生视频功能...")
    
    # 创建一个测试图片
    test_image_content = b"fake_image_data_for_testing"
    
    files = {
        'media': ('test.jpg', test_image_content, 'image/jpeg')
    }
    
    data = {
        "prompt": "让这张图片动起来，生成视频",
        "mode": "img2video",
        "session_id": SESSION_ID
    }
    
    response = requests.post(
        f"{BASE_URL}/api/generate",
        headers={"Authorization": f"Token {TOKEN}"},
        data=data,
        files=files,
        timeout=150
    )
    
    if response.status_code == 200:
        result = response.json()
        video_url = result.get('video_url')
        if video_url:
            print(f"✅ 图生视频成功: {video_url[:80]}...")
            return True
        else:
            print(f"❌ 图生视频返回格式错误: {result}")
            return False
    else:
        print(f"❌ 图生视频失败: {response.status_code} - {response.text}")
        return False

def check_task_status(task_id):
    """检查特定任务状态"""
    print(f"\n🔍 检查任务状态: {task_id}")
    
    response = requests.get(
        f"{BASE_URL}/api/task_status/{task_id}",
        headers={"Authorization": f"Token {TOKEN}"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"任务状态: {data}")
        return data
    else:
        print(f"❌ 获取任务状态失败: {response.status_code}")
        return None

def fix_stuck_tasks():
    """修复卡住的任务"""
    print("\n🔧 检查并修复卡住的任务...")
    
    inspect = app.control.inspect()
    active_tasks = inspect.active()
    
    if active_tasks:
        for tasks in active_tasks.values():
            for task in tasks:
                task_id = task['id']
                task_name = task['name']
                
                # 检查任务运行时间
                if 'time_start' in task:
                    # 如果任务运行超过10分钟，考虑终止
                    print(f"检查任务 {task_id}: {task_name}")
                    
                    result = AsyncResult(task_id)
                    if result.status == 'PENDING':
                        print("  ⚠️ 任务状态为PENDING，可能已卡住")
                    elif result.status == 'STARTED':
                        print("  ▶️ 任务正在运行中")
    
    print("修复完成")

def select_test_types():
    """交互式选择要测试的功能类型"""
    test_options = {
        '1': ('text2img', '文生图', test_text_to_image),
        '2': ('img2img', '图生图', test_image_to_image), 
        '3': ('text2video', '文生视频', test_text_to_video),
        '4': ('img2video', '图生视频', test_image_to_video),
        '5': ('celery', 'Celery任务检查', check_celery_tasks),
        '6': ('messages', '最近消息检查', check_recent_messages),
        '7': ('fix_tasks', '修复卡住的任务', fix_stuck_tasks),
    }
    
    print("\n🎯 请选择要测试的功能类型:")
    print("=" * 50)
    for key, (_, name, _) in test_options.items():
        print(f"  {key}. {name}")
    print("  a. 全部测试")
    print("  q. 退出")
    print("=" * 50)
    
    while True:
        choice = input("\n请输入选项 (可输入多个，用逗号分隔，如: 1,2,3): ").strip()
        
        if choice.lower() == 'q':
            return []
        elif choice.lower() == 'a':
            return list(test_options.values())
        else:
            selected = []
            for c in choice.split(','):
                c = c.strip()
                if c in test_options:
                    selected.append(test_options[c])
                else:
                    print(f"❌ 无效选项: {c}")
                    continue
            
            if selected:
                return selected
            else:
                print("❌ 请选择有效的选项")

def main():
    print("🚀 开始诊断生成功能问题...\n")
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='诊断生成功能问题')
    parser.add_argument('--all', action='store_true', 
                       help='运行所有测试')
    parser.add_argument('--text2img', action='store_true', 
                       help='只测试文生图')
    parser.add_argument('--img2img', action='store_true', 
                       help='只测试图生图')
    parser.add_argument('--text2video', action='store_true', 
                       help='只测试文生视频')
    parser.add_argument('--img2video', action='store_true', 
                       help='只测试图生视频')
    parser.add_argument('--celery', action='store_true', 
                       help='只检查Celery任务')
    parser.add_argument('--messages', action='store_true', 
                       help='只检查最近消息')
    
    args = parser.parse_args()
    
    # 确定要运行的测试
    selected_tests = []
    
    if args.all:
        # 运行所有测试
        selected_tests = [
            ('celery', 'Celery任务检查', check_celery_tasks),
            ('messages', '最近消息检查', check_recent_messages),
            ('text2img', '文生图', test_text_to_image),
            ('img2img', '图生图', test_image_to_image),
            ('text2video', '文生视频', test_text_to_video),
            ('img2video', '图生视频', test_image_to_video),
        ]
    elif any([args.text2img, args.img2img, args.text2video, 
              args.img2video, args.celery, args.messages]):
        # 根据命令行参数选择测试
        if args.celery:
            selected_tests.append(('celery', 'Celery任务检查', check_celery_tasks))
        if args.messages:
            selected_tests.append(('messages', '最近消息检查', check_recent_messages))
        if args.text2img:
            selected_tests.append(('text2img', '文生图', test_text_to_image))
        if args.img2img:
            selected_tests.append(('img2img', '图生图', test_image_to_image))
        if args.text2video:
            selected_tests.append(('text2video', '文生视频', test_text_to_video))
        if args.img2video:
            selected_tests.append(('img2video', '图生视频', test_image_to_video))
    else:
        # 交互式选择
        selected_tests = select_test_types()
        if not selected_tests:
            print("👋 退出测试")
            return
    
    # 运行选定的测试
    results = {}
    for test_type, test_name, test_func in selected_tests:
        print(f"\n{'='*60}")
        print(f"🧪 开始测试: {test_name}")
        print(f"{'='*60}")
        
        try:
            if test_type in ['text2img', 'img2img', 'text2video', 'img2video']:
                result = test_func()
                results[test_type] = result
            else:
                test_func()  # 检查类的函数不返回结果
        except Exception as e:
            print(f"❌ 测试 {test_name} 时出错: {e}")
            results[test_type] = False
    
    # 总结结果
    if results:
        print(f"\n{'='*60}")
        print("📊 测试结果总结:")
        print(f"{'='*60}")
        for test_type, result in results.items():
            if result is True:
                status = "✅ 成功"
            elif result == 'processing':
                status = "⏳ 处理中"
            elif result is False:
                status = "❌ 失败"
            else:
                status = "❓ 未知"
            print(f"  {test_type}: {status}")
        
        # 提供解决建议
        print(f"\n💡 问题分析和建议:")
        
        failed_tests = [k for k, v in results.items() if v is False]
        if len(failed_tests) == len(results):
            print("🔴 所有生成功能都有问题，建议:")
            print("  1. 检查Celery worker是否正常运行")
            print("  2. 检查API密钥配置")
            print("  3. 检查网络连接")
            print("  4. 重启Celery worker")
        elif 'img2img' in failed_tests:
            print("🟡 图生图功能有问题，建议:")
            print("  1. 检查万象API账户状态")
            print("  2. 考虑启用Gemini图片编辑API")
            print("  3. 检查图片编辑API配置")
        elif any(v != True for v in results.values()):
            print("🟡 部分功能有问题，建议:")
            print("  1. 视频生成需要更长时间（2-5分钟）")
            print("  2. 检查相关API的配额和限制")
            print("  3. 前端可能需要增加超时时间")
        else:
            print("🟢 功能基本正常，可能是偶发性问题")
    
    print(f"\n✨ 诊断完成")

if __name__ == "__main__":
    main() 