#!/usr/bin/env python3
"""
恢复聊天历史和图片生成结果
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
django.setup()

from core.models import ChatSession, ChatMessage, User
import json


def find_image_results():
    """查找数据库中的图片生成结果"""
    print("🔍 搜索图片生成结果...")
    
    # 查找用户的会话
    user = User.objects.get(email='<EMAIL>')
    sessions = ChatSession.objects.filter(user=user).order_by('-created_at')
    
    results = []
    for session in sessions:
        print(f"\n📁 会话: {session.title} ({session.id})")
        messages = ChatMessage.objects.filter(session=session).order_by('created_at')
        
        for msg in messages:
            if msg.image_url or msg.video_url or msg.content:
                result = {
                    'session_id': str(session.id),
                    'session_title': session.title,
                    'message_id': str(msg.id),
                    'type': msg.type,
                    'content': msg.content,
                    'image_url': msg.image_url,
                    'video_url': msg.video_url,
                    'created_at': msg.created_at.isoformat()
                }
                results.append(result)
                
                status = "🖼️" if msg.image_url else "🎬" if msg.video_url else "💬"
                print(f"  {status} {msg.type}: {msg.content[:50]}")
                if msg.image_url:
                    print(f"     图片: {msg.image_url[:80]}...")
                if msg.video_url:
                    print(f"     视频: {msg.video_url[:80]}...")
    
    return results


def restore_missing_ai_responses():
    """恢复缺失的AI响应消息"""
    print("\n🔧 恢复缺失的AI响应...")
    
    # 查找只有用户消息但没有AI回复的情况
    user = User.objects.get(email='<EMAIL>')
    sessions = ChatSession.objects.filter(user=user)
    
    restored_count = 0
    for session in sessions:
        messages = list(ChatMessage.objects.filter(session=session).order_by('created_at'))
        
        # 检查是否有孤立的用户消息（没有后续的assistant回复）
        for i, msg in enumerate(messages):
            if msg.type == 'user':
                # 检查下一条消息是否是assistant回复
                next_msg = messages[i + 1] if i + 1 < len(messages) else None
                if not next_msg or next_msg.type != 'assistant':
                    # 缺少AI回复，创建一个占位回复
                    if '生成' in msg.content and ('图片' in msg.content or '图' in msg.content):
                        # 这是图片生成请求，创建占位回复
                        ChatMessage.objects.create(
                            session=session,
                            type='assistant',
                            content='图片生成中...',
                            image_url=None,
                            video_url=None,
                            metadata={'restored': True, 'original_request': msg.content}
                        )
                        restored_count += 1
                        print(f"  ✅ 为会话 {session.id} 恢复了图片生成回复")
    
    print(f"📊 总共恢复了 {restored_count} 条AI回复")


def main():
    print("🚀 开始恢复聊天历史和生成结果...\n")
    
    try:
        # 1. 查找现有的生成结果
        results = find_image_results()
        
        # 2. 保存结果到文件
        with open('/tmp/chat_history_backup.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 历史记录已保存到 /tmp/chat_history_backup.json")
        print(f"📊 总共找到 {len(results)} 条记录")
        
        # 3. 恢复缺失的AI响应
        restore_missing_ai_responses()
        
        # 4. 统计信息
        print("\n📈 统计信息:")
        image_count = len([r for r in results if r['image_url']])
        video_count = len([r for r in results if r['video_url']])
        text_count = len([r for r in results if r['content'] and not r['image_url'] and not r['video_url']])
        
        print(f"  🖼️  图片消息: {image_count}")
        print(f"  🎬 视频消息: {video_count}")
        print(f"  💬 文本消息: {text_count}")
        
        print("\n✨ 恢复完成！现在前端应该能显示历史记录了。")
        
    except Exception as e:
        print(f"❌ 恢复过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 