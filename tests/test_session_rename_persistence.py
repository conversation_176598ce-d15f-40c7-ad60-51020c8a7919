#!/usr/bin/env python3
"""
测试会话重命名持久化问题的调试脚本
"""
import os
import sys
import requests
from datetime import datetime

# 设置Django环境
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')

import django
django.setup()

from core.models import User, ChatSession

def test_session_rename_persistence():
    """测试会话重命名的持久化"""
    print("🔄 测试会话重命名持久化...")
    
    # 1. 检查数据库中现有的会话
    print("\n📊 数据库中的会话列表:")
    sessions = ChatSession.objects.all().order_by('-updated_at')[:10]
    for session in sessions:
        print(f"  ID: {session.id}")
        print(f"  用户: {session.user.email if session.user else 'Anonymous'}")
        print(f"  标题: '{session.title}'")
        print(f"  创建时间: {session.created_at}")
        print(f"  更新时间: {session.updated_at}")
        print("  ---")
    
    # 2. 如果有会话，查看第一个会话的详细信息
    if sessions:
        first_session = sessions[0]
        print(f"\n🔍 详细检查第一个会话 (ID: {first_session.id}):")
        print(f"  当前标题: '{first_session.title}'")
        print(f"  用户: {first_session.user.email if first_session.user else 'None'}")
        
        # 3. 模拟重命名
        old_title = first_session.title
        new_title = f"重命名测试 - {datetime.now().strftime('%H:%M:%S')}"
        
        print(f"\n📝 模拟重命名: '{old_title}' -> '{new_title}'")
        first_session.title = new_title
        first_session.save()
        
        # 4. 重新查询验证
        updated_session = ChatSession.objects.get(id=first_session.id)
        print(f"✅ 重命名后标题: '{updated_session.title}'")
        print(f"✅ 更新时间: {updated_session.updated_at}")
        
        # 5. 模拟API调用
        if first_session.user:
            print(f"\n🌐 测试API调用 (用户: {first_session.user.email})...")
            try:
                # 这里需要获取用户的认证token
                from rest_framework.authtoken.models import Token
                token, created = Token.objects.get_or_create(user=first_session.user)
                
                print(f"🔑 用户Token: {token.key}")
                
                # 构建API请求
                api_url = f"http://localhost:8000/api/chat/sessions/{first_session.id}/update/"
                headers = {
                    'Authorization': f'Token {token.key}',
                    'Content-Type': 'application/json',
                }
                test_title = f"API测试重命名 - {datetime.now().strftime('%H:%M:%S')}"
                data = {'title': test_title}
                
                print(f"📡 发送API请求: PATCH {api_url}")
                print(f"📦 请求数据: {data}")
                
                # 注意：这个测试假设Django服务器在运行
                # 如果服务器没运行，这部分会失败
                try:
                    response = requests.patch(api_url, headers=headers, json=data, timeout=5)
                    print(f"📨 API响应状态: {response.status_code}")
                    print(f"📨 API响应内容: {response.text}")
                    
                    if response.status_code == 200:
                        # 再次查询数据库验证
                        final_session = ChatSession.objects.get(id=first_session.id)
                        print(f"✅ API更新后标题: '{final_session.title}'")
                        print(f"✅ 最终更新时间: {final_session.updated_at}")
                    else:
                        print(f"❌ API调用失败: {response.status_code}")
                except requests.exceptions.RequestException as e:
                    print(f"⚠️ API请求失败 (可能服务器未运行): {e}")
                    
            except Exception as e:
                print(f"❌ Token获取失败: {e}")
    else:
        print("❌ 数据库中没有会话数据")

def check_user_sessions(email):
    """检查特定用户的会话"""
    try:
        user = User.objects.get(email=email)
        print(f"\n👤 用户 {email} 的会话:")
        sessions = ChatSession.objects.filter(user=user).order_by('-updated_at')
        
        for session in sessions:
            print(f"  📝 {session.title} (ID: {session.id})")
            print(f"     创建: {session.created_at}")
            print(f"     更新: {session.updated_at}")
            print("     ---")
            
        return sessions
    except User.DoesNotExist:
        print(f"❌ 用户 {email} 不存在")
        return []

if __name__ == "__main__":
    print("🚀 开始测试会话重命名持久化...")
    
    # 运行基本测试
    test_session_rename_persistence()
    
    # 如果你知道具体的用户邮箱，可以取消注释下面这行
    # check_user_sessions("<EMAIL>")
    
    print("\n✅ 测试完成!") 