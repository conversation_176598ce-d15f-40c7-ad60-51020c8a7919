import uuid, pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

@pytest.mark.parametrize("prompt, attachment, expected_tools", [
    ("画一幅抽象派艺术",                     None,                                ["img_gen"]),
    ("请编辑这张照片，去除背景",             {"type":"image","url":"http://img"},  ["img_edit"]),
    ("把这段视频转换成黑白",                 {"type":"video","url":"http://vid"}, ["video_keyframe","img_edit","img2video"]),
    ("生成一个宇航员漫步月球的视频",         {"type":"video","url":"http://vid"},  ["video_gen"]),
])
def test_various_routes(prompt, attachment, expected_tools):
    req = {
        "session_id": str(uuid.uuid4()),
        "user_id":    str(uuid.uuid4()),
        "utterance":  prompt,
    }
    if attachment:
        req["attachments"] = [attachment]

    r = client.post("/v1/agent/route", json=req)
    assert r.status_code == 200
    tools = [node["tool"] for node in r.json()["nodes"]]
    assert tools == expected_tools