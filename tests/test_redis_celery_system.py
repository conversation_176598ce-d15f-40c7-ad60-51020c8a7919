#!/usr/bin/env python3
"""
Redis + Celery + Django 完整系统测试
"""

import os
import sys
import time
import redis
import subprocess
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
sys.path.append(str(Path(__file__).parent))

import django
django.setup()

from django.core.cache import cache
from app.celery_app import app as celery_app
from app.tasks import execute_jobplan
from app.models import JobPlan, Node

def test_redis_connection():
    """测试Redis连接"""
    print("🔍 测试Redis连接...")
    try:
        r = redis.Redis(host='127.0.0.1', port=6379, db=0)
        result = r.ping()
        print(f"✅ Redis连接成功: {result}")
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def test_django_cache():
    """测试Django缓存"""
    print("🔍 测试Django缓存...")
    try:
        # 测试缓存写入和读取
        test_key = "test_cache_key"
        test_value = {"message": "Hello Redis!", "timestamp": time.time()}
        
        cache.set(test_key, test_value, 60)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            print("✅ Django缓存测试成功")
            return True
        else:
            print(f"❌ Django缓存测试失败: 期望 {test_value}, 得到 {retrieved_value}")
            return False
    except Exception as e:
        print(f"❌ Django缓存测试失败: {e}")
        return False

def test_celery_connection():
    """测试Celery连接"""
    print("🔍 测试Celery连接...")
    try:
        # 检查Celery broker连接
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print("✅ Celery连接成功，发现活跃worker:")
            for worker, stat in stats.items():
                print(f"  - {worker}: {stat.get('pool', {}).get('max-concurrency', 'N/A')} 并发")
            return True
        else:
            print("⚠️  Celery broker连接成功，但没有发现活跃的worker")
            return False
    except Exception as e:
        print(f"❌ Celery连接失败: {e}")
        return False

def test_celery_task():
    """测试Celery任务执行"""
    print("🔍 测试Celery任务执行...")
    try:
        # 创建一个测试计划 - 使用正确的JobPlan对象
        test_plan = JobPlan(nodes=[
            Node(
                id="n0",
                tool="img_gen",
                inputs={"prompt": "这是一个Celery测试任务 - 生成一只可爱的小猫"}
            )
        ])
        
        # 将计划存储到缓存
        plan_id = f"test_plan_{int(time.time())}"
        cache.set(f"plan:{plan_id}", test_plan, 300)
        
        # 提交Celery任务
        print(f"📤 提交任务: {plan_id}")
        result = execute_jobplan.delay(plan_id)
        
        print(f"📋 任务ID: {result.id}")
        print("⏳ 等待任务完成...")
        
        # 等待任务完成（最多30秒）
        try:
            task_result = result.get(timeout=30)
            print(f"✅ 任务执行成功: {task_result}")
            return True
        except Exception as task_e:
            print(f"❌ 任务执行失败: {task_e}")
            return False
            
    except Exception as e:
        print(f"❌ Celery任务测试失败: {e}")
        return False

def check_services():
    """检查必要的服务是否运行"""
    print("🔍 检查系统服务...")
    
    # 检查Redis服务
    try:
        result = subprocess.run(['redis-cli', 'ping'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0 and 'PONG' in result.stdout:
            print("✅ Redis服务运行正常")
        else:
            print("❌ Redis服务未运行")
            return False
    except Exception as e:
        print(f"❌ 无法检查Redis服务: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始Redis + Celery + Django系统测试")
    print("=" * 50)
    
    tests = [
        ("服务检查", check_services),
        ("Redis连接", test_redis_connection),
        ("Django缓存", test_django_cache),
        ("Celery连接", test_celery_connection),
        ("Celery任务", test_celery_task),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！Redis + Celery + Django系统运行正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        print("\n💡 故障排除建议:")
        print("1. 确保Redis服务运行: brew services start redis")
        print("2. 启动Celery worker: ./start_celery.sh")
        print("3. 检查网络连接和防火墙设置")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 