import asyncio
from app.adapters.video_gen import generate_video, VideoGenError
from app.config.settings import settings

async def main():
    # 如果没有 .env 文件或想要临时覆盖配置，可以在此手动赋值：
    settings.video_gen_api_key = "sk-44c96a761fb740a0888b183edf89f526"
    settings.video_gen_base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis"
    settings.video_task_base_url = "https://dashscope.aliyuncs.com/api/v1/tasks"

    prompt = "给我生成小猫在月球奔跑"
    try:
        result = await generate_video(prompt)
        print("生成结果：", result)
    except VideoGenError as e:
        print("生成失败：", e)

if __name__ == "__main__":
    asyncio.run(main())