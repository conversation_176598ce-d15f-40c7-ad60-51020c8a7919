import os
import base64
import tempfile
import asyncio
import httpx
from openai import AsyncOpenAI
from app.config.settings import settings


API_KEY = settings.openai_api_key
BASE_URL = settings.openai_base_url
client = AsyncOpenAI(api_key=API_KEY, base_url=BASE_URL, timeout=600.0, max_retries=3,)

async def edit_image(image_url: str, prompt: str) -> str:
    """
    使用 OpenAI gpt-image-1 接口对 image_url 对应的图片进行编辑。

    返回：本地保存的编辑后图片路径
    """
    
    # 1. 下载原图
    try:
        async with httpx.AsyncClient() as http:
            resp = await http.get(image_url, timeout=30.0)
            resp.raise_for_status()
            img_bytes = resp.content
    except Exception as e:
        raise RuntimeError(f"下载原图失败: {e}")

    # 写入临时输入文件
    tmp_in = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
    tmp_in.write(img_bytes)
    tmp_in.flush()
    tmp_in.close()

    # 2. 调用 OpenAI images.edit（使用文件列表格式）
    try:
        with open(tmp_in.name, "rb") as f:
            result = await client.images.edit(
                model="gpt-image-1",
                image=[f],
                prompt=prompt,
                size="1024x1024",
                n=1,
            )
    except Exception as e:
        # 根据错误信息可进一步判断 moderation_blocked 等情况
        raise RuntimeError(f"调用 OpenAI 图像编辑失败: {e}")

    # 3. 解析返回
    item = result.data[0]
    if getattr(item, "url", None):
        # 远程 URL 模式
        edit_url = item.url
        # 下载编辑后图
        async with httpx.AsyncClient() as http:
            r2 = await http.get(edit_url, timeout=30.0)
            r2.raise_for_status()
            out_bytes = r2.content
    elif getattr(item, "b64_json", None):
        # Base64 模式
        out_bytes = base64.b64decode(item.b64_json)
    else:
        raise RuntimeError("OpenAI 返回的数据中既无 url 也无 b64_json")

    # 写入临时输出文件
    tmp_out = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
    tmp_out.write(out_bytes)
    tmp_out.flush()
    tmp_out.close()

    return tmp_out.name

# 简单测试
async def main():
    ori = "https://ugen.oss-cn-beijing.aliyuncs.com/tmp_imgs/output_1748178785.png?OSSAccessKeyId=LTAI5tE3G5dKW4XgYNQTV62q&Expires=1752099713&Signature=9Bq9ZroAd9iH4Duoj%2Flxuro0jWU%3D"
    prompt = "change the cat to dog"
    try:
        out_path = await edit_image(ori, prompt)
        print("编辑后图片已保存：", out_path)
    except Exception as e:
        print("编辑失败：", e)

if __name__ == "__main__":
    asyncio.run(main())