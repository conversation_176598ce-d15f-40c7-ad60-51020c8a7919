#!/usr/bin/env python3
"""
OSS视频上传测试脚本
测试火山视频URL到OSS的上传功能
"""

import asyncio
import json
import time
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.tools.oss_upload import download_and_upload_video_to_oss, direct_copy_video_to_oss


class OSSVideoUploadTester:
    def __init__(self):
        # 使用一个公开的测试视频URL（Big Buck Bunny短片）
        self.test_video_url = "https://ark-content-generation-ap-southeast-1.tos-ap-southeast-1.volces.com/seedance-1-0-pro/02175233170640000000000000000000000ffffc0a8701a754a62.mp4?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTYjg3ZjNlOGM0YzQyNGE1MmI2MDFiOTM3Y2IwMTY3OTE%2F20250712%2Fap-southeast-1%2Ftos%2Frequest&X-Tos-Date=20250712T144918Z&X-Tos-Expires=86400&X-Tos-Signature=a3d1c84a2ed4c129654bc41d1b9e511d60100d8520d99c1e05d7cc0adbaf3f88&X-Tos-SignedHeaders=host"
        self.results = {
            "test_time": datetime.now().isoformat(),
            "test_video_url": self.test_video_url,
            "url_accessibility": None,
            "direct_copy_test": None,
            "download_upload_test": None,
            "performance_comparison": None,
            "recommendations": []
        }
    
    async def test_url_accessibility(self):
        """测试视频URL的可访问性（使用GET请求获取前1KB数据）"""
        print("🔍 测试视频URL可访问性...")
        
        try:
            import aiohttp
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Range': 'bytes=0-1023'  # 只请求前1KB数据来测试可访问性
            }
            async with aiohttp.ClientSession(headers=headers) as session:
                start_time = time.time()
                async with session.get(self.test_video_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    end_time = time.time()
                    
                    # 对于Range请求，206或200都表示成功
                    is_accessible = response.status in [200, 206]
                    
                    accessibility_result = {
                        "accessible": is_accessible,
                        "status_code": response.status,
                        "response_time": round(end_time - start_time, 2),
                        "content_length": response.headers.get('content-length'),
                        "content_type": response.headers.get('content-type'),
                        "content_range": response.headers.get('content-range')
                    }
                    
                    self.results["url_accessibility"] = accessibility_result
                    
                    if accessibility_result["accessible"]:
                        print(f"✅ URL可访问 (状态码: {response.status}, 响应时间: {accessibility_result['response_time']}s)")
                        if accessibility_result["content_length"]:
                            size_mb = round(int(accessibility_result["content_length"]) / (1024 * 1024), 2)
                            print(f"📦 视频大小: {size_mb}MB")
                        if accessibility_result["content_range"]:
                            print(f"📊 Range支持: {accessibility_result['content_range']}")
                    else:
                        print(f"❌ URL不可访问 (状态码: {response.status})")
                        
        except Exception as e:
            print(f"❌ URL可访问性测试失败: {str(e)}")
            self.results["url_accessibility"] = {
                "accessible": False,
                "error": str(e)
            }
    
    async def test_direct_copy_method(self):
        """测试直接复制方法（流式传输）"""
        print("\n🚀 测试直接复制方法（流式传输）...")
        
        try:
            start_time = time.time()
            result_url = await direct_copy_video_to_oss(self.test_video_url)
            end_time = time.time()
            
            test_result = {
                "success": result_url is not None and result_url != self.test_video_url,
                "duration": round(end_time - start_time, 2),
                "result_url": result_url,
                "method": "direct_copy"
            }
            
            self.results["direct_copy_test"] = test_result
            
            if test_result["success"]:
                print(f"✅ 直接复制成功 (耗时: {test_result['duration']}s)")
                print(f"🔗 OSS URL: {result_url}")
            else:
                print(f"❌ 直接复制失败 (耗时: {test_result['duration']}s)")
                
        except Exception as e:
            print(f"❌ 直接复制方法测试失败: {str(e)}")
            self.results["direct_copy_test"] = {
                "success": False,
                "error": str(e),
                "method": "direct_copy"
            }
    
    async def test_download_upload_method(self):
        """测试下载上传方法（先下载到本地再上传）"""
        print("\n📥 测试下载上传方法（先下载到本地再上传）...")
        
        try:
            start_time = time.time()
            result_url = await download_and_upload_video_to_oss(self.test_video_url)
            end_time = time.time()
            
            test_result = {
                "success": result_url is not None and result_url != self.test_video_url,
                "duration": round(end_time - start_time, 2),
                "result_url": result_url,
                "method": "download_upload"
            }
            
            self.results["download_upload_test"] = test_result
            
            if test_result["success"]:
                print(f"✅ 下载上传成功 (耗时: {test_result['duration']}s)")
                print(f"🔗 OSS URL: {result_url}")
            else:
                print(f"❌ 下载上传失败 (耗时: {test_result['duration']}s)")
                
        except Exception as e:
            print(f"❌ 下载上传方法测试失败: {str(e)}")
            self.results["download_upload_test"] = {
                "success": False,
                "error": str(e),
                "method": "download_upload"
            }
    
    def analyze_performance(self):
        """分析性能对比"""
        print("\n📊 性能分析...")
        
        direct_test = self.results.get("direct_copy_test")
        download_test = self.results.get("download_upload_test")
        
        if not direct_test or not download_test:
            print("❌ 无法进行性能对比，测试数据不完整")
            return
        
        comparison = {
            "direct_copy_duration": direct_test.get("duration"),
            "download_upload_duration": download_test.get("duration"),
            "direct_copy_success": direct_test.get("success", False),
            "download_upload_success": download_test.get("success", False)
        }
        
        # 计算性能差异
        if comparison["direct_copy_duration"] and comparison["download_upload_duration"]:
            if comparison["direct_copy_duration"] < comparison["download_upload_duration"]:
                faster_method = "direct_copy"
                time_saved = comparison["download_upload_duration"] - comparison["direct_copy_duration"]
                improvement = round((time_saved / comparison["download_upload_duration"]) * 100, 1)
            else:
                faster_method = "download_upload"
                time_saved = comparison["direct_copy_duration"] - comparison["download_upload_duration"]
                improvement = round((time_saved / comparison["direct_copy_duration"]) * 100, 1)
            
            comparison["faster_method"] = faster_method
            comparison["time_saved"] = round(time_saved, 2)
            comparison["improvement_percentage"] = improvement
        
        self.results["performance_comparison"] = comparison
        
        # 输出分析结果
        print(f"⚡ 直接复制方法: {comparison['direct_copy_duration']}s ({'成功' if comparison['direct_copy_success'] else '失败'})")
        print(f"📥 下载上传方法: {comparison['download_upload_duration']}s ({'成功' if comparison['download_upload_success'] else '失败'})")
        
        if comparison.get("faster_method"):
            method_name = "直接复制" if comparison["faster_method"] == "direct_copy" else "下载上传"
            print(f"🏆 {method_name}方法更快，节省时间: {comparison['time_saved']}s ({comparison['improvement_percentage']}%)")
    
    def generate_recommendations(self):
        """生成建议"""
        print("\n💡 生成建议...")
        
        recommendations = []
        
        # URL可访问性建议
        url_test = self.results.get("url_accessibility")
        if url_test and not url_test.get("accessible"):
            recommendations.append("视频URL不可访问，建议检查URL有效性或网络连接")
        
        # 性能建议
        comparison = self.results.get("performance_comparison")
        if comparison:
            if comparison.get("direct_copy_success") and comparison.get("download_upload_success"):
                if comparison.get("faster_method") == "direct_copy":
                    recommendations.append("建议优先使用直接复制方法，性能更好")
                else:
                    recommendations.append("建议优先使用下载上传方法，性能更好")
            elif comparison.get("direct_copy_success"):
                recommendations.append("建议使用直接复制方法，下载上传方法失败")
            elif comparison.get("download_upload_success"):
                recommendations.append("建议使用下载上传方法，直接复制方法失败")
            else:
                recommendations.append("两种方法都失败，建议检查OSS配置和网络连接")
        
        # 超时建议
        direct_test = self.results.get("direct_copy_test")
        download_test = self.results.get("download_upload_test")
        
        if direct_test and direct_test.get("duration", 0) > 300:
            recommendations.append("直接复制方法耗时过长，建议增加超时时间或优化网络")
        
        if download_test and download_test.get("duration", 0) > 300:
            recommendations.append("下载上传方法耗时过长，建议增加超时时间或优化网络")
        
        self.results["recommendations"] = recommendations
        
        if recommendations:
            print("📋 建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        else:
            print("✅ 所有测试都通过，无特殊建议")
    
    def save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"oss_video_upload_test_{timestamp}.json"
        filepath = Path("tests") / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 测试结果已保存到: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 保存测试结果失败: {str(e)}")
            return None
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("📊 测试摘要")
        print("="*60)
        
        url_test = self.results.get("url_accessibility")
        if url_test:
            status = "✅ 可访问" if url_test.get("accessible") else "❌ 不可访问"
            print(f"URL可访问性: {status}")
        
        direct_test = self.results.get("direct_copy_test")
        if direct_test:
            status = "✅ 成功" if direct_test.get("success") else "❌ 失败"
            duration = direct_test.get("duration", "N/A")
            print(f"直接复制方法: {status} ({duration}s)")
        
        download_test = self.results.get("download_upload_test")
        if download_test:
            status = "✅ 成功" if download_test.get("success") else "❌ 失败"
            duration = download_test.get("duration", "N/A")
            print(f"下载上传方法: {status} ({duration}s)")
        
        comparison = self.results.get("performance_comparison")
        if comparison and comparison.get("faster_method"):
            method_name = "直接复制" if comparison["faster_method"] == "direct_copy" else "下载上传"
            improvement = comparison.get("improvement_percentage", 0)
            print(f"推荐方法: {method_name} (性能提升 {improvement}%)")
        
        print("="*60)
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🎯 开始OSS视频上传测试")
        print("="*60)
        
        # 测试URL可访问性
        await self.test_url_accessibility()
        
        # 如果URL不可访问，仍然尝试上传测试（有些服务器不支持HEAD请求但支持GET请求）
        if not self.results.get("url_accessibility", {}).get("accessible"):
            print("⚠️ HEAD请求失败，但仍然尝试GET请求进行下载测试...")
            print("💡 某些服务器可能不支持HEAD请求但支持GET请求")
        
        # 测试两种上传方法
        # await self.test_direct_copy_method()
        await self.test_download_upload_method()
        
        # 性能分析
        self.analyze_performance()
        
        # 生成建议
        self.generate_recommendations()
        
        # 保存结果
        self.save_results()
        
        # 打印摘要
        self.print_summary()


async def main():
    """主函数"""
    tester = OSSVideoUploadTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
