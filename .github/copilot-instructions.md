# MirageMakers AI - AI Agent Instructions

## Project Overview
This is a full-stack AI content generation platform built with Django + Next.js. The system enables multimodal AI interactions including image generation, video creation, and intelligent chat.

## Architecture Overview
- **Frontend**: Next.js app with real-time capabilities
- **Backend**: Django REST API + Celery for async tasks
- **Services**: Redis (caching/queues), MySQL (production DB)
- **AI Integration**: DashScope API for AI services

## Key Patterns & Conventions

### Frontend Patterns
1. **Component Structure**
```jsx
// Components use explicit prop types with defaults
const Component = ({
    prop1 = defaultValue,
    prop2,
    ...props
}) => {
    // State management at top
    const [state, setState] = useState();
    
    // Handlers after state
    const handleEvent = useCallback(() => {
        // Implementation
    }, [deps]);

    return (...)
};
```

2. **Styling**
- Use Tailwind CSS with consistent class ordering
- Custom styles in `frontend/styles/`
- Use dynamic classes with template literals for conditional styling

3. **State Management**
- React Context for global state (`contexts/`)
- Local state for component-specific data
- Optimistic updates for better UX

### Backend Patterns
1. **API Structure**
- RESTful endpoints in `api/views/`
- Business logic in service classes
- Celery tasks for async operations

2. **Error Handling**
```python
try:
    result = await service.operation()
except ServiceException as e:
    raise HTTPException(status_code=e.status_code, detail=str(e))
```

3. **Task Processing**
- Long-running tasks go through Celery
- Use Redis for task queue and results
- Task status updates via WebSocket

## Development Workflow

### Setup
```bash
# Frontend
cd frontend
npm install
npm run dev

# Backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

### Testing
- Frontend: Jest + React Testing Library
- Backend: Django test suite
- Run tests: `python manage.py test`

### Debugging
- Use Django Debug Toolbar for backend
- React DevTools for frontend
- Redis CLI for queue inspection

## Common Tasks

### Adding New Features
1. Backend:
   - Add models in `app/models.py`
   - Create API views in `api/views/`
   - Register URLs in `api/urls.py`

2. Frontend:
   - Add components in `frontend/components/`
   - Update state in relevant context
   - Add API calls in `frontend/services/`

### AI Integration
- AI service calls go through `app/agents/`
- Response handling in `app/adapters/`
- Error handling follows standard patterns

## Reference Files
- Architecture: `docker/README.md`
- Frontend patterns: `frontend/components/creative/`
- Backend patterns: `app/main.py`
- API docs: `docs/`
