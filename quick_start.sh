#!/bin/bash

# ========================================
# miragemakers AI 快速启动脚本 (自动化版本)
# ========================================

echo "🚀 快速启动 miragemakers AI 系统..."

# 设置脚本在出错时退出
set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 定义端口变量
DJANGO_PORT=8000
FRONTEND_PORT=3000
REDIS_PORT=6379
MYSQL_PORT=3306

# 函数：检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if lsof -ti:$port >/dev/null 2>&1; then
        echo "✅ $service 端口 $port 已被占用"
        return 0
    else
        echo "❌ $service 端口 $port 未占用"
        return 1
    fi
}

# 函数：获取Redis端口
get_redis_port() {
    local port=$(redis-cli -p 6379 CONFIG GET port 2>/dev/null | tail -1)
    if [ -n "$port" ] && [ "$port" != "6379" ]; then
        echo "$port"
    else
        echo "6379"
    fi
}

# 函数：获取MySQL端口
get_mysql_port() {
    local port=$(mysql -e "SHOW VARIABLES LIKE 'port';" 2>/dev/null | grep port | awk '{print $2}')
    if [ -n "$port" ]; then
        echo "$port"
    else
        echo "3306"
    fi
}

# 检查虚拟环境，如果不存在则创建
if [ ! -d ".venv" ]; then
    echo "📦 创建虚拟环境..."
    python3.11 -m venv .venv
    echo "✅ 虚拟环境创建完成"
fi

# 激活虚拟环境
echo "⚙️  激活虚拟环境..."
source .venv/bin/activate
echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"

# 安装Python依赖
echo "📦 检查并安装Python依赖..."
pip install -r requirements.txt >/dev/null 2>&1 || {
    echo "❌ Python依赖安装失败"
    exit 1
}
echo "✅ Python依赖已安装"

# 检查并安装前端依赖
echo "📦 检查前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install >/dev/null 2>&1 || {
        echo "❌ 前端依赖安装失败"
        exit 1
    }
    echo "✅ 前端依赖已安装"
else
    echo "✅ 前端依赖已存在"
fi
cd ..

# 检查Redis
echo "🔄 检查Redis服务..."
if ! redis-cli ping >/dev/null 2>&1; then
    echo "⚠️  Redis未运行，正在启动..."
    if command -v brew >/dev/null 2>&1; then
        brew services start redis >/dev/null 2>&1
        sleep 3
    else
        echo "❌ 请手动启动Redis服务"
        exit 1
    fi
    if ! redis-cli ping >/dev/null 2>&1; then
        echo "❌ Redis启动失败"
        exit 1
    fi
fi
echo "✅ Redis服务正常"

# 检查MySQL
echo "🗄️  检查MySQL服务..."
if ! mysqladmin ping >/dev/null 2>&1; then
    echo "⚠️  MySQL未运行，正在启动..."
    if command -v brew >/dev/null 2>&1; then
        # 尝试启动MySQL服务
        brew services start mysql >/dev/null 2>&1
        sleep 5
        # 如果brew服务启动失败，尝试直接启动mysqld
        if ! mysqladmin ping >/dev/null 2>&1; then
            echo "⚠️  正在尝试直接启动MySQL..."
            sudo mysqld_safe --user=mysql --datadir=/opt/homebrew/var/mysql >/dev/null 2>&1 &
            sleep 5
        fi
    else
        echo "⚠️  尝试启动MySQL服务..."
        sudo systemctl start mysql >/dev/null 2>&1 || sudo service mysql start >/dev/null 2>&1 || true
        sleep 3
    fi

    # 再次检查MySQL状态
    if ! mysqladmin ping >/dev/null 2>&1; then
        echo "❌ MySQL启动失败，请手动启动MySQL服务"
        echo "💡 macOS: brew services start mysql"
        echo "💡 Linux: sudo systemctl start mysql"
        exit 1
    fi
fi
echo "✅ MySQL服务正常"

# 清理旧进程
echo "🧹 清理旧进程..."
pkill -f "celery.*worker" 2>/dev/null || true
pkill -f "manage.py runserver" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
kill $(lsof -ti:8000) 2>/dev/null || true
kill $(lsof -ti:3000) 2>/dev/null || true
rm -f /tmp/celery.pid
sleep 2
echo "✅ 清理完成"

# 数据库迁移
echo "📊 数据库迁移..."
python manage.py migrate --noinput >/dev/null 2>&1
echo "✅ 数据库准备完成"

echo ""
echo "🔧 自动启动所有服务..."

# 显示数据库服务状态
echo "🔄 Redis服务已启动并运行"
echo "🗄️  MySQL服务已启动并运行"

# 启动Celery Worker (后台)
echo "⚙️  启动Celery Worker..."
celery -A app.celery_app worker --loglevel=info --queues=default --concurrency=2 > /tmp/celery.log 2>&1 &
CELERY_PID=$!
echo "✅ Celery Worker已启动 (PID: $CELERY_PID)"

# 启动Django Backend (后台)
echo "🔧 启动Django Backend..."
python manage.py runserver $DJANGO_PORT > /tmp/django.log 2>&1 &
DJANGO_PID=$!
echo "✅ Django Backend已启动 (PID: $DJANGO_PID)"

# 启动Next.js Frontend (后台)
echo "🎨 启动Next.js Frontend..."
cd frontend
npm run dev -- --hostname 127.0.0.1 --port $FRONTEND_PORT > /tmp/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..
echo "✅ Next.js Frontend已启动 (PID: $FRONTEND_PID)"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo ""
echo "🔍 检查服务状态..."

# 检查后端
if curl -s http://127.0.0.1:$DJANGO_PORT/ >/dev/null 2>&1; then
    echo "✅ Django Backend 运行正常"
else
    echo "❌ Django Backend 启动失败，查看日志: tail /tmp/django.log"
fi

# 检查前端
if curl -s http://127.0.0.1:$FRONTEND_PORT/ >/dev/null 2>&1; then
    echo "✅ Next.js Frontend 运行正常"
else
    echo "❌ Next.js Frontend 启动失败，查看日志: tail /tmp/frontend.log"
fi

# 检查Celery
if ps -p $CELERY_PID > /dev/null 2>&1; then
    echo "✅ Celery Worker 运行正常"
else
    echo "❌ Celery Worker 启动失败，查看日志: tail /tmp/celery.log"
fi

# 检查Redis并获取实际端口
if redis-cli ping >/dev/null 2>&1; then
    ACTUAL_REDIS_PORT=$(get_redis_port)
    echo "✅ Redis 服务运行正常 (端口: $ACTUAL_REDIS_PORT)"
else
    echo "❌ Redis 服务异常"
fi

# 检查MySQL并获取实际端口
if mysqladmin ping >/dev/null 2>&1; then
    ACTUAL_MYSQL_PORT=$(get_mysql_port)
    echo "✅ MySQL 服务运行正常 (端口: $ACTUAL_MYSQL_PORT)"
else
    echo "❌ MySQL 服务异常"
fi

# 显示状态
echo ""
echo "🎉 系统启动完成！"
echo "======================================"
echo "📱 Next.js Frontend: http://127.0.0.1:$FRONTEND_PORT"
echo "🔧 Django Backend: http://127.0.0.1:$DJANGO_PORT"
echo "🔄 Redis缓存: 127.0.0.1:${ACTUAL_REDIS_PORT:-$REDIS_PORT}"
echo "🗄️  MySQL数据库: 127.0.0.1:${ACTUAL_MYSQL_PORT:-$MYSQL_PORT}"
echo "⚙️  Celery PID: $CELERY_PID"
echo "📊 Django PID: $DJANGO_PID"
echo "🎨 Frontend PID: $FRONTEND_PID"
echo "======================================"
echo ""
echo "💡 运行测试: python system_test.py"
echo "🛑 停止服务: ./stop_server.sh 或 Ctrl+C"
echo "📄 查看日志:"
echo "   应用服务: tail /tmp/{celery,django,frontend}.log"
echo "   Redis: brew services list | grep redis 或 redis-cli info"
echo "   MySQL: brew services list | grep mysql 或 tail /opt/homebrew/var/mysql/*.err"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止所有服务..."
    kill $CELERY_PID $DJANGO_PID $FRONTEND_PID 2>/dev/null || true
    sleep 2
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup INT TERM

# 等待用户中断
echo "⏳ 系统运行中... (按 Ctrl+C 停止)"
wait