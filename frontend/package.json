{"name": "frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --hostname 0.0.0.0 --port 3000", "dev:local": "next dev --hostname 127.0.0.1 --port 3000", "build": "next build", "build:analyze": "ANALYZE=true npm run build", "start": "next start --hostname 0.0.0.0 --port 3000", "start:local": "next start --hostname 127.0.0.1 --port 3000", "lint": "next lint", "bundle-analyze": "ANALYZE=true npm run build"}, "keywords": [], "author": "", "license": "ISC", "description": "Next.js frontend for Visual Generation Agent", "dependencies": {"@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "framer-motion": "^11.15.0", "lucide-react": "^0.263.1", "next": "^14.1.0", "postcss": "^8.4.35", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.1.0", "eslint": "^8", "eslint-config-next": "15.4.1"}}