import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Layout from '../components/Layout';

const StripeTroubleshootPage = () => {
    const [networkTests, setNetworkTests] = useState({
        stripe: null,
        api: null,
        dns: null
    });

    useEffect(() => {
        testConnections();
    }, []);

    const testConnections = async () => {
        // Test Stripe connectivity
        try {
            await fetch('https://api.stripe.com/healthcheck', {
                method: 'HEAD',
                mode: 'no-cors'
            });
            setNetworkTests(prev => ({ ...prev, stripe: true }));
        } catch (error) {
            setNetworkTests(prev => ({ ...prev, stripe: false }));
        }

        // Test local API
        try {
            const response = await fetch('/api/health/', { method: 'GET' });
            setNetworkTests(prev => ({ ...prev, api: response.ok }));
        } catch (error) {
            setNetworkTests(prev => ({ ...prev, api: false }));
        }

        // Test DNS resolution
        try {
            await fetch('https://*******/', {
                method: 'HEAD',
                mode: 'no-cors'
            });
            setNetworkTests(prev => ({ ...prev, dns: true }));
        } catch (error) {
            setNetworkTests(prev => ({ ...prev, dns: false }));
        }
    };

    const getStatusIcon = (status) => {
        if (status === null) return '⏳';
        return status ? '✅' : '❌';
    };

    const getStatusText = (status) => {
        if (status === null) return 'Testing...';
        return status ? 'Connected' : 'Failed';
    };

    return (
        <Layout showNavbar={true} navbarVariant="transparent">
            <Head>
                <title>Stripe Network Troubleshooting - MirageMakers AI</title>
            </Head>

            <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 pt-20">
                <div className="max-w-4xl mx-auto p-6">
                    <div className="bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl p-8">
                        <h1 className="text-3xl font-bold text-white mb-8">
                            🔧 Stripe Network Troubleshooting
                        </h1>

                        {/* Network Tests */}
                        <div className="mb-8">
                            <h2 className="text-xl font-semibold text-white mb-4">
                                Network Connectivity Tests
                            </h2>
                            <div className="space-y-3">
                                <div className="flex justify-between items-center p-3 bg-white/5 rounded-lg">
                                    <span className="text-gray-300">Stripe API Access</span>
                                    <span className="text-lg">
                                        {getStatusIcon(networkTests.stripe)} {getStatusText(networkTests.stripe)}
                                    </span>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-white/5 rounded-lg">
                                    <span className="text-gray-300">Local API Backend</span>
                                    <span className="text-lg">
                                        {getStatusIcon(networkTests.api)} {getStatusText(networkTests.api)}
                                    </span>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-white/5 rounded-lg">
                                    <span className="text-gray-300">DNS Resolution</span>
                                    <span className="text-lg">
                                        {getStatusIcon(networkTests.dns)} {getStatusText(networkTests.dns)}
                                    </span>
                                </div>
                            </div>
                            <button
                                onClick={testConnections}
                                className="mt-4 px-4 py-2 bg-[#8b5cf6] hover:bg-[#7c3aed] text-white rounded-lg transition-colors"
                            >
                                🔄 Retest Connections
                            </button>
                        </div>

                        {/* Common Issues & Solutions */}
                        <div className="mb-8">
                            <h2 className="text-xl font-semibold text-white mb-4">
                                Common Issues & Solutions
                            </h2>

                            <div className="space-y-4">
                                {/* Ad Blocker Issue */}
                                <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                                    <h3 className="text-red-300 font-semibold mb-2">
                                        ❌ net::ERR_BLOCKED_BY_CLIENT
                                    </h3>
                                    <p className="text-gray-300 mb-3">
                                        This error indicates that requests to Stripe are being blocked by an ad blocker or browser extension.
                                    </p>
                                    <div className="text-sm text-gray-400">
                                        <strong>Solutions:</strong>
                                        <ul className="list-disc list-inside mt-2 space-y-1">
                                            <li>Temporarily disable ad blockers (uBlock Origin, AdBlock Plus, etc.)</li>
                                            <li>Add this site to your ad blocker's whitelist</li>
                                            <li>Try using an incognito/private window</li>
                                            <li>Switch to a different browser temporarily</li>
                                        </ul>
                                    </div>
                                </div>

                                {/* Database Issues */}
                                <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                                    <h3 className="text-yellow-300 font-semibold mb-2">
                                        ⚠️ Database Table Missing
                                    </h3>
                                    <p className="text-gray-300 mb-3">
                                        "Table 'authtoken_token' doesn't exist" - Database migration issue.
                                    </p>
                                    <div className="text-sm text-gray-400">
                                        <strong>This issue has been resolved:</strong>
                                        <ul className="list-disc list-inside mt-2 space-y-1">
                                            <li>Database migrations have been applied</li>
                                            <li>All required tables now exist</li>
                                            <li>Django backend is fully operational</li>
                                        </ul>
                                    </div>
                                </div>

                                {/* Network Security */}
                                <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                                    <h3 className="text-blue-300 font-semibold mb-2">
                                        🔐 Corporate Network/Firewall
                                    </h3>
                                    <p className="text-gray-300 mb-3">
                                        Some corporate networks block financial service domains.
                                    </p>
                                    <div className="text-sm text-gray-400">
                                        <strong>Solutions:</strong>
                                        <ul className="list-disc list-inside mt-2 space-y-1">
                                            <li>Contact your IT department to whitelist *.stripe.com</li>
                                            <li>Try using a mobile hotspot temporarily</li>
                                            <li>Use a different network (home Wi-Fi, public Wi-Fi)</li>
                                        </ul>
                                    </div>
                                </div>

                                {/* Browser Compatibility */}
                                <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                                    <h3 className="text-green-300 font-semibold mb-2">
                                        ✅ Environment Configuration Fixed
                                    </h3>
                                    <p className="text-gray-300 mb-3">
                                        All environment variables and configurations have been updated.
                                    </p>
                                    <div className="text-sm text-gray-400">
                                        <strong>Fixed issues:</strong>
                                        <ul className="list-disc list-inside mt-2 space-y-1">
                                            <li>Stripe API keys properly configured</li>
                                            <li>Invalid colorBackground values corrected</li>
                                            <li>Database connections restored</li>
                                            <li>All Docker services healthy</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Test Stripe Loading */}
                        <div className="mb-8">
                            <h2 className="text-xl font-semibold text-white mb-4">
                                Test Stripe Integration
                            </h2>
                            <button
                                onClick={async () => {
                                    try {
                                        const { loadStripe } = await import('@stripe/stripe-js');
                                        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
                                        if (stripe) {
                                            alert('✅ Stripe loaded successfully!');
                                        } else {
                                            alert('❌ Failed to load Stripe - check API key');
                                        }
                                    } catch (error) {
                                        alert(`❌ Stripe loading error: ${error.message}`);
                                    }
                                }}
                                className="px-6 py-3 bg-gradient-to-r from-[#6d28d9] to-[#8b5cf6] text-white rounded-lg hover:from-[#5b21b6] hover:to-[#7c3aed] transition-all"
                            >
                                🧪 Test Stripe SDK Loading
                            </button>
                        </div>

                        {/* Environment Info */}
                        <div className="mb-8">
                            <h2 className="text-xl font-semibold text-white mb-4">
                                Environment Information
                            </h2>
                            <div className="p-4 bg-white/5 rounded-lg font-mono text-sm">
                                <div className="text-gray-300">User Agent: {typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'}</div>
                                <div className="text-gray-300">API URL: {process.env.NEXT_PUBLIC_API_URL || 'Not set'}</div>
                                <div className="text-gray-300">Stripe Key Available: {!!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'Yes' : 'No'}</div>
                                <div className="text-gray-300">Current Time: {new Date().toISOString()}</div>
                            </div>
                        </div>

                        {/* Navigation */}
                        <div className="text-center space-x-4">
                            <a
                                href="/"
                                className="inline-block px-6 py-3 bg-gradient-to-r from-[#8b5cf6] to-[#ec4899] text-white rounded-lg hover:shadow-lg transition-all"
                            >
                                🔙 Back to Home
                            </a>
                            <a
                                href="/test-env"
                                className="inline-block px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                            >
                                🔍 Environment Test
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
};

export default StripeTroubleshootPage; 