function generateSiteMap() {
    const baseUrl = 'https://miragemakers.ai';

    // 定义网站的公开页面（与robots.txt保持一致）
    const staticPages = [
        '',           // 首页
        '/auth/login', // 登录页面
        '/auth/register', // 注册页面
    ];

    return `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${staticPages
            .map((page) => {
                return `
                    <url>
                        <loc>${baseUrl}${page}</loc>
                        <lastmod>${new Date().toISOString()}</lastmod>
                        <changefreq>weekly</changefreq>
                        <priority>${page === '' ? '1.0' : page === '/chat' ? '0.9' : '0.8'}</priority>
                    </url>
                `;
            })
            .join('')}
    </urlset>
    `;
}

function SiteMap() {
    // getServerSideProps 会处理这个
}

export async function getServerSideProps({ res }) {
    // 生成 XML sitemap
    const sitemap = generateSiteMap();

    res.setHeader('Content-Type', 'text/xml');
    res.setHeader('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate');

    // 向浏览器发送XML
    res.write(sitemap);
    res.end();

    return {
        props: {},
    };
}

export default SiteMap; 