// Creative页面 - 基于即梦layout的全新设计
import CreativePage from '../components/creative/CreativePage';

// 防止MetaMask扩展错误
if (typeof window !== 'undefined') {
    // 防止MetaMask注入错误
    window.addEventListener('error', (event) => {
        if (event.error && event.error.message &&
            (event.error.message.includes('MetaMask') ||
                event.error.message.includes('ChromeTransport') ||
                event.error.message.includes('provider-injection'))) {
            event.preventDefault();
            console.warn('MetaMask extension error suppressed:', event.error.message);
            return false;
        }
    });

    // 防止未处理的Promise rejection
    window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && event.reason.message &&
            (event.reason.message.includes('MetaMask') ||
                event.reason.message.includes('ChromeTransport') ||
                event.reason.message.includes('provider-injection'))) {
            event.preventDefault();
            console.warn('MetaMask promise rejection suppressed:', event.reason.message);
            return false;
        }
    });
}

// 导出Creative页面组件
export default function Creative() {
    return <CreativePage />;
}
