import React, { useState, useRef, useEffect } from 'react';
import Head from 'next/head';
import axios from 'axios';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';
import { useUserContext } from '../contexts/UserContext';
import { useToast } from '../components/Toast';
import LanguageSwitcher from '../components/LanguageSwitcher';
import ModernNavbar from '../components/ModernNavbar';
import UserAvatar from '../components/UserAvatar';
import Loading from '../components/Loading';
import UpgradeModal from '../components/UpgradeModal';
import LoadingDots from '../components/LoadingDots';
import ReferenceSelector from '../components/ReferenceSelector';
import ReferencePreview from '../components/ReferencePreview';
import ErrorBoundary from '../components/ErrorBoundary';
import { useAtMention } from '../hooks/useAtMention';
import { getCsrfToken } from '../utils/csrf';

// 防止MetaMask扩展错误
if (typeof window !== 'undefined') {
    // 防止MetaMask注入错误
    window.addEventListener('error', (event) => {
        if (event.error && event.error.message &&
            (event.error.message.includes('MetaMask') ||
                event.error.message.includes('ChromeTransport') ||
                event.error.message.includes('provider-injection'))) {
            event.preventDefault();
            console.warn('MetaMask extension error suppressed:', event.error.message);
            return false;
        }
    });

    // 防止未处理的Promise rejection
    window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && event.reason.message &&
            (event.reason.message.includes('MetaMask') ||
                event.reason.message.includes('ChromeTransport') ||
                event.reason.message.includes('provider-injection'))) {
            event.preventDefault();
            console.warn('MetaMask promise rejection suppressed:', event.reason.message);
            return false;
        }
    });
}

// 配置axios默认设置
axios.defaults.withCredentials = true;
axios.defaults.timeout = 30000; // 30秒默认超时

// 添加请求拦截器
axios.interceptors.request.use(
    (config) => {
        // 确保所有请求都包含必要的头部
        config.headers = {
            ...config.headers,
            'X-Requested-With': 'XMLHttpRequest',
        };
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 添加响应拦截器
axios.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        // 统一处理网络错误
        if (error.code === 'ERR_NETWORK' || error.code === 'ERR_CONNECTION_CLOSED') {
            console.error('network connection error:', error.message);
            // 可以在这里添加重试逻辑或显示友好的错误信息
        }
        return Promise.reject(error);
    }
);

// 简化界面，移除特性按钮
const TEXT_CHAT_MODE = 'chat';
const TEXT_TO_IMG_MODE = 'text2img';
const TEXT_TO_VIDEO_MODE = 'text2video';
const IMG_TO_IMG_MODE = 'img2img';
const IMG_TO_VIDEO_MODE = 'img2video';

// 图片模态框组件
function ImageModal({ src, alt, isOpen, onClose }) {
    const { t } = useLanguage();

    if (!isOpen) return null;

    const handleDownload = async () => {
        try {
            // 使用代理方式避免CORS问题
            const proxyUrl = `/api/download-proxy/?url=${encodeURIComponent(src)}`;
            const response = await fetch(proxyUrl, {
                method: 'GET',
                headers: {
                    'Authorization': localStorage.getItem('token') ? `Token ${localStorage.getItem('token')}` : '',
                },
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`Download failed: ${response.status}`);
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `MirageMakers-image-${Date.now()}.jpg`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();

            // 清理
            setTimeout(() => {
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }, 100);

        } catch (error) {
            console.error('download failed:', error);
            // 降级方案：直接打开图片
            window.open(src, '_blank');
        }
    };

    return (
        <div
            className="fixed inset-0 bg-black/80 z-40 flex items-center justify-center p-4"
            onClick={onClose}
        >
            <div className="relative max-w-5xl max-h-[90vh] w-full h-full flex items-center justify-center">
                {/* 关闭按钮 */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-colors z-10"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>

                {/* 下载按钮 */}
                <button
                    onClick={handleDownload}
                    className="absolute top-4 right-16 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-colors z-10"
                    title={t('image.download')}
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                        <polyline points="7,10 12,15 17,10" />
                        <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                </button>

                {/* 图片 */}
                <img
                    src={src}
                    alt={alt}
                    className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                    onClick={(e) => e.stopPropagation()}
                />
            </div>
        </div>
    );
}

// LoadingDots 组件已移至共享组件文件

// 简化思考动画组件 - 只保留旋转光环
function ThinkingAnimation({ startTime, estimatedTime = 30, taskType = 'text_to_image' }) {
    const { language } = useLanguage();
    const [elapsedTime, setElapsedTime] = useState(0);
    const [currentStage, setCurrentStage] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            const now = Date.now();
            const elapsed = Math.floor((now - (startTime || now)) / 1000);
            setElapsedTime(elapsed);

            // 根据时间和任务类型更新阶段
            const isVideoTask = ['video_gen', 'img2video', 'text_to_video', 'image_to_video', 'multi_step_composite'].includes(taskType);
            if (isVideoTask) {
                // 视频生成有更长的阶段
                if (elapsed >= 480) { // 8分钟
                    setCurrentStage(5); // 视频渲染最终阶段
                } else if (elapsed >= 300) { // 5分钟
                    setCurrentStage(4); // 视频合成中
                } else if (elapsed >= 180) { // 3分钟
                    setCurrentStage(3); // 视频渲染中
                } else if (elapsed >= 120) { // 2分钟
                    setCurrentStage(2); // 图像分析完成，开始视频生成
                } else if (elapsed >= 60) { // 1分钟
                    setCurrentStage(1); // 图像分析中
                } else {
                    setCurrentStage(0); // 初始分析
                }
            } else {
                // 普通生成任务
                if (elapsed >= 30) {
                    setCurrentStage(3); // 长时间等待
                } else if (elapsed >= 15) {
                    setCurrentStage(2); // 复杂处理
                } else if (elapsed >= 8) {
                    setCurrentStage(1); // 深度分析
                } else {
                    setCurrentStage(0); // 初始思考
                }
            }
        }, 1000);

        return () => clearInterval(interval);
    }, [startTime, taskType]);

    // 计算进度百分比 - 智能进度计算
    const isVideoTask = ['video_gen', 'img2video', 'text_to_video', 'image_to_video', 'multi_step_composite'].includes(taskType);

    // 确保estimatedTime是数字，处理字符串情况
    const parseEstimatedTime = (time) => {
        if (typeof time === 'number') return time;
        if (typeof time === 'string') {
            // 尝试提取数字，如果是"3-6 minutes"这样的格式，取较大值
            const numbers = time.match(/\d+/g);
            if (numbers && numbers.length > 0) {
                return parseInt(numbers[numbers.length - 1]) * 60; // 转换为秒
            }
        }
        return 30; // 默认30秒
    };

    const validEstimatedTime = parseEstimatedTime(estimatedTime);
    const maxTime = isVideoTask ? 1800 : Math.max(validEstimatedTime, 90); // 最少90秒，视频生成最多18分钟

    // 智能进度计算：前期增长快，后期减慢
    let progress = 0;
    if (elapsedTime > 0) {
        const baseProgress = (elapsedTime / maxTime) * 100;

        // 使用对数函数让进度更自然：前期快速增长，后期缓慢
        if (baseProgress <= 80) {
            progress = Math.min(baseProgress, 80);
        } else {
            // 超过80%后，进度增长变得很慢
            progress = 80 + (baseProgress - 80) * 0.3;
        }

        // 确保progress是有效数字
        progress = Math.min(isNaN(progress) ? 0 : progress, 90);
    }

    // 不同阶段的提示文字
    const getStageText = () => {
        if (language === 'zh') {
            if (isVideoTask) {
                switch (currentStage) {
                    case 0: return '正在分析您的需求...';
                    case 1: return '正在分析您的图像...';
                    case 2: return '分析完成，开始生成视频...';
                    case 3: return '视频生成中，请耐心等待...';
                    case 4: return '视频合成处理中...';
                    case 5: return '视频渲染最终阶段...';
                    default: return '正在处理...';
                }
            } else {
                switch (currentStage) {
                    case 0: return '正在分析您的需求...';
                    case 1: return '正在制定生成计划...';
                    case 2: return '正在调用AI服务生成...';
                    case 3: return '生成中，请耐心等待...';
                    default: return '正在思考...';
                }
            }
        } else {
            if (isVideoTask) {
                switch (currentStage) {
                    case 0: return 'Analyzing your request...';
                    case 1: return 'Analyzing your images...';
                    case 2: return 'Analysis is complete, starting video generation...';
                    case 3: return 'Generating video, please be patient...';
                    case 4: return 'Video composition in progress...';
                    case 5: return 'Final video rendering...';
                    default: return 'Processing...';
                }
            } else {
                switch (currentStage) {
                    case 0: return 'Analyzing your request...';
                    case 1: return 'Creating generation plan...';
                    case 2: return 'Calling AI services...';
                    case 3: return 'Generating, please wait...';
                    default: return 'Thinking...';
                }
            }
        }
    };

    // 获取估计时间提示 - 统一改为12-18分钟
    const getTimeEstimate = () => {
        if (isVideoTask) {
            return language === 'zh' ? '预计需要 12-18 分钟' : 'Estimated 12-18 minutes';
        } else {
            return language === 'zh' ? '预计需要 12-18 分钟' : 'Estimated 12-18 minutes';
        }
    };

    return (
        <div className="flex flex-col space-y-3 w-full">
            {/* 主要思考动画 */}
            <div className="flex items-center space-x-3">
                {/* AI图标 */}
                <div className="relative">
                    <div className="w-6 h-6 bg-gradient-to-br from-purple-400 via-violet-500 to-blue-500 rounded-lg flex items-center justify-center shadow-lg">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" className="text-white">
                            <path d="M12 2L2 7V17L12 22L22 17V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            <path d="M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            <path d="M2 7L12 12L22 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    </div>
                    {/* 外层旋转光环 */}
                    <div className="absolute -inset-1 rounded-lg border-2 border-purple-400/40 animate-spin" style={{ animationDuration: '2s' }}></div>
                    {/* 内层快速光环 */}
                    <div className="absolute inset-0.5 rounded-md border border-white/30 animate-spin" style={{ animationDuration: '1s', animationDirection: 'reverse' }}></div>
                </div>

                {/* 思考文字和时间 */}
                <div className="flex flex-col">
                    <span className="text-white/90 text-sm font-medium">
                        {getStageText()}
                    </span>
                    <div className="flex items-center space-x-2 mt-1">
                        {elapsedTime > 5 && (
                            <span className="text-white/60 text-xs">
                                {language === 'zh' ? `已用时 ${elapsedTime}s` : `${elapsedTime}s elapsed`}
                            </span>
                        )}
                    </div>
                </div>
            </div>

            {/* 进度条 - 8秒后显示 */}
            {elapsedTime > 8 && (
                <div className="w-full">
                    <div className="flex justify-between text-xs text-white/60 mb-1">
                        <span>{language === 'zh' ? '处理进度' : 'Progress'}</span>
                        <span>{Math.round(progress)}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                        <div
                            className="bg-gradient-to-r from-purple-400 to-blue-500 h-2 rounded-full transition-all duration-1000 ease-out"
                            style={{ width: `${progress}%` }}
                        ></div>
                    </div>
                    {/* 更详细的等待提示 */}
                    {elapsedTime > 15 && (
                        <div className="text-xs text-white/60 mt-1 text-center">
                            {isVideoTask ? (
                                language === 'zh' ?
                                    '视频生成需要较长时间，请耐心等待，请勿关闭页面' :
                                    'Video generation takes time, please be patient and do not close the page'
                            ) : (
                                language === 'zh' ?
                                    '复杂任务处理中，感谢您的耐心等待' :
                                    'Complex task in progress, thank you for your patience'
                            )}
                        </div>
                    )}
                    {/* 视频生成的额外提示 */}
                    {isVideoTask && elapsedTime > 60 && (
                        <div className="text-xs text-white/50 mt-1 text-center">
                            {language === 'zh' ?
                                '正在进行高质量视频渲染，预计还需 7-9 分钟' :
                                'High-quality video rendering in progress, estimated 7-9 minutes remaining'
                            }
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

function MessageBubble({ message, isUser }) {
    const [showImageModal, setShowImageModal] = useState(false);
    const { t } = useLanguage();



    const handleDownload = async () => {
        try {
            // 使用代理方式避免CORS问题
            const proxyUrl = `/api/download-proxy/?url=${encodeURIComponent(message.image_url)}`;
            const response = await fetch(proxyUrl, {
                method: 'GET',
                headers: {
                    'Authorization': user ? `Token ${localStorage.getItem('token')}` : '',
                },
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`Download failed: ${response.status}`);
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `MirageMakers-image-${Date.now()}.jpg`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();

            // 清理
            setTimeout(() => {
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }, 100);

        } catch (error) {
            console.error('download failed:', error);
            // 降级方案：直接打开图片
            window.open(message.image_url, '_blank');
        }
    };

    // 获取引用媒体数据 - 优先使用后端返回的数据
    const getReferencedMedia = () => {
        // 1. 优先使用后端返回的 referenced_media 数据
        if (message.referenced_media && message.referenced_media.length > 0) {
            return message.referenced_media;
        }

        // 2. 如果没有后端数据，尝试从 metadata 中获取
        if (message.metadata && message.metadata.referenced_media && message.metadata.referenced_media.length > 0) {
            return message.metadata.referenced_media;
        }

        // 3. 最后回退到解析文本内容中的@引用
        if (message.content) {
            const mentionRegex = /@(\d+|image_\d+|video_\d+)/g;
            const mentions = [];
            let match;

            while ((match = mentionRegex.exec(message.content)) !== null) {
                const referenceId = match[1];
                let type = 'unknown';

                // 判断引用类型
                if (referenceId.startsWith('image_')) {
                    type = 'image';
                } else if (referenceId.startsWith('video_')) {
                    type = 'video';
                } else if (/^\d+$/.test(referenceId)) {
                    // 简化格式的数字引用，类型未知，显示为占位符
                    type = 'media';
                }

                mentions.push({
                    reference_id: referenceId,
                    type: type,
                    url: null, // 没有URL，会显示占位符
                    display_name: `Reference ${referenceId}`
                });
            }

            return mentions;
        }

        return [];
    };

    const referencedMedia = getReferencedMedia();

    return (
        <>
            <div className={`w-full flex mb-8 ${isUser ? 'justify-end' : 'justify-start'}`}>
                <div className={`flex items-start max-w-[85%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
                    {/* 消息内容容器 */}
                    <div className={`flex flex-col w-full ${isUser ? 'items-end' : 'items-start'}`}>
                        {/* 文本消息气泡 */}
                        {(message.content || message.isLoading) && (
                            <div className={`${isUser ? 'bg-purple-600 text-white' : 'bg-black/40 text-white backdrop-blur-xl'} p-6 rounded-[2rem] ${isUser ? 'rounded-br-lg' : 'rounded-bl-lg'} shadow-lg w-full`}>
                                {isUser && message.isLoading ? (
                                    <LoadingDots />
                                ) : message.sender === 'bot' && message.isLoading ? (
                                    <ThinkingAnimation
                                        startTime={message.startTime || Date.now()}
                                        estimatedTime={message.estimated_time || 30}
                                        taskType={message.taskType || 'text_to_image'}
                                    />
                                ) : (
                                    <div className="text-lg leading-relaxed whitespace-pre-wrap">
                                        {message.content}
                                    </div>
                                )}
                            </div>
                        )}

                        {/* 被引用的媒体显示区域 - 移动到文字下方 */}
                        {referencedMedia.length > 0 && (
                            <div className={`${message.content ? 'mt-4' : ''} ${isUser ? 'items-end' : 'items-start'} flex flex-col w-full`}>
                                {/* 引用媒体网格 */}
                                <div className={`flex flex-wrap gap-2 ${isUser ? 'justify-end' : 'justify-start'} mb-2`}>
                                    {referencedMedia.map((media, index) => (
                                        <div key={index} className="relative group">
                                            <div
                                                className="relative cursor-pointer overflow-hidden rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20"
                                                style={{ width: '80px', height: '80px' }}
                                                onClick={() => {
                                                    if (media.type === 'image' && media.url) {
                                                        setShowImageModal(media.url);
                                                    }
                                                }}
                                            >
                                                {media.url ? (
                                                    // 有URL的情况 - 显示实际媒体
                                                    media.type === 'image' ? (
                                                        <img
                                                            src={media.url}
                                                            alt={`Referenced image ${media.reference_id}`}
                                                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                                        />
                                                    ) : (
                                                        <video
                                                            src={media.url}
                                                            className="w-full h-full object-cover rounded-xl"
                                                            preload="metadata"
                                                        >
                                                            Your browser does not support the video tag.
                                                        </video>
                                                    )
                                                ) : (
                                                    // 没有URL的情况 - 显示占位符
                                                    <div className="w-full h-full flex flex-col items-center justify-center bg-gray-800 text-white text-center border-2 border-orange-500/50 rounded-xl">
                                                        <div className="text-2xl mb-1">
                                                            {media.type === 'image' ? '🖼️' :
                                                                media.type === 'video' ? '🎥' :
                                                                    '📎'}
                                                        </div>
                                                        <div className="text-xs opacity-75">
                                                            {media.type === 'media' ? 'Ref' : media.type}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* References 标签 */}
                                <div className={`${isUser ? 'self-end' : 'self-start'}`}>
                                    <div className="bg-purple-900/30 text-purple-300 text-xs px-3 py-1 rounded-full border border-purple-500/20">
                                        {referencedMedia.length} references
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* 图片显示区域 */}
                        {message.image_url && (
                            <div className={`${message.content || referencedMedia.length > 0 ? 'mt-4' : ''} relative group`}>
                                <div
                                    className="relative cursor-pointer overflow-hidden rounded-3xl transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/20"
                                    onClick={() => setShowImageModal(message.image_url)}
                                >
                                    <img
                                        src={message.image_url}
                                        alt="Generated content"
                                        className="w-full h-auto max-w-[500px] rounded-3xl hover:scale-[1.02] transition-transform duration-500"
                                        style={{ maxHeight: '600px', objectFit: 'contain' }}
                                    />

                                    {/* 下载按钮 */}
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleDownload(message.image_url);
                                        }}
                                        className="absolute top-3 right-3 w-10 h-10 bg-black/60 hover:bg-black/80 rounded-full flex items-center justify-center text-white transition-all duration-200 opacity-0 group-hover:opacity-100 hover:scale-110"
                                        title={t('image.download')}
                                    >
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                            <polyline points="7,10 12,15 17,10" />
                                            <line x1="12" y1="15" x2="12" y2="3" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* 视频显示区域 */}
                        {message.video_url && (
                            <div className={`${message.content || message.image_url || referencedMedia.length > 0 ? 'mt-4' : ''} relative group`}>
                                <div
                                    className="relative overflow-hidden rounded-3xl transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/20"
                                    style={{ maxWidth: '500px' }}
                                >
                                    <video
                                        src={message.video_url}
                                        controls
                                        className="w-full h-auto rounded-3xl"
                                        style={{ maxHeight: '400px' }}
                                        preload="metadata"
                                    >
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* 图片模态框 */}
            <ImageModal
                src={message.image_url}
                alt="Generated image"
                isOpen={showImageModal}
                onClose={() => setShowImageModal(false)}
            />
        </>
    );
}

function SessionItem({ session, active, onClick, onDelete, onRename }) {
    const { language } = useLanguage();
    const [isRenaming, setIsRenaming] = useState(false);
    const [newTitle, setNewTitle] = useState(session.title);

    // 🔧 重要修复: 当session.title变化时，同步更新newTitle状态
    useEffect(() => {
        setNewTitle(session.title);
    }, [session.title]);

    const handleRename = (e) => {
        e.stopPropagation();
        if (isRenaming) {
            onRename(session.id, newTitle);
            setIsRenaming(false);
        } else {
            setIsRenaming(true);
        }
    };

    const handleDelete = (e) => {
        e.stopPropagation();
        onDelete(session.id);
    };

    const formatCreatedTime = (dateString) => {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffInHours = (now - date) / (1000 * 60 * 60);

            if (diffInHours < 24) {
                return language === 'zh' ? '今天' : 'Today';
            } else if (diffInHours < 48) {
                return language === 'zh' ? '昨天' : 'Yesterday';
            } else if (diffInHours < 168) { // 7 days
                const dayNames = language === 'zh'
                    ? ['日', '一', '二', '三', '四', '五', '六']
                    : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                return dayNames[date.getDay()];
            } else {
                return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', {
                    month: 'short',
                    day: 'numeric'
                });
            }
        } catch (error) {
            return '';
        }
    };

    return (
        <div
            className={`relative group p-4 rounded-2xl cursor-pointer transition-all duration-300 mb-2 ${active
                ? 'bg-purple-500/15 backdrop-blur-xl shadow-lg shadow-purple-500/10'
                : 'hover:bg-black/20 backdrop-blur-sm'
                }`}
            onClick={onClick}
        >
            <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                    {isRenaming ? (
                        <input
                            type="text"
                            value={newTitle}
                            onChange={(e) => setNewTitle(e.target.value)}
                            onBlur={() => setIsRenaming(false)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    onRename(session.id, newTitle);
                                    setIsRenaming(false);
                                }
                                if (e.key === 'Escape') {
                                    setNewTitle(session.title);
                                    setIsRenaming(false);
                                }
                            }}
                            className="w-full bg-transparent text-white text-sm font-medium focus:outline-none border-b border-purple-400"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                        />
                    ) : (
                        <div className="text-white text-sm font-medium truncate pr-2">
                            {session.title}
                        </div>
                    )}
                    <div className="text-white/60 text-xs mt-1">
                        {formatCreatedTime(session.created_at)}
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                        onClick={handleRename}
                        className="p-2 text-white hover:text-white hover:bg-purple-500/20 rounded-lg transition-all duration-200"
                        title={language === 'zh' ? '重命名' : 'Rename'}
                    >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                        </svg>
                    </button>
                    <button
                        onClick={handleDelete}
                        className="p-2 text-white hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200"
                        title={language === 'zh' ? '删除' : 'Delete'}
                    >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3,6 5,6 21,6" />
                            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );
}

export default function Chat() {
    const router = useRouter();
    const { t, language } = useLanguage();
    const { user, loading: userLoading, error: userError, updateUser, updateBalance, profile, actions } = useUserContext();
    const toast = useToast();
    const [sessions, setSessions] = useState([]);
    const [activeSessionId, setActiveSessionId] = useState(null);
    const [messages, setMessages] = useState([]);
    const [currentPrompt, setCurrentPrompt] = useState('');
    const [currentFile, setCurrentFile] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [csrfToken, setCsrfToken] = useState('');
    const [showSessions, setShowSessions] = useState(false);
    const [advancedMode, setAdvancedMode] = useState(false);
    const [currentMode, setMode] = useState(TEXT_CHAT_MODE);
    const [pendingRequest, setPendingRequest] = useState(null);
    const [sidebarOpen, setSidebarOpen] = useState(true);

    // User interface state
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const [tokenBalance, setTokenBalance] = useState(null);
    const [membershipInfo, setMembershipInfo] = useState({
        current_plan: 'Free',
        current_balance: 0,
        is_expired: false
    });
    const [selectedImage, setSelectedImage] = useState(null);
    const [previewReference, setPreviewReference] = useState(null); // For referenced media preview
    const [authCheckComplete, setAuthCheckComplete] = useState(false);
    const [sessionMedia, setSessionMedia] = useState([]); // 会话媒体数据
    const [apiConnectionError, setApiConnectionError] = useState(null); // API连接错误状态

    const messagesEndRef = useRef(null);
    const textareaRef = useRef(null);
    const fileInputRef = useRef(null);

    // @mention functionality
    const {
        isReferenceDropdownOpen,
        referenceSearchQuery,
        dropdownPosition,
        selectedReferences,
        visualReferences,
        handleInputChange: handleAtMentionInputChange,
        handleKeyDown: handleAtMentionKeyDown,
        handleReferenceSelect,
        closeReferenceDropdown,
        removeReference,
        parseReferences,
        processMessageForSubmission,
        clearReferences,
        reset: resetAtMention,
        clearAtSymbol,
        forceCleanAtSymbol
    } = useAtMention(textareaRef, setCurrentPrompt);

    // 使用ref来跟踪上一次的消息数量，避免每次输入都触发滚动
    const prevMessageCountRef = useRef(0);

    useEffect(() => {
        // 只有当消息数量真正增加时才滚动（新增消息）
        if (messages.length > prevMessageCountRef.current) {
            const timeoutId = setTimeout(() => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            prevMessageCountRef.current = messages.length;
            return () => clearTimeout(timeoutId);
        } else {
            // 更新计数但不滚动（消息内容更新）
            prevMessageCountRef.current = messages.length;
        }
    }, [messages]);

    // 开发模式下的双重执行检测
    useEffect(() => {
        if (process.env.NODE_ENV === 'development') {

        }
    }, []);

    useEffect(() => {
        // 自动调整文本框高度
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, [currentPrompt]);

    // 处理用户登录状态检查和重定向
    useEffect(() => {
        const token = localStorage.getItem('token');

        // 如果没有token，直接重定向
        if (!token) {
            setAuthCheckComplete(true);
            router.push('/');
            return;
        }

        // 设置一个延迟，给UserContext时间加载用户信息
        const timer = setTimeout(() => {
            setAuthCheckComplete(true);
            // 如果有token但是用户加载完成后仍然没有用户信息，则重定向
            if (!userLoading && !user) {
                console.log('用户认证失败，重定向到首页');
                router.push('/');
            }
        }, 2000); // 等待2秒让UserContext完成加载

        // 如果用户已经加载，清除定时器
        if (user) {
            clearTimeout(timer);
            setAuthCheckComplete(true);
        }

        return () => clearTimeout(timer);
    }, [user, userLoading, router]);

    // 处理已登录用户的数据加载（只执行一次）
    useEffect(() => {
        console.log('🔄 User effect triggered:', {
            user: !!user,
            sessionsLength: sessions.length,
            activeSessionId,
            messagesLength: messages.length
        });

        // 只有当用户已登录且还没有加载会话时才加载
        if (user && sessions.length === 0 && !activeSessionId) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🚀 Loading chat sessions for user:', user.id);

                // 先进行健康检查，然后加载会话
                testAPIConnection().then(isHealthy => {
                    if (isHealthy) {
                        loadChatSessions(token);
                    } else {
                        console.error('❌ API健康检查失败，无法加载会话');
                    }
                });
            }
        }
    }, [user]); // 只依赖user，避免循环依赖

    // 单独处理活动会话的消息加载
    useEffect(() => {
        // 如果有用户、有会话列表、有活动会话ID，但没有消息，则加载消息
        if (user && sessions.length > 0 && activeSessionId && messages.length === 0) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🎯 Loading messages for active session:', activeSessionId);
                loadChatMessages(activeSessionId, token);
            }
        }
    }, [user, activeSessionId, messages.length]); // 依赖这些特定状态

    // 从UserContext同步token余额数据
    useEffect(() => {
        if (user && user.tokens !== undefined) {
            // 直接使用user.tokens，确保数据一致性
            setTokenBalance(user.tokens);
            setMembershipInfo({
                current_plan: user?.current_plan || 'Free',
                current_balance: user.tokens, // 使用user.tokens而不是profile.balance
                is_expired: user?.tokens_expires_at ? new Date(user.tokens_expires_at) < new Date() : false,
                tokens_expires_at: user?.tokens_expires_at,
                total_generations: user?.total_generations || 0
            });
        } else if (profile && profile.balance !== undefined) {
            // 备用：如果user.tokens不可用，使用profile.balance
            setTokenBalance(profile.balance);
            setMembershipInfo({
                current_plan: user?.current_plan || 'Free',
                current_balance: profile.balance,
                is_expired: user?.tokens_expires_at ? new Date(user.tokens_expires_at) < new Date() : false,
                tokens_expires_at: user?.tokens_expires_at,
                total_generations: user?.total_generations || 0
            });
        }
    }, [user, profile]); // 优先依赖user数据

    // CSRF token获取（独立执行）
    useEffect(() => {
        fetchCsrfToken();
    }, []); // 只在组件挂载时执行一次

    // 刷新token余额和会员信息（现在依赖UserContext）
    const refreshTokenBalance = () => {
        if (actions && actions.fetchUser) {
            actions.fetchUser(); // 使用UserContext的fetchUser方法
        }
    };

    // 暴露到全局window对象，供支付按钮调用
    useEffect(() => {
        window.refreshTokenBalance = refreshTokenBalance;
        return () => {
            delete window.refreshTokenBalance;
        };
    }, [actions]);

    // 加载用户聊天会话
    const loadChatSessions = async (token) => {
        try {
            console.log('🔄 Loading chat sessions...');
            const response = await axios.get('/api/chat/sessions/', {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000, // 10秒超时
                withCredentials: true, // 包含认证cookie
                validateStatus: function (status) {
                    return status < 500; // 只有500+才算错误
                }
            });

            console.log('📋 Sessions response status:', response.status);
            console.log('📋 Sessions response data:', response.data);

            // 处理认证错误
            if (response.status === 401 || response.status === 403) {
                console.log('🔐 Token已过期，清除本地存储');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                updateUser(null);
                router.push('/');
                return;
            }

            // 处理成功响应
            if (response.status === 200 && response.data.sessions && response.data.sessions.length > 0) {
                // 🔧 简化: 由于logout时已清除本地状态，直接使用服务器数据
                setSessions(response.data.sessions);

                console.log('✅ Sessions loaded from server:', {
                    count: response.data.sessions.length,
                    firstSession: response.data.sessions[0]?.title
                });

                // 只在没有活动会话时设置第一个会话为活动会话
                if (!activeSessionId && response.data.sessions.length > 0) {
                    const firstSessionId = response.data.sessions[0].id;
                    setActiveSessionId(firstSessionId);
                    console.log('✅ Sessions loaded, activating first session:', firstSessionId);
                    // 加载第一个会话的消息
                    await loadChatMessages(firstSessionId, token);
                }
            } else {
                // 如果没有会话，设置空状态
                console.log('📋 No sessions found, setting empty state');
                setSessions([]);
                setActiveSessionId(null);
                setMessages([]);
            }
        } catch (error) {
            console.error('❌ Loading chat sessions failed:', error);

            // 详细错误处理
            if (error.response) {
                console.error('Sessions API error:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                });

                // 如果是认证错误，清除本地存储并重定向
                if (error.response.status === 401 || error.response.status === 403) {
                    console.log('🔐 Authentication failed, clearing tokens');
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    updateUser(null);
                    router.push('/');
                    return;
                }
            } else {
                console.error('Network or other error:', error.message);
            }

            // 其他错误，设置空状态让用户可以继续使用
            console.log('Setting empty state due to error');
            setSessions([]);
            setActiveSessionId(null);
            setMessages([]);
        }
    };

    // 获取会话媒体数据，用于@引用功能 - 使用新的Agent API
    const loadSessionMedia = async (sessionId, token) => {
        try {
            console.log(`🔄 Loading session media for: ${sessionId}`);

            // 首先尝试新的媒体历史API
            const response = await axios.get(`/api/chat/sessions/${sessionId}/media-history/`, {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000,
                validateStatus: function (status) {
                    return status < 500; // 只有500+才算错误
                }
            });

            if (response.status === 200 && response.data.success && response.data.media_messages) {
                const mediaMessages = response.data.media_messages.map((msg, index) => ({
                    id: msg.id,
                    message_id: msg.id,
                    reference_id: msg.reference_id || `${msg.type}_${index + 1}`,
                    type: msg.type,
                    url: msg.url,
                    content: msg.content || '',
                    display_name: msg.display_name || `${msg.type === 'image' ? 'Image' : 'Video'} ${index + 1}`,
                    created_at: msg.created_at
                }));
                setSessionMedia(mediaMessages);
                console.log('✅ Session media loaded (new API):', mediaMessages.length);
            } else {
                console.log('📭 No session media found (new API)');
                setSessionMedia([]);
            }
        } catch (error) {
            console.warn('⚠️ New media API failed, trying fallback:', error.response?.status || error.message);

            // 降级处理：使用原有方法
            try {
                const response = await axios.get(`/api/sessions/${sessionId}/media/`, {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 10000,
                    validateStatus: function (status) {
                        return status < 500;
                    }
                });

                if (response.status === 200 && response.data.media_messages) {
                    setSessionMedia(response.data.media_messages);
                    console.log('✅ Session media loaded (fallback):', response.data.media_messages.length);
                } else {
                    console.log('📭 No session media found (fallback)');
                    setSessionMedia([]);
                }
            } catch (fallbackError) {
                console.warn('⚠️ Session media loading failed (fallback also failed):', fallbackError.response?.status || fallbackError.message);
                setSessionMedia([]);
            }
        }
    };

    // 加载指定会话的消息
    const loadChatMessages = async (sessionId, token) => {
        try {
            console.log(`🔄 Loading messages for session: ${sessionId}`);
            const response = await axios.get(`/api/sessions/${sessionId}/messages/`, {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000,
                validateStatus: function (status) {
                    return status < 500; // 只有500+才算错误
                }
            });

            console.log('📨 Messages response status:', response.status);
            console.log('📨 Messages response data:', response.data);

            // 检查响应状态
            if (response.status === 404) {
                console.log('📭 Session not found or no messages');
                setMessages([]);
                return;
            }

            if (response.status === 401 || response.status === 403) {
                console.log('🔐 Authentication failed, redirecting to login');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                updateUser(null);
                router.push('/');
                return;
            }

            if (response.data.messages && response.data.messages.length > 0) {
                // 确保消息格式正确映射
                const formattedMessages = response.data.messages.map(msg => {
                    const formattedMsg = {
                        id: msg.id,
                        sender: msg.type === 'user' ? 'user' : 'bot',
                        content: msg.content,
                        image_url: msg.image_url,
                        video_url: msg.video_url,
                        created_at: msg.created_at,
                        metadata: msg.metadata || {},
                        referenced_media: msg.referenced_media || [] // 确保包含引用媒体
                    };

                    return formattedMsg;
                });

                console.log(`✅ Setting ${formattedMessages.length} formatted messages`);
                setMessages(formattedMessages);

                // 只在有引用时显示调试信息
                const withReferences = formattedMessages.filter(msg => msg.referenced_media && msg.referenced_media.length > 0);
                if (withReferences.length > 0) {
                    console.log('✅ Loaded messages with references:', {
                        total: formattedMessages.length,
                        withReferences: withReferences.length
                    });
                }
            } else {
                console.log('📭 No messages found for session');
                setMessages([]);
            }

            // 尝试加载会话媒体数据（非关键，失败不影响主功能）
            try {
                await loadSessionMedia(sessionId, token);
            } catch (mediaError) {
                console.warn('⚠️ Session media loading failed (continuing anyway):', mediaError.message);
            }

        } catch (error) {
            console.error('❌ Failed to load chat messages:', error);

            // 详细错误日志
            if (error.response) {
                console.error('Error response:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                });

                // 根据错误状态码处理
                if (error.response.status === 401 || error.response.status === 403) {
                    console.log('🔐 Authentication failed, clearing tokens and redirecting');
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    updateUser(null);
                    router.push('/');
                    return;
                }
            } else {
                console.error('Network or other error:', error.message);
            }

            // 设置空状态但不阻止用户继续使用
            setMessages([]);
            setSessionMedia([]);
        }
    };

    // 获取CSRF令牌
    const fetchCsrfToken = async () => {
        try {
            const token = localStorage.getItem('token');

            const response = await axios.get("/api/generate/", {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Accept': 'application/json'
                },
                withCredentials: true,
                timeout: 10000,
                validateStatus: function (status) {
                    return status < 500;
                }
            });

            // 从cookie中提取CSRF令牌
            const cookies = document.cookie.split(';');
            const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('csrftoken='));

            if (csrfCookie) {
                const token = csrfCookie.split('=')[1];
                setCsrfToken(token);
                console.log('CSRF令牌:', token);
            }
        } catch (error) {
            console.error('获取CSRF令牌失败:', error);
        }
    };

    const handleLogout = () => {
        // 清除认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        updateUser(null); // 使用UserContext的更新函数

        // 🔧 重要修复: 清除所有chat页面的本地状态，避免状态污染
        setSessions([]);
        setActiveSessionId(null);
        setMessages([]);
        setSessionMedia([]);
        setCurrentPrompt('');
        setCurrentFile(null);
        setIsLoading(false);

        // 重置文件输入
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }

        // 清除其他状态
        setShowUserMenu(false);
        setShowUpgradeModal(false);
        setSelectedImage(null);
        setPreviewReference(null);

        console.log('🔄 All local state cleared on logout');

        toast.success(t('auth.logout.success') || 'Logged out successfully');
        router.push('/');
    };

    const handleInputChange = (e) => {
        // Integrate @mention functionality - call it first
        handleAtMentionInputChange(e, (event) => {
            setCurrentPrompt(event.target.value);
        });
    };

    const handleKeyDown = (e) => {
        // Handle @mention keyboard events first
        handleAtMentionKeyDown(e, (event) => {
            // Original key down handler logic
            if (event.key === 'Enter' && !event.shiftKey && !isReferenceDropdownOpen) {
                event.preventDefault();
                handleSubmit(event);
            }
        });

        // Only handle Enter key if @mention dropdown is not open
        if (e.key === 'Enter' && !e.shiftKey && !isReferenceDropdownOpen) {
            e.preventDefault();
            handleSubmit(e);
        }
    };

    const handleNewSession = async () => {
        // 🔧 简化：因为chat页面要求登录，所以user一定存在
        try {
            const token = localStorage.getItem('token');
            const response = await axios.post('/api/chat/sessions/create/',
                { title: t('chat.new.session') },
                {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 10000,
                    withCredentials: true,
                    validateStatus: function (status) {
                        return status < 500;
                    }
                }
            );

            if (response.status === 201 && response.data.session) {
                const newSession = response.data.session;
                // 只添加新会话到列表顶部，不重新加载整个列表
                setSessions(prev => [newSession, ...prev]);
                setActiveSessionId(newSession.id);
                setMessages([]);

                console.log('✅ New session created successfully:', {
                    sessionId: newSession.id,
                    title: newSession.title
                });
            } else {
                throw new Error(`Unexpected response status: ${response.status}`);
            }
        } catch (error) {
            console.error('创建会话失败:', error);
            // 如果是认证错误，重定向到登录页
            if (error.response?.status === 401 || error.response?.status === 403) {
                handleLogout();
                return;
            }
            // 其他错误显示给用户
            toast.error('Failed to create new session. Please try again.');
        }
    };

    const handleSessionClick = (sessionId) => {
        console.log(`🎯 Session clicked: ${sessionId}, current active: ${activeSessionId}`);

        // 如果点击的是当前活动会话，不需要重新加载
        if (sessionId === activeSessionId && messages.length > 0) {
            console.log('Same session clicked, no reload needed');
            return;
        }

        setActiveSessionId(sessionId);
        const token = localStorage.getItem('token');

        // 清空当前消息（立即清空，避免显示旧消息）
        setMessages([]);

        // 重置引用状态
        resetAtMention();

        // 加载选中会话的消息
        console.log(`🔄 Loading messages for session: ${sessionId}`);
        loadChatMessages(sessionId, token);
    };

    const handleFileChange = (e) => {
        if (e.target.files && e.target.files.length > 0) {
            const files = Array.from(e.target.files);
            console.log('Files selected:', files.map(f => f.name));

            // For now, we'll handle the first file for backward compatibility
            // TODO: Implement multi-file upload support in backend
            setCurrentFile(files[0]);

            // If advanced mode is enabled and it's an image file, automatically switch to the appropriate mode
            if (advancedMode) {
                setMode(files[0].type.startsWith('image/') ? IMG_TO_IMG_MODE : TEXT_CHAT_MODE);
            }

            // Show notification about multiple file selection
            if (files.length > 1) {
                console.log(`Note: ${files.length} files selected, but currently processing only the first file: ${files[0].name}`);
                // TODO: Show user notification about multi-file limitation
            }
        }
    };

    const handlePlusClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const removeSelectedFile = () => {
        setCurrentFile(null);

        // Reset the file input element
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }

        // 如果在高级模式下，重置回文本聊天模式
        if (advancedMode) {
            setMode(TEXT_CHAT_MODE);
        }
    };

    const toggleAdvancedMode = () => {
        setAdvancedMode(!advancedMode);

        // 如果关闭高级模式，恢复为普通聊天模式
        if (advancedMode) {
            setMode(TEXT_CHAT_MODE);
        }
    };

    const handleModeChange = (mode) => {
        setMode(mode);
    };

    // 根据当前选择的模式和文件类型，判断是否显示相应的按钮
    const shouldShowButton = (buttonMode) => {
        if (!advancedMode) return false;

        if (!currentFile) {
            // 无文件状态下，只显示文本生成相关按钮
            return [TEXT_CHAT_MODE, TEXT_TO_IMG_MODE, TEXT_TO_VIDEO_MODE].includes(buttonMode);
        }

        if (currentFile.type.startsWith('image/')) {
            // 图片文件状态下，显示图像处理相关按钮
            return [TEXT_CHAT_MODE, IMG_TO_IMG_MODE, IMG_TO_VIDEO_MODE].includes(buttonMode);
        }

        // 视频文件状态下，只显示聊天按钮
        return buttonMode === TEXT_CHAT_MODE;
    };

    // Add task polling functionality - Enhanced version with better error handling
    const pollTaskStatus = async (taskId, messageIndex, messageId = null) => {
        const maxPollingTime = 10 * 60 * 1000; // 延长到10分钟最大轮询时间
        const pollInterval = 8000; // 8秒轮询间隔
        const startTime = Date.now();

        console.log('🚀 pollTaskStatus started for task:', taskId, 'messageIndex:', messageIndex, 'messageId:', messageId, 'maxPollingTime:', maxPollingTime);

        const poll = async () => {
            try {
                const token = localStorage.getItem('token');
                const config = {
                    headers: {
                        'Authorization': token ? `Token ${token}` : '',
                        'Accept': 'application/json',
                    },
                    withCredentials: true
                };

                // 使用新的任务状态检查API
                const params = new URLSearchParams({ task_id: taskId });
                if (messageId) {
                    params.append('message_id', messageId);
                }

                const response = await axios.get(`/api/task-status/?${params.toString()}`, config);
                const data = response.data;

                console.log(`🔄 Polling task status: ${taskId}`, data);

                if ((data.status === 'SUCCESS' || data.status === 'completed') && data.success) {
                    // Task completed successfully
                    console.log('🎉 Task completed successfully!', data);
                    const result = data.result || {};

                    // 🔍 Enhanced debugging for result processing
                    console.log('📊 Task result details:', {
                        chat_response: result.chat_response,
                        image_url: result.image_url,
                        video_url: result.video_url,
                        tokens_consumed: result.tokens_consumed,
                        hasImageUrl: !!result.image_url,
                        hasVideoUrl: !!result.video_url,
                        imageUrlType: typeof result.image_url,
                        videoUrlType: typeof result.video_url
                    });

                    // 🔧 Enhanced result processing with fallback mechanisms
                    let finalImageUrl = result.image_url || null;
                    let finalVideoUrl = result.video_url || null;
                    let finalChatResponse = result.chat_response || result.message || 'Generation completed successfully!';

                    // 🔍 Check for alternative result formats (in case of revert issues)
                    if (!finalImageUrl && !finalVideoUrl) {
                        console.warn('⚠️ No media URLs found in standard format, checking alternatives...');

                        // Try to extract from different possible locations
                        if (data.image_url) {
                            finalImageUrl = data.image_url;
                            console.log('✅ Found image_url in root response:', finalImageUrl);
                        }
                        if (data.video_url) {
                            finalVideoUrl = data.video_url;
                            console.log('✅ Found video_url in root response:', finalVideoUrl);
                        }

                        // Check if result is a string that might contain URLs
                        if (typeof result === 'string') {
                            console.log('🔍 Result is string, checking for URLs:', result);
                            const urlRegex = /https?:\/\/[^\s]+/g;
                            const urls = result.match(urlRegex);
                            if (urls && urls.length > 0) {
                                const url = urls[0];
                                if (url.includes('.mp4') || url.includes('.mov') || url.includes('video')) {
                                    finalVideoUrl = url;
                                    console.log('✅ Extracted video URL from string result:', finalVideoUrl);
                                } else if (url.includes('.jpg') || url.includes('.png') || url.includes('.jpeg') || url.includes('image')) {
                                    finalImageUrl = url;
                                    console.log('✅ Extracted image URL from string result:', finalImageUrl);
                                }
                            }
                        }
                    }

                    // 🔍 Final validation
                    if (!finalImageUrl && !finalVideoUrl) {
                        console.warn('⚠️ Still no media URLs found, but task marked as successful');
                        finalChatResponse = finalChatResponse || 'Task completed, but no media was generated. Please try again.';
                    }

                    setMessages(prev => {
                        const newMessages = [...prev];
                        if (newMessages[messageIndex]) {
                            newMessages[messageIndex] = {
                                ...newMessages[messageIndex],
                                content: finalChatResponse,
                                image_url: finalImageUrl,
                                video_url: finalVideoUrl,
                                isTaskCompleted: true,
                                isLoading: false,
                                taskId: null,
                                tokens_consumed: result.tokens_consumed || 0,
                                // Enhanced debugging info
                                debugInfo: {
                                    taskId: taskId,
                                    originalResult: result,
                                    processedAt: new Date().toISOString(),
                                    foundMedia: {
                                        image: !!finalImageUrl,
                                        video: !!finalVideoUrl
                                    }
                                },
                                // 延迟2秒后移除加载状态，确保用户能看到完成效果
                                shouldRemoveLoadingDelay: true
                            };
                        }
                        return newMessages;
                    });

                    // 延迟移除加载状态，让用户看到完成效果
                    setTimeout(() => {
                        setMessages(prev => {
                            const newMessages = [...prev];
                            if (newMessages[messageIndex] && newMessages[messageIndex].shouldRemoveLoadingDelay) {
                                newMessages[messageIndex] = {
                                    ...newMessages[messageIndex],
                                    shouldRemoveLoadingDelay: false
                                };
                            }
                            return newMessages;
                        });
                    }, 2000);

                    // 刷新Token余额
                    if (result.tokens_consumed > 0) {
                        refreshTokenBalance();
                    }

                    return; // Stop polling
                } else if (data.status === 'FAILURE' || data.status === 'failed' || (!data.success && data.error)) {
                    // Task failed
                    console.log('❌ Task failed:', data.error);

                    // 🔍 Enhanced error logging
                    console.error('🔍 Task failure details:', {
                        taskId: taskId,
                        status: data.status,
                        success: data.success,
                        error: data.error,
                        fullResponse: data
                    });

                    setMessages(prev => {
                        const newMessages = [...prev];
                        if (newMessages[messageIndex]) {
                            newMessages[messageIndex] = {
                                ...newMessages[messageIndex],
                                content: `Generation failed: ${data.error || 'Unknown error'}`,
                                error: data.error || 'Task execution failed',
                                isLoading: false,
                                taskId: null,
                                // Enhanced debugging info
                                debugInfo: {
                                    taskId: taskId,
                                    failureReason: data.error,
                                    failedAt: new Date().toISOString(),
                                    fullResponse: data
                                }
                            };
                        }
                        return newMessages;
                    });
                    return; // Stop polling
                } else if (data.status === 'PENDING' || data.status === 'RETRY' || data.status === 'STARTED' || data.status === 'PROGRESS' || data.status === 'processing' || (data.success === null && data.message)) {
                    // Task still in progress
                    const elapsed = Date.now() - startTime;

                    // Update waiting message with elapsed time
                    const waitingMinutes = Math.floor(elapsed / 60000);
                    const waitingSeconds = Math.floor((elapsed % 60000) / 1000);
                    let waitingMessage = data.message || 'Processing your request...';

                    if (elapsed > 60000) { // Over 1 minute
                        waitingMessage = `Please be patient, generation is in progress... (${waitingMinutes}m ${waitingSeconds}s)`;
                    }

                    // 🔍 Enhanced progress logging
                    if (elapsed % 30000 === 0) { // Log every 30 seconds
                        console.log(`⏱️ Task ${taskId} still in progress:`, {
                            elapsed: `${waitingMinutes}m ${waitingSeconds}s`,
                            status: data.status,
                            message: data.message
                        });
                    }

                    setMessages(prev => {
                        const newMessages = [...prev];
                        if (newMessages[messageIndex]) {
                            newMessages[messageIndex] = {
                                ...newMessages[messageIndex],
                                content: waitingMessage,
                                isLoading: true,
                                taskId: taskId
                            };
                        }
                        return newMessages;
                    });

                    if (elapsed < maxPollingTime) {
                        // Continue polling
                        setTimeout(poll, pollInterval);
                    } else {
                        // Timeout - 但不放弃，提示用户任务仍在后台运行
                        console.warn(`⏰ Task ${taskId} polling timeout after ${maxPollingTime}ms`);
                        setMessages(prev => {
                            const newMessages = [...prev];
                            if (newMessages[messageIndex]) {
                                newMessages[messageIndex] = {
                                    ...newMessages[messageIndex],
                                    content: 'Generation is taking longer than expected. The task is still running in the background. Please refresh the page later to check the result.',
                                    isLoading: false,
                                    taskId: null
                                };
                            }
                            return newMessages;
                        });
                    }
                } else {
                    // Unknown status, log and continue polling for debugging
                    console.log('⚠️ Unknown task status:', data.status, 'Data:', data);

                    // 🔍 Enhanced unknown status logging
                    console.warn('🔍 Unknown status details:', {
                        taskId: taskId,
                        status: data.status,
                        success: data.success,
                        hasResult: !!data.result,
                        hasError: !!data.error,
                        fullResponse: data
                    });

                    const elapsed = Date.now() - startTime;

                    if (elapsed < maxPollingTime) {
                        // Continue polling for unknown status
                        setTimeout(poll, pollInterval);
                    } else {
                        // Timeout
                        setMessages(prev => {
                            const newMessages = [...prev];
                            if (newMessages[messageIndex]) {
                                newMessages[messageIndex] = {
                                    ...newMessages[messageIndex],
                                    content: 'Task status unclear. Please refresh the page to check for results.',
                                    isLoading: false,
                                    taskId: null
                                };
                            }
                            return newMessages;
                        });
                    }
                }
            } catch (error) {
                console.error('Polling task status failed:', error);

                // 🔍 Enhanced error logging
                console.error('🔍 Polling error details:', {
                    taskId: taskId,
                    messageIndex: messageIndex,
                    errorMessage: error.message,
                    errorStack: error.stack,
                    errorStatus: error.response?.status,
                    errorData: error.response?.data,
                    timestamp: new Date().toISOString()
                });

                // 🔧 关键修复：检查HTTP错误状态码
                if (error.response) {
                    const status = error.response.status;
                    const errorData = error.response.data;

                    if (status === 402) {
                        // 🔍 Token不足错误 - 立即停止轮询并显示错误
                        console.log('💰 Token insufficient error detected, stopping polling');

                        const tokenError = errorData.error || 'Insufficient token balance';
                        const tokenDetails = errorData.tokens_required ?
                            `Required: ${errorData.tokens_required}, Current: ${errorData.current_balance || 0}` : '';

                        setMessages(prev => {
                            const newMessages = [...prev];
                            if (newMessages[messageIndex]) {
                                newMessages[messageIndex] = {
                                    ...newMessages[messageIndex],
                                    content: `${tokenError}${tokenDetails ? ` (${tokenDetails})` : ''}`,
                                    error: tokenError,
                                    errorType: 'insufficient_balance',
                                    isLoading: false,
                                    taskId: null,
                                    // 添加token错误的样式标识
                                    isTokenError: true
                                };
                            }
                            return newMessages;
                        });

                        // 刷新token余额显示
                        refreshTokenBalance();
                        return; // 立即停止轮询
                    }

                    if (status === 401) {
                        // 🔍 认证错误 - 停止轮询并跳转到登录页
                        console.log('🔐 Authentication error detected, stopping polling');

                        setMessages(prev => {
                            const newMessages = [...prev];
                            if (newMessages[messageIndex]) {
                                newMessages[messageIndex] = {
                                    ...newMessages[messageIndex],
                                    content: 'Authentication required. Please log in again.',
                                    error: 'Authentication failed',
                                    isLoading: false,
                                    taskId: null,
                                    isAuthError: true
                                };
                            }
                            return newMessages;
                        });

                        // 跳转到登录页
                        setTimeout(() => {
                            router.push('/auth/login');
                        }, 2000);
                        return; // 立即停止轮询
                    }

                    if (status === 400) {
                        // 🔍 业务逻辑错误 - 停止轮询并显示错误
                        console.log('❌ Business logic error detected, stopping polling');

                        const businessError = errorData.error || 'Task execution failed';

                        setMessages(prev => {
                            const newMessages = [...prev];
                            if (newMessages[messageIndex]) {
                                newMessages[messageIndex] = {
                                    ...newMessages[messageIndex],
                                    content: `Generation failed: ${businessError}`,
                                    error: businessError,
                                    errorType: errorData.error_type || 'task_failed',
                                    isLoading: false,
                                    taskId: null
                                };
                            }
                            return newMessages;
                        });
                        return; // 立即停止轮询
                    }
                }

                const elapsed = Date.now() - startTime;

                // 对于网络错误或其他未知错误，继续轮询
                if (elapsed < maxPollingTime) {
                    // 🔍 Log retry attempt
                    console.log(`🔄 Retrying polling for task ${taskId} in ${pollInterval}ms...`);
                    setTimeout(poll, pollInterval);
                } else {
                    // 轮询超时，提示用户任务可能仍在运行
                    console.error(`💥 Final polling timeout for task ${taskId}`);
                    setMessages(prev => {
                        const newMessages = [...prev];
                        if (newMessages[messageIndex]) {
                            newMessages[messageIndex] = {
                                ...newMessages[messageIndex],
                                content: 'Network polling timeout. Your task may still be processing. Please refresh the page to check for results.',
                                isLoading: false,
                                taskId: null
                            };
                        }
                        return newMessages;
                    });
                }
            }
        };

        // Start polling immediately
        setTimeout(poll, 1000); // Start first poll after 1 second
    };

    // 🔍 Debug helper function for analyzing task issues
    const debugTaskStatus = async (taskId) => {
        try {
            const token = localStorage.getItem('token');
            const config = {
                headers: {
                    'Authorization': token ? `Token ${token}` : '',
                    'Accept': 'application/json',
                },
                withCredentials: true
            };

            const response = await axios.get(`/api/task-status/?task_id=${taskId}`, config);
            console.log('🔍 [DEBUG] Task Status Analysis:', {
                taskId: taskId,
                timestamp: new Date().toISOString(),
                response: response.data,
                statusCode: response.status,
                headers: response.headers
            });

            // Log detailed analysis
            const data = response.data;
            console.log('🔍 [DEBUG] Detailed Analysis:', {
                hasResult: !!data.result,
                hasImageUrl: !!(data.result?.image_url),
                hasVideoUrl: !!(data.result?.video_url),
                hasChatResponse: !!(data.result?.chat_response),
                resultKeys: data.result ? Object.keys(data.result) : [],
                fullResult: data.result
            });

            return data;
        } catch (error) {
            console.error('🔍 [DEBUG] Task status debug failed:', error);
            return null;
        }
    };

    // 🔍 Add debug info to window for manual inspection
    if (typeof window !== 'undefined') {
        window.debugTaskStatus = debugTaskStatus;
        window.debugInfo = {
            messages: messages,
            currentTaskIds: messages.filter(m => m.taskId).map(m => m.taskId),
            lastPollingData: null
        };
    }

    const handleSubmit = async (e) => {
        e.preventDefault();

        if ((!currentPrompt.trim() && !currentFile) || isLoading) {
            return;
        }

        // Prevent duplicate submissions
        if (typeof window !== 'undefined' && window.submitInProgress) {
            console.log('Request in progress, ignoring duplicate submission');
            return;
        }
        if (typeof window !== 'undefined') {
            window.submitInProgress = true;
        }

        // Create session if none exists
        let sessionId = activeSessionId;
        if (!sessionId) {
            try {
                const token = localStorage.getItem('token');
                const response = await axios.post('/api/chat/sessions/create/',
                    { title: t('chat.new.session') },
                    {
                        headers: {
                            'Authorization': `Token ${token}`,
                            'Content-Type': 'application/json',
                        },
                    }
                );
                sessionId = response.data.session.id;
                const newSession = response.data.session;
                // 只添加新会话，不重新加载整个列表
                setSessions(prev => [newSession, ...prev]);
                setActiveSessionId(sessionId);

                console.log('✅ New session created during submit:', {
                    sessionId,
                    title: newSession.title
                });
            } catch (error) {
                console.error('Failed to create session:', error);
                // 如果是认证错误，重定向到登录页
                if (error.response?.status === 401 || error.response?.status === 403) {
                    handleLogout();
                    return;
                }
                // 其他错误，抛出异常停止提交
                throw new Error('Failed to create session');
            }
        }

        // Process @mentions in the message
        const { message: processedMessage, references: messageReferences, referencedMessageIds: messageReferencedIds, selectedReferences: messageSelectedReferences } = processMessageForSubmission(currentPrompt);

        const formData = new FormData();
        formData.append('prompt', processedMessage);
        formData.append('mode', currentMode);
        formData.append('session_id', sessionId);

        // Add reference information if present
        if (messageReferencedIds.length > 0) {
            formData.append('referenced_message_ids', JSON.stringify(messageReferencedIds));
            formData.append('has_references', 'true');
        }

        if (currentFile) {
            formData.append('media', currentFile);
        }

        // Add user message to chat
        const userMessage = {
            sender: 'user',
            content: currentPrompt,
            image_url: currentFile && currentFile.type.startsWith('image/') ? URL.createObjectURL(currentFile) : null,
            video_url: currentFile && currentFile.type.startsWith('video/') ? URL.createObjectURL(currentFile) : null,
            referenced_media: messageSelectedReferences.length > 0 ? messageSelectedReferences.map((ref, index) => ({
                reference_id: `${index + 1}`, // 使用简化编号格式
                type: ref.type,
                url: ref.url,
                display_name: ref.display_name || `Reference ${index + 1}`,
                message_id: ref.message_id // 保留原始消息ID
            })) : []
        };

        console.log('👤 User message with references:', {
            content: currentPrompt.substring(0, 50) + '...',
            referencesCount: messageSelectedReferences.length,
            references: userMessage.referenced_media.map(ref => ({
                id: ref.reference_id,
                type: ref.type,
                url: ref.url ? 'has_url' : 'no_url'
            }))
        });

        // 初始化为未知任务类型，将由后端响应确定
        let taskType = 'unknown';

        // Update messages with user input and loading state
        setMessages(prev => [...prev, userMessage, {
            sender: 'bot',
            isLoading: true,
            content: null,
            startTime: Date.now(), // 记录开始时间用于进度计算
            taskType: taskType // 初始任务类型，将由响应更新
        }]);

        // Clear input and file, but keep references until response is complete
        setCurrentPrompt('');
        setCurrentFile(null);

        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }

        setIsLoading(true);

        try {
            // 根据是否有文件上传选择请求格式
            let config;
            let requestData;
            const token = localStorage.getItem('token');

            if (currentFile) {
                // 有文件上传，使用FormData格式
                requestData = new FormData();
                requestData.append('prompt', processedMessage);
                requestData.append('mode', currentMode);
                requestData.append('session_id', sessionId);
                requestData.append('media', currentFile);

                // 添加引用信息
                if (messageSelectedReferences.length > 0) {
                    requestData.append('references', JSON.stringify(messageSelectedReferences));
                }

                config = {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        // 不设置Content-Type，让浏览器自动设置multipart/form-data
                    },
                    withCredentials: true,
                    timeout: 360000, // 6 minutes timeout
                };
            } else {
                // 无文件上传，使用JSON格式
                requestData = {
                    session_id: sessionId,
                    prompt: processedMessage,
                    mode: currentMode,
                    references: messageSelectedReferences.length > 0 ? messageSelectedReferences : [],
                    attachments: []
                };

                config = {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    withCredentials: true,
                    timeout: 360000, // 6 minutes timeout
                };
            }

            console.log('🚀 Sending request to unified AI service:', {
                endpoint: '/api/generate/',
                hasFile: !!currentFile,
                references: messageSelectedReferences.length,
                mode: currentMode
            });

            const response = await axios.post("/api/generate/", requestData, config);

            console.log('✅ Agent API response:', response.data);

            // 从后端响应获取任务类型
            taskType = response.data.task_type || 'text_to_image';
            const hasVideoResult = response.data.video_url;
            const isVideoTask = ['video_gen', 'img2video', 'text_to_video', 'image_to_video', 'multi_step_composite'].includes(taskType) || hasVideoResult;

            // 对于视频生成任务，延迟移除加载状态以保持进度提示更长时间
            if (isVideoTask && hasVideoResult) {
                // 视频生成完成，延迟2秒移除加载状态，让用户看到最终阶段提示
                setTimeout(() => {
                    setMessages(prev => prev.filter(msg => !msg.isLoading));
                }, 2000);
            } else {
                // 立即移除加载状态
                setMessages(prev => prev.filter(msg => !msg.isLoading));
            }

            // 处理Agent API响应格式
            if (response.data.success) {
                const agentResult = response.data;  // 直接使用response.data

                // 检查是否是异步任务（视频生成等）
                if (agentResult.async_task && agentResult.task_id) {
                    // 异步任务处理
                    const processingMessage = {
                        sender: 'bot',
                        content: agentResult.chat_response || agentResult.message || 'Processing your request... This may take 3-6 minutes.',
                        taskId: agentResult.task_id,
                        messageId: agentResult.message_id,
                        isTaskProcessing: true,
                        isLoading: true,
                        estimated_time: agentResult.estimated_time || '3-6 minutes',
                        mode: currentMode,
                        async_task: true,
                        startTime: Date.now(), // 记录开始时间用于进度计算
                        taskType: taskType // 使用后端返回的任务类型
                    };

                    setMessages(prev => {
                        const newMessages = [...prev, processingMessage];
                        const messageIndex = newMessages.length - 1;

                        console.log('🔄 Starting async task polling for task ID:', agentResult.task_id, 'message ID:', agentResult.message_id, 'at message index:', messageIndex);
                        pollTaskStatus(agentResult.task_id, messageIndex, agentResult.message_id);

                        return newMessages;
                    });
                } else {
                    // 同步响应处理
                    let botMessage = {
                        sender: 'bot',
                        content: agentResult.message || agentResult.chat_response || agentResult.response || 'Generated successfully',
                        image_url: agentResult.image_url || null,
                        video_url: agentResult.video_url || null,
                        timestamp: Date.now(),
                        mode: response.data.mode,
                        references_processed: response.data.references_processed,
                        agent_metadata: agentResult.metadata || {}
                    };

                    // 如果用户消息包含引用，AI响应也应该显示这些引用
                    if (messageSelectedReferences.length > 0) {
                        botMessage.referenced_media = messageSelectedReferences.map((ref, index) => ({
                            reference_id: `${index + 1}`,
                            type: ref.type,
                            url: ref.url,
                            display_name: ref.display_name || `Reference ${index + 1}`
                        }));

                        console.log('🤖 AI response with references:', {
                            count: messageSelectedReferences.length,
                            references: botMessage.referenced_media.map(r => r.reference_id)
                        });
                    }

                    // Check for duplicate responses
                    setMessages(prev => {
                        const lastMessage = prev[prev.length - 1];
                        if (lastMessage &&
                            lastMessage.sender === 'bot' &&
                            lastMessage.content === botMessage.content &&
                            lastMessage.image_url === botMessage.image_url &&
                            lastMessage.video_url === botMessage.video_url) {
                            console.log('Detected duplicate robot response, skipping addition');
                            return prev;
                        }
                        return [...prev, botMessage];
                    });

                    // 🔄 如果生成了新的图片或视频，刷新媒体列表
                    if (botMessage.image_url || botMessage.video_url) {
                        console.log('🔄 New media generated, refreshing session media list');
                        setTimeout(() => {
                            const token = localStorage.getItem('token');
                            if (token && activeSessionId) {
                                loadSessionMedia(activeSessionId, token);
                            }
                        }, 1000); // 延迟1秒确保数据库已更新
                    }
                }
            } else {
                // 处理Agent API错误响应
                console.error('❌ Agent API error:', response.data);

                // 检查是否是token相关错误
                if (response.data.error_type === 'insufficient_balance' ||
                    response.data.error_type === 'token_expired') {

                    // Token错误处理
                    setShowUpgradeModal(true);
                    setMessages(prev => [...prev, {
                        sender: 'bot',
                        content: response.data.details || response.data.chat_response || 'Insufficient token balance. Please purchase tokens to continue.',
                        isTokenError: true,
                        upgradeInfo: {
                            title: response.data.error_type === 'insufficient_balance' ? 'Insufficient Token Balance' : 'Token Expired',
                            message: response.data.details || response.data.chat_response,
                            error_type: 'payment_required',
                            details: {
                                tokens_required: response.data.tokens_required || 0,
                                current_balance: response.data.current_balance || 0,
                                recommended_plan: 'BASIC'
                            }
                        }
                    }]);
                } else {
                    // 其他错误
                    setMessages(prev => [...prev, {
                        sender: 'bot',
                        content: response.data.error || response.data.details || 'Agent processing failed',
                        agent_error: true
                    }]);
                }
            }

            // Update user token balance display
            if (response.data.user_tokens !== undefined) {
                updateUser(prev => ({ ...prev, tokens: response.data.user_tokens }));
            }

        } catch (error) {
            console.error('Request failed:', error);

            // Remove loading message
            setMessages(prev => prev.filter(msg => !msg.isLoading));

            let errorMessage = 'Request failed, please try again';
            let tokenErrorHandled = false; // 标记token错误是否已被特殊处理

            if (error.response) {
                const status = error.response.status;
                const data = error.response.data;

                if (status === 401) {
                    // Authentication failed - redirect to login
                    console.log('🔐 Authentication failed, redirecting to login');
                    handleLogout();
                    return;
                } else if (status === 400 || status === 402) {
                    // Payment required - insufficient tokens
                    console.log('🔍 [Chat Debug] 检测到400/402错误，检查是否为token错误');
                    console.log('🔍 [Chat Debug] 响应数据:', data);
                    console.log('🔍 [Chat Debug] error_type:', data.error_type);
                    console.log('�� [Chat Debug] error:', data.error);

                    if (data.error_type === 'insufficient_balance' ||
                        data.error_type === 'payment_required' ||
                        data.error === 'Token balance issue') {

                        console.log('🔍 [Chat Debug] 确认为token错误，开始处理');
                        errorMessage = data.details || data.chat_response || data.message || 'Insufficient token balance. Please purchase tokens to continue.';
                        console.log('🔍 [Chat Debug] errorMessage:', errorMessage);

                        // 临时强制显示调试信息
                        if (typeof window !== 'undefined') {
                            window.TOKEN_DEBUG = {
                                detected: true,
                                error_type: data.error_type,
                                errorMessage: errorMessage,
                                data: data
                            };
                        }

                        // Show upgrade modal for token purchase
                        setShowUpgradeModal(true);
                        console.log('🔍 [Chat Debug] 设置充值弹窗显示');

                        // 构造升级信息
                        const upgradeData = {
                            title: 'Insufficient Token Balance',
                            message: errorMessage,
                            error_type: 'payment_required',
                            details: {
                                tokens_required: data.tokens_required || 0,
                                current_balance: data.current_balance || 0,
                                recommended_plan: 'BASIC'
                            }
                        };

                        // 直接添加token错误消息到聊天中（使用content字段）
                        console.log('🔍 [Chat Debug] 即将添加token错误消息到聊天');
                        setMessages(prev => {
                            console.log('🔍 [Chat Debug] 当前消息数:', prev.length);
                            const newMessages = [...prev, {
                                sender: 'bot',
                                content: errorMessage,
                                isTokenError: true,
                                upgradeInfo: upgradeData
                            }];
                            console.log('🔍 [Chat Debug] 新消息数:', newMessages.length);
                            console.log('🔍 [Chat Debug] 新增的token错误消息:', newMessages[newMessages.length - 1]);
                            return newMessages;
                        });

                        tokenErrorHandled = true; // 标记已处理

                        // 立即设置加载状态为false，避免finally块中的操作干扰
                        setIsLoading(false);
                        if (typeof window !== 'undefined') {
                            window.submitInProgress = false;
                        }

                        console.log('🔍 [Chat Debug] token错误处理完成，准备返回');
                        return; // 提前返回，避免在下面再次添加错误消息
                    } else {
                        errorMessage = data.message || 'Request failed. Please try again.';
                    }
                } else if (status === 504) {
                    errorMessage = 'Request timeout. The AI service is busy, please try again later.';
                } else if (status === 500) {
                    errorMessage = 'Server error. Please try again later.';
                } else if (data && data.error) {
                    errorMessage = data.error;
                    // 特殊处理安全系统错误
                    if (data.error_type === 'safety_violation' ||
                        data.error.includes('safety system') ||
                        data.error.includes('moderation_blocked')) {
                        errorMessage = data.details || data.error;
                    }
                }
            } else if (error.code === 'ECONNABORTED') {
                errorMessage = 'Request timeout. Please try again.';
            } else if (error.code === 'ERR_NETWORK') {
                errorMessage = 'Network connection error. Please check your connection.';
            }

            // Only add error message if token error wasn't already handled
            if (!tokenErrorHandled) {
                const isTokenError = (error.response?.status === 400 || error.response?.status === 402) &&
                    error.response?.data &&
                    (error.response.data.error_type === 'insufficient_balance' ||
                        error.response.data.error_type === 'payment_required' ||
                        error.response.data.error === 'Token balance issue');

                setMessages(prev => [...prev, {
                    sender: 'bot',
                    content: errorMessage, // 改为使用content字段，保持一致性
                    isTokenError: isTokenError
                }]);
            }

        } finally {
            setIsLoading(false);
            if (typeof window !== 'undefined') {
                window.submitInProgress = false;
            }

            // 在请求完成后清除引用数据和@符号
            clearReferences();
            setTimeout(() => {
                forceCleanAtSymbol();
            }, 100);
        }
    };

    const handleDeleteSession = async (sessionId) => {
        try {
            const token = localStorage.getItem('token');

            // 直接使用POST方法，因为生产环境服务器不支持DELETE
            // 这样避免不必要的405错误显示
            const response = await axios.post(`/api/chat/sessions/${sessionId}/delete/`, {}, {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000,
                withCredentials: true,
                validateStatus: function (status) {
                    return status < 500;
                }
            });

            // 检查响应状态
            if (response.status >= 400) {
                throw new Error(`删除失败: ${response.status} ${response.statusText}`);
            }

            console.log('会话删除成功');
        } catch (error) {
            console.error('删除会话失败:', error);
            // 显示错误消息给用户
            if (error.response?.status === 401) {
                console.error('认证失败，请重新登录');
                handleLogout();
                return; // 不继续执行删除逻辑
            } else {
                console.error('网络错误或服务器错误，请稍后重试');
                toast.error('Failed to delete session. Please try again.');
            }
            // 如果删除失败，不要从本地状态中移除会话
            return;
        }

        // 从本地状态中移除会话
        setSessions(prev => prev.filter(s => s.id !== sessionId));

        // 如果删除的是当前活动会话，切换到第一个可用会话
        if (sessionId === activeSessionId) {
            const remainingSessions = sessions.filter(s => s.id !== sessionId);
            if (remainingSessions.length > 0) {
                setActiveSessionId(remainingSessions[0].id);
                const token = localStorage.getItem('token');
                loadChatMessages(remainingSessions[0].id, token);
            } else {
                // 如果没有剩余会话，创建新会话
                handleNewSession();
            }
        }
    };

    const handleRenameSession = async (sessionId, newTitle) => {
        // 保存原始标题用于错误回滚
        const originalSession = sessions.find(s => s.id === sessionId);
        if (!originalSession) {
            console.error('找不到要重命名的会话');
            return;
        }
        const originalTitle = originalSession.title;

        // 立即更新本地状态，避免等待网络请求期间的状态不一致
        const tempTimestamp = new Date().toISOString();
        setSessions(prev =>
            prev.map(session =>
                session.id === sessionId
                    ? { ...session, title: newTitle, updated_at: tempTimestamp }
                    : session
            )
        );

        try {
            const token = localStorage.getItem('token');
            const response = await axios.patch(`/api/chat/sessions/${sessionId}/update/`,
                { title: newTitle },
                {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 10000,
                    withCredentials: true,
                    validateStatus: function (status) {
                        return status < 500;
                    }
                }
            );

            // 检查响应状态
            if (response.status === 200 && response.data.session) {
                // 成功后使用服务器返回的真实时间戳更新本地状态
                setSessions(prev =>
                    prev.map(session =>
                        session.id === sessionId
                            ? { ...session, title: newTitle, updated_at: response.data.session.updated_at }
                            : session
                    )
                );
                console.log('✅ Session renamed successfully:', {
                    sessionId,
                    oldTitle: originalTitle,
                    newTitle,
                    updated_at: response.data.session.updated_at
                });
                toast.success('Session renamed successfully');
            } else {
                // API返回非200状态，回滚到原始状态
                console.error('重命名失败，API返回状态:', response.status);
                setSessions(prev =>
                    prev.map(session =>
                        session.id === sessionId
                            ? { ...session, title: originalTitle, updated_at: originalSession.updated_at }
                            : session
                    )
                );
                toast.error(`Failed to rename session: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.error('重命名会话失败:', error);

            // 回滚到原始状态
            setSessions(prev =>
                prev.map(session =>
                    session.id === sessionId
                        ? { ...session, title: originalTitle, updated_at: originalSession.updated_at }
                        : session
                )
            );

            if (error.response?.status === 401) {
                // 认证失败，重新登录
                console.log('🔐 Authentication failed, redirecting to login');
                handleLogout();
                return;
            }

            // 显示具体的错误信息
            const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
            console.error('重命名失败详情:', errorMessage);
            toast.error(`Failed to rename session: ${errorMessage}`);
        }
    };

    const handleGoHome = () => {
        router.push('/');
    };

    // Handle reference preview
    const handleReferencePreview = (reference) => {
        console.log('📱 Opening reference preview:', reference);
        setPreviewReference(reference);
    };

    // Handle reference removal
    const handleReferenceRemove = (reference, index) => {
        removeReference(reference, textareaRef);
    };

    // 简单的API健康检查
    const testAPIConnection = async () => {
        try {
            const token = localStorage.getItem('token');

            // 清除之前的错误状态
            setApiConnectionError(null);

            // 测试基本连接
            console.log('🔍 Testing API connection...');
            const healthResponse = await axios.get('/health/', {
                timeout: 5000,
                validateStatus: function (status) {
                    return status < 500;
                }
            });

            console.log('💚 Health check:', healthResponse.status);

            // 如果有用户令牌，测试认证
            if (token) {
                const authResponse = await axios.get('/api/chat/sessions/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 5000,
                    validateStatus: function (status) {
                        return status < 500;
                    }
                });

                console.log('🔐 Auth test:', authResponse.status);

                if (authResponse.status === 401 || authResponse.status === 403) {
                    console.log('❌ Token invalid, clearing and redirecting');
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    updateUser(null);
                    router.push('/');
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.error('❌ API connection test failed:', error.message);
            setApiConnectionError('Unable to connect to server. Please check your connection and try again.');
            return false;
        }
    };

    // 显示加载状态
    if (userLoading || !authCheckComplete) {
        return (
            <div className="min-h-screen bg-[#0f0a1d] flex items-center justify-center">
                <Loading type="spinner" size="lg" color="purple" />
            </div>
        );
    }

    return (
        <ErrorBoundary>
            <div className="h-screen flex bg-[#0f0a1d] overflow-hidden">
                <Head>
                    <title>MirageMakers AI - {t('chat.title')}</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                </Head>

                {/* 左侧侧边栏 - GPT风格 */}
                <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-black/25 backdrop-blur-xl flex-shrink-0 relative z-0`}>
                    <div className="w-80 h-full flex flex-col relative">
                        {/* 侧边栏头部 - GPT风格设计 */}
                        <div className="p-4 border-b border-white/10">
                            {/* 顶部品牌区域 - GPT风格 */}
                            <div className="flex items-center justify-between mb-3">
                                {/* Logo - 更大尺寸 */}
                                <div className="flex items-center">
                                    <span className="text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-purple-300 text-transparent bg-clip-text">
                                        MirageMakers AI
                                    </span>
                                </div>

                                {/* 收起按钮 - GPT风格 */}
                                <button
                                    onClick={() => setSidebarOpen(false)}
                                    className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                                    title={language === 'zh' ? '收起侧边栏' : 'Collapse sidebar'}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M15 18l-6-6 6-6" />
                                    </svg>
                                </button>
                            </div>

                            {/* New Chat 按钮 - GPT风格 */}
                            <button
                                onClick={handleNewSession}
                                disabled={isLoading}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-white hover:bg-white/10 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {/* 新建会话图标 - 编辑笔图标 */}
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                    <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                </svg>

                                {/* 文字 */}
                                <span className="font-medium text-sm">
                                    {language === 'zh' ? '新建对话' : 'New chat'}
                                </span>

                                {/* 加载指示器 */}
                                {isLoading && (
                                    <div className="ml-auto">
                                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                    </div>
                                )}
                            </button>
                        </div>

                        {/* 会话列表 - 改进样式 */}
                        <div className="flex-1 overflow-y-auto px-4 pb-4">
                            <div className="space-y-2">
                                {sessions.map(s => (
                                    <SessionItem
                                        key={s.id}
                                        session={s}
                                        active={s.id === activeSessionId}
                                        onClick={() => handleSessionClick(s.id)}
                                        onDelete={handleDeleteSession}
                                        onRename={handleRenameSession}
                                    />
                                ))}
                            </div>
                        </div>

                        {/* 升级按钮 - GPT风格 */}
                        <div className="p-4 mt-auto border-t border-white/10">
                            <button
                                onClick={() => setShowUpgradeModal(true)}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                            >
                                {/* 闪电升级图标 */}
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-amber-400">
                                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                                </svg>
                                <span className="font-medium text-sm">
                                    {language === 'zh' ? '升级计划' : 'Upgrade plan'}
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* 主内容区域 */}
                <div className="flex-1 flex flex-col min-w-0">
                    {/* 顶部导航栏 - 去掉边框 */}
                    <div className="flex items-center justify-between p-4 bg-black/10 backdrop-blur-xl flex-shrink-0 relative z-10">
                        <div className="flex items-center gap-3">
                            {/* 侧边栏切换按钮 */}
                            {!sidebarOpen && (
                                <button
                                    onClick={() => setSidebarOpen(true)}
                                    className="p-2.5 text-white/70 hover:text-white hover:bg-purple-500/10 rounded-xl transition-all duration-300"
                                    title={language === 'zh' ? '显示侧边栏' : 'Show sidebar'}
                                >
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M3 12h18M3 6h18M3 18h18" />
                                    </svg>
                                </button>
                            )}
                        </div>

                        <div className="flex items-center gap-3">


                            {/* 用户头像菜单 */}
                            {user && (
                                <UserAvatar
                                    user={user}
                                    size="md"
                                    showOnlineStatus={true}
                                    onUpgrade={() => setShowUpgradeModal(true)}
                                />
                            )}
                        </div>
                    </div>

                    {/* 聊天内容区域 */}
                    <div className="flex-1 flex flex-col min-h-0 relative z-0">
                        {/* 消息显示区域 */}
                        <div className="flex-1 overflow-y-auto relative z-0">
                            {/* API连接错误提示 */}
                            {apiConnectionError && (
                                <div className="max-w-3xl mx-auto px-4 py-4">
                                    <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-4">
                                        <div className="flex items-center">
                                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                            </svg>
                                            <span className="font-medium">Connection Error</span>
                                        </div>
                                        <p className="mt-1 text-sm">{apiConnectionError}</p>
                                        <button
                                            onClick={() => {
                                                setApiConnectionError(null);
                                                testAPIConnection().then(isHealthy => {
                                                    if (isHealthy && user) {
                                                        const token = localStorage.getItem('token');
                                                        if (token) loadChatSessions(token);
                                                    }
                                                });
                                            }}
                                            className="mt-2 text-sm bg-red-600 hover:bg-red-700 px-3 py-1 rounded"
                                        >
                                            Retry Connection
                                        </button>
                                    </div>
                                </div>
                            )}

                            {messages.length === 0 ? (
                                <div className="h-full flex flex-col items-center justify-center text-center px-4">
                                    <h1 className="text-3xl font-medium text-white mb-8">What can I help with?</h1>
                                </div>
                            ) : (
                                <div className="max-w-3xl mx-auto px-4 py-8">
                                    {messages
                                        .filter(msg => !msg.isSystem && !msg.isTemporary) // 过滤掉系统消息和临时消息
                                        .map((msg, idx) => (
                                            <MessageBubble key={`${msg.id || idx}-${msg.timestamp || Date.now()}`} message={msg} isUser={msg.sender === 'user'} />
                                        ))}
                                    <div ref={messagesEndRef} />
                                </div>
                            )}
                        </div>

                        {/* 底部输入区域 */}
                        <div className="p-6 flex-shrink-0 relative z-0">
                            <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
                                {/* Reference preview area */}
                                <ReferencePreview
                                    references={visualReferences}
                                    onRemove={handleReferenceRemove}
                                    onPreview={handleReferencePreview}
                                />

                                <div className="relative">
                                    <div className="relative flex items-center bg-black/20 backdrop-blur-xl rounded-[2rem] hover:bg-purple-500/5 focus-within:bg-purple-500/10 transition-all duration-300">
                                        {/* 文件上传按钮 */}
                                        <button
                                            type="button"
                                            className="ml-5 p-3 text-white hover:bg-purple-500/20 rounded-xl transition-all duration-300"
                                            onClick={handlePlusClick}
                                            disabled={isLoading}
                                        >
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                <path d="M12 5v14M5 12h14" />
                                            </svg>
                                            <input
                                                ref={fileInputRef}
                                                type="file"
                                                accept="image/*,video/*"
                                                onChange={handleFileChange}
                                                className="hidden"
                                                disabled={isLoading}
                                                key={messages.length}
                                                multiple
                                            />
                                        </button>

                                        {/* 文件预览 */}
                                        {currentFile && (
                                            <div className="flex items-center ml-3">
                                                <div className="flex items-center gap-3 px-4 py-2 rounded-2xl bg-purple-500/10 backdrop-blur-sm">
                                                    {currentFile.type.startsWith('image/') ? (
                                                        <>
                                                            <img
                                                                src={URL.createObjectURL(currentFile)}
                                                                alt={t('chat.file.preview')}
                                                                className="w-7 h-7 object-cover rounded-xl"
                                                            />
                                                            <span className="text-sm text-white font-medium">{t('chat.file.image')}</span>
                                                        </>
                                                    ) : (
                                                        <>
                                                            <span className="text-white text-lg">🎬</span>
                                                            <span className="text-sm text-white font-medium">{t('chat.file.video')}</span>
                                                        </>
                                                    )}
                                                    <button
                                                        type="button"
                                                        className="ml-2 text-white hover:text-red-400 transition-colors"
                                                        onClick={removeSelectedFile}
                                                    >
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        )}

                                        {/* 输入框 */}
                                        <input
                                            ref={textareaRef}
                                            type="text"
                                            placeholder={language === 'zh' ? '与AI助手对话...' : 'Message AI Assistant...'}
                                            className="flex-1 px-6 py-5 bg-transparent text-white text-lg placeholder-purple-300/60 focus:outline-none"
                                            value={currentPrompt}
                                            onChange={handleInputChange}
                                            onKeyDown={handleKeyDown}
                                            disabled={isLoading}
                                        />

                                        {/* 发送按钮 */}
                                        <button
                                            type="submit"
                                            className={`mr-5 p-3 rounded-2xl transition-all duration-300 ${isLoading || (!currentPrompt.trim() && !currentFile)
                                                ? 'text-white/40 bg-black/10'
                                                : 'text-white bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 shadow-lg shadow-purple-500/25 hover:scale-105'
                                                }`}
                                            disabled={isLoading || (!currentPrompt.trim() && !currentFile)}
                                        >
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                                                <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                {/* Reference Selector for @mentions */}
                <ReferenceSelector
                    isOpen={isReferenceDropdownOpen}
                    onClose={closeReferenceDropdown}
                    onSelect={handleReferenceSelect}
                    searchQuery={referenceSearchQuery}
                    position={dropdownPosition}
                    sessionMedia={sessionMedia}
                    selectedReferences={selectedReferences}
                />

                {/* Reference Preview Modal */}
                {previewReference && (
                    <ImageModal
                        src={previewReference.url}
                        alt={previewReference.display_name}
                        isOpen={!!previewReference}
                        onClose={() => setPreviewReference(null)}
                    />
                )}

                {/* Upgrade Modal */}
                <UpgradeModal
                    isOpen={showUpgradeModal}
                    onClose={() => setShowUpgradeModal(false)}
                />
            </div>
        </ErrorBoundary>
    );
}