import React, { useState, useRef, useEffect } from 'react';
import Head from 'next/head';
import axios from 'axios';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

// 防止MetaMask扩展错误
if (typeof window !== 'undefined') {
  // 防止MetaMask注入错误
  window.addEventListener('error', (event) => {
    if (event.error && event.error.message &&
      (event.error.message.includes('MetaMask') ||
        event.error.message.includes('ChromeTransport') ||
        event.error.message.includes('provider-injection'))) {
      event.preventDefault();
      console.warn('MetaMask extension error suppressed:', event.error.message);
      return false;
    }
  });

  // 防止未处理的Promise rejection
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message &&
      (event.reason.message.includes('MetaMask') ||
        event.reason.message.includes('ChromeTransport') ||
        event.reason.message.includes('provider-injection'))) {
      event.preventDefault();
      console.warn('MetaMask promise rejection suppressed:', event.reason.message);
      return false;
    }
  });
}

// 关键组件 - 立即加载
const LanguageSwitcher = dynamic(() => import('../components/LanguageSwitcher'), {
  loading: () => <div className="w-8 h-8 bg-gray-300 rounded animate-pulse"></div>,
  ssr: false
});

const ModernNavbar = dynamic(() => import('../components/ModernNavbar'), {
  loading: () => <div className="h-16 bg-transparent"></div>,
  ssr: false
});

const UserAvatar = dynamic(() => import('../components/UserAvatar'), {
  loading: () => <div className="w-8 h-8 bg-gray-300 rounded-full animate-pulse"></div>,
  ssr: false
});

const Loading = dynamic(() => import('../components/Loading'), {
  ssr: false
});

const Layout = dynamic(() => import('../components/Layout'), {
  loading: () => <div className="min-h-screen bg-gray-900"></div>,
  ssr: false
});

const LoadingDots = dynamic(() => import('../components/LoadingDots'), {
  ssr: false
});

// 非关键组件 - 懒加载，延迟加载
const FeatureCard3D = dynamic(() => import('../components/ui/feature-card-3d'), {
  loading: () => <div className="h-96 bg-purple-500/10 rounded-xl animate-pulse"></div>,
  ssr: false
});

// 评论组件 - 延迟加载
const Marquee = dynamic(() => import('../components/ui/marquee').then(mod => mod.Marquee), {
  loading: () => <div className="h-48 bg-gray-800/50 rounded-xl animate-pulse"></div>,
  ssr: false
});

const ReviewCard = dynamic(() => import('../components/ui/marquee').then(mod => mod.ReviewCard), {
  ssr: false
});

// Import reviews data and components
import { reviews, firstRow, secondRow } from '../data/reviews';
import { aiFeatures } from '../components/ui/feature-card-3d';

// 配置axios默认设置（如果尚未配置）
if (!axios.defaults.withCredentials) {
  axios.defaults.withCredentials = true;
  axios.defaults.timeout = 30000; // 30秒默认超时

  // 添加请求拦截器
  axios.interceptors.request.use(
    (config) => {
      config.headers = {
        ...config.headers,
        'X-Requested-With': 'XMLHttpRequest',
      };
      return config;
    },
    (error) => Promise.reject(error)
  );

  // 添加响应拦截器
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.code === 'ERR_NETWORK' || error.code === 'ERR_CONNECTION_CLOSED') {
        console.error('网络连接错误:', error.message);
      }
      return Promise.reject(error);
    }
  );
}
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';
import { useUser } from '../contexts/UserContext';
import { useToast } from '../components/Toast';

// Import UpgradeModal
const UpgradeModal = dynamic(() => import('../components/UpgradeModal'), {
  ssr: false
});

// 简化界面，移除特性按钮
const TEXT_CHAT_MODE = 'chat';
const TEXT_TO_IMG_MODE = 'text2img';
const TEXT_TO_VIDEO_MODE = 'text2video';
const IMG_TO_IMG_MODE = 'img2img';
const IMG_TO_VIDEO_MODE = 'img2video';

// 高级极光流体鼠标跟随组件 - 性能优化版
function AuroraMouseTracker() {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const particlesRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0, isMoving: false });
  const timeRef = useRef(0);
  const fpsRef = useRef(0);
  const lastTimeRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // 性能检测
    const isLowPerformance = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      return userAgent.includes('mobile') ||
        userAgent.includes('android') ||
        userAgent.includes('iphone') ||
        navigator.hardwareConcurrency < 4;
    };

    const lowPerf = isLowPerformance();
    const particleCount = lowPerf ? 60 : 120; // 移动设备减少粒子数
    const targetFPS = lowPerf ? 30 : 60;
    const fpsInterval = 1000 / targetFPS;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 初始化粒子系统 - 优化版
    const initParticles = () => {
      particlesRef.current = [];
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: 0,
          vy: 0,
          size: Math.random() * 2 + 0.5,
          opacity: Math.random() * 0.5 + 0.3,
          hue: Math.random() * 60 + 260,
          life: Math.random() * 100,
          maxLife: 100,
        });
      }
    };

    initParticles();

    // 节流的鼠标事件
    let mouseMoveTimer;
    const handleMouseMove = (e) => {
      clearTimeout(mouseMoveTimer);
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
      mouseRef.current.isMoving = true;

      mouseMoveTimer = setTimeout(() => {
        mouseRef.current.isMoving = false;
      }, 100);
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    // 优化的动画循环
    const animate = (currentTime) => {
      // FPS 控制
      if (currentTime - lastTimeRef.current < fpsInterval) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }
      lastTimeRef.current = currentTime;

      timeRef.current += 0.01;

      // 使用更高效的清除方法
      ctx.globalCompositeOperation = 'source-over';
      ctx.fillStyle = 'rgba(8, 5, 15, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const mouse = mouseRef.current;
      const particles = particlesRef.current;

      // 批量更新粒子
      const updateBatchSize = lowPerf ? 10 : 20;
      const frameParticles = particles.slice(0, Math.min(particles.length, updateBatchSize));

      frameParticles.forEach((particle) => {
        // 简化的物理计算
        if (mouse.isMoving) {
          const dx = mouse.x - particle.x;
          const dy = mouse.y - particle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 200 && distance > 0) {
            const force = (200 - distance) / 200 * 0.3;
            particle.vx += (dx / distance) * force * 0.2;
            particle.vy += (dy / distance) * force * 0.2;
          }
        }

        // 简化的阻力
        particle.vx *= 0.95;
        particle.vy *= 0.95;

        // 减少噪声计算
        if (Math.random() < 0.1) {
          particle.vx += (Math.sin(timeRef.current + particle.x * 0.01) * 0.05);
          particle.vy += (Math.cos(timeRef.current + particle.y * 0.01) * 0.05);
        }

        particle.x += particle.vx;
        particle.y += particle.vy;

        // 边界处理
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // 渲染粒子
        ctx.globalCompositeOperation = 'lighter';
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = `hsla(${particle.hue}, 70%, 60%, ${particle.opacity})`;
        ctx.fill();

        // 连接线
        particles.forEach((other) => {
          const dx = particle.x - other.x;
          const dy = particle.y - other.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(other.x, other.y);
            ctx.strokeStyle = `hsla(${particle.hue}, 50%, 50%, ${0.1 * (100 - distance) / 100})`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        });
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
      clearTimeout(mouseMoveTimer);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ mixBlendMode: 'screen' }}
    />
  );
}

// 深空极光流体粒子系统 - 性能优化版
function DeepSpaceAuroraEffect() {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const particlesRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0, isMoving: false });
  const lastTimeRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // 性能自适应
    const isLowPerformance = () => {
      return navigator.userAgent.toLowerCase().includes('mobile') ||
        navigator.hardwareConcurrency < 4;
    };

    const lowPerf = isLowPerformance();
    const targetFPS = lowPerf ? 30 : 60;
    const fpsInterval = 1000 / targetFPS;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 减少粒子数量
    const initParticles = () => {
      particlesRef.current = [];
      const particleCount = Math.min(lowPerf ? 75 : 150, Math.floor((canvas.width * canvas.height) / 12000));

      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          size: Math.random() * 2 + 1,
          opacity: Math.random() * 0.5 + 0.3,
          hue: 270 + Math.random() * 30,
          saturation: 70 + Math.random() * 30,
          lightness: 50 + Math.random() * 30,
          life: Math.random() * 100,
        });
      }
    };

    initParticles();

    // 节流鼠标事件
    let mouseMoveTimer;
    const handleMouseMove = (e) => {
      clearTimeout(mouseMoveTimer);
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
      mouseRef.current.isMoving = true;

      // 减少鼠标粒子生成
      if (Math.random() < (lowPerf ? 0.1 : 0.2)) {
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * 80 + 40;
        particlesRef.current.push({
          x: e.clientX + Math.cos(angle) * distance,
          y: e.clientY + Math.sin(angle) * distance,
          vx: (Math.random() - 0.5) * 1.5,
          vy: (Math.random() - 0.5) * 1.5,
          size: Math.random() * 2 + 1,
          opacity: 1,
          hue: 280 + Math.random() * 40,
          saturation: 80 + Math.random() * 20,
          lightness: 60 + Math.random() * 20,
          life: 0,
          temporary: true,
        });
      }

      mouseMoveTimer = setTimeout(() => {
        mouseRef.current.isMoving = false;
      }, 150);
    };

    document.addEventListener('mousemove', handleMouseMove, { passive: true });

    // 优化的动画循环
    const animate = (currentTime) => {
      if (currentTime - lastTimeRef.current < fpsInterval) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }
      lastTimeRef.current = currentTime;

      ctx.fillStyle = 'rgba(2, 1, 6, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const mouse = mouseRef.current;
      const particles = particlesRef.current;

      // 批量处理粒子
      for (let i = particles.length - 1; i >= 0; i--) {
        const particle = particles[i];

        // 简化鼠标交互
        if (mouse.isMoving && Math.random() < 0.3) {
          const dx = mouse.x - particle.x;
          const dy = mouse.y - particle.y;
          const distanceSq = dx * dx + dy * dy;

          if (distanceSq < 40000) { // 200px squared
            const distance = Math.sqrt(distanceSq);
            const force = (200 - distance) / 200 * 0.6;
            const angle = Math.atan2(dy, dx);
            particle.vx += Math.cos(angle) * force * 0.3;
            particle.vy += Math.sin(angle) * force * 0.3;
          }
        }

        particle.vx *= 0.98;
        particle.vy *= 0.98;

        // 减少随机扰动
        if (Math.random() < 0.05) {
          particle.vx += (Math.random() - 0.5) * 0.05;
          particle.vy += (Math.random() - 0.5) * 0.05;
        }

        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        particle.life += 1;
        if (particle.temporary && particle.life > 40) {
          particles.splice(i, 1);
          continue;
        }

        particle.opacity = Math.sin(particle.life * 0.02) * 0.3 + 0.4;
      }

      // 减少连接线绘制
      const maxConnections = lowPerf ? 30 : 60;
      let connectionCount = 0;

      for (let i = 0; i < particles.length && connectionCount < maxConnections; i++) {
        for (let j = i + 1; j < particles.length && connectionCount < maxConnections; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distanceSq = dx * dx + dy * dy;

          if (distanceSq < 14400) { // 120px squared
            connectionCount++;
            const distance = Math.sqrt(distanceSq);
            const opacity = (1 - distance / 120) * 0.3;

            ctx.strokeStyle = `hsla(${particles[i].hue}, ${particles[i].saturation}%, ${particles[i].lightness}%, ${opacity})`;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
          }
        }
      }

      // 简化粒子绘制
      particles.forEach(particle => {
        ctx.fillStyle = `hsla(${particle.hue}, ${particle.saturation}%, ${particle.lightness}%, ${particle.opacity})`;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size * 2, 0, Math.PI * 2);
        ctx.fill();
      });

      // 简化鼠标光晕
      if (mouse.isMoving) {
        const gradient = ctx.createRadialGradient(mouse.x, mouse.y, 0, mouse.x, mouse.y, 150);
        gradient.addColorStop(0, 'hsla(280, 100%, 70%, 0.1)');
        gradient.addColorStop(1, 'hsla(250, 100%, 40%, 0)');
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(mouse.x, mouse.y, 150, 0, Math.PI * 2);
        ctx.fill();
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      document.removeEventListener('mousemove', handleMouseMove);
      clearTimeout(mouseMoveTimer);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-10"
      style={{
        background: 'linear-gradient(135deg, #000000 0%, #0a0412 25%, #1a0b2e 50%, #0a0412 75%, #000000 100%)',
        mixBlendMode: 'normal'
      }}
    />
  );
}

// 客户端渲染包装器
function ClientOnlyBackground({ children }) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 在服务端渲染时返回占位符
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-[#0f0a1d] text-white overflow-hidden">
        <div className="h-16 bg-transparent"></div>
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// 极深空背景
function DeepSpaceBackground() {
  const [stars, setStars] = useState([]);

  useEffect(() => {
    // 只在客户端生成星星，避免 hydration mismatch
    const generateStars = () => {
      const starArray = [];
      for (let i = 0; i < 80; i++) {
        starArray.push({
          id: i,
          left: Math.random() * 100,
          top: Math.random() * 100,
          width: 0.5 + Math.random() * 1.5,
          height: 0.5 + Math.random() * 1.5,
          color: Math.random() > 0.7 ? '139, 92, 246' : Math.random() > 0.5 ? '124, 58, 237' : '168, 85, 247',
          opacity: 0.3 + Math.random() * 0.4,
          duration: 4 + Math.random() * 8,
          delay: Math.random() * 8,
          boxShadow: 1 + Math.random() * 3
        });
      }
      setStars(starArray);
    };

    generateStars();
  }, []);

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* 主背景 - 极深黑紫色 */}
      <div
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at center,
            #000000 0%,
            #020106 20%,
            #040208 40%,
            #060312 60%,
            #0a0520 80%,
            #000000 100%
          )`
        }}
      />

      {/* 深空星云 - 极小透明度 */}
      <div className="absolute inset-0 opacity-30">
        <div
          className="absolute top-1/4 left-1/3 w-[600px] h-[400px] rounded-full"
          style={{
            background: `radial-gradient(ellipse,
              rgba(88, 28, 135, 0.1) 0%,
              rgba(59, 7, 100, 0.05) 50%,
              transparent 100%
            )`,
            filter: 'blur(100px)',
            animation: 'deep-drift 50s ease-in-out infinite'
          }}
        />

        <div
          className="absolute bottom-1/3 right-1/4 w-[500px] h-[350px] rounded-full"
          style={{
            background: `radial-gradient(ellipse,
              rgba(67, 56, 202, 0.08) 0%,
              rgba(79, 70, 229, 0.04) 50%,
              transparent 100%
            )`,
            filter: 'blur(120px)',
            animation: 'deep-drift 40s ease-in-out infinite reverse'
          }}
        />
      </div>

      {/* 稀疏星场 - 使用 state 中的星星数据 */}
      <ClientOnlyBackground>
        <div className="absolute inset-0">
          {stars.map((star) => (
            <div
              key={star.id}
              className="absolute rounded-full"
              style={{
                left: `${star.left}%`,
                top: `${star.top}%`,
                width: `${star.width}px`,
                height: `${star.height}px`,
                background: `rgba(${star.color}, ${star.opacity})`,
                animation: `twinkle ${star.duration}s ease-in-out infinite`,
                animationDelay: `${star.delay}s`,
                boxShadow: `0 0 ${star.boxShadow}px currentColor`
              }}
            />
          ))}
        </div>
      </ClientOnlyBackground>
    </div>
  );
}

// Note: Removed old FeatureCard component, now using FeatureCard3D

// LoadingDots 组件已移至共享组件文件

function MessageBubble({ message, isUser }) {
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-[70%] p-3 rounded-lg ${isUser
        ? 'bg-[#00d2ff] text-white'
        : 'bg-gray-100 text-gray-800'
        }`}>
        {message.isLoading ? (
          <div className="flex items-center">
            <span>正在生成</span>
            <LoadingDots />
          </div>
        ) : (
          <span className="whitespace-pre-wrap break-words">{message.content}</span>
        )}

        {message.image_url && (
          <div className="mt-2">
            <img
              src={message.image_url}
              alt="Generated image"
              className="max-w-full h-auto rounded"
            />
          </div>
        )}

        {message.video_url && (
          <div className="mt-2">
            <video
              src={message.video_url}
              controls
              className="max-w-full h-auto rounded"
            >
              您的浏览器不支持视频播放。
            </video>
          </div>
        )}
      </div>
    </div>
  );
}

function SessionItem({ session, active, onClick }) {
  return (
    <div
      className={`px-3 py-2.5 cursor-pointer rounded-lg mb-1 flex items-center gap-2 transition ${active
        ? 'bg-gradient-to-r from-[#00d2ff]/15 to-[#7a5ffa]/15 text-white'
        : 'hover:bg-[#1d2440]/50 text-gray-300'
        }`}
      onClick={onClick}
    >
      <div className="w-5 h-5 flex-shrink-0 flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${active ? 'text-[#00d2ff]' : 'text-gray-400'}`}>
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
      </div>
      <span className="truncate flex-1 text-sm">{session.title || '新会话'}</span>
      {active && (
        <span className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-[#00d2ff] to-[#7a5ffa]"></span>
      )}
    </div>
  );
}

export default function Home() {
  const router = useRouter();
  const { t, language } = useLanguage();
  const { user, loading: userLoading, error: userError, updateUser } = useUser();
  const { showToast } = useToast();
  const [sessions, setSessions] = useState([]);
  const [activeSessionId, setActiveSessionId] = useState(null);
  const [messages, setMessages] = useState([]);
  const [currentPrompt, setCurrentPrompt] = useState('');
  const [currentFile, setCurrentFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [csrfToken, setCsrfToken] = useState('');
  const [showSessions, setShowSessions] = useState(false);
  const [advancedMode, setAdvancedMode] = useState(false);
  const [currentMode, setMode] = useState(TEXT_CHAT_MODE);
  const [pendingRequest, setPendingRequest] = useState(null);


  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);
  const fileInputRef = useRef(null);

  const [showFeatures, setShowFeatures] = useState(false);

  // 新增状态：代币不足弹窗
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeInfo, setUpgradeInfo] = useState(null);
  const [plans, setPlans] = useState([]);

  useEffect(() => {
    // 滚动到底部
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // 自动调整文本框高度
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [currentPrompt]);

  useEffect(() => {
    // 获取CSRF令牌
    fetchCsrfToken();

    // 如果用户已登录，加载聊天历史
    if (user) {
      const token = localStorage.getItem('token');
      if (token) {
        loadChatSessions(token);
      }
    } else {
      // 如果未登录，创建默认会话
      const defaultSession = {
        id: 'default',
        title: '新会话',
        messages: []
      };
      setSessions([defaultSession]);
      setActiveSessionId('default');
    }
  }, [user]);



  // 加载用户聊天会话
  const loadChatSessions = async (token) => {
    try {
      const response = await axios.get('/api/chat/sessions/', {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (response.data.sessions && response.data.sessions.length > 0) {
        setSessions(response.data.sessions);
        setActiveSessionId(response.data.sessions[0].id);
        loadChatMessages(response.data.sessions[0].id, token);
      } else {
        // 如果没有会话，创建一个新的
        handleNewSession();
      }
    } catch (error) {
      console.error('加载聊天会话失败:', error);
      // 创建默认会话
      const defaultSession = {
        id: 'default',
        title: '新会话',
        messages: []
      };
      setSessions([defaultSession]);
      setActiveSessionId('default');
    }
  };

  // 加载指定会话的消息
  const loadChatMessages = async (sessionId, token) => {
    try {
      const response = await axios.get(`/api/chat/sessions/${sessionId}/messages/`, {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      const messagesData = response.data.messages.map(msg => ({
        sender: msg.type,
        content: msg.content,
        image_url: msg.image_url,
        video_url: msg.video_url,
        id: msg.id,
        created_at: msg.created_at
      }));

      setMessages(messagesData);
    } catch (error) {
      console.error('加载消息失败:', error);
      setMessages([]);
    }
  };

  // 获取CSRF令牌
  const fetchCsrfToken = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Accept': 'application/json'
      };

      // 如果有token，添加认证头
      if (token) {
        headers['Authorization'] = `Token ${token}`;
      }

      const response = await axios.get("/api/generate/", {
        headers: headers,
        withCredentials: true
      });

      // 从cookie中提取CSRF令牌
      const cookies = document.cookie.split(';');
      const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('csrftoken='));

      if (csrfCookie) {
        const token = csrfCookie.split('=')[1];
        setCsrfToken(token);
        console.log('CSRF令牌:', token);
      }
    } catch (error) {
      console.error('获取CSRF令牌失败:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    updateUser(null); // 使用UserContext的更新函数
    showToast(t('auth.logout.success') || 'Logged out successfully', 'success');
    router.push('/');
  };

  const handleInputChange = (e) => {
    setCurrentPrompt(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleNewSession = async () => {
    if (user) {
      // 如果用户已登录，在服务器创建新会话
      try {
        const token = localStorage.getItem('token');
        const response = await axios.post('/api/chat/sessions/create/',
          { title: '新对话' },
          {
            headers: {
              'Authorization': `Token ${token}`,
              'Content-Type': 'application/json',
            },
          }
        );

        const newSession = response.data.session;
        setSessions(prev => [newSession, ...prev]);
        setActiveSessionId(newSession.id);
        setMessages([]);
      } catch (error) {
        console.error('创建会话失败:', error);
        // 回退到本地会话
        const newSessionId = Date.now().toString();
        const newSession = { id: newSessionId, title: '新对话', messages: [] };
        setSessions(prev => [newSession, ...prev]);
        setActiveSessionId(newSessionId);
        setMessages([]);
      }
    } else {
      // 未登录用户使用本地会话
      const newSessionId = Date.now().toString();
      const newSession = { id: newSessionId, title: '新对话', messages: [] };
      setSessions(prev => [newSession, ...prev]);
      setActiveSessionId(newSessionId);
      setMessages([]);
    }
  };

  const handleSessionClick = (sessionId) => {
    setActiveSessionId(sessionId);

    if (user && sessionId !== 'default') {
      const token = localStorage.getItem('token');
      loadChatMessages(sessionId, token);
    } else {
      // 本地会话
      const session = sessions.find(s => s.id === sessionId);
      if (session && session.messages) {
        setMessages(session.messages);
      } else {
        setMessages([]);
      }
    }
  };

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      // Clear any previous file first
      if (currentFile) {
        setCurrentFile(null);
      }
      // Set the new file
      setCurrentFile(e.target.files[0]);
      console.log('File selected:', e.target.files[0].name);
      // If advanced mode is enabled and it's an image file, automatically switch to the appropriate mode
      if (advancedMode) {
        setMode(e.target.files[0].type.startsWith('image/') ? IMG_TO_IMG_MODE : TEXT_CHAT_MODE);
      }
    }
  };

  const handlePlusClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removeSelectedFile = () => {
    setCurrentFile(null);

    // Reset the file input element
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // 如果在高级模式下，重置回文本聊天模式
    if (advancedMode) {
      setMode(TEXT_CHAT_MODE);
    }
  };

  const toggleAdvancedMode = () => {
    setAdvancedMode(!advancedMode);

    // 如果关闭高级模式，恢复为普通聊天模式
    if (advancedMode) {
      setMode(TEXT_CHAT_MODE);
    }
  };

  const handleModeChange = (mode) => {
    setMode(mode);
  };

  // 根据当前选择的模式和文件类型，判断是否显示相应的按钮
  const shouldShowButton = (buttonMode) => {
    if (!advancedMode) return false;

    if (!currentFile) {
      // 无文件状态下，只显示文本生成相关按钮
      return [TEXT_CHAT_MODE, TEXT_TO_IMG_MODE, TEXT_TO_VIDEO_MODE].includes(buttonMode);
    }

    if (currentFile.type.startsWith('image/')) {
      // 图片文件状态下，显示图像处理相关按钮
      return [TEXT_CHAT_MODE, IMG_TO_IMG_MODE, IMG_TO_VIDEO_MODE].includes(buttonMode);
    }

    // 视频文件状态下，只显示聊天按钮
    return buttonMode === TEXT_CHAT_MODE;
  };

  // 添加任务轮询功能 - Enhanced version
  const pollTaskStatus = async (taskId, messageIndex, messageId = null) => {
    const maxPollingTime = 5 * 60 * 1000; // 5分钟最大轮询时间
    const pollInterval = 3000; // 3秒轮询间隔
    const startTime = Date.now();

    const poll = async () => {
      try {
        const token = localStorage.getItem('token');
        const config = {
          headers: {
            'Authorization': token ? `Token ${token}` : '',
            'Accept': 'application/json',
          },
          withCredentials: true
        };

        // 策略1：查询Celery任务状态 - 使用新的任务状态检查API
        const params = new URLSearchParams({ task_id: taskId });
        if (messageId) {
            params.append('message_id', messageId);
        }
        const response = await axios.get(`/api/task-status/?${params.toString()}`, config);
        const data = response.data;

        console.log(`🔄 Polling task status: ${taskId}, messageId: ${messageId || 'none'}`, data);

        if ((data.status === 'SUCCESS' || data.status === 'completed') && data.success) {
          // 任务完成，更新消息
          const result = data.result || data;
          setMessages(prev => {
            const newMessages = [...prev];
            if (newMessages[messageIndex]) {
              newMessages[messageIndex] = {
                ...newMessages[messageIndex],
                content: result.chat_response || 'Here\'s your generated content!',
                image_url: result.image_url || data.image_url || null,
                video_url: result.video_url || data.video_url || null,
                isTaskCompleted: true,
                isLoading: false, // 停止加载动画
                taskId: null // 清除task ID
              };
            }
            return newMessages;
          });
          return; // 停止轮询
        } else if (data.status === 'FAILURE' || data.status === 'failed' || (!data.success && data.error)) {
          // 任务失败
          setMessages(prev => {
            const newMessages = [...prev];
            if (newMessages[messageIndex]) {
              newMessages[messageIndex] = {
                ...newMessages[messageIndex],
                content: 'Video generation failed. Please try again later.',
                error: data.error || 'Task execution failed',
                isLoading: false, // 停止加载动画
                taskId: null
              };
            }
            return newMessages;
          });
          return; // 停止轮询
        } else if (data.status === 'PENDING' || data.status === 'RETRY' || data.status === 'STARTED' || data.status === 'PROGRESS' || (data.success === null && data.message)) {
          // 任务仍在进行中
          const elapsed = Date.now() - startTime;

          // 策略2：如果任务运行超过2分钟，检查数据库中是否已有结果
          if (elapsed > 120000) { // 2分钟后
            try {
              const messageResponse = await axios.get(`/api/tasks/${taskId}/message/`, config);
              if (messageResponse.data.success && messageResponse.data.has_result) {
                const message = messageResponse.data.message;
                setMessages(prev => {
                  const newMessages = [...prev];
                  if (newMessages[messageIndex]) {
                    newMessages[messageIndex] = {
                      ...newMessages[messageIndex],
                      content: message.content || 'Your video has been generated!',
                      image_url: message.image_url || null,
                      video_url: message.video_url || null,
                      isTaskCompleted: true,
                      isLoading: false,
                      taskId: null
                    };
                  }
                  return newMessages;
                });
                console.log('Found completed task in database:', message);
                return; // 停止轮询
              }
            } catch (dbError) {
              console.log('Database check failed, continuing with normal polling:', dbError);
            }
          }

          // 更新等待消息，显示已等待时间
          const waitingMinutes = Math.floor(elapsed / 60000);
          const waitingSeconds = Math.floor((elapsed % 60000) / 1000);
          let waitingMessage = 'Video is being generated...';

          if (elapsed > 60000) { // 超过1分钟
            waitingMessage = `Please be patient, video generation is in progress... (${waitingMinutes}m ${waitingSeconds}s)`;
          }

          setMessages(prev => {
            const newMessages = [...prev];
            if (newMessages[messageIndex]) {
              newMessages[messageIndex] = {
                ...newMessages[messageIndex],
                content: waitingMessage,
                isLoading: true, // 保持加载状态
                taskId: data.task_id || newMessages[messageIndex].taskId
              };
            }
            return newMessages;
          });

          if (elapsed < maxPollingTime) {
            // 继续轮询
            setTimeout(poll, pollInterval);
          } else {
            // 超时 - 但最后再检查一次数据库
            try {
              const messageResponse = await axios.get(`/api/tasks/${taskId}/message/`, config);
              if (messageResponse.data.success && messageResponse.data.has_result) {
                const message = messageResponse.data.message;
                setMessages(prev => {
                  const newMessages = [...prev];
                  if (newMessages[messageIndex]) {
                    newMessages[messageIndex] = {
                      ...newMessages[messageIndex],
                      content: message.content || 'Your video has been generated!',
                      image_url: message.image_url || null,
                      video_url: message.video_url || null,
                      isTaskCompleted: true,
                      isLoading: false,
                      taskId: null
                    };
                  }
                  return newMessages;
                });
                return;
              }
            } catch (dbError) {
              console.log('Final database check failed:', dbError);
            }

            // 最终超时消息
            setMessages(prev => {
              const newMessages = [...prev];
              if (newMessages[messageIndex]) {
                newMessages[messageIndex] = {
                  ...newMessages[messageIndex],
                  content: 'Video generation is taking longer than expected. Please refresh the page later to check the result.',
                  isLoading: false,
                  taskId: null
                };
              }
              return newMessages;
            });
          }
        } else {
          // Unknown status, log and continue polling for debugging
          console.log('⚠️ Unknown task status:', data.status, 'Data:', data);
          const elapsed = Date.now() - startTime;
          
          if (elapsed < maxPollingTime) {
            // Continue polling for unknown status
            setTimeout(poll, pollInterval);
          } else {
            // Timeout
            setMessages(prev => {
              const newMessages = [...prev];
              if (newMessages[messageIndex]) {
                newMessages[messageIndex] = {
                  ...newMessages[messageIndex],
                  content: 'Task status unclear. Please refresh the page to check for results.',
                  isLoading: false,
                  taskId: null
                };
              }
              return newMessages;
            });
          }
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error);

        // 🔧 关键修复：检查HTTP错误状态码
        if (error.response) {
          const status = error.response.status;
          const errorData = error.response.data;

          if (status === 402) {
            // 🔍 Token不足错误 - 立即停止轮询并显示错误
            console.log('💰 Token insufficient error detected, stopping polling');

            const tokenError = errorData.error || 'Insufficient token balance';
            const tokenDetails = errorData.tokens_required ?
              `Required: ${errorData.tokens_required}, Current: ${errorData.current_balance || 0}` : '';

            setMessages(prev => {
              const newMessages = [...prev];
              if (newMessages[messageIndex]) {
                newMessages[messageIndex] = {
                  ...newMessages[messageIndex],
                  content: `${tokenError}${tokenDetails ? ` (${tokenDetails})` : ''}`,
                  error: tokenError,
                  errorType: 'insufficient_balance',
                  isLoading: false,
                  taskId: null,
                  isTokenError: true
                };
              }
              return newMessages;
            });
            return; // 立即停止轮询
          }

          if (status === 401) {
            // 🔍 认证错误 - 停止轮询并跳转到登录页
            console.log('🔐 Authentication error detected, stopping polling');

            setMessages(prev => {
              const newMessages = [...prev];
              if (newMessages[messageIndex]) {
                newMessages[messageIndex] = {
                  ...newMessages[messageIndex],
                  content: 'Authentication required. Please log in again.',
                  error: 'Authentication failed',
                  isLoading: false,
                  taskId: null,
                  isAuthError: true
                };
              }
              return newMessages;
            });
            return; // 立即停止轮询
          }

          if (status === 400) {
            // 🔍 业务逻辑错误 - 停止轮询并显示错误
            console.log('❌ Business logic error detected, stopping polling');

            const businessError = errorData.error || 'Task execution failed';

            setMessages(prev => {
              const newMessages = [...prev];
              if (newMessages[messageIndex]) {
                newMessages[messageIndex] = {
                  ...newMessages[messageIndex],
                  content: `Generation failed: ${businessError}`,
                  error: businessError,
                  errorType: errorData.error_type || 'task_failed',
                  isLoading: false,
                  taskId: null
                };
              }
              return newMessages;
            });
            return; // 立即停止轮询
          }
        }

        // 策略3：如果轮询API失败，尝试直接查询数据库
        const elapsed = Date.now() - startTime;
        if (elapsed > 60000) { // 1分钟后才尝试数据库查询
          try {
            const token = localStorage.getItem('token');
            const config = {
              headers: {
                'Authorization': token ? `Token ${token}` : '',
                'Accept': 'application/json',
              },
              withCredentials: true
            };

            const messageResponse = await axios.get(`/api/tasks/${taskId}/message/`, config);
            if (messageResponse.data.success && messageResponse.data.has_result) {
              const message = messageResponse.data.message;
              setMessages(prev => {
                const newMessages = [...prev];
                if (newMessages[messageIndex]) {
                  newMessages[messageIndex] = {
                    ...newMessages[messageIndex],
                    content: message.content || 'Your video has been generated!',
                    image_url: message.image_url || null,
                    video_url: message.video_url || null,
                    isTaskCompleted: true,
                    isLoading: false,
                    taskId: null
                  };
                }
                return newMessages;
              });
              console.log('Found result in database after polling error:', message);
              return; // 停止轮询
            }
          } catch (dbError) {
            console.log('Database fallback also failed:', dbError);
          }
        }

        // 如果轮询失败，继续尝试（除非是致命错误）
        if (elapsed < maxPollingTime) {
          setTimeout(poll, pollInterval);
        }
      }
    };

    // 开始轮询
    setTimeout(poll, 1000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if ((!currentPrompt.trim() && !currentFile) || isLoading) {
      return;
    }

    const formData = new FormData();
    formData.append('prompt', currentPrompt);
    formData.append('mode', currentMode);
    formData.append('session_id', activeSessionId);

    if (currentFile) {
      formData.append('media', currentFile);
    }

    // 添加用户消息到聊天
    const userMessage = {
      sender: 'user',
      content: currentPrompt,
      image_url: currentFile && currentFile.type.startsWith('image/') ? URL.createObjectURL(currentFile) : null,
      video_url: currentFile && currentFile.type.startsWith('video/') ? URL.createObjectURL(currentFile) : null
    };

    setMessages(prev => [...prev, userMessage]);

    // 添加加载状态消息
    setMessages(prev => [...prev, { sender: 'bot', isLoading: true }]);

    // 清空输入
    setCurrentPrompt('');
    setCurrentFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    setIsLoading(true);

    try {
      const config = {
        headers: {
          'X-CSRFToken': csrfToken,
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json',
        },
        withCredentials: true
      };

      // 如果用户已登录，添加认证头
      if (user) {
        const token = localStorage.getItem('token');
        config.headers['Authorization'] = `Token ${token}`;
      }

      const response = await axios.post("/api/generate/", formData, config);

      // 移除加载状态消息
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      // 检查是否是异步任务响应
      if (response.data.task_id && response.data.status === 'processing') {
        // 这是一个异步任务，添加处理中的消息并开始轮询
        const processingMessage = {
          id: response.data.message_id, // 🔧 关键修复：使用后端返回的message_id作为id字段
          sender: 'bot',
          content: response.data.chat_response || 'Video is being generated...',
          taskId: response.data.task_id,
          messageId: response.data.message_id, // 也添加messageId字段以保持一致性
          isTaskProcessing: true,
          isLoading: true, // 显示加载动画
          estimated_time: response.data.estimated_time
        };

        setMessages(prev => {
          const newMessages = [...prev, processingMessage];
          const messageIndex = newMessages.length - 1;

          // 开始轮询任务状态
          console.log('🔄 Starting async task polling for task ID:', response.data.task_id, 'message ID:', response.data.message_id, 'at message index:', messageIndex);
          pollTaskStatus(response.data.task_id, messageIndex, response.data.message_id); // 🔧 修复：传递messageId参数

          return newMessages;
        });
      } else {
        // 同步响应（图片生成等）
        const botMessage = {
          sender: 'bot',
          content: response.data.chat_response || null,
          image_url: response.data.image_url || null,
          video_url: response.data.video_url || null
        };

        setMessages(prev => [...prev, botMessage]);
      }

      // 更新当前会话ID（如果服务器返回了新的）
      if (response.data.session_id && response.data.session_id !== activeSessionId) {
        setActiveSessionId(response.data.session_id);

        // 如果是新创建的会话，更新会话列表
        if (user) {
          const token = localStorage.getItem('token');
          loadChatSessions(token);
        }
      }

      // 对于未登录用户，更新本地会话（简化处理）
      if (!user && !response.data.task_id) {
        setSessions(prev =>
          prev.map(session =>
            session.id === activeSessionId
              ? { ...session, messages: [...(session.messages || []), userMessage, { sender: 'bot', content: response.data.chat_response }] }
              : session
          )
        );
      }

    } catch (error) {
      console.error('Request failed:', error);

      // 移除加载状态消息
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      // 处理代币不足错误 - 400 Bad Request 或 402 Payment Required
      if (error.response?.status === 400 || error.response?.status === 402) {
        const errorData = error.response.data;

        // 检查是否是余额不足错误
        if (errorData.error_type === 'insufficient_balance' ||
          errorData.error_type === 'payment_required' ||
          errorData.error === 'Token balance issue') {

          // 构造升级信息
          const upgradeData = {
            title: 'Insufficient Token Balance',
            message: errorData.details || errorData.chat_response || 'You need more tokens to continue.',
            error_type: 'payment_required',
            details: {
              tokens_required: errorData.tokens_required || 0,
              current_balance: errorData.current_balance || 0,
              recommended_plan: 'BASIC'
            }
          };

          setUpgradeInfo(upgradeData);
          setShowUpgradeModal(true);

          // 在聊天中显示友好提示
          setMessages(prev => [...prev, {
            sender: 'bot',
            content: errorData.details || errorData.chat_response || 'Insufficient token balance. Please purchase more tokens to continue.',
            isTokenError: true,
            upgradeInfo: upgradeData
          }]);
        } else {
          // 处理其他支付相关错误
          setMessages(prev => [...prev, {
            sender: 'bot',
            error: errorData.message || errorData.error || 'Insufficient token balance, please go to account center to recharge'
          }]);
        }
        return;
      }

      // 处理其他错误情况
      if (error.response?.status === 401) {
        setMessages(prev => [
          ...prev,
          { sender: 'bot', error: '会话已过期，请重新登录' }
        ]);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        setUser(null);
        return;
      }

      if (error.response?.status === 403) {
        setMessages(prev => [...prev,
        { sender: 'bot', content: '该功能目前正在开发中，敬请期待！' }]);
        return;
      }

      setMessages(prev =>
        [...prev,
        { sender: 'bot', error: error.response?.data?.error || error.message || 'Processing...\n\nLots of people are creating content right now, so this might take a bit. We\'ll notify you when your content is ready.' }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleGetStarted = () => {
    if (user) {
      // 已登录用户直接进入创作页面
      router.push('/creative');
    } else {
      // 未登录用户进入注册页面
      router.push('/auth/register');
    }
  };

  // 新增：快速进入创作功能
  const handleQuickChat = () => {
    if (user) {
      router.push('/creative');
    } else {
      router.push('/auth/login');
    }
  };

  // 新增：用户资料页面
  const handleProfile = () => {
    router.push('/profile');
  };

  // Use aiFeatures data from the component which includes background images
  const features = aiFeatures;

  const fetchMembershipPlans = async () => {
    try {
      const response = await fetch('/api/membership/plans/');
      if (response.ok) {
        const data = await response.json();
        setPlans(data.plans);
      }
    } catch (error) {
      console.error('获取套餐信息失败:', error);
    }
  };

  const handlePurchase = async (planType) => {
    if (!user) {
      showToast('Please log in first to purchase a plan', 'error');
      router.push('/auth/login');
      return;
    }

    // 直接打开UpgradeModal
    setShowUpgradeModal(true);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  useEffect(() => {
    fetchMembershipPlans();
  }, []);

  // 显示加载状态
  if (userLoading) {
    return (
      <div className="min-h-screen bg-[#0f0a1d] flex items-center justify-center">
        <Loading type="spinner" size="lg" color="purple" />
      </div>
    );
  }

  return (
    <ClientOnlyBackground>
      <div className="min-h-screen bg-[#0f0a1d] text-white overflow-hidden">
        <Head>
          <title>MirageMakers AI</title>
          <meta name="description" content={t('hero.subtitle')} />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

        {/* 高级极光流体鼠标跟随效果 */}
        <ClientOnlyBackground>
          <AuroraMouseTracker />
        </ClientOnlyBackground>

        {/* 深空极光流体粒子系统 - 真正的1:1 Inspira UI还原 */}
        <ClientOnlyBackground>
          <DeepSpaceAuroraEffect />
        </ClientOnlyBackground>

        {/* 深空星域背景 */}
        <DeepSpaceBackground />

        {/* 导航栏 */}
        <ModernNavbar
          user={user}
          onProfileClick={() => router.push('/profile')}
          onUpgradeClick={() => setShowUpgradeModal(true)}
          navItems={[]}
          startCreatingUrl="/creative"
        />

        {/* 主要内容 */}
        <main className="relative z-10">
          <div className="container mx-auto px-6 py-20">
            {/* Hero区域 */}
            {/* 功能特性区域 - 使用3D卡片效果 */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  {language === 'zh' ? '为什么选择 MirageMakers AI？' : 'Why Choose MirageMakers AI?'}
                </h2>
                <p className="text-[#b8a1ff] text-lg max-w-2xl mx-auto">
                  {language === 'zh' ? '集成最新AI技术，为您提供专业级的创意工具' : 'Integrated with cutting-edge AI technology, providing professional-grade creative tools'}
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {features.map((feature, index) => (
                  <FeatureCard3D key={index} feature={feature} index={index} />
                ))}
              </div>
            </div>

            <div className="text-center mb-20">
              <div className="relative">
              </div>

              {/* CTA Button - 透明背景设计 */}
              <div className="relative inline-block">
                <button
                  onClick={handleGetStarted}
                  className="group relative inline-flex items-center px-16 py-6 text-xl font-bold text-white backdrop-blur-xl rounded-[2rem] hover:shadow-2xl hover:shadow-purple-500/40 transition-all duration-500 transform hover:scale-110 hover:-translate-y-2 bg-gradient-to-r from-purple-500/20 via-violet-500/20 to-purple-500/20 hover:from-purple-500/30 hover:via-violet-500/30 hover:to-purple-500/30"
                >
                  <span className="relative z-10 flex items-center tracking-wide">
                    {user ? t('cta.primary') : t('get.started')}
                    <svg
                      className="ml-4 w-7 h-7 transition-transform group-hover:translate-x-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>

                  {/* 全息扫描线 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-[2rem]"></div>

                  {/* 按钮周围的光圈 */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/50 to-violet-600/50 rounded-[2rem] opacity-0 group-hover:opacity-60 blur-xl transition-opacity duration-500"></div>
                </button>

                {/* 按钮下方的光线效果 */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-40 h-1 bg-gradient-to-r from-transparent via-purple-500/60 to-transparent rounded-full mt-6"></div>
              </div>
            </div>

            {/* Reviews Section - Added before footer */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  {language === 'zh' ? '用户怎么说' : 'What Our Users Say'}
                </h2>
                <p className="text-[#b8a1ff] text-lg max-w-2xl mx-auto">
                  {language === 'zh' ? '来自全球创作者的真实反馈' : 'Real feedback from creators around the world'}
                </p>
              </div>

              <div className="relative">
                <Marquee pauseOnHover className="[--duration:20s]">
                  {firstRow.map((review) => (
                    <ReviewCard key={review.username} {...review} />
                  ))}
                </Marquee>
                <Marquee reverse pauseOnHover className="[--duration:20s]">
                  {secondRow.map((review) => (
                    <ReviewCard key={review.username} {...review} />
                  ))}
                </Marquee>
                <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-black via-black/50 to-transparent"></div>
                <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-black via-black/50 to-transparent"></div>
              </div>
            </div>
          </div>
        </main>

        {/* 页脚 - Simplified single row with Contact Us email functionality */}
        <footer className="relative z-10 border-t border-purple-500/20 bg-black/20 backdrop-blur-xl">
          <div className="container mx-auto px-6 py-6">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="flex items-center space-x-4 mb-4 md:mb-0">
                <span className="text-white font-semibold">MirageMakers AI</span>
                <span className="text-purple-300 font-semibold">© 2025 All rights reserved</span>
              </div>

              <div className="flex items-center space-x-6">
                {/* Discord 社区链接 */}
                <button
                  onClick={() => window.open('https://discord.com/channels/1382904923095826473/1382904925616607336', '_blank')}
                  className="group relative flex items-center space-x-3 px-2 py-2 hover:bg-indigo-500/10 rounded-xl transition-all duration-300 transform hover:scale-105"
                >
                  {/* Discord icon with enhanced styling */}
                  <div className="relative z-10 flex items-center justify-center w-8 h-8 bg-indigo-500/20 rounded-lg group-hover:bg-indigo-500/30 transition-colors duration-300">
                    <svg className="w-5 h-5 text-indigo-300 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                    </svg>
                  </div>

                  <div className="relative z-10 flex flex-col">
                    <span className="text-white font-semibold text-sm group-hover:text-indigo-100 transition-colors duration-300">Join Discord</span>
                    <span className="text-indigo-300 text-xs group-hover:text-indigo-200 transition-colors duration-300">Community & Support</span>
                  </div>
                </button>

                {/* Email 联系方式 */}
                <button
                  onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Contact%20Us&body=Hello%20MirageMakers%20AI%20Team,'}
                  className="group relative flex items-center space-x-3 px-2 py-2 hover:bg-purple-500/10 rounded-xl transition-all duration-300 transform hover:scale-105"
                >
                  {/* Email icon with enhanced styling */}
                  <div className="relative z-10 flex items-center justify-center w-8 h-8 bg-purple-500/20 rounded-lg group-hover:bg-purple-500/30 transition-colors duration-300">
                    <svg className="w-4 h-4 text-purple-300 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>

                  <div className="relative z-10 flex flex-col">
                    <span className="text-white font-semibold text-sm group-hover:text-purple-100 transition-colors duration-300">Contact Us</span>
                    <span className="text-purple-300 text-xs group-hover:text-purple-200 transition-colors duration-300"><EMAIL></span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </footer>

        {/* UpgradeModal */}
        <UpgradeModal 
          isOpen={showUpgradeModal} 
          onClose={() => setShowUpgradeModal(false)} 
        />
      </div>
    </ClientOnlyBackground>
  );
}