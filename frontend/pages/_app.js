import '../styles/globals.css';
import React from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import axios from 'axios';
import { LanguageProvider } from '../contexts/LanguageContext';
import { UserProvider } from '../contexts/UserContext';
import { ToastProvider } from '../components/Toast';
import ErrorSuppressor from '../components/ErrorSuppressor';

// 不要在这里设置全局axios默认配置，应在useEffect中设置

function MyApp({ Component, pageProps }) {
  const router = useRouter();
  const [error, setError] = React.useState(null);

  // 全局错误处理
  React.useEffect(() => {
    const handleError = (err) => {
      console.error('全局错误:', err);
      setError(err);
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  // axios全局配置 - 只在客户端设置
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      axios.defaults.baseURL = window.location.origin;
    }
  }, []);

  // 路由变化监听
  React.useEffect(() => {
    const handleRouteChange = () => {
      // 重置错误状态
      setError(null);
    };

    router.events.on('routeChangeStart', handleRouteChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, []);

  // 如果有错误，显示错误组件
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#080b13] text-white">
        <div className="bg-gradient-to-br from-[#1c1f33]/60 to-[#050816]/90 backdrop-blur-md p-8 rounded-2xl shadow-xl border border-cyan-500/20 max-w-md">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-300 to-purple-400 text-transparent bg-clip-text mb-4">
            An Error Occurred
          </h1>
          <p className="text-cyan-100 mb-6">
            Sorry, the application encountered an issue. Please refresh the page or return to the homepage to try again.
          </p>
          <div className="flex justify-center">
            <button
              onClick={() => window.location.href = '/'}
              className="px-6 py-3 rounded-xl bg-gradient-to-r from-cyan-500 to-purple-600 text-white font-medium hover:opacity-90 transition-all"
            >
              Return to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 正常渲染
  return (
    <ToastProvider>
      <UserProvider>
        <LanguageProvider>
          <ErrorSuppressor />
          <Head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <meta name="description" content="MirageMakers AI" />
            <title>MirageMakers AI</title>
          </Head>
          <Component {...pageProps} />
        </LanguageProvider>
      </UserProvider>
    </ToastProvider>
  );
}

export default MyApp; 
