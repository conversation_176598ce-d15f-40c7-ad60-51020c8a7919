import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

function Error({ statusCode, hasGetInitialPropsRun, err }) {
  const router = useRouter();

  const handleGoHome = () => {
    router.push('/');
  };

  const getErrorTitle = () => {
    if (statusCode === 404) {
      return 'Page Not Found';
    } else if (statusCode === 500) {
      return 'Server Error';
    } else if (statusCode) {
      return `Error ${statusCode}`;
    } else {
      return 'Application Error';
    }
  };

  const getErrorMessage = () => {
    if (statusCode === 404) {
      return 'The page you are looking for could not be found.';
    } else if (statusCode === 500) {
      return 'A server-side error occurred. Please try again later.';
    } else if (statusCode) {
      return `An error ${statusCode} occurred on the server.`;
    } else {
      return 'A client-side error occurred. Please refresh the page and try again.';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0b2e] to-[#16213e] flex items-center justify-center px-4">
      <Head>
        <title>{getErrorTitle()} - MirageMakers AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-blue-500/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Error Content */}
      <div className="relative z-10 text-center max-w-2xl mx-auto">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto bg-gradient-to-br from-purple-500/20 to-violet-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-purple-500/30">
            <div className="text-4xl">
              {statusCode === 404 ? '🔍' : statusCode === 500 ? '⚠️' : '❌'}
            </div>
          </div>
        </div>

        {/* Error Title */}
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-white via-purple-100 to-violet-100 bg-clip-text text-transparent">
          {getErrorTitle()}
        </h1>

        {/* Error Message */}
        <p className="text-lg text-purple-200/80 mb-8 leading-relaxed">
          {getErrorMessage()}
        </p>

        {/* Status Code Display */}
        {statusCode && (
          <div className="mb-8">
            <div className="inline-block px-4 py-2 bg-purple-500/10 border border-purple-500/30 rounded-full">
              <span className="text-purple-300 text-sm font-medium">
                Error Code: {statusCode}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={handleGoHome}
            className="px-8 py-3 bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white font-medium rounded-2xl transition-all duration-300 shadow-lg shadow-purple-500/25 hover:scale-105 hover:shadow-purple-500/40"
          >
            Go to Homepage
          </button>

          <button
            onClick={() => window.location.reload()}
            className="px-8 py-3 bg-white/5 hover:bg-white/10 text-white font-medium rounded-2xl transition-all duration-300 border border-white/10 hover:border-white/20 backdrop-blur-sm"
          >
            Refresh Page
          </button>
        </div>

        {/* Additional Help Text */}
        <div className="mt-12 p-6 bg-purple-500/5 border border-purple-500/20 rounded-2xl backdrop-blur-sm">
          <h3 className="text-white font-medium mb-2">Need Help?</h3>
          <p className="text-purple-200/70 text-sm">
            If this error persists, please check your internet connection or contact our support team.
          </p>
        </div>

        {/* Development Info (only in development) */}
        {process.env.NODE_ENV === 'development' && err && (
          <div className="mt-8 p-4 bg-red-500/10 border border-red-500/30 rounded-xl backdrop-blur-sm text-left">
            <h4 className="text-red-300 font-medium mb-2">Development Error Details:</h4>
            <pre className="text-red-200/80 text-xs overflow-auto">
              {err.stack || err.message || 'Unknown error'}
            </pre>
          </div>
        )}
      </div>

      <style jsx>{`
                @keyframes float {
                    0%, 100% { transform: translateY(0px); }
                    50% { transform: translateY(-20px); }
                }
                .animate-float {
                    animation: float 6s ease-in-out infinite;
                }
            `}</style>
    </div>
  );
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default Error; 