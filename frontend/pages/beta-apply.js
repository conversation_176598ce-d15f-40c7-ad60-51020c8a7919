import React, { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

const ModernNavbar = dynamic(() => import('../components/ModernNavbar'), {
    ssr: false
});

const BetaApplyPage = () => {
    const router = useRouter();
    const [formData, setFormData] = useState({
        fullName: '',
        email: '',
        company: '',
        useCase: '',
        reason: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitted, setSubmitted] = useState(false);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            await new Promise(resolve => setTimeout(resolve, 1500));
            setSubmitted(true);
        } catch (error) {
            console.error('Error submitting beta application:', error);
            alert('Submission failed, please try again');
        } finally {
            setIsSubmitting(false);
        }
    };

    if (submitted) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0b2e] to-[#2d1b4e]">
                <Head>
                    <title>Beta Application Success - MirageMaker AI</title>
                </Head>

                <div className="fixed inset-0 bg-[url('/noise.png')] opacity-5 -z-10"></div>
                <ModernNavbar variant="transparent" />

                <main className="relative z-10 pt-24 pb-12">
                    <div className="container mx-auto px-6 text-center">
                        <div className="max-w-2xl mx-auto">
                            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-[#6d28d9]/30 to-[#8b5cf6]/30 rounded-full flex items-center justify-center backdrop-blur-sm border border-[#6d28d9]/30">
                                <svg className="w-12 h-12 text-[#8b5cf6]" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <h1 className="text-4xl font-bold text-white mb-4">
                                Application Submitted!
                            </h1>
                            <p className="text-xl text-[#b8a1ff] mb-6">
                                Thank you for applying for MirageMakers AI Beta. We will review your application within 3-5 business days.
                            </p>
                            <button
                                onClick={() => router.push('/')}
                                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-semibold rounded-2xl hover:shadow-2xl hover:shadow-[#8b5cf6]/40 transition-all duration-500 transform hover:scale-105"
                            >
                                Back to Home
                            </button>
                        </div>
                    </div>
                </main>

                <footer className="relative z-10 border-t border-white/10 bg-black/10 backdrop-blur-xl py-8">
                    <div className="container mx-auto px-6">
                        <div className="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-6 text-sm text-[#b8a1ff]">
                            <span className="text-white font-medium">© 2025 MirageMaker AI</span>
                            <span className="hidden md:inline text-[#8b5cf6]">•</span>
                            <Link href="/privacy" className="hover:text-white transition-all duration-300">
                                Privacy Policy
                            </Link>
                            <span className="hidden md:inline text-[#8b5cf6]">•</span>
                            <a href="mailto:<EMAIL>" className="hover:text-white transition-all duration-300">
                                Contact Us
                            </a>
                        </div>
                    </div>
                </footer>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0b2e] to-[#2d1b4e]">
            <Head>
                <title>Beta Application - MirageMaker AI</title>
            </Head>

            <div className="fixed inset-0 bg-[url('/noise.png')] opacity-5 -z-10"></div>
            <ModernNavbar variant="transparent" />

            <main className="relative z-10 pt-24 pb-12">
                <div className="container mx-auto px-6">
                    <div className="text-center mb-12">
                        <h1 className="text-4xl md:text-5xl font-bold mb-6">
                            <span className="bg-gradient-to-r from-violet-400 to-purple-400 text-transparent bg-clip-text">
                                MirageMakers AI
                            </span>
                        </h1>
                        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                            Request Beta Access
                        </h2>
                        <p className="text-xl text-[#b8a1ff] max-w-3xl mx-auto">
                            Become a MirageMakers AI Beta tester and experience cutting-edge AI creation features.
                        </p>
                    </div>

                    <div className="max-w-2xl mx-auto">
                        <form onSubmit={handleSubmit} className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8">
                            <div className="space-y-6">
                                <div className="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-[#b8a1ff] text-sm font-medium mb-2">
                                            Full Name *
                                        </label>
                                        <input
                                            type="text"
                                            name="fullName"
                                            value={formData.fullName}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-[#b8a1ff]/60 focus:border-[#8b5cf6] focus:ring-0 focus:outline-none transition-all duration-300"
                                            placeholder="Enter your full name"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-[#b8a1ff] text-sm font-medium mb-2">
                                            Email Address *
                                        </label>
                                        <input
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-[#b8a1ff]/60 focus:border-[#8b5cf6] focus:ring-0 focus:outline-none transition-all duration-300"
                                            placeholder="Enter your email address"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-[#b8a1ff] text-sm font-medium mb-2">
                                        Company/Organization
                                    </label>
                                    <input
                                        type="text"
                                        name="company"
                                        value={formData.company}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-[#b8a1ff]/60 focus:border-[#8b5cf6] focus:ring-0 focus:outline-none transition-all duration-300"
                                        placeholder="Enter your company or organization"
                                    />
                                </div>

                                <div>
                                    <label className="block text-[#b8a1ff] text-sm font-medium mb-2">
                                        Use Case *
                                    </label>
                                    <select
                                        name="useCase"
                                        value={formData.useCase}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-[#8b5cf6] focus:ring-0 focus:outline-none transition-all duration-300"
                                    >
                                        <option value="" className="bg-gray-900">
                                            Select your use case
                                        </option>
                                        <option value="content-creation" className="bg-gray-900">
                                            Content Creation
                                        </option>
                                        <option value="marketing" className="bg-gray-900">
                                            Marketing
                                        </option>
                                        <option value="education" className="bg-gray-900">
                                            Education
                                        </option>
                                        <option value="personal" className="bg-gray-900">
                                            Personal Use
                                        </option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-[#b8a1ff] text-sm font-medium mb-2">
                                        Why do you want to join our Beta? *
                                    </label>
                                    <textarea
                                        name="reason"
                                        value={formData.reason}
                                        onChange={handleInputChange}
                                        required
                                        rows="4"
                                        className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-[#b8a1ff]/60 focus:border-[#8b5cf6] focus:ring-0 focus:outline-none transition-all duration-300 resize-none"
                                        placeholder="Please describe why you want to participate in the Beta..."
                                    />
                                </div>

                                <div className="pt-4">
                                    <button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className={`
                                            w-full py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105
                                            ${isSubmitting
                                                ? 'bg-gray-600 cursor-not-allowed opacity-50'
                                                : 'bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 hover:shadow-lg hover:shadow-violet-500/25'
                                            }
                                            text-white
                                        `}
                                    >
                                        {isSubmitting ? 'Submitting...' : 'Submit'}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>

            <footer className="relative z-10 border-t border-white/10 bg-black/10 backdrop-blur-xl py-8">
                <div className="container mx-auto px-6">
                    <div className="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-6 text-sm text-[#b8a1ff]">
                        <span className="text-white font-medium">© 2025 MirageMaker AI</span>
                        <span className="hidden md:inline text-[#8b5cf6]">•</span>
                        <Link href="/privacy" className="hover:text-white transition-all duration-300">
                            Privacy Policy
                        </Link>
                        <span className="hidden md:inline text-[#8b5cf6]">•</span>
                        <a href="mailto:<EMAIL>" className="hover:text-white transition-all duration-300">
                            Contact Us
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default BetaApplyPage; 