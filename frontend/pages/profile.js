import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';
import { useUser } from '../contexts/UserContext';
import { getDisplayPlan } from '../utils/planMapping';

import ModernNavbar from '../components/ModernNavbar';
import Loading from '../components/Loading';
import UpgradeModal from '../components/UpgradeModal';

export default function Profile() {
    const router = useRouter();
    const { user, loading: userLoading } = useUser();
    const { language, setLanguage } = useLanguage();
    const [profile, setProfile] = useState({
        balance: 0,
        current_plan: 'Free Plan',
        usage_count: 0
    });
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        company: '',
        phone: '',
        bio: ''
    });
    const [activeTab, setActiveTab] = useState('overview');
    const [loading, setLoading] = useState(false);
    const [showToast, setShowToast] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);

    // New states for usage, billing and settings
    const [usageData, setUsageData] = useState([]);
    const [billingData, setBillingData] = useState([]);
    const [loadingUsage, setLoadingUsage] = useState(false);
    const [loadingBilling, setLoadingBilling] = useState(false);
    const [usageDataFetched, setUsageDataFetched] = useState(false);
    const [passwordData, setPasswordData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    const [showLanguageChangeDialog, setShowLanguageChangeDialog] = useState(false);
    const [usageAnalytics, setUsageAnalytics] = useState({
        total_used_30days: 0,
        total_requests: 0,
        daily_usage: [],
        seven_day_usage: 0,
        monthly_spend: 0,
        by_service: {}
    });

    // 新增：图表类型切换状态
    const [chartType, setChartType] = useState('tokens'); // 'tokens' 或 'requests'

    // 新增状态来控制数据获取
    const [dataFetched, setDataFetched] = useState(false);
    const [billingDataFetched, setBillingDataFetched] = useState(false);

    // 新增：全局数据缓存，防止页面切换时数据丢失
    const [globalData, setGlobalData] = useState({
        profile: null,
        usageAnalytics: null,
        usageData: null,
        billingData: null
    });

    // Tab configuration with bookmark style
    const tabs = [
        {
            id: 'overview',
            name: language === 'zh' ? '概览' : 'Overview',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
            )
        },
        {
            id: 'usage',
            name: language === 'zh' ? '使用情况' : 'Usage',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
            )
        },
        {
            id: 'billing',
            name: language === 'zh' ? '账单发票' : 'Billing & Invoices',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
            )
        },
        {
            id: 'settings',
            name: language === 'zh' ? '设置' : 'Settings',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            )
        }
    ];

    // Filter tabs to show - hide password tab unless it's active
    const visibleTabs = tabs.filter(tab => {
        if (tab.id === 'password') return false; // Don't show password in main tabs
        return true;
    });

    useEffect(() => {
        // 检查是否有token，如果没有token才重定向
        const token = localStorage.getItem('token');
        if (!token) {
            router.push('/auth/login');
            return;
        }

        // 如果UserContext还在加载，等待加载完成
        if (userLoading) {
            return;
        }

        // 如果有token但没有user数据，可能是网络问题，不要立即重定向
        // 给UserContext更多时间来加载用户数据
        if (!user) {
            console.log('User data not loaded yet, waiting...');
            return;
        }

        // 处理URL参数中的tab
        if (router.query.tab && tabs.some(tab => tab.id === router.query.tab)) {
            setActiveTab(router.query.tab);
        }
    }, [user, userLoading, router.query.tab]);

    // 单独的effect处理数据获取，避免重复调用
    useEffect(() => {
        if (user && !dataFetched) {
            console.log('User data available, fetching profile data...'); // Debug log
            console.log('User tokens:', user.tokens); // Debug log

            setFormData({
                firstName: user?.first_name || '',
                lastName: user?.last_name || '',
                email: user?.email || '',
                company: user?.company || '',
                phone: user?.phone || '',
                bio: user?.bio || ''
            });

            // 立即设置基本profile数据
            setProfile({
                balance: user.tokens || 0,
                current_plan: user.current_plan || 'Free Plan',
                usage_count: user.total_generations || 0
            });

            // 获取详细数据
            fetchProfile();
            setDataFetched(true);
        }
    }, [user, dataFetched]);

    const fetchProfile = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.error('No token found in localStorage');
                // 使用UserContext中的数据作为fallback
                if (user) {
                    console.log('Using UserContext data as fallback');
                    const profileData = {
                        balance: user.tokens || 0,
                        current_plan: user.current_plan || 'Free Plan',
                        usage_count: user.total_generations || 0
                    };
                    setProfile(profileData);
                    setGlobalData(prev => ({ ...prev, profile: profileData }));

                    // 设置默认的使用数据
                    const defaultUsageAnalytics = {
                        total_used_30days: 0,
                        total_requests: 0,
                        daily_usage: [],
                        seven_day_usage: 0,
                        monthly_spend: 0,
                        by_service: {}
                    };
                    setUsageData([]);
                    setUsageAnalytics(defaultUsageAnalytics);
                    setGlobalData(prev => ({
                        ...prev,
                        usageAnalytics: defaultUsageAnalytics,
                        usageData: []
                    }));
                }
                return;
            }

            console.log('Fetching profile data...'); // Debug log
            console.log('Current user:', user); // Debug log
            console.log('Token (first 20 chars):', token.substring(0, 20) + '...'); // Debug log

            // 优先使用UserContext中的用户数据，确保基本信息立即显示
            if (user) {
                console.log('Setting profile from user context:', user.tokens); // Debug log
                const profileData = {
                    balance: user.tokens || 0,  // 使用正确的字段名
                    current_plan: user.current_plan || 'Free Plan',
                    usage_count: user.total_generations || 0
                };
                setProfile(profileData);
                setGlobalData(prev => ({ ...prev, profile: profileData }));
            }

            // 尝试获取详细的使用统计数据，但不阻塞基本显示
            try {
                console.log('Making API request to /api/user/membership/'); // Debug log
                const analyticsResponse = await fetch('/api/user/membership/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Analytics response received:', analyticsResponse.status, analyticsResponse.statusText); // Debug log

                if (analyticsResponse.ok) {
                    const analyticsData = await analyticsResponse.json();
                    console.log('Analytics data received successfully:', analyticsData.success); // Debug log

                    if (analyticsData.success && analyticsData.user_info) {
                        const userInfo = analyticsData.user_info;
                        const monthlyStats = analyticsData.monthly_stats || {};
                        const recentOrders = analyticsData.recent_orders || [];
                        const usageRecords = analyticsData.usage_records || [];
                        const dailyUsage = analyticsData.daily_usage || [];

                        console.log('Updating profile with API data'); // Debug log
                        console.log('Monthly stats:', monthlyStats); // Debug log

                        // 更新profile数据，使用API返回的实际数据
                        const updatedProfile = {
                            balance: userInfo.tokens || user?.tokens || 0,
                            current_plan: userInfo.current_plan || user?.current_plan || 'Free Plan',
                            usage_count: userInfo.total_generations || user?.total_generations || 0
                        };
                        setProfile(updatedProfile);
                        setGlobalData(prev => ({ ...prev, profile: updatedProfile }));

                        // 使用API返回的数据更新usageData
                        setUsageData(usageRecords);
                        setGlobalData(prev => ({ ...prev, usageData: usageRecords }));

                        // 使用API返回的Monthly Spend数据
                        const monthlySpend = monthlyStats.spend_usd || 0;

                        // 计算7天使用量
                        const sevenDayUsage = dailyUsage.reduce((sum, day) => sum + (day.tokens_used || 0), 0);

                        const updatedAnalytics = {
                            total_used_30days: monthlyStats.tokens_used || 0,
                            total_requests: Object.values(monthlyStats.usage_by_service || {}).reduce((sum, service) => sum + service.count, 0),
                            daily_usage: dailyUsage,
                            seven_day_usage: sevenDayUsage,
                            monthly_spend: monthlySpend, // 使用计算出的月度消费
                            by_service: monthlyStats.usage_by_service || {}
                        };

                        setUsageAnalytics(updatedAnalytics);
                        setGlobalData(prev => ({ ...prev, usageAnalytics: updatedAnalytics }));

                        console.log('Profile data updated successfully. Monthly spend:', monthlySpend); // Debug log
                    } else {
                        console.warn('Analytics API returned success=false or missing user_info'); // Debug log
                    }
                } else {
                    console.error(`Analytics API failed with status ${analyticsResponse.status}`); // Debug log
                }
            } catch (apiError) {
                console.error('Error calling analytics API:', apiError);
            }
        } catch (error) {
            console.error('Error in fetchProfile:', error);
        }
    };

    // Fetch usage data - 直接调用usage API端点
    const fetchUsageData = async () => {
        if (usageDataFetched) return; // Prevent duplicate calls

        setLoadingUsage(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.error('No token found for usage data fetch');
                setLoadingUsage(false);
                return;
            }

            console.log('Fetching usage data from /api/user/usage/'); // Debug log

            const response = await fetch('/api/user/usage/', {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Usage API response:', response.status, response.statusText); // Debug log

            if (response.ok) {
                const data = await response.json();
                console.log('Usage data received successfully:', data.success); // Debug log

                if (data.success) {
                    // 设置使用记录数据
                    const usageRecords = data.usage_records || [];
                    setUsageData(usageRecords);
                    setGlobalData(prev => ({ ...prev, usageData: usageRecords }));

                    // 更新使用分析数据
                    const updatedAnalytics = {
                        total_used_30days: data.total_tokens_used || 0,
                        total_requests: data.total_requests || 0,
                        daily_usage: data.daily_usage || [],
                        seven_day_usage: data.daily_usage ? data.daily_usage.reduce((sum, day) => sum + (day.tokens_used || 0), 0) : 0,
                        monthly_spend: globalData.usageAnalytics?.monthly_spend || 0, // 保持现有的monthly_spend值
                        by_service: data.usage_by_service || {}
                    };

                    setUsageAnalytics(updatedAnalytics);
                    setGlobalData(prev => ({ ...prev, usageAnalytics: updatedAnalytics }));

                    console.log('Usage data updated successfully, records count:', usageRecords.length); // Debug log
                } else {
                    console.warn('Usage API returned success=false'); // Debug log
                    const emptyData = [];
                    setUsageData(emptyData);
                    setGlobalData(prev => ({ ...prev, usageData: emptyData }));
                }
            } else {
                console.error(`Usage API failed with status ${response.status}`); // Debug log
                // 尝试使用fallback数据
                const fallbackAnalytics = {
                    total_used_30days: 0,
                    total_requests: 0,
                    daily_usage: [],
                    seven_day_usage: 0,
                    monthly_spend: globalData.usageAnalytics?.monthly_spend || 0, // 保持现有的monthly_spend值
                    by_service: {}
                };

                setUsageData([]);
                setUsageAnalytics(fallbackAnalytics);
                setGlobalData(prev => ({
                    ...prev,
                    usageData: [],
                    usageAnalytics: fallbackAnalytics
                }));
            }
        } catch (error) {
            console.error('Error fetching usage data:', error);
            // 设置默认数据
            const defaultAnalytics = {
                total_used_30days: 0,
                total_requests: 0,
                daily_usage: [],
                seven_day_usage: 0,
                monthly_spend: globalData.usageAnalytics?.monthly_spend || 0, // 保持现有的monthly_spend值
                by_service: {}
            };

            setUsageData([]);
            setUsageAnalytics(defaultAnalytics);
            setGlobalData(prev => ({
                ...prev,
                usageData: [],
                usageAnalytics: defaultAnalytics
            }));
        } finally {
            setLoadingUsage(false);
            setUsageDataFetched(true); // Mark as fetched regardless of success/failure
        }
    };

    // Fetch billing data
    const fetchBillingData = async () => {
        if (billingDataFetched || loadingBilling) return; // Prevent duplicate calls

        setLoadingBilling(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            console.log('Fetching billing data...'); // Debug log

            // Call actual payment history API
            const response = await fetch('/api/stripe/payment-history/', {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Billing API response:', response.status, response.statusText); // Debug log

            if (response.ok) {
                const data = await response.json();
                console.log('Payment history data:', data); // Debug log

                if (data.success && data.orders) {
                    // Transform API data to match frontend format
                    const transformedData = data.orders.map(order => {
                        const date = new Date(order.created_at);
                        const formattedDate = date.toLocaleString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: true
                        });

                        return {
                            id: order.id,
                            date: formattedDate,
                            status: order.status === 'COMPLETED' ? 'Paid' : order.status,
                            amount: `$${order.amount}`,
                            invoice: `${order.plan_type} (+${order.tokens_to_add.toLocaleString()} tokens)`,
                            description: `${order.plan_type} Plan`,
                            payment_type: order.payment_type,
                            stripe_payment_intent_id: order.stripe_payment_intent_id,
                            completed_at: order.completed_at,
                            raw_amount: parseFloat(order.amount) // 保存原始数值用于计算
                        };
                    });

                    setBillingData(transformedData);
                    setGlobalData(prev => ({ ...prev, billingData: transformedData }));

                    // 重新计算并更新Monthly Spend
                    const currentMonth = new Date().getMonth();
                    const currentYear = new Date().getFullYear();

                    const monthlySpend = data.orders
                        .filter(order => {
                            const orderDate = new Date(order.completed_at || order.created_at);
                            return orderDate.getMonth() === currentMonth &&
                                orderDate.getFullYear() === currentYear &&
                                order.status === 'COMPLETED';
                        })
                        .reduce((sum, order) => sum + parseFloat(order.amount || 0), 0);

                    // 更新usageAnalytics中的monthly_spend
                    const updatedAnalytics = {
                        ...usageAnalytics,
                        monthly_spend: monthlySpend
                    };

                    setUsageAnalytics(updatedAnalytics);
                    setGlobalData(prev => ({ ...prev, usageAnalytics: updatedAnalytics }));

                    console.log('Billing data set successfully, count:', transformedData.length); // Debug log
                    console.log('Monthly spend updated to:', monthlySpend); // Debug log
                } else {
                    console.error('Invalid payment history response:', data);
                    const emptyData = [];
                    setBillingData(emptyData);
                    setGlobalData(prev => ({ ...prev, billingData: emptyData }));
                }
            } else {
                console.error('Failed to fetch payment history:', response.status);
                const emptyData = [];
                setBillingData(emptyData);
                setGlobalData(prev => ({ ...prev, billingData: emptyData }));
            }
        } catch (error) {
            console.error('Error fetching billing data:', error);
            const emptyData = [];
            setBillingData(emptyData);
            setGlobalData(prev => ({ ...prev, billingData: emptyData }));
        } finally {
            setLoadingBilling(false);
            setBillingDataFetched(true); // Mark as fetched regardless of success/failure
        }
    };

    const handleSaveProfile = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            const response = await fetch('/api/profile/update', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                setShowToast(true);
                setTimeout(() => setShowToast(false), 3000);
            }
        } catch (error) {
            console.error('Error saving profile:', error);
        } finally {
            setLoading(false);
        }
    };

    const handlePasswordChange = async () => {
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            setShowToast({
                message: language === 'zh' ? '新密码和确认密码不匹配' : 'New password and confirm password do not match',
                type: 'error'
            });
            setTimeout(() => setShowToast(false), 3000);
            return;
        }

        if (passwordData.newPassword.length < 6) {
            setShowToast({
                message: language === 'zh' ? '新密码至少需要6个字符' : 'New password must be at least 6 characters long',
                type: 'error'
            });
            setTimeout(() => setShowToast(false), 3000);
            return;
        }

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setShowToast({
                    message: language === 'zh' ? '请先登录' : 'Please login first',
                    type: 'error'
                });
                setTimeout(() => setShowToast(false), 3000);
                return;
            }

            setLoading(true);

            // 获取CSRF token
            const csrfResponse = await fetch('/api/auth/csrf-token/', {
                method: 'GET',
                credentials: 'include'
            });
            const csrfData = await csrfResponse.json();

            // 调用修改密码API
            const response = await fetch('/api/auth/change-password/', {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfData.csrfToken
                },
                credentials: 'include',
                body: JSON.stringify({
                    current_password: passwordData.currentPassword,
                    new_password: passwordData.newPassword
                })
            });

            const data = await response.json();

            if (response.ok) {
                setPasswordData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                });

                setShowToast({
                    message: language === 'zh' ? '密码修改成功' : 'Password changed successfully',
                    type: 'success'
                });
                setTimeout(() => setShowToast(false), 3000);
            } else {
                setShowToast({
                    message: data.message || (language === 'zh' ? '密码修改失败' : 'Failed to change password'),
                    type: 'error'
                });
                setTimeout(() => setShowToast(false), 3000);
            }
        } catch (error) {
            console.error('Error changing password:', error);
            setShowToast({
                message: language === 'zh' ? '密码修改失败，请重试' : 'Failed to change password, please try again',
                type: 'error'
            });
            setTimeout(() => setShowToast(false), 3000);
        } finally {
            setLoading(false);
        }
    };

    const handleLanguageChange = (newLanguage) => {
        if (newLanguage === 'zh') {
            setShowLanguageChangeDialog(true);
        } else {
            setLanguage(newLanguage);
        }
    };

    const getServiceIcon = (service) => {
        switch (service) {
            case 'Image to Video':
                return '🎬';
            case 'Image Editing':
                return '🎨';
            case 'Video Keyframe Extraction':
                return '🎞️';
            case 'Text to Image':
                return '🖼️';
            case 'Text to Video':
                return '📹';
            default:
                return '⚡';
        }
    };

    // 处理tab切换
    const handleTabChange = (tabId) => {
        setActiveTab(tabId);

        // 从全局缓存恢复数据
        if (globalData.profile) {
            setProfile(globalData.profile);
        }
        if (globalData.usageAnalytics) {
            setUsageAnalytics(globalData.usageAnalytics);
        }
        if (globalData.usageData) {
            setUsageData(globalData.usageData);
        }
        if (globalData.billingData) {
            setBillingData(globalData.billingData);
        }

        // 根据tab类型获取相应数据
        if (tabId === 'usage' && !usageDataFetched) {
            fetchUsageData();
        } else if (tabId === 'billing' && !billingDataFetched) {
            fetchBillingData();
        }
    };

    const renderTabContent = () => {
        switch (activeTab) {
            case 'overview':
                return (
                    <div className="space-y-6">
                        {/* 上半部分：指标卡片 */}
                        <div className="bg-white/5 backdrop-blur-sm rounded-3xl border border-white/10 p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                {/* Token Balance */}
                                <div className="text-center">
                                    <div className="flex items-center justify-center mb-3">
                                        <div className="w-2.5 h-2.5 bg-green-400 rounded-full mr-2"></div>
                                        <h3 className="text-white/70 text-sm font-medium">
                                            {language === 'zh' ? 'Token余额' : 'Token Balance'}
                                        </h3>
                                    </div>
                                    <div className="text-3xl font-bold text-white mb-2">
                                        {profile?.balance !== null ? profile.balance.toLocaleString() : '0'}
                                    </div>
                                    <div className="text-sm text-white/60">
                                        {language === 'zh' ? '可用代币' : 'Available tokens'}
                                    </div>
                                </div>

                                {/* Monthly Requests */}
                                <div className="text-center">
                                    <div className="flex items-center justify-center mb-3">
                                        <div className="w-2.5 h-2.5 bg-blue-400 rounded-full mr-2"></div>
                                        <h3 className="text-white/70 text-sm font-medium">
                                            {language === 'zh' ? '月度请求' : 'Monthly Requests'}
                                        </h3>
                                    </div>
                                    <div className="text-3xl font-bold text-white mb-2">
                                        {usageAnalytics.total_requests.toLocaleString()}
                                    </div>
                                    <div className="text-sm text-white/60">
                                        {language === 'zh' ? '30天内API调用' : 'API calls in 30 days'}
                                    </div>
                                </div>

                                {/* Monthly Spend */}
                                <div className="text-center">
                                    <div className="flex items-center justify-center mb-3">
                                        <div className="w-2.5 h-2.5 bg-purple-400 rounded-full mr-2"></div>
                                        <h3 className="text-white/70 text-sm font-medium">
                                            {language === 'zh' ? '月度消费' : 'Monthly Spend'}
                                        </h3>
                                    </div>
                                    <div className="text-3xl font-bold text-white mb-2">
                                        ${typeof usageAnalytics.monthly_spend === 'number'
                                            ? usageAnalytics.monthly_spend.toFixed(2)
                                            : '0.00'}
                                    </div>
                                    <div className="text-sm text-white/60">
                                        {language === 'zh' ? '30天内总消费' : 'Total spend in 30 days'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 下半部分：7天使用趋势图表 */}
                        <div className="bg-white/5 backdrop-blur-sm rounded-3xl border border-white/10 p-6">
                            <div className="flex items-center justify-between mb-6">
                                <div>
                                    <h3 className="text-xl font-semibold text-white mb-2">
                                        {language === 'zh' ? '7天使用趋势' : '7-Day Usage Trend'}
                                    </h3>
                                    <p className="text-white/60 text-sm">
                                        {chartType === 'tokens'
                                            ? (language === 'zh' ? '每日代币使用量统计' : 'Daily token usage statistics')
                                            : (language === 'zh' ? '每日请求次数统计' : 'Daily request count statistics')
                                        }
                                    </p>
                                </div>
                                <div className="flex items-center space-x-6">
                                    <button
                                        onClick={() => setChartType('tokens')}
                                        className={`flex items-center space-x-2 text-sm cursor-pointer transition-all duration-300 ${chartType === 'tokens'
                                            ? 'text-white scale-110'
                                            : 'text-white/50 hover:text-white/70'
                                            }`}
                                    >
                                        <div className={`w-3 h-3 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full ${chartType === 'tokens' ? 'animate-pulse' : ''
                                            }`}></div>
                                        <span>{language === 'zh' ? '代币使用' : 'Tokens Used'}</span>
                                    </button>
                                    <button
                                        onClick={() => setChartType('requests')}
                                        className={`flex items-center space-x-2 text-sm cursor-pointer transition-all duration-300 ${chartType === 'requests'
                                            ? 'text-white scale-110'
                                            : 'text-white/50 hover:text-white/70'
                                            }`}
                                    >
                                        <div className={`w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full ${chartType === 'requests' ? 'animate-pulse' : ''
                                            }`} style={{ animationDelay: '0.5s' }}></div>
                                        <span>{language === 'zh' ? '请求次数' : 'Requests'}</span>
                                    </button>
                                </div>
                            </div>

                            {/* 图表区域 - 改为折线图样式 */}
                            <div className="h-80 relative">
                                {/* 计算图表参数 */}
                                {(() => {
                                    let actualMaxValue, stepValue, chartMaxValue;
                                    if (chartType === 'tokens') {
                                        actualMaxValue = Math.max(...usageAnalytics.daily_usage.slice(-7).map(d => d.tokens_used || 0), 100);
                                        stepValue = Math.ceil(actualMaxValue / 5 / 100) * 100;
                                    } else {
                                        actualMaxValue = Math.max(...usageAnalytics.daily_usage.slice(-7).map(d => d.requests_count || 0), 5);
                                        stepValue = Math.ceil(actualMaxValue / 5);
                                    }
                                    chartMaxValue = stepValue * 5;

                                    // 计算数据点位置
                                    const chartHeight = 240; // 固定图表高度
                                    const chartWidth = 600; // 固定图表宽度
                                    const data = usageAnalytics.daily_usage.slice(-7);
                                    const points = data.map((day, index) => {
                                        const currentValue = chartType === 'tokens' ? (day.tokens_used || 0) : (day.requests_count || 0);
                                        const x = (index / (data.length - 1)) * chartWidth;
                                        const y = chartHeight - (currentValue / chartMaxValue) * chartHeight;
                                        return { x, y, value: currentValue, date: day.date };
                                    });

                                    // 生成SVG路径
                                    const pathData = points.reduce((path, point, index) => {
                                        if (index === 0) {
                                            return `M ${point.x} ${point.y}`;
                                        }
                                        return `${path} L ${point.x} ${point.y}`;
                                    }, '');

                                    const lineColor = chartType === 'tokens' ? '#8B5CF6' : '#06B6D4';
                                    const gradientColor = chartType === 'tokens' ? 'rgba(139, 92, 246, 0.1)' : 'rgba(6, 182, 212, 0.1)';

                                    return (
                                        <>
                                            {/* Y轴标签 */}
                                            <div className="absolute left-0 top-4 bottom-16 w-16 flex flex-col justify-between text-sm text-white/60">
                                                {Array.from({ length: 6 }, (_, i) => (
                                                    <span key={i} className="text-right text-xs font-mono" style={{ lineHeight: '1' }}>
                                                        {((5 - i) * stepValue).toLocaleString()}
                                                    </span>
                                                ))}
                                            </div>

                                            {/* 图表主体 */}
                                            <div className="ml-20 mr-4 absolute top-4 bottom-16 left-0 right-0">
                                                {/* 网格线 */}
                                                <div className="absolute inset-0 flex flex-col justify-between">
                                                    {Array.from({ length: 6 }, (_, i) => (
                                                        <div key={i} className="w-full h-px bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
                                                    ))}
                                                </div>

                                                {/* SVG 折线图 */}
                                                <svg className="absolute inset-0 w-full h-full" viewBox={`0 0 ${chartWidth} ${chartHeight}`} preserveAspectRatio="none">
                                                    {/* 渐变定义 */}
                                                    <defs>
                                                        <linearGradient id={`gradient-${chartType}`} x1="0%" y1="0%" x2="0%" y2="100%">
                                                            <stop offset="0%" stopColor={lineColor} stopOpacity="0.3" />
                                                            <stop offset="100%" stopColor={lineColor} stopOpacity="0" />
                                                        </linearGradient>
                                                    </defs>

                                                    {/* 填充区域 */}
                                                    <path
                                                        d={`${pathData} L ${chartWidth} ${chartHeight} L 0 ${chartHeight} Z`}
                                                        fill={`url(#gradient-${chartType})`}
                                                        className="animate-fade-in"
                                                        style={{ animationDelay: '0.3s' }}
                                                    />

                                                    {/* 主线条 */}
                                                    <path
                                                        d={pathData}
                                                        stroke={lineColor}
                                                        strokeWidth="3"
                                                        fill="none"
                                                        className="animate-draw-line"
                                                        style={{
                                                            strokeDasharray: '1000',
                                                            strokeDashoffset: '1000',
                                                            animation: 'drawLine 1.5s ease-out forwards'
                                                        }}
                                                    />

                                                    {/* 数据点 */}
                                                    {points.map((point, index) => (
                                                        <g key={index}>
                                                            {/* 外圈光环 */}
                                                            <circle
                                                                cx={point.x}
                                                                cy={point.y}
                                                                r="8"
                                                                fill={lineColor}
                                                                fillOpacity="0.2"
                                                                className="animate-pulse"
                                                                style={{ animationDelay: `${index * 0.1 + 1}s` }}
                                                            />
                                                            {/* 数据点 */}
                                                            <circle
                                                                cx={point.x}
                                                                cy={point.y}
                                                                r="4"
                                                                fill={lineColor}
                                                                className="hover:r-6 transition-all duration-200 cursor-pointer animate-scale-in"
                                                                style={{ animationDelay: `${index * 0.1 + 1}s` }}
                                                            />
                                                        </g>
                                                    ))}
                                                </svg>

                                                {/* 交互式数据点和悬停提示 */}
                                                {points.map((point, index) => (
                                                    <div
                                                        key={index}
                                                        className="absolute group cursor-pointer"
                                                        style={{
                                                            left: `${(point.x / chartWidth) * 100}%`,
                                                            top: `${(point.y / chartHeight) * 100}%`,
                                                            transform: 'translate(-50%, -50%)'
                                                        }}
                                                    >
                                                        {/* 隐形的交互区域 */}
                                                        <div className="w-8 h-8 rounded-full"></div>

                                                        {/* 悬停提示 - 智能定位 */}
                                                        <div className={`absolute left-1/2 transform -translate-x-1/2 ${point.y < chartHeight * 0.4 ? 'top-full mt-2' : 'bottom-full mb-2'} bg-black/95 backdrop-blur-xl text-white text-sm px-4 py-3 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap z-30 border border-white/10 shadow-2xl`}>
                                                            <div className="space-y-1">
                                                                <div className="flex items-center space-x-2">
                                                                    <div className={`w-2 h-2 rounded-full ${chartType === 'tokens' ? 'bg-purple-400' : 'bg-blue-400'}`}></div>
                                                                    <span className="font-semibold">
                                                                        {point.value.toLocaleString()} {chartType === 'tokens' ? (language === 'zh' ? '代币' : 'tokens') : (language === 'zh' ? '请求' : 'requests')}
                                                                    </span>
                                                                </div>
                                                                <div className="text-xs text-white/60 border-t border-white/10 pt-1">
                                                                    {point.date || `Day ${index + 1}`}
                                                                </div>
                                                            </div>
                                                            {/* 智能箭头 */}
                                                            <div className={`absolute left-1/2 transform -translate-x-1/2 ${point.y < chartHeight * 0.4 ? 'bottom-full border-4 border-transparent border-b-black/95' : 'top-full border-4 border-transparent border-t-black/95'}`}></div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>

                                            {/* X轴标签 */}
                                            <div className="absolute bottom-0 left-20 right-4 h-12 flex items-center justify-between text-xs text-white/50">
                                                {data.map((day, index) => (
                                                    <span key={index} className="font-medium">
                                                        {day.date ? day.date.slice(-5) : `Day ${index + 1}`}
                                                    </span>
                                                ))}
                                            </div>
                                        </>
                                    );
                                })()}
                            </div>
                        </div>
                    </div>
                );

            case 'usage':
                // Load usage data when tab is first accessed (only if not already loading or loaded)
                if (!usageDataFetched && !loadingUsage) {
                    fetchUsageData();
                }

                return (
                    <div className="space-y-8">
                        <div className="flex items-center justify-between">
                            <h3 className="text-xl font-bold text-white mb-2">
                                {language === 'zh' ? '使用记录' : 'Usage Event Records'}
                            </h3>
                        </div>

                        {loadingUsage ? (
                            <div className="flex justify-center py-12">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400"></div>
                            </div>
                        ) : usageData.length > 0 ? (
                            <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden">
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead>
                                            <tr className="border-b border-white/10">
                                                <th className="text-left py-4 px-6 text-white/70 font-medium">
                                                    {language === 'zh' ? '日期' : 'Date'}
                                                </th>
                                                <th className="text-left py-4 px-6 text-white/70 font-medium">
                                                    {language === 'zh' ? '服务' : 'Service'}
                                                </th>
                                                <th className="text-left py-4 px-6 text-white/70 font-medium">
                                                    {language === 'zh' ? 'Token消耗' : 'Tokens Used'}
                                                </th>
                                                <th className="text-left py-4 px-6 text-white/70 font-medium">
                                                    {language === 'zh' ? '状态' : 'Status'}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {usageData.map((record, index) => (
                                                <tr key={record.id} className={index !== usageData.length - 1 ? 'border-b border-white/5' : ''}>
                                                    <td className="py-4 px-6 text-white/80 text-sm">
                                                        {record.date}
                                                    </td>
                                                    <td className="py-4 px-6">
                                                        <div className="flex items-center space-x-3">
                                                            <span className="text-white font-medium">{record.service}</span>
                                                        </div>
                                                    </td>
                                                    <td className="py-4 px-6 text-white/80 font-medium">
                                                        <span className="text-blue-400">{record.tokens}</span>
                                                    </td>
                                                    <td className="py-4 px-6">
                                                        <span className="px-3 py-1 bg-green-500/20 text-green-400 text-xs font-medium rounded-full">
                                                            {record.status}
                                                        </span>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        ) : (
                            <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-12 text-center">
                                <div className="text-white/40 text-lg mb-2">
                                    {language === 'zh' ? '暂无使用记录' : 'No usage records yet'}
                                </div>
                                <div className="text-white/30 text-sm">
                                    {language === 'zh' ? '开始使用AI服务后，您的使用记录将显示在这里' : 'Your usage records will appear here once you start using AI services'}
                                </div>
                            </div>
                        )}
                    </div>
                );

            case 'billing':
                // Load billing data when tab is first accessed
                if (!billingDataFetched && !loadingBilling) {
                    fetchBillingData();
                }

                return (
                    <div className="space-y-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-xl font-bold text-white mb-2">
                                    {language === 'zh' ? '账单与发票' : 'Billing & Invoices'}
                                </h3>
                                <p className="text-white/60 text-sm">
                                    {language === 'zh' ? '查看您的付款历史和发票' : 'View your payment history and invoices'}
                                </p>
                            </div>
                        </div>

                        <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="p-2 bg-purple-500/20 rounded-lg">
                                    <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <h4 className="text-lg font-semibold text-white">
                                    {language === 'zh' ? '付款历史' : 'Payment History'}
                                </h4>
                            </div>

                            {loadingBilling ? (
                                <div className="flex justify-center py-12">
                                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400"></div>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead>
                                            <tr className="border-b border-white/10">
                                                <th className="text-left py-4 px-2 text-white/70 font-medium">
                                                    {language === 'zh' ? '日期' : 'Date'}
                                                </th>
                                                <th className="text-left py-4 px-2 text-white/70 font-medium">
                                                    {language === 'zh' ? '状态' : 'Status'}
                                                </th>
                                                <th className="text-left py-4 px-2 text-white/70 font-medium">
                                                    {language === 'zh' ? '金额' : 'Amount'}
                                                </th>
                                                <th className="text-left py-4 px-2 text-white/70 font-medium">
                                                    {language === 'zh' ? '发票' : 'Invoice'}
                                                </th>
                                                <th className="text-left py-4 px-2 text-white/70 font-medium">
                                                    {language === 'zh' ? '操作' : 'Actions'}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {billingData.map((record, index) => (
                                                <tr key={record.id} className={index !== billingData.length - 1 ? 'border-b border-white/5' : ''}>
                                                    <td className="py-4 px-2 text-white/80 text-sm">
                                                        {record.date}
                                                    </td>
                                                    <td className="py-4 px-2">
                                                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${record.status === 'Paid'
                                                            ? 'bg-green-500/20 text-green-400'
                                                            : 'bg-yellow-500/20 text-yellow-400'
                                                            }`}>
                                                            {record.status}
                                                        </span>
                                                    </td>
                                                    <td className="py-4 px-2 text-white font-medium">
                                                        {record.amount}
                                                    </td>
                                                    <td className="py-4 px-2 text-white/80 text-sm">
                                                        {record.invoice}
                                                    </td>
                                                    <td className="py-4 px-2">
                                                        <button className="flex items-center space-x-1 px-3 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 text-xs font-medium rounded-lg transition-all duration-200">
                                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                            </svg>
                                                            <span>{language === 'zh' ? '发票' : 'Invoice'}</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </div>
                );

            case 'settings':
                return (
                    <div className="space-y-6">
                        <div>
                            <h3 className="text-xl font-bold text-white mb-2">
                                {language === 'zh' ? '设置' : 'Settings'}
                            </h3>
                            <p className="text-white/60 text-sm">
                                {language === 'zh' ? '管理您的账户设置' : 'Manage your account settings'}
                            </p>
                        </div>

                        {/* Account Settings Section */}
                        <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="p-2 bg-blue-500/20 rounded-lg">
                                    <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <h4 className="text-lg font-semibold text-white">
                                    {language === 'zh' ? '账户设置' : 'Account Settings'}
                                </h4>
                            </div>

                            <div className="space-y-6">
                                {/* Username */}
                                <div className="flex items-center justify-between py-4">
                                    <div>
                                        <div className="text-white font-medium">
                                            {language === 'zh' ? '用户名' : 'Username'}
                                        </div>
                                        <div className="text-white/60 text-sm">
                                            {user?.email || '<EMAIL>'}
                                        </div>
                                    </div>
                                    <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
                                        {language === 'zh' ? '编辑' : 'Edit'}
                                    </button>
                                </div>

                                {/* Email Address */}
                                <div className="flex items-center justify-between py-4 border-t border-white/5">
                                    <div>
                                        <div className="text-white font-medium">
                                            {language === 'zh' ? '邮箱地址' : 'Email Address'}
                                        </div>
                                        <div className="text-white/60 text-sm">
                                            {user?.email || '<EMAIL>'}
                                        </div>
                                    </div>
                                    <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
                                        {language === 'zh' ? '编辑' : 'Edit'}
                                    </button>
                                </div>

                                {/* Password */}
                                <div className="flex items-center justify-between py-4 border-t border-white/5">
                                    <div>
                                        <div className="text-white font-medium">
                                            {language === 'zh' ? '密码' : 'Password'}
                                        </div>
                                        <div className="text-white/60 text-sm">
                                            ••••••••
                                        </div>
                                    </div>
                                    <button
                                        onClick={() => setActiveTab('password')}
                                        className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors"
                                    >
                                        {language === 'zh' ? '更改' : 'Change'}
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Language & Region Section */}
                        <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
                            <div className="flex items-center space-x-3 mb-6">
                                <div className="p-2 bg-green-500/20 rounded-lg">
                                    <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                                    </svg>
                                </div>
                                <h4 className="text-lg font-semibold text-white">
                                    {language === 'zh' ? '语言与地区' : 'Language & Region'}
                                </h4>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <div className="text-white font-medium mb-2">
                                        {language === 'zh' ? '界面语言' : 'Interface Language'}
                                    </div>
                                    <div className="text-white/60 text-sm mb-4">
                                        {language === 'zh' ? '选择您偏好的界面语言' : 'Choose your preferred language for the interface'}
                                    </div>
                                    <select
                                        value={language}
                                        onChange={(e) => handleLanguageChange(e.target.value)}
                                        className="w-32 px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white text-sm focus:outline-none focus:border-purple-400/50 appearance-none"
                                    >
                                        <option value="en" className="bg-gray-800">English</option>
                                        <option value="zh" className="bg-gray-800">中文</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                );

            case 'password':
                return (
                    <div className="space-y-6">
                        <div>
                            <h3 className="text-xl font-bold text-white mb-2">
                                {language === 'zh' ? '更改密码' : 'Change Password'}
                            </h3>
                            <p className="text-white/60 text-sm">
                                {language === 'zh' ? '更新您的密码以保护账户安全' : 'Update your password to keep your account secure'}
                            </p>
                        </div>

                        <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
                            <div className="space-y-6">
                                <div>
                                    <label className="block text-white/70 text-sm font-medium mb-3">
                                        {language === 'zh' ? '当前密码' : 'Current Password'}
                                    </label>
                                    <input
                                        type="password"
                                        value={passwordData.currentPassword}
                                        onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                                        className="w-full px-6 py-4 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:border-purple-400/50 focus:bg-white/10 transition-all duration-300"
                                        placeholder={language === 'zh' ? '输入当前密码' : 'Enter current password'}
                                    />
                                </div>
                                <div>
                                    <label className="block text-white/70 text-sm font-medium mb-3">
                                        {language === 'zh' ? '新密码' : 'New Password'}
                                    </label>
                                    <input
                                        type="password"
                                        value={passwordData.newPassword}
                                        onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                                        className="w-full px-6 py-4 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:border-purple-400/50 focus:bg-white/10 transition-all duration-300"
                                        placeholder={language === 'zh' ? '输入新密码' : 'Enter new password'}
                                    />
                                </div>
                                <div>
                                    <label className="block text-white/70 text-sm font-medium mb-3">
                                        {language === 'zh' ? '确认新密码' : 'Confirm New Password'}
                                    </label>
                                    <input
                                        type="password"
                                        value={passwordData.confirmPassword}
                                        onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                                        className="w-full px-6 py-4 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:border-purple-400/50 focus:bg-white/10 transition-all duration-300"
                                        placeholder={language === 'zh' ? '再次输入新密码' : 'Enter new password again'}
                                    />
                                </div>
                            </div>

                            <div className="flex space-x-4 mt-8">
                                <button
                                    onClick={() => setActiveTab('settings')}
                                    className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300"
                                >
                                    {language === 'zh' ? '取消' : 'Cancel'}
                                </button>
                                <button
                                    onClick={handlePasswordChange}
                                    disabled={!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                                    className="px-6 py-3 bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-purple-500/25 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                >
                                    {language === 'zh' ? '更改密码' : 'Change Password'}
                                </button>
                            </div>
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    if (userLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0f2e] to-[#0f0a1d] flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }

    // Add protection for SSR - don't render if no user
    if (!user) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0f2e] to-[#0f0a1d] flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }

    return (
        <>
            <Head>
                <title>{language === 'zh' ? '个人资料' : 'Profile'} - MirageMakers AI</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta name="description" content={language === 'zh' ? '管理您的个人资料和账户设置' : 'Manage your profile and account settings'} />
                <style jsx>{`
                    .animate-blob {
                        animation: blob 7s infinite;
                    }
                    .animation-delay-2000 {
                        animation-delay: 2s;
                    }
                    .animation-delay-4000 {
                        animation-delay: 4s;
                    }
                    @keyframes blob {
                        0% { transform: translate(0px, 0px) scale(1); }
                        33% { transform: translate(30px, -50px) scale(1.1); }
                        66% { transform: translate(-20px, 20px) scale(0.9); }
                        100% { transform: translate(0px, 0px) scale(1); }
                    }
                    .paper-texture {
                        background-image: 
                            radial-gradient(circle at 25px 25px, rgba(255,255,255,0.02) 2%, transparent 0%),
                            radial-gradient(circle at 75px 75px, rgba(255,255,255,0.01) 1%, transparent 0%);
                        background-size: 100px 100px;
                    }
                    .bookmark-active {
                        clip-path: polygon(0 0, calc(100% - 12px) 0, 100% 50%, calc(100% - 12px) 100%, 0 100%);
                    }
                    .bookmark-inactive {
                        clip-path: polygon(0 0, calc(100% - 12px) 0, calc(100% - 8px) 50%, calc(100% - 12px) 100%, 0 100%);
                    }
                    @keyframes slideUp {
                        from {
                            height: 0;
                            opacity: 0;
                        }
                        to {
                            opacity: 1;
                        }
                    }
                    @keyframes fadeInUp {
                        from {
                            opacity: 0;
                            transform: translateY(10px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }
                    @keyframes drawLine {
                        from {
                            stroke-dashoffset: 1000;
                        }
                        to {
                            stroke-dashoffset: 0;
                        }
                    }
                    @keyframes fadeIn {
                        from {
                            opacity: 0;
                        }
                        to {
                            opacity: 1;
                        }
                    }
                    @keyframes scaleIn {
                        from {
                            transform: scale(0);
                        }
                        to {
                            transform: scale(1);
                        }
                    }
                `}</style>
            </Head>

            <ModernNavbar
                user={user}
                currentPage="profile"
                onUpgrade={() => setShowUpgradeModal(true)}
            />

            {/* 主要内容 - 书签式导航设计 */}
            <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0f2e] to-[#0f0a1d] pt-16">
                {/* 背景装饰 */}
                <div className="fixed inset-0 overflow-hidden pointer-events-none">
                    <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                    <div className="absolute top-40 right-10 w-72 h-72 bg-violet-500/10 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
                    <div className="absolute -bottom-20 left-1/2 w-72 h-72 bg-purple-600/5 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
                </div>

                {/* 纸质质感容器与书签导航 */}
                <div className="relative z-10 max-w-7xl mx-auto px-6 py-12">
                    <div className="bg-black/10 backdrop-blur-2xl rounded-[3rem] shadow-2xl shadow-purple-500/5 border border-white/5 overflow-hidden paper-texture flex">
                        {/* 纸张纹理效果 */}
                        <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] via-transparent to-purple-500/[0.02] pointer-events-none"></div>

                        {/* 左侧书签式导航 */}
                        <div className="w-80 relative z-10 py-12">
                            <div className="space-y-2">
                                {visibleTabs.map((tab, index) => (
                                    <div key={tab.id} className="relative">
                                        <button
                                            onClick={() => handleTabChange(tab.id)}
                                            className={`w-full flex items-center space-x-4 px-8 py-4 text-left transition-all duration-300 ${activeTab === tab.id
                                                ? 'bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-white bookmark-active border-r-4 border-purple-400 shadow-lg shadow-purple-500/20'
                                                : 'text-white/70 hover:text-white hover:bg-white/5 bookmark-inactive'
                                                }`}
                                        >
                                            <div className={`${activeTab === tab.id ? 'text-white' : 'text-white/60'}`}>
                                                {tab.icon}
                                            </div>
                                            <span className="font-medium text-sm">{tab.name}</span>
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* 右侧内容区域 */}
                        <div className="flex-1 relative z-10 px-12 py-12">
                            {/* 内容头部 */}
                            <div className="mb-8">
                                <h1 className="text-3xl font-bold bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-transparent mb-2">
                                    {activeTab === 'password' ? (language === 'zh' ? '更改密码' : 'Change Password') :
                                        visibleTabs.find(tab => tab.id === activeTab)?.name}
                                </h1>
                                <p className="text-white/60">
                                    {activeTab === 'overview' && (language === 'zh' ? '查看您的账户状态和使用情况' : 'View your account status and usage')}
                                    {activeTab === 'usage' && (language === 'zh' ? '查看详细的使用分析和统计' : 'View detailed usage analytics and statistics')}
                                    {activeTab === 'billing' && (language === 'zh' ? '管理您的计费和订阅' : 'Manage your billing and subscriptions')}
                                    {activeTab === 'settings' && (language === 'zh' ? '个性化您的账户设置' : 'Personalize your account settings')}
                                    {activeTab === 'password' && (language === 'zh' ? '更新您的密码以保护账户安全' : 'Update your password to keep your account secure')}
                                </p>
                            </div>

                            {/* 标签内容 - 固定高度避免框体变化 */}
                            <div className="max-w-4xl min-h-[600px]">
                                {renderTabContent()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Toast notifications */}
            {showToast && (
                <div className="fixed bottom-6 right-6 z-50">
                    <div className={`backdrop-blur-xl text-white px-6 py-3 rounded-2xl shadow-lg animate-fadeInUp ${showToast.type === 'error' ? 'bg-red-500/90' :
                        showToast.type === 'success' ? 'bg-green-500/90' :
                            'bg-blue-500/90'
                        }`}>
                        {typeof showToast === 'string' ?
                            (language === 'zh' ? '个人资料已更新' : 'Profile updated successfully') :
                            showToast.message
                        }
                    </div>
                </div>
            )}

            {/* Upgrade Modal */}
            <UpgradeModal 
                isOpen={showUpgradeModal} 
                onClose={() => setShowUpgradeModal(false)} 
            />

            {/* Language Change Dialog */}
            {showLanguageChangeDialog && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-40">
                    <div className="bg-black/80 backdrop-blur-xl rounded-3xl p-8 border border-white/10 max-w-md w-full mx-4">
                        <h3 className="text-2xl font-bold text-white mb-4">
                            语言切换
                        </h3>
                        <p className="text-white/70 mb-6">
                            中文版本正在开发中，敬请期待！
                        </p>
                        <div className="flex justify-center">
                            <button
                                onClick={() => setShowLanguageChangeDialog(false)}
                                className="px-8 py-3 bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white rounded-xl transition-all duration-300"
                            >
                                知道了
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}



