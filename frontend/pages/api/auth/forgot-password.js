import axios from 'axios';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        // 在 Docker 环境中使用容器名，在本地开发中使用 localhost
        const backendUrl = process.env.DOCKER_ENV === 'true'
            ? 'http://django:8000'
            : 'http://127.0.0.1:8000';

        // 直接调用后端API
        const response = await axios.post(`${backendUrl}/api/auth/forgot-password/`, req.body, {
            headers: {
                'Content-Type': 'application/json',
            },
        });

        res.status(200).json(response.data);
    } catch (error) {
        console.error('Forgot password API error:', error);

        if (error.response) {
            res.status(error.response.status).json(error.response.data);
        } else {
            res.status(500).json({ message: '服务器错误' });
        }
    }
} 