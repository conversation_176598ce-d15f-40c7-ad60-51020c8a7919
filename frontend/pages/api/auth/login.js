import axios from 'axios';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        // 在 Docker 环境中使用容器名，支持多域名配置
        const backendUrl = process.env.DOCKER_ENV === 'true'
            ? 'http://django:8000'
            : process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

        const endpoint = 'login';
        const response = await axios.post(`${backendUrl}/api/auth/${endpoint}/`, req.body, {
            headers: {
                'Content-Type': 'application/json',
                'X-Forwarded-For': req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                'X-Forwarded-Proto': 'https',
                'X-Real-IP': req.headers['x-real-ip'] || req.connection.remoteAddress,
            },
            timeout: 10000, // 10秒超时
        });

        res.status(200).json(response.data);
    } catch (error) {
        console.error('API Error:', error.message);
        if (error.response) {
            res.status(error.response.status).json(error.response.data);
        } else if (error.code === 'ECONNREFUSED') {
            res.status(503).json({ message: '后端服务暂时不可用，请稍后重试' });
        } else {
            res.status(500).json({ message: '服务器内部错误' });
        }
    }
}
