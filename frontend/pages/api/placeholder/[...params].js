export default function handler(req, res) {
    const { params } = req.query;
    const [width, height] = params;
    const { text = 'U', bg = '8B5CF6', color = 'white' } = req.query;

    const svg = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#${bg}"/>
            <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${Math.min(parseInt(width), parseInt(height)) * 0.4}" font-weight="bold" fill="${color}" text-anchor="middle" dominant-baseline="central">
                ${text}
            </text>
        </svg>
    `;

    res.setHeader('Content-Type', 'image/svg+xml');
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
    res.status(200).send(svg);
} 