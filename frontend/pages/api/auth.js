import axios from 'axios';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // 在 Docker 环境中使用容器名，在本地开发中使用 localhost
    const backendUrl = process.env.DOCKER_ENV === 'true'
      ? 'http://django:8000'
      : 'http://127.0.0.1:8000';

    const response = await axios.get(`${backendUrl}/`);
    res.status(200).json(response.data);
  } catch (error) {
    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({ message: '服务器错误' });
    }
  }
}

// Helper function to extract CSRF token from HTML
function extractCsrfToken(html) {
  const match = html.match(/name="csrfmiddlewaretoken" value="(.+?)"/);
  return match ? match[1] : null;
} 
