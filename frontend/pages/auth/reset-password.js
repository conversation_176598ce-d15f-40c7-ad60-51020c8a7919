import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import axios from 'axios';
import { useToast } from '../../components/Toast';

export default function ResetPassword() {
  const router = useRouter();
  const { email: queryEmail } = router.query;
  const { showToast } = useToast();
  const [formData, setFormData] = useState({
    email: '',
    code: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // 从 URL 参数或本地存储加载邮箱地址
  useEffect(() => {
    if (queryEmail) {
      setFormData(prev => ({ ...prev, email: queryEmail }));
    } else if (typeof window !== 'undefined') {
      // 确保只在客户端环境中访问 localStorage
      const savedEmail = localStorage.getItem('reset_email');
      if (savedEmail) {
        setFormData(prev => ({ ...prev, email: savedEmail }));
      }
    }
  }, [queryEmail]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Please enter verification code';
    } else if (formData.code.length !== 6) {
      newErrors.code = 'Verification code must be 6 digits';
    }

    if (!formData.password) {
      newErrors.password = 'Please enter new password';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await axios.post('/api/auth/reset-password', {
        email: formData.email.trim(),
        code: formData.code.trim(),
        password: formData.password
      });

      // 清除保存的邮箱 - 确保只在客户端环境中操作
      if (typeof window !== 'undefined') {
        localStorage.removeItem('reset_email');
      }

      // 显示成功消息
      showToast('密码重置成功！请使用新密码登录', 'success');

      // 跳转到登录页面，并传递成功消息
      router.push('/auth/login?message=password_reset_success');

    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Password reset failed, please try again';
      setErrors({
        general: errorMessage
      });
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0f0a1d] flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <Head>
        <title>Reset Password - MirageMakers AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <Link href="/">
            <div className="text-3xl font-bold bg-gradient-to-r from-violet-400 to-purple-400 text-transparent bg-clip-text cursor-pointer">
              MirageMakers AI
            </div>
          </Link>
          <h2 className="mt-6 text-center text-2xl font-medium text-white">
            Reset Your Password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-400">
            Enter the verification code from your email and create a new password
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-[#241b3a]/80 backdrop-blur-lg py-8 px-4 shadow-2xl border border-[#3a2a5a]/50 sm:rounded-2xl sm:px-10">
          {errors.general && (
            <div className="mb-6 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
              <p className="text-red-400 text-sm">{errors.general}</p>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                Email Address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-[#3a2a5a]/50 rounded-lg shadow-sm placeholder-gray-500 bg-[#1a0f2e]/60 text-white focus:outline-none focus:ring-2 focus:ring-violet-600 focus:border-violet-600 sm:text-sm"
                  placeholder="Enter your email address"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-400">{errors.email}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="code" className="block text-sm font-medium text-gray-300">
                Verification Code
              </label>
              <div className="mt-1">
                <input
                  id="code"
                  name="code"
                  type="text"
                  maxLength="6"
                  required
                  value={formData.code}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-[#3a2a5a]/50 rounded-lg shadow-sm placeholder-gray-500 bg-[#1a0f2e]/60 text-white focus:outline-none focus:ring-2 focus:ring-violet-600 focus:border-violet-600 sm:text-sm text-center tracking-widest"
                  placeholder="6-digit code"
                />
                {errors.code && (
                  <p className="mt-1 text-sm text-red-400">{errors.code}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                New Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-[#3a2a5a]/50 rounded-lg shadow-sm placeholder-gray-500 bg-[#1a0f2e]/60 text-white focus:outline-none focus:ring-2 focus:ring-violet-600 focus:border-violet-600 sm:text-sm"
                  placeholder="Enter new password (at least 6 characters)"
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400">{errors.password}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
                Confirm New Password
              </label>
              <div className="mt-1">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-[#3a2a5a]/50 rounded-lg shadow-sm placeholder-gray-500 bg-[#1a0f2e]/60 text-white focus:outline-none focus:ring-2 focus:ring-violet-600 focus:border-violet-600 sm:text-sm"
                  placeholder="Confirm your new password"
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-400">{errors.confirmPassword}</p>
                )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 hover:shadow-lg hover:shadow-violet-500/25 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Resetting Password...
                  </div>
                ) : (
                  'Reset Password'
                )}
              </button>
            </div>

            <div className="text-center">
              <Link href="/auth/forgot-password">
                <span className="text-sm font-medium text-violet-400 hover:text-violet-300 cursor-pointer transition-colors">
                  Didn't receive code? Send again
                </span>
              </Link>
            </div>

            <div className="text-center">
              <Link href="/auth/login">
                <span className="text-sm font-medium text-violet-400 hover:text-violet-300 cursor-pointer transition-colors">
                  Back to Login
                </span>
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 