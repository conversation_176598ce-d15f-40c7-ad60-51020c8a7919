import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import axios from 'axios';
import { useLanguage } from '../../contexts/LanguageContext';
import { useUserContext } from '../../contexts/UserContext';
import { useToast } from '../../components/Toast';
import ModernNavbar from '../../components/ModernNavbar';
import Loading from '../../components/Loading';

export default function Login() {
  const router = useRouter();
  const { t } = useLanguage();
  const { message } = router.query;
  const { user, loading: userLoading, updateUser } = useUserContext();
  const { showToast } = useToast();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [socialProviders, setSocialProviders] = useState([]);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    // 检查是否有成功消息
    if (message === 'password_reset_success') {
      setSuccessMessage(t('auth.password.reset.success'));
    }

    // 获取社交登录提供商
    const fetchSocialProviders = async () => {
      try {
        const response = await axios.get('/api/auth/social-success/providers/');
        setSocialProviders(response.data.providers || []);
      } catch (error) {
        console.log('Failed to fetch social providers:', error);
        // 如果API失败，设置默认的社交登录选项
        setSocialProviders([
          { name: 'google', display_name: 'Google', login_url: '/accounts/google/login/' },
          { name: 'facebook', display_name: 'Facebook', login_url: '/accounts/facebook/login/' },
          { name: 'discord', display_name: 'Discord', login_url: '/accounts/discord/login/' },
          { name: 'twitter', display_name: 'X (Twitter)', login_url: '/accounts/twitter/login/' }
        ]);
      }
    };
    fetchSocialProviders();
  }, [message, t]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = t('auth.email.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('auth.email.invalid');
    }

    if (!formData.password) {
      newErrors.password = t('auth.password.required');
    } else if (formData.password.length < 6) {
      newErrors.password = t('auth.password.min.length');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await axios.post('/api/auth/login', formData);

      // Store user data and token
      if (typeof window !== 'undefined') {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }

      // Update user context
      updateUser(response.data.user);

      // Show success message
      showToast(t('auth.login.success') || 'Login successful!', 'success');

      // Redirect to main page
      router.push('/creative');

    } catch (error) {
      const errorMessage = error.response?.data?.message || t('auth.login.failed');
      setErrors({
        general: errorMessage
      });
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 如果用户已登录，重定向到创作页面
  if (user && !userLoading) {
    router.push('/creative');
    return null;
  }

  // 显示加载状态
  if (userLoading) {
    return (
      <div className="min-h-screen bg-[#0f0a1d] flex items-center justify-center">
        <Loading type="spinner" size="lg" color="purple" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0f0a1d] flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <Head>
        <title>Sign In - MirageMakers AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <Link href="/">
            <div className="text-3xl font-bold bg-gradient-to-r from-violet-400 to-purple-400 text-transparent bg-clip-text cursor-pointer">
              MirageMakers AI
            </div>
          </Link>
          <h2 className="mt-6 text-center text-2xl font-medium text-white">
            Welcome back
          </h2>
          <p className="mt-2 text-center text-sm text-gray-400">
            Sign in to your account to continue
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-[#241b3a]/80 backdrop-blur-lg py-8 px-4 shadow-2xl border border-[#3a2a5a]/50 sm:rounded-2xl sm:px-10">
          {errors.general && (
            <div className="mb-6 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
              <p className="text-red-400 text-sm">{errors.general}</p>
            </div>
          )}

          {successMessage && (
            <div className="mb-6 p-3 rounded-lg bg-green-500/10 border border-green-500/20">
              <p className="text-green-400 text-sm">{successMessage}</p>
            </div>
          )}

          {/* Social Sign In Buttons */}
          {false && socialProviders.length > 0 && (
            <div className="mb-6">
              <div className="text-center text-sm text-gray-400 mb-4">Sign in with social account</div>
              <div className="grid grid-cols-2 gap-3">
                {socialProviders.filter(provider => provider.name !== 'google').map((provider) => (
                  <a
                    key={provider.name}
                    href={provider.login_url}
                    className="flex items-center justify-center px-4 py-3 border border-slate-700 rounded-lg text-sm font-medium text-gray-300 hover:bg-violet-600/10 transition-all hover:border-violet-500/50 hover:shadow-lg group"
                  >
                    <div className="flex items-center space-x-3">
                      {provider.name === 'google' && (
                        <svg className="w-5 h-5" viewBox="0 0 24 24">
                          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                        </svg>
                      )}
                      {provider.name === 'facebook' && (
                        <svg className="w-5 h-5" viewBox="0 0 24 24">
                          <path fill="#1877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                        </svg>
                      )}
                      {provider.name === 'discord' && (
                        <svg className="w-5 h-5" viewBox="0 0 24 24">
                          <path fill="#5865F2" d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                        </svg>
                      )}
                      {provider.name === 'twitter' && (
                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                        </svg>
                      )}
                      <span className="group-hover:text-white transition-colors">
                        {provider.display_name}
                      </span>
                    </div>
                  </a>
                ))}
              </div>
              <div className="mt-6 relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-700" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-slate-900/50 text-gray-400">Or sign in with email</span>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                Email
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-[#3a2a5a]/50 rounded-lg shadow-sm placeholder-gray-500 bg-[#1a0f2e]/60 text-white focus:outline-none focus:ring-2 focus:ring-[#6d28d9] focus:border-[#6d28d9] sm:text-sm"
                  placeholder="Enter your email"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-400">{errors.email}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-[#3a2a5a]/50 rounded-lg shadow-sm placeholder-gray-500 bg-[#1a0f2e]/60 text-white focus:outline-none focus:ring-2 focus:ring-[#6d28d9] focus:border-[#6d28d9] sm:text-sm"
                  placeholder="Enter your password"
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400">{errors.password}</p>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm">
                <Link href="/auth/forgot-password">
                  <span className="font-medium text-violet-400 hover:text-violet-300 cursor-pointer transition-colors">
                    Forgot your password?
                  </span>
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 hover:shadow-lg hover:shadow-violet-500/25 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </div>
                ) : (
                  'Sign In'
                )}
              </button>
            </div>

            <div className="text-center">
              <span className="text-sm text-gray-400">
                Don't have an account?
                <Link href="/auth/register">
                  <span className="font-medium text-violet-400 hover:text-violet-300 cursor-pointer transition-colors ml-1">
                    Register
                  </span>
                </Link>
              </span>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 