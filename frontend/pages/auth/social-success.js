import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function SocialSuccess() {
  const router = useRouter();

  useEffect(() => {
    const { token, user_id, error } = router.query;

    if (error) {
      // 社交登录失败，重定向到登录页面并显示错误
      router.push(`/auth/login?error=${error}`);
      return;
    }

    if (token && user_id) {
      // 获取用户信息
      const fetchUserData = async () => {
        try {
          const response = await fetch('/api/auth/profile/', {
            headers: {
              'Authorization': `Token ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const data = await response.json();

            // 保存token和用户信息
            localStorage.setItem('token', token);
            localStorage.setItem('user', JSON.stringify(data.user));

            // 重定向到聊天页面
            router.push('/creative');
          } else {
            // 获取用户信息失败
            router.push('/auth/login?error=profile_fetch_failed');
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          router.push('/auth/login?error=network_error');
        }
      };

      fetchUserData();
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-[#0d1121] flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <Head>
        <title>登录处理中 - MirageMakers AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="text-3xl font-bold bg-gradient-to-r from-[#00d9ff] to-[#7b5dfa] text-transparent bg-clip-text mb-8">
            MirageMakers AI
          </div>

          <div className="bg-[#0f1429] py-8 px-4 shadow border border-[#1d2440] sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00d2ff] mx-auto mb-4"></div>
              <h2 className="text-xl font-medium text-white mb-2">
                登录处理中...
              </h2>
              <p className="text-gray-400 text-sm">
                正在完成您的社交账号登录，请稍候
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 