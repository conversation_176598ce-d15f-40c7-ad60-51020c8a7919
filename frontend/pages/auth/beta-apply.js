import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import axios from 'axios';
import { useToast } from '../../components/Toast';

export default function BetaApply() {
    const router = useRouter();
    const { showToast } = useToast();
    const [formData, setFormData] = useState({
        email: '',
        name: '',
        company: '',
        purpose: '',
        source: '',
        source_detail: ''
    });
    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);

    const sourceOptions = [
        { value: 'SOCIAL_MEDIA', label: 'Social Media' },
        { value: 'SEARCH_ENGINE', label: 'Search Engine' },
        { value: 'FRIEND_REFERRAL', label: 'Friend Referral' },
        { value: 'TECH_BLOG', label: 'Tech Blog' },
        { value: 'ONLINE_COMMUNITY', label: 'Online Community' },
        { value: 'ADVERTISING', label: 'Advertising' },
        { value: 'OTHER', label: 'Other' }
    ];

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        if (!formData.purpose.trim()) {
            newErrors.purpose = 'Purpose is required';
        } else if (formData.purpose.trim().length < 10) {
            newErrors.purpose = 'Please describe your purpose in detail (at least 10 characters)';
        }

        if (!formData.source) {
            newErrors.source = 'Please select how you heard about us';
        }

        if (!formData.source_detail.trim()) {
            newErrors.source_detail = 'Source details are required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        try {
            const response = await axios.post('/api/auth/beta-apply', formData);

            showToast(
                'Application submitted successfully! We will review your application and contact you via email soon.',
                'success'
            );

            // 清空表单
            setFormData({
                email: '',
                name: '',
                company: '',
                purpose: '',
                source: '',
                source_detail: ''
            });

        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Application failed, please try again later';
            setErrors({
                general: errorMessage
            });
            showToast(errorMessage, 'error');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-[#0f0a1d] flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <Head>
                <title>Beta Application - MirageMakers AI</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            </Head>

            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <div className="text-center">
                    <Link href="/">
                        <div className="text-3xl font-bold bg-gradient-to-r from-violet-400 to-purple-400 text-transparent bg-clip-text cursor-pointer">
                            MirageMakers AI
                        </div>
                    </Link>
                    <h2 className="mt-6 text-center text-2xl font-medium text-white">
                        Beta Application
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-400">
                        Apply to join our beta program and experience the infinite possibilities of AI creation
                    </p>
                </div>
            </div>

            <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                <div className="bg-[#241b3a]/80 backdrop-blur-lg py-8 px-4 shadow-2xl border border-[#3a2a5a]/50 sm:rounded-2xl sm:px-10">
                    {errors.general && (
                        <div className="mb-6 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                            <p className="text-red-400 text-sm">{errors.general}</p>
                        </div>
                    )}

                    <form className="space-y-6" onSubmit={handleSubmit}>
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                                Email Address <span className="text-red-400">*</span>
                            </label>
                            <div className="mt-1">
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    required
                                    value={formData.email}
                                    onChange={handleChange}
                                    className="appearance-none block w-full px-3 py-2 border border-[#1d2440] rounded-lg shadow-sm placeholder-gray-500 bg-[#0d1121] text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 sm:text-sm"
                                    placeholder="Enter your email address"
                                />
                                {errors.email && (
                                    <p className="mt-1 text-sm text-red-400">{errors.email}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-300">
                                Name
                            </label>
                            <div className="mt-1">
                                <input
                                    id="name"
                                    name="name"
                                    type="text"
                                    autoComplete="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    className="appearance-none block w-full px-3 py-2 border border-[#1d2440] rounded-lg shadow-sm placeholder-gray-500 bg-[#0d1121] text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 sm:text-sm"
                                    placeholder="Enter your name (optional)"
                                />
                                {errors.name && (
                                    <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="company" className="block text-sm font-medium text-gray-300">
                                Company/Organization
                            </label>
                            <div className="mt-1">
                                <input
                                    id="company"
                                    name="company"
                                    type="text"
                                    value={formData.company}
                                    onChange={handleChange}
                                    className="appearance-none block w-full px-3 py-2 border border-[#1d2440] rounded-lg shadow-sm placeholder-gray-500 bg-[#0d1121] text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 sm:text-sm"
                                    placeholder="Enter your company or organization (optional)"
                                />
                                {errors.company && (
                                    <p className="mt-1 text-sm text-red-400">{errors.company}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="purpose" className="block text-sm font-medium text-gray-300">
                                Purpose <span className="text-red-400">*</span>
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="purpose"
                                    name="purpose"
                                    rows={4}
                                    required
                                    value={formData.purpose}
                                    onChange={handleChange}
                                    className="appearance-none block w-full px-3 py-2 border border-[#1d2440] rounded-lg shadow-sm placeholder-gray-500 bg-[#0d1121] text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 sm:text-sm"
                                    placeholder="Please describe in detail how you plan to use our AI services, e.g., content creation, marketing materials, personal projects, etc..."
                                />
                                {errors.purpose && (
                                    <p className="mt-1 text-sm text-red-400">{errors.purpose}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="source" className="block text-sm font-medium text-gray-300">
                                How did you hear about us? <span className="text-red-400">*</span>
                            </label>
                            <div className="mt-1">
                                <select
                                    id="source"
                                    name="source"
                                    required
                                    value={formData.source}
                                    onChange={handleChange}
                                    className="appearance-none block w-full px-3 py-2 border border-[#1d2440] rounded-lg shadow-sm bg-[#0d1121] text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 sm:text-sm"
                                >
                                    <option value="">
                                        Please select...
                                    </option>
                                    {sourceOptions.map(option => (
                                        <option key={option.value} value={option.value}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                                {errors.source && (
                                    <p className="mt-1 text-sm text-red-400">{errors.source}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <label htmlFor="source_detail" className="block text-sm font-medium text-gray-300">
                                Source Details <span className="text-red-400">*</span>
                            </label>
                            <div className="mt-1">
                                <input
                                    id="source_detail"
                                    name="source_detail"
                                    type="text"
                                    required
                                    value={formData.source_detail}
                                    onChange={handleChange}
                                    className="appearance-none block w-full px-3 py-2 border border-[#1d2440] rounded-lg shadow-sm placeholder-gray-500 bg-[#0d1121] text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 sm:text-sm"
                                    placeholder="Please specify, e.g., social media account, friend's name, specific website, etc..."
                                />
                                {errors.source_detail && (
                                    <p className="mt-1 text-sm text-red-400">{errors.source_detail}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 hover:shadow-lg hover:shadow-violet-500/25 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                            >
                                {isLoading ? (
                                    <div className="flex items-center">
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Submitting...
                                    </div>
                                ) : (
                                    'Submit'
                                )}
                            </button>
                        </div>

                        <div className="text-center">
                            <span className="text-sm text-gray-400">
                                Already have an account?
                                <Link href="/auth/login">
                                    <span className="font-medium text-violet-400 hover:text-violet-300 cursor-pointer transition-colors ml-1">
                                        Sign in now
                                    </span>
                                </Link>
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
} 