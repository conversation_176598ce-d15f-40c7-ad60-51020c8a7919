import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Head from 'next/head';

export default function ResultPage() {
  const router = useRouter();
  const [resultData, setResultData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // The result data would typically be passed via query params or context
    // In a real implementation, you might want to fetch this from a server endpoint
    if (router.query.data) {
      try {
        setResultData(JSON.parse(router.query.data));
      } catch (e) {
        console.error('Failed to parse result data:', e);
      }
    }
    setLoading(false);
  }, [router.query]);

  if (loading) {
    return <div className="p-4 text-center">加载中...</div>;
  }

  if (!resultData) {
    return (
      <div className="p-4 text-center">
        <p className="text-red-500">未找到结果数据</p>
        <button 
          onClick={() => router.push('/')}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
        >
          返回首页
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <Head>
        <title>生成结果</title>
      </Head>

      <div className="max-w-2xl mx-auto bg-white p-6 rounded shadow">
        <h2 className="text-2xl mb-4">生成结果</h2>

        {/* Chat response display */}
        {resultData.chat_response && (
          <div id="chat-response" className="p-2 bg-gray-100 rounded">
            <div dangerouslySetInnerHTML={{ __html: resultData.chat_response }} />
          </div>
        )}

        {/* Error message display */}
        {resultData.error && (
          <span id="error-text" className="text-red-500">{resultData.error}</span>
        )}

        {/* Media generation results */}
        {!resultData.chat_response && !resultData.error && (
          <>
            {/* Thinking logs */}
            {resultData.logs && resultData.logs.length > 0 && (
              <div id="thinking-logs">
                {resultData.logs.map((line, index) => (
                  <div key={index} className="italic text-gray-600 text-sm mb-1">{line}</div>
                ))}
              </div>
            )}

            {/* Video display */}
            {resultData.video_url && (
              <video controls className="max-w-full rounded">
                <source src={resultData.video_url} type="video/mp4" />
                您的浏览器不支持视频播放。
              </video>
            )}

            {/* Image display */}
            {resultData.image_url && (
              <img src={resultData.image_url} alt="生成图像" className="max-w-full rounded" />
            )}

            {/* No media message */}
            {!resultData.video_url && !resultData.image_url && (
              <p>未返回任何媒体链接。</p>
            )}
          </>
        )}

        <button 
          onClick={() => router.push('/')}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
        >
          返回首页
        </button>
      </div>
    </div>
  );
}
