import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useUser } from '../../contexts/UserContext';

export default function PaymentSuccess() {
    const router = useRouter();
    const { session_id } = router.query;
    const [loading, setLoading] = useState(true);
    const [result, setResult] = useState(null);
    const { actions } = useUser(); // 获取UserContext的actions

    useEffect(() => {
        if (session_id) {
            handlePaymentSuccess();
        }
    }, [session_id]);

    const handlePaymentSuccess = async () => {
        const authToken = localStorage.getItem('token');
        if (!authToken) {
            router.push('/auth/login');
            return;
        }

        try {
            console.log('Processing Stripe payment success with session_id:', session_id);

            const response = await fetch('/api/stripe/checkout-success/', {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${authToken}`,
                    'Content-Type': 'application/json',
                },
                // 添加session_id作为查询参数
                url: `/api/stripe/checkout-success/?session_id=${session_id}`
            });

            // 修正请求方式
            const successResponse = await fetch(`/api/stripe/checkout-success/?session_id=${session_id}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${authToken}`,
                    'Content-Type': 'application/json',
                }
            });

            console.log('Response status:', successResponse.status);
            const data = await successResponse.json();
            console.log('Response data:', data);

            if (successResponse.ok && data.success) {
                setResult({
                    success: true,
                    tokens_added: data.tokens_added,
                    message: data.message || '支付成功！代币已添加到您的账户。',
                    order_id: data.order_id
                });

                // 刷新用户数据以更新Token余额
                try {
                    await actions.fetchUser();
                    console.log('User data refreshed after payment success');
                } catch (refreshError) {
                    console.error('Failed to refresh user data:', refreshError);
                }

                // 3秒后跳转到profile页面
                setTimeout(() => {
                    router.push('/profile');
                }, 3000);
            } else {
                setResult({
                    success: false,
                    error: data.error || '支付处理失败'
                });
            }
        } catch (error) {
            console.error('Payment success handling error:', error);
            setResult({
                success: false,
                error: '网络错误，请稍后重试'
            });
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#241b3a] to-[#0f0a1d] flex items-center justify-center">
                <Head>
                    <title>处理支付结果 - MirageMakers AI</title>
                </Head>

                <div className="max-w-md w-full bg-[#241b3a] border border-[#3a2a5a]/50 rounded-lg p-8 text-center">
                    <div className="w-16 h-16 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
                    <h1 className="text-xl font-bold text-white mb-2">处理支付结果</h1>
                    <p className="text-gray-300">请稍候，正在验证您的支付...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#241b3a] to-[#0f0a1d] flex items-center justify-center">
            <Head>
                <title>支付结果 - MirageMakers AI</title>
            </Head>

            <div className="max-w-md w-full bg-[#241b3a] border border-[#3a2a5a]/50 rounded-lg p-8 text-center">
                {result?.success ? (
                    <>
                        {/* 成功图标 */}
                        <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>

                        <h1 className="text-2xl font-bold text-white mb-4">支付成功！</h1>
                        <p className="text-gray-300 mb-6">{result.message}</p>

                        {result.order_id && (
                            <div className="text-xs text-gray-500 mb-4">
                                订单号: {result.order_id}
                            </div>
                        )}

                        <div className="bg-purple-600/20 border border-purple-500/30 rounded-lg p-4 mb-6">
                            <div className="text-purple-300 text-sm mb-1">获得代币</div>
                            <div className="text-2xl font-bold text-white">+{result.tokens_added?.toLocaleString()}</div>
                        </div>

                        <div className="text-sm text-gray-400 mb-4">
                            3秒后自动跳转到个人中心...
                        </div>

                        <button
                            onClick={() => router.push('/profile')}
                            className="w-full py-3 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-purple-500/20 transition-all"
                        >
                            立即查看
                        </button>
                    </>
                ) : (
                    <>
                        {/* 失败图标 */}
                        <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>

                        <h1 className="text-2xl font-bold text-white mb-4">支付处理失败</h1>
                        <p className="text-gray-300 mb-6">{result?.error}</p>

                        <div className="space-y-3">
                            <button
                                onClick={() => router.push('/profile')}
                                className="w-full py-3 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-purple-500/20 transition-all"
                            >
                                返回个人中心
                            </button>
                            <button
                                onClick={() => router.push('/')}
                                className="w-full py-3 border border-gray-500 text-gray-300 rounded-lg hover:bg-gray-500/10 transition-all"
                            >
                                返回首页
                            </button>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
