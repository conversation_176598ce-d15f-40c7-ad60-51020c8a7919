# 📖 MirageMaker AI - 前端开发快速指南 (Docker环境)

这是一份详细的**Docker化前端开发**指南，帮助你快速理解项目结构、技术栈，并学会如何在Docker环境中修改各种前端元素。

> ⚠️ **重要**: 本项目完全基于Docker开发，所有依赖安装和环境配置都在Docker容器中完成，请不要在物理机上运行npm命令。

## 🏗️ 技术架构

### 🔧 技术栈 & 版本

| 技术 | 版本 | 描述 |
|------|------|------|
| **Framework** | Next.js 14 | React 全栈框架，支持SSR/SSG |
| **UI Framework** | React 18 | 前端UI框架 |
| **样式框架** | Tailwind CSS 3.4 | 原子化CSS框架 |
| **动画库** | Framer Motion 12+ | 现代动画库 (Docker容器中安装) |
| **3D库** | Three.js | 3D渲染和交互 |
| **状态管理** | React Context + Hooks | 内置状态管理 |
| **路由** | Next.js Router | 内置路由系统 |
| **构建工具** | Next.js (Webpack + SWC) | 内置构建优化 |
| **包管理器** | npm (Docker内部) | 依赖管理 |
| **开发环境** | Docker + Hot Reload | 容器化开发 |

### 🐳 Docker化开发流程

```mermaid
graph TD
    A[本地代码修改] --> B[Docker自动热重载]
    B --> C[容器内重新构建]
    C --> D[浏览器自动刷新]
    D --> E[测试效果]
    E --> F{是否满意?}
    F -->|否| A
    F -->|是| G[提交代码]
```

### 🏛️ 项目架构 (Docker环境)

```
visual_gen_agent/
├── 📁 docker/                  # Docker配置和脚本
│   ├── start.sh                # 主部署脚本 (包含依赖安装)
│   ├── deploy-with-docker.sh   # Docker化部署脚本
│   ├── hot_reload.sh           # 热重载监控脚本
│   └── docker-compose.yml      # 容器编排配置
├── 📁 frontend/                # 前端代码 (容器内执行)
│   ├── 📁 pages/               # Next.js 页面路由
│   │   ├── index.js            # 首页
│   │   ├── chat.js             # AI聊天页面
│   │   ├── gallery.js          # 作品画廊 (需要framer-motion)
│   │   ├── profile.js          # 个人中心
│   │   └── auth/               # 认证相关页面
│   ├── 📁 components/          # React 组件
│   │   ├── ui/                 # UI基础组件
│   │   │   ├── logo.jsx        # Logo组件
│   │   │   ├── flip-card.jsx   # 3D卡片 (依赖framer-motion)
│   │   │   └── gallery-grid.jsx # Gallery网格布局
│   │   ├── Layout.jsx          # 页面布局
│   │   └── ModernNavbar.jsx    # 导航栏
│   ├── package.json            # 依赖配置 (容器内安装)
│   └── Dockerfile              # 前端容器配置
└── README.md
```

## 🚀 开发环境启动

### 1. 🐳 Docker环境启动 (推荐)

```bash
# 进入项目根目录
cd visual_gen_agent

# 启动完整Docker环境 (包含依赖安装)
./docker/start.sh --local

# 或者使用Docker化部署 (专门处理3D组件)
./docker/deploy-with-docker.sh --local
```

### 2. 🔧 修复依赖问题

如果遇到framer-motion或其他3D组件报错：

```bash
# 使用修复模式启动 (自动安装缺失依赖)
./docker/start.sh --local --fix

# 手动在容器中重新安装依赖
docker exec MirageMakers-frontend npm install

# 验证关键依赖
docker exec MirageMakers-frontend npm ls framer-motion
docker exec MirageMakers-frontend npm ls three
```

### 3. ⚡ 热重载开发

```bash
# 启动热重载监控 (自动检测文件变化)
./docker/hot_reload.sh

# 手动重启前端容器
docker-compose restart frontend
```

### ❌ 不要在物理机上运行

```bash
# ❌ 错误：不要在物理机运行
npm install
npm run dev

# ✅ 正确：在Docker容器中开发
docker exec MirageMakers-frontend npm install
docker exec MirageMakers-frontend npm run dev
```

## 🎨 常见修改操作 (Docker环境)

### 1. 📝 Logo 修改

**位置**: `frontend/components/ui/logo.jsx`

```jsx
// 修改Logo文字
<span className="...">
    MirageMaker AI  {/* 👈 修改这里 */}
</span>

// 修改Logo图标 (SVG path)
<path d="M50 10C30 10..." fill="currentColor"/>  {/* 👈 修改SVG路径 */}
```

**热重载测试**:
```bash
# 保存文件后，Docker会自动重新构建
# 浏览器会自动刷新显示更改
```

### 2. 🎨 样式和3D效果修改

**Framer Motion动画** (`components/ui/flip-card.jsx`):
```jsx
// 修改3D翻转动画
<motion.div
    whileHover={{
        scale: 1.05,           // 👈 修改缩放比例
        rotateY: 5,            // 👈 修改旋转角度
        z: 50                  // 👈 修改Z轴位移
    }}
    transition={{
        duration: 0.3,         // 👈 修改动画时长
        ease: "easeOut"        // 👈 修改缓动函数
    }}
>
```

**验证3D组件**:
```bash
# 检查framer-motion是否正常工作
docker exec MirageMakers-frontend node -e "
try {
    const { motion } = require('framer-motion');
    console.log('✅ framer-motion loaded successfully');
} catch (e) {
    console.error('❌ framer-motion failed:', e.message);
}
"
```

### 3. 🧭 导航栏和按钮修改

**配置文件**: `frontend/components/ui/navigation-config.jsx`

```jsx
// 添加新导航项
rightNavItems: (language, router, isAuthenticated) => [
    {
        id: 'upgrade',                     // 👈 统一ID
        href: '/upgrade',                  // 👈 链接地址
        label: 'Upgrade',                  // 👈 统一显示文字
        active: router?.pathname === '/upgrade',
        style: 'upgrade-button'            // 👈 统一样式类
    }
]
```

### 4. 📱 响应式布局修复

**解决重叠问题**:
```jsx
// 在Layout.jsx中添加正确的z-index层级
<nav className="fixed top-0 left-0 right-0 z-50">           {/* 导航栏最高 */}
<main className="pt-16 relative z-10">                      {/* 主内容 */}
<div className="dropdown-menu absolute right-0 top-full z-40"> {/* 下拉菜单 */}
```

## 🔧 Docker环境管理

### 1. 📦 依赖管理

```bash
# 添加新依赖 (在容器中)
docker exec MirageMakers-frontend npm install new-package

# 查看已安装依赖
docker exec MirageMakers-frontend npm ls

# 查看容器内package.json
docker exec MirageMakers-frontend cat package.json
```

### 2. 🐛 调试和日志

```bash
# 查看前端容器日志
docker-compose logs -f frontend

# 进入前端容器进行调试
docker exec -it MirageMakers-frontend /bin/bash

# 在容器内运行Next.js开发模式
docker exec MirageMakers-frontend npm run dev
```

### 3. 🔄 容器重建

```bash
# 重建前端容器 (解决依赖问题)
docker-compose build --no-cache frontend

# 重启特定服务
docker-compose restart frontend

# 完全重新部署
./docker/start.sh --local --rebuild
```

## 🚨 常见问题 & Docker解决方案

### 1. 🐛 Framer Motion相关错误

**问题**: Gallery页面报错 "Module not found: Can't resolve 'framer-motion'"

```bash
# ✅ 解决方案：在Docker容器中修复
./docker/start.sh --local --fix

# 或手动修复
docker exec MirageMakers-frontend npm install framer-motion
docker-compose restart frontend
```

### 2. 🔄 热重载不工作

**问题**: 代码修改后页面不自动刷新

```bash
# ✅ 解决方案：启用热重载监控
./docker/hot_reload.sh

# 检查文件监控权限
ls -la frontend/
```

### 3. 📦 依赖版本冲突

**问题**: npm install报错或依赖冲突

```bash
# ✅ 解决方案：清理并重新安装
docker exec MirageMakers-frontend rm -rf node_modules package-lock.json
docker exec MirageMakers-frontend npm install
docker-compose restart frontend
```

### 4. 🔒 权限问题

**问题**: 容器内文件权限错误

```bash
# ✅ 解决方案：修复容器权限
docker exec MirageMakers-frontend chown -R node:node /app
```

## 📊 性能监控 (Docker环境)

### 1. 🔍 容器资源监控

```bash
# 查看容器资源使用情况
docker stats MirageMakers-frontend

# 查看容器详细信息
docker inspect MirageMakers-frontend
```

### 2. 📈 构建分析

```bash
# 在容器中进行Bundle分析
docker exec MirageMakers-frontend npm run build:analyze

# 查看构建产物大小
docker exec MirageMakers-frontend ls -la .next/static/
```

## 🎯 最佳实践

### ✅ Docker环境开发建议

1. **依赖管理**: 始终在Docker容器中管理依赖，不要在物理机安装npm包
2. **热重载**: 使用`./docker/hot_reload.sh`进行自动化开发
3. **调试**: 使用`docker exec`进入容器进行调试，而不是本地调试
4. **构建**: 让Docker处理所有构建过程，确保环境一致性
5. **部署**: 使用`./docker/deploy-with-docker.sh`进行完整部署

### ✅ 代码修改流程

```mermaid
graph LR
    A[修改代码] --> B[保存文件]
    B --> C[Docker检测变化]
    C --> D[容器内重新构建]
    D --> E[浏览器自动刷新]
    E --> F[验证效果]
```

### ✅ 文件结构建议

- **组件**: 放在`components/ui/`下，使用清晰的命名
- **样式**: 使用Tailwind CSS类，避免自定义CSS文件
- **配置**: 集中在`*-config.jsx`文件中管理
- **类型**: 使用TypeScript类型注释提高代码质量

## 🆘 获取帮助

### 📚 Docker相关文档
- [Docker Compose 官方文档](https://docs.docker.com/compose/)
- [Next.js Docker部署指南](https://nextjs.org/docs/deployment#docker-image)

### 🛠️ 调试命令快查

```bash
# 常用Docker调试命令
docker ps                              # 查看运行中的容器
docker-compose logs frontend           # 查看前端日志
docker exec -it MirageMakers-frontend bash  # 进入前端容器
docker exec MirageMakers-frontend npm ls    # 查看容器内依赖

# 重启和重建命令
docker-compose restart frontend        # 重启前端服务
docker-compose build --no-cache frontend  # 重建前端镜像
./docker/start.sh --local --rebuild    # 完全重新部署
```

---

💡 **重要提醒**: 本项目采用完全Docker化开发模式，所有npm命令都应该在Docker容器内执行。物理机上的node_modules和package-lock.json文件请忽略，以Docker容器内的环境为准。 