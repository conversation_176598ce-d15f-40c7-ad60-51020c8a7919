import { useState, useRef, useCallback, useEffect } from 'react';

export const useAtMention = (inputRefForAtMention, setCurrentPrompt) => {
    // contentEditable和input兼容性工具函数
    const getInputValue = (input) => {
        return input.value !== undefined ? input.value : (input.textContent || '');
    };

    const setInputValue = (input, value) => {
        if (input.value !== undefined) {
            // 传统input元素
            input.value = value;
        } else {
            // contentEditable元素
            input.textContent = value;
        }
    };

    const getCursorPosition = (input) => {
        if (input.value !== undefined) {
            return input.selectionStart || input.value.length;
        } else {
            // 对于contentEditable，返回文本长度作为默认位置
            return (input.textContent || '').length;
        }
    };

    const [isReferenceDropdownOpen, setIsReferenceDropdownOpen] = useState(false);
    const [referenceSearchQuery, setReferenceSearchQuery] = useState('');
    const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
    const [selectedReferences, setSelectedReferences] = useState([]);
    const [visualReferences, setVisualReferences] = useState([]);

    // 使用 ref 来存储 mention 状态，避免异步问题
    const mentionStateRef = useRef({
        startPosition: null,
        isActive: false,
        query: ''
    });

    // Parse @mentions from text
    const parseReferences = useCallback((text) => {
        // 支持四种格式：
        // 1. 完整格式：@image_123, @video_456, @latest_image 等
        // 2. 简化@格式：@1, @2, @3 等
        // 3. 方括号格式：[1], [2], [3] 等
        // 4. Media格式：Media_1, Media_2, Media_3 等
        const mentionRegex = /@(image_\d+|video_\d+|latest_image|latest_video|msg_[\w-]+|\d+)|\[(\d+)\]|Media_(\d+)/g;
        const mentions = [];
        let match;

        while ((match = mentionRegex.exec(text)) !== null) {
            mentions.push({
                full: match[0],
                reference_id: match[1] || match[2] || match[3], // match[1] for @format, match[2] for [format], match[3] for Media_format
                start: match.index,
                end: match.index + match[0].length
            });
        }

        return mentions;
    }, []);

    // Calculate dropdown position based on cursor
    const calculateDropdownPosition = useCallback((input, cursorPosition) => {
        // 更严格的输入验证
        if (!input) {
            console.warn('❌ calculateDropdownPosition: input is null or undefined');
            return { top: 100, left: 100 };
        }
        
        if (typeof input !== 'object' || !input.getBoundingClientRect) {
            console.warn('❌ calculateDropdownPosition: invalid input element', input, 'type:', typeof input);
            // 使用inputRefForAtMention作为备选
            const fallbackInput = inputRefForAtMention?.current;
            if (fallbackInput && fallbackInput.getBoundingClientRect) {
                console.log('🔧 Using fallback input element');
                input = fallbackInput;
            } else {
                return { top: 100, left: 100 };
            }
        }

        const inputRect = input.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const dropdownWidth = 384;
        const dropdownHeight = 384;

        // 尝试获取更精确的光标位置
        let cursorX = inputRect.left + 20; // 默认偏移
        let cursorY = inputRect.top;

        // 对于contentEditable元素，尝试获取selection位置
        try {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                const rect = range.getBoundingClientRect();
                if (rect.width > 0 || rect.height > 0) {
                    cursorX = rect.left;
                    cursorY = rect.top;
                }
            }
        } catch (e) {
            console.warn('Failed to get cursor position:', e);
        }

        // 水平位置：优先左对齐输入框，必要时调整
        let left = inputRect.left;
        
        // 如果右侧空间不足，右对齐
        if (left + dropdownWidth > viewportWidth - 16) {
            left = Math.max(16, inputRect.right - dropdownWidth);
        }
        
        // 确保不超出左边界
        if (left < 16) {
            left = 16;
        }

        // 优先显示在输入框上方（适合FloatingMessageBar）
        let top = inputRect.top - dropdownHeight - 16;
        
        // 如果上方空间不足，显示在下方
        if (top < 16) {
            top = inputRect.bottom + 8;
            // 确保下方也有足够空间
            if (top + dropdownHeight > viewportHeight - 16) {
                top = Math.max(16, viewportHeight - dropdownHeight - 16);
            }
        }

        console.log('📍 [DEBUG] Position calculated:', {
            top,
            left,
            inputRect: { top: inputRect.top, bottom: inputRect.bottom, left: inputRect.left, right: inputRect.right },
            dropdownSize: { width: dropdownWidth, height: dropdownHeight },
            viewport: { width: viewportWidth, height: viewportHeight }
        });

        return { top, left };
    }, []);

    // 强制清理输入框中的 @ 符号
    const forceCleanAtSymbol = useCallback(() => {
        if (!inputRefForAtMention?.current) return false;

        const input = inputRefForAtMention.current;
        const currentValue = getInputValue(input);
        const cursorPosition = getCursorPosition(input);

        // 查找并清除所有未完成的 @ mention（更精确的匹配）
        let cleanedValue = currentValue;

        // 清除末尾的不完整@mention
        cleanedValue = cleanedValue.replace(/@[^@\s]*$/g, '');

        // 清除光标位置附近的不完整@mention
        const textBeforeCursor = currentValue.substring(0, cursorPosition);
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');

        if (lastAtIndex !== -1) {
            const queryAfterAt = textBeforeCursor.substring(lastAtIndex + 1);
            // 如果@后面的内容不是完整的引用ID，则清除
            if (queryAfterAt && !queryAfterAt.match(/^\d+$/) && !queryAfterAt.includes(' ')) {
                const beforeAt = currentValue.substring(0, lastAtIndex);
                const afterCursor = currentValue.substring(cursorPosition);
                cleanedValue = beforeAt + afterCursor;
            }
        }

        if (cleanedValue !== currentValue) {
            setInputValue(input, cleanedValue);

            // 设置光标位置
            const newCursorPos = Math.min(cursorPosition, cleanedValue.length);
            if (input.value !== undefined) {
                // 传统input元素
            input.setSelectionRange(newCursorPos, newCursorPos);
            } else {
                // contentEditable元素 - 设置光标位置
                const selection = window.getSelection();
                const range = document.createRange();
                if (input.childNodes.length > 0) {
                    const textNode = input.childNodes[0];
                    if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                        range.setStart(textNode, Math.min(newCursorPos, textNode.textContent.length));
                        range.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            }

            // 触发 React 事件
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);


            return true;
        }

        return false;
    }, [inputRefForAtMention]);

    // Handle input change with @mention detection - 增强版
    const handleInputChange = useCallback((e, originalHandler) => {
        // 优先使用inputRefForAtMention.current作为真正的DOM元素
        const input = inputRefForAtMention?.current || e.target;
        const value = getInputValue(input);
        const cursorPosition = getCursorPosition(input);

        // 首先调用原始处理器
        if (originalHandler) {
            originalHandler(e);
        }

        // 智能@检测系统 - 增强版，避免在token内部触发
        const textBeforeCursor = value.substring(0, cursorPosition);
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');

        if (lastAtIndex !== -1) {
            // 检查是否在已存在的token内部
            const isInToken = (() => {
                // 首先确保input是DOM元素
                if (input && typeof input === 'object' && input.nodeType === Node.ELEMENT_NODE &&
                    (input.contentEditable === 'true' || input.hasAttribute('contenteditable'))) {
                    // 对于contentEditable，检查光标是否在token元素内部
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        let node = range.startContainer;
                        
                        // 向上遍历DOM树，检查是否在token内部
                        while (node && node !== input) {
                            if (node.nodeType === Node.ELEMENT_NODE && 
                                (node.classList.contains('ai-token') || node.closest('.ai-token'))) {
                                return true;
                            }
                            node = node.parentNode;
                        }
                        
                        // 检查紧邻的兄弟节点是否是token
                        if (range.startContainer.nodeType === Node.TEXT_NODE) {
                            const textNode = range.startContainer;
                            const prevSibling = textNode.previousSibling;
                            if (prevSibling && prevSibling.classList && prevSibling.classList.contains('ai-token')) {
                                // 如果光标紧邻token且@符号很近，可能是误触发
                                const distanceFromToken = range.startOffset;
                                if (distanceFromToken <= 2) {
                                    return true;
                                }
                            }
                        }
                    }
                }
                return false;
            })();

            // 如果在token内部，不触发@mention
            if (isInToken) {
                console.log('🚫 [DEBUG] @mention detection skipped: cursor in token');
                if (mentionStateRef.current.isActive) {
                    mentionStateRef.current = {
                        startPosition: null,
                        isActive: false,
                        query: ''
                    };
                    setIsReferenceDropdownOpen(false);
                    setReferenceSearchQuery('');
                }
                return;
            }

            // 更宽松的@检测 - 允许在中文、英文、标点符号后使用@
            const charBeforeAt = lastAtIndex > 0 ? textBeforeCursor[lastAtIndex - 1] : ' ';

            // 允许的前置字符：空格、开头、中文字符、标点符号
            const allowedPrecedingChars = /[\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef，。！？、；：""''（）【】]/;
            const isValidPosition = lastAtIndex === 0 || allowedPrecedingChars.test(charBeforeAt);

            if (isValidPosition) {
                const queryAfterAt = textBeforeCursor.substring(lastAtIndex + 1);

                // 智能引用检测：排除已完成的引用
                const isCompleteReference = /^(image|video)_\d+/.test(queryAfterAt);
                const hasSpaceInQuery = queryAfterAt.includes(' ');
                
                // 检查是否是已完成引用后的内容（如 @image_2@ 或 @image_2后面跟其他字符）
                const isAfterCompleteReference = /^(image|video)_\d+./.test(queryAfterAt);

                // 显示下拉框的条件：
                // 1. 查询中没有空格
                // 2. 不是完整的引用格式
                // 3. 不是在完整引用后继续输入
                // 4. 查询长度不超过20个字符（避免误触发）
                if (!hasSpaceInQuery && !isCompleteReference && !isAfterCompleteReference && queryAfterAt.length <= 20) {
                    mentionStateRef.current = {
                        startPosition: lastAtIndex,
                        isActive: true,
                        query: queryAfterAt
                    };

                    setReferenceSearchQuery(queryAfterAt);
                    console.log('🔧 [DEBUG] About to calculate position, input:', input, 'type:', typeof input, 'isElement:', input instanceof Element);
                    const position = calculateDropdownPosition(input, cursorPosition);
                    setDropdownPosition(position);
                    setIsReferenceDropdownOpen(true);

                    console.log('🔍 [Debug] @检测激活:', {
                        query: queryAfterAt,
                        startPosition: lastAtIndex,
                        charBefore: `"${charBeforeAt}"`
                    });

                    return;
                }
            }
        }

        // 关闭下拉框如果没有有效的 @ mention 上下文
        if (mentionStateRef.current.isActive) {
            mentionStateRef.current = {
                startPosition: null,
                isActive: false,
                query: ''
            };
            setIsReferenceDropdownOpen(false);
            setReferenceSearchQuery('');
            console.log('🔍 [Debug] @检测关闭');
        }

    }, [inputRefForAtMention]);

    // Close dropdown
    const closeReferenceDropdown = useCallback(() => {
        setIsReferenceDropdownOpen(false);
        setReferenceSearchQuery('');
        mentionStateRef.current = {
            startPosition: null,
            isActive: false,
            query: ''
        };

    }, []);

    // 批量插入引用到输入框
    const insertBatchReferences = useCallback((references) => {
        if (!inputRefForAtMention?.current || !references.length) return;

        const input = inputRefForAtMention.current;
        const currentValue = getInputValue(input);
        let newValue = currentValue;
        let cleaned = false;

        // 对于contentEditable，需要特殊处理
        if (input && typeof input === 'object' && input.nodeType === Node.ELEMENT_NODE &&
            (input.contentEditable === 'true' || input.hasAttribute('contenteditable'))) {
            // 查找并清理所有@mention文本节点
            const walker = document.createTreeWalker(
                input,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let textNode;
            const nodesToProcess = [];
            while (textNode = walker.nextNode()) {
                const text = textNode.textContent;
                const lastAtIndex = text.lastIndexOf('@');
                if (lastAtIndex !== -1) {
                    const textAfterAt = text.substring(lastAtIndex + 1);
                    // 如果找到了@mention模式，标记处理
                    if (/^[a-zA-Z0-9_]*$/.test(textAfterAt)) {
                        nodesToProcess.push({
                            node: textNode,
                            beforeAt: text.substring(0, lastAtIndex).trimEnd(),
                            afterAt: '' // 清空@后的内容
                        });
                    }
                }
            }

            // 清理所有找到的@mention文本
            nodesToProcess.forEach(({ node, beforeAt }) => {
                if (beforeAt) {
                    node.textContent = beforeAt + ' '; // 保留前面的文本，添加空格
                } else {
                    node.textContent = ''; // 如果没有前文，直接清空
                }
            });

            cleaned = true;
        } else {
            // 传统input的清理逻辑
        if (mentionStateRef.current.isActive && mentionStateRef.current.startPosition !== null) {
            const beforeMention = currentValue.substring(0, mentionStateRef.current.startPosition);
                const cursorPos = getCursorPosition(input);
                const afterCursor = currentValue.substring(cursorPos);
            newValue = beforeMention + afterCursor;
            cleaned = true;

            // 重置状态
            mentionStateRef.current = {
                startPosition: null,
                isActive: false,
                query: ''
            };
        } else {
                // 更强力的清理逻辑 - 查找最后一个@符号及其后的内容
                const lastAtIndex = currentValue.lastIndexOf('@');
                if (lastAtIndex !== -1) {
                    // 检查@符号后的内容是否看起来像是未完成的引用
                    const textAfterAt = currentValue.substring(lastAtIndex + 1);
                    const beforeAt = currentValue.substring(0, lastAtIndex);

                    // 如果@后面的文本包含字母、数字、下划线，认为是未完成的引用
                    if (/^[a-zA-Z0-9_]*$/.test(textAfterAt)) {
                        // 清理整个@mention文本
                        newValue = beforeAt.trimEnd(); // 移除@及其后的内容，并清理末尾空格
                cleaned = true;
                    }
                }
            }
        }

        // 检查是否是contentEditable元素（FloatingMessageBar的情况）
        if (input && typeof input === 'object' && input.nodeType === Node.ELEMENT_NODE &&
            (input.contentEditable === 'true' || input.hasAttribute('contenteditable'))) {
            // 为所有引用创建chip元素
            const chipsToInsert = references.map((reference) => {
                // 创建Token组件 - 采用现代AI工具风格
                const token = document.createElement('span');
                token.className = 'ai-token';
                token.contentEditable = 'false';
                token.dataset.type = reference.type || 'image';
                token.dataset.referenceId = reference.reference_id;
                token.dataset.instanceId = reference.instance_id;

                // Token样式 - 参考Notion/Cursor风格
                token.style.cssText = `
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;
                    padding: 4px 10px;
                    margin: 0 2px;
                    background: rgba(255, 255, 255, 0.12);
                    border: 1px solid rgba(255, 255, 255, 0.25);
                    border-radius: 12px;
                    font-size: 13px;
                    font-weight: 500;
                    color: rgba(255, 255, 255, 0.95);
                    vertical-align: middle;
                    cursor: pointer;
                    user-select: none;
                    white-space: nowrap;
                    max-width: 180px;
                    backdrop-filter: blur(10px);
                    transition: all 0.2s ease;
                `;

                // 悬停效果
                token.onmouseenter = () => {
                    token.style.background = 'rgba(255, 255, 255, 0.2)';
                    token.style.transform = 'translateY(-1px)';
                    token.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                };
                token.onmouseleave = () => {
                    token.style.background = 'rgba(255, 255, 255, 0.12)';
                    token.style.transform = 'translateY(0)';
                    token.style.boxShadow = 'none';
                };

                // 类型图标
                const icon = document.createElement('span');
                icon.style.cssText = 'font-size: 14px; line-height: 1;';

                if (reference.type === 'image') {
                    // 使用缩略图
                    const thumbnail = document.createElement('img');
                    thumbnail.src = reference.url;
                    thumbnail.alt = '';
                    thumbnail.style.cssText = `
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        object-fit: cover;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    `;
                    thumbnail.onerror = () => {
                        thumbnail.style.display = 'none';
                        icon.textContent = '🖼️';
                        token.insertBefore(icon, token.firstChild);
                    };
                    icon.appendChild(thumbnail);
                } else if (reference.type === 'video') {
                    icon.textContent = '🎥';
                } else {
                    icon.textContent = '📎';
                }

                // 标签文本
                const label = document.createElement('span');
                label.textContent = `@${reference.reference_id || reference.display_name}`;
                label.style.cssText = 'font-weight: 500; letter-spacing: 0.01em;';

                // 删除按钮 - 现代化设计
                const removeBtn = document.createElement('button');
                removeBtn.innerHTML = '×';
                removeBtn.style.cssText = `
                    margin-left: 4px;
                    width: 18px;
                    height: 18px;
                    background: rgba(0, 0, 0, 0.3);
                    color: rgba(255, 255, 255, 0.8);
                    border: 1px solid rgba(255, 255, 255, 0.15);
                    border-radius: 50%;
                    font-size: 11px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0.7;
                    backdrop-filter: blur(4px);
                    transition: all 0.15s ease;
                `;

                removeBtn.onmouseenter = () => {
                    removeBtn.style.opacity = '1';
                    removeBtn.style.background = 'rgba(239, 68, 68, 0.8)';
                    removeBtn.style.transform = 'scale(1.1)';
                };
                removeBtn.onmouseleave = () => {
                    removeBtn.style.opacity = '0.7';
                    removeBtn.style.background = 'rgba(0, 0, 0, 0.3)';
                    removeBtn.style.transform = 'scale(1)';
                };

                removeBtn.onclick = (e) => {
                    e.stopPropagation();
                    token.remove();
                    // 触发更新
                    const event = new Event('input', { bubbles: true });
                    input.dispatchEvent(event);
                };

                // 点击预览功能
                token.onclick = (e) => {
                    if (e.target === removeBtn) return;
                    // 触发预览事件
                    const previewEvent = new CustomEvent('tokenPreview', {
                        detail: { reference, element: token }
                    });
                    input.dispatchEvent(previewEvent);
                };

                // 组装Token
                token.appendChild(icon);
                token.appendChild(label);
                token.appendChild(removeBtn);

                return token;
            });

            // 一次性插入所有Token
            let lastInsertedElement = null;
            chipsToInsert.forEach((token, index) => {
                // 智能空格管理：检查前面是否需要空格
                if (index === 0) {
                    const lastChild = input.lastChild;
                    const needsSpaceBefore = !lastChild ||
                        (lastChild.nodeType === Node.TEXT_NODE && !lastChild.textContent.endsWith(' ')) ||
                        (lastChild.nodeType === Node.ELEMENT_NODE);

                    if (needsSpaceBefore) {
                        input.appendChild(document.createTextNode(' '));
                    }
                }

                // 插入token
                input.appendChild(token);

                // 每个Token后都添加空格，方便连续引用
                const spaceAfter = document.createTextNode(' ');
                input.appendChild(spaceAfter);
                lastInsertedElement = spaceAfter; // 记录最后的空格节点
            });

            // 设置光标到最后一个空格之后，确保能立即输入或引用
            if (lastInsertedElement) {
                const selection = window.getSelection();
                const range = document.createRange();
                range.setStartAfter(lastInsertedElement);
                range.collapse(true);
                selection.removeAllRanges();
                selection.addRange(range);

                console.log('🔍 [Debug] 光标已设置到最后一个Token后的空格之后');
            }

            // 触发更新事件
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);
        } else {
            // 传统input元素 - 使用Media_N格式
        const referenceTexts = references.map((ref, index) => `Media_${index + 1}`).join(' ');
        let finalValue = newValue;
        if (finalValue && !finalValue.endsWith(' ')) finalValue += ' ';
        finalValue += referenceTexts;
        finalValue = finalValue.trim();

        // 使用setCurrentPrompt更新React状态，而不是直接操作DOM
        if (setCurrentPrompt) {
            setCurrentPrompt(finalValue);
        } else {
                setInputValue(input, finalValue);

                // 设置光标位置
                if (input.value !== undefined) {
            input.setSelectionRange(finalValue.length, finalValue.length);
                }

            // 触发事件
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);
        }
        }

        // 更新选择状态 - 插入后保持视觉引用但清空选择状态，避免下次打开时显示为已选中
        console.log('🔧 [DEBUG] Setting visualReferences:', references);
        setSelectedReferences([]); // 清空选择状态，避免"全选"问题

        // 防重复的智能累加逻辑
        setVisualReferences(prev => {
            const existingIds = new Set(prev.map(ref => ref.instance_id));
            const newReferences = references.filter(ref => !existingIds.has(ref.instance_id));

            if (newReferences.length > 0) {
                console.log('🔧 [DEBUG] Adding new references:', newReferences);
                return [...prev, ...newReferences];
            } else {
                console.log('🔧 [DEBUG] No new references to add, all already exist');
                return prev;
            }
        });

        // 验证设置后的状态
        setTimeout(() => {
            console.log('🔧 [DEBUG] After setState, checking visualReferences...');
        }, 100);

        // 直接关闭下拉框，不依赖其他函数
        setIsReferenceDropdownOpen(false);
        setReferenceSearchQuery('');

        // 重置mention状态
        mentionStateRef.current = {
            startPosition: null,
            isActive: false,
            query: ''
        };

    }, [inputRefForAtMention, setCurrentPrompt, setSelectedReferences, setVisualReferences, setIsReferenceDropdownOpen, setReferenceSearchQuery]);

    // Handle reference selection - 支持多选和批量插入模式
    const handleReferenceSelect = useCallback((reference, isSelected = true) => {
        // 如果传入的是引用数组，则进行批量插入
        if (Array.isArray(reference)) {
            insertBatchReferences(reference);
            return;
        }

        if (!reference) return;

        if (!inputRefForAtMention?.current) {
            console.error('❌ No input ref available for @ mention');
            return;
        }

        const input = inputRefForAtMention.current;

        try {
            if (isSelected) {
                // 选择引用 - 批量插入模式

                // 添加引用到选中列表
                setSelectedReferences(prev => {
                    const filtered = prev.filter(ref => ref.message_id !== reference.message_id);
                    return [...filtered, reference];
                });

                // 添加到视觉引用
                setVisualReferences(prev => {
                    const filtered = prev.filter(ref => ref.message_id !== reference.message_id);
                    return [...filtered, reference];
                });

                // 如果用户正在@输入过程中，直接插入引用文本
                if (mentionStateRef.current.isActive && mentionStateRef.current.startPosition !== null) {
                    const referenceText = `@${reference.reference_id}`;
                    const currentValue = input.value;
                    const beforeMention = currentValue.substring(0, mentionStateRef.current.startPosition);
                    const afterCursor = currentValue.substring(input.selectionStart);

                    const newValue = beforeMention + referenceText + ' ' + afterCursor;
                    input.value = newValue;

                    const cursorPos = beforeMention.length + referenceText.length + 1;
                    input.setSelectionRange(cursorPos, cursorPos);

                    // 清理 @ mention 状态
                    mentionStateRef.current = {
                        startPosition: null,
                        isActive: false,
                        query: ''
                    };



                    // 触发 React input 事件
                    const event = new Event('input', { bubbles: true });
                    input.dispatchEvent(event);
                }

            } else {
                // 取消选择引用

                // 从选中列表移除
                setSelectedReferences(prev =>
                    prev.filter(ref => ref.message_id !== reference.message_id)
                );

                // 从视觉引用移除
                setVisualReferences(prev =>
                    prev.filter(ref => ref.message_id !== reference.message_id)
                );

                // 从输入框中移除引用文本
                const currentValue = input.value;
                const referencePattern = new RegExp(`@${reference.reference_id}\\s*`, 'g');
                const newValue = currentValue.replace(referencePattern, '');

                if (newValue !== currentValue) {
                    input.value = newValue;
                    input.setSelectionRange(newValue.length, newValue.length);

                    // 触发 React input 事件
                    const event = new Event('input', { bubbles: true });
                    input.dispatchEvent(event);
                }
            }



        } catch (error) {
            console.error('❌ Reference selection error:', error);
        }
    }, [inputRefForAtMention, selectedReferences, visualReferences, insertBatchReferences]);

    // Handle key down events for dropdown
    const handleKeyDown = useCallback((e, originalHandler) => {
        // 先调用原始处理器
        if (originalHandler) {
            originalHandler(e);
        }

        // 如果下拉框打开，处理导航
        if (isReferenceDropdownOpen) {
            switch (e.key) {
                case 'Escape':
                    e.preventDefault();
                    closeReferenceDropdown();
                    // 只清理当前未完成的@mention，不影响已插入的引用
                    setTimeout(() => {
                        if (!inputRefForAtMention?.current) return;

                        const input = inputRefForAtMention.current;
                        const currentValue = getInputValue(input);
                        const cursorPosition = getCursorPosition(input);
                        
                        // 只清理光标附近的未完成@mention
                        const textBeforeCursor = currentValue.substring(0, cursorPosition);
                        const lastAtIndex = textBeforeCursor.lastIndexOf('@');
                        
                        if (lastAtIndex !== -1) {
                            const queryAfterAt = textBeforeCursor.substring(lastAtIndex + 1);
                            // 只清理短的、未完成的@mention，保留完整的引用
                            if (queryAfterAt.length <= 20 && !queryAfterAt.includes(' ') && 
                                !/^(image|video)_\d+$/.test(queryAfterAt)) {
                                const beforeAt = currentValue.substring(0, lastAtIndex);
                                const afterCursor = currentValue.substring(cursorPosition);
                                const cleanedValue = beforeAt + afterCursor;
                                
                                setInputValue(input, cleanedValue);
                                
                                // 设置光标位置到@符号原位置
                                if (input.value !== undefined) {
                                    input.setSelectionRange(lastAtIndex, lastAtIndex);
                                } else {
                                    // contentEditable光标设置
                                    const selection = window.getSelection();
                                    const range = document.createRange();
                                    if (input.childNodes.length > 0) {
                                        const textNode = input.childNodes[0];
                                        if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                                            const pos = Math.min(lastAtIndex, textNode.textContent.length);
                                            range.setStart(textNode, pos);
                                            range.collapse(true);
                                            selection.removeAllRanges();
                                            selection.addRange(range);
                                        }
                                    }
                                }
                                
                                // 触发input事件
                                const event = new Event('input', { bubbles: true });
                                input.dispatchEvent(event);
                            }
                        }
                    }, 50);
                    break;
            }
        }
    }, [isReferenceDropdownOpen, closeReferenceDropdown, forceCleanAtSymbol]);

    // Process message for backend submission
    const processMessageForSubmission = useCallback((message, sessionMedia = []) => {
        console.log('🔍 [DEBUG] processMessageForSubmission called:', {
            messageLength: message?.length || 0,
            sessionMediaCount: sessionMedia?.length || 0,
            selectedReferencesCount: selectedReferences?.length || 0,
            messageContent: message?.substring(0, 50) + '...'
        });

        const references = parseReferences(message);
        console.log('🔍 [DEBUG] Parsed references:', references);

        // 对于Media_N格式，需要根据索引匹配selectedReferences
        const messageSelectedReferences = [];
        const referencedMessageIds = [];

        references.forEach(ref => {
            const refId = ref.reference_id;
            console.log('🔍 [DEBUG] Processing reference:', { refId, fullMatch: ref.full });

            // 如果是数字格式（来自Media_N），则根据索引获取对应的引用
            if (/^\d+$/.test(refId)) {
                const index = parseInt(refId) - 1; // Media_1对应索引0
                console.log('🔍 [DEBUG] Numeric reference detected:', { refId, index, selectedReferencesLength: selectedReferences.length });
                if (index >= 0 && index < selectedReferences.length) {
                    const selectedRef = selectedReferences[index];
                    messageSelectedReferences.push(selectedRef);
                    referencedMessageIds.push(selectedRef.message_id);
                    console.log('✅ [DEBUG] Numeric reference matched from selectedReferences:', {
                        index,
                        refId: selectedRef.reference_id,
                        hasUrl: !!selectedRef.url
                    });
                } else {
                    console.warn('⚠️ [DEBUG] Numeric reference index out of bounds:', {
                        index,
                        selectedReferencesLength: selectedReferences.length
                    });
                }
            } else {
                // 优先从selectedReferences中匹配
                let matchedRef = selectedReferences.find(r => r.reference_id === refId);
                console.log('🔍 [DEBUG] Looking for named reference in selectedReferences:', {
                    refId,
                    foundInSelected: !!matchedRef,
                    selectedReferencesIds: selectedReferences.map(r => r.reference_id)
                });
                
                // 如果在selectedReferences中没找到，尝试在sessionMedia中查找
                if (!matchedRef && sessionMedia.length > 0) {
                    matchedRef = sessionMedia.find(media => media.reference_id === refId);
                    console.log('🔍 [DEBUG] Looking for reference in sessionMedia:', {
                        refId,
                        foundInSessionMedia: !!matchedRef,
                        sessionMediaCount: sessionMedia.length,
                        sessionMediaIds: sessionMedia.map(m => m.reference_id).slice(0, 5)
                    });
                }
                
                if (matchedRef) {
                    messageSelectedReferences.push(matchedRef);
                    referencedMessageIds.push(matchedRef.message_id || matchedRef.id);
                    console.log('✅ [DEBUG] Named reference matched:', {
                        refId,
                        url: matchedRef.url ? 'has_url' : 'no_url',
                        source: selectedReferences.includes(matchedRef) ? 'selectedReferences' : 'sessionMedia'
                    });
                } else {
                    console.warn('⚠️ [DEBUG] Reference not found:', {
                        refId,
                        selectedReferencesCount: selectedReferences.length,
                        selectedReferencesIds: selectedReferences.map(r => r.reference_id),
                        sessionMediaCount: sessionMedia.length,
                        sessionMediaIds: sessionMedia.map(m => m.reference_id)
                    });
                }
            }
        });

        const result = {
            message: message,
            references: references,
            referencedMessageIds: referencedMessageIds,
            selectedReferences: messageSelectedReferences
        };

        console.log('🔍 [DEBUG] processMessageForSubmission result:', {
            originalReferencesCount: references.length,
            matchedReferencesCount: messageSelectedReferences.length,
            referencedMessageIds: referencedMessageIds
        });

        return result;
    }, [selectedReferences, parseReferences]); // sessionMedia作为参数传入，不需要在依赖中

    // Remove reference from visual display and input text
    const removeReference = useCallback((reference, inputRef) => {

        // 从视觉引用中移除
        setVisualReferences(prev =>
            prev.filter(ref => ref.reference_id !== reference.reference_id)
        );

        // 从选中引用中移除
        setSelectedReferences(prev =>
            prev.filter(ref => ref.reference_id !== reference.reference_id)
        );

        // 从输入文本中移除
        if (inputRef?.current) {
            const input = inputRef.current;
            const currentValue = getInputValue(input);
            const referencePattern = new RegExp(`@${reference.reference_id}\\s*`, 'g');
            const newValue = currentValue.replace(referencePattern, '');

            if (newValue !== currentValue) {
                setInputValue(input, newValue);
                const inputEvent = new Event('input', { bubbles: true });
                input.dispatchEvent(inputEvent);
                
                // 对于contentEditable，需要重新设置焦点和光标位置
                if (input.contentEditable === 'true') {
                    input.focus();
                    // 将光标移动到末尾
                    const range = document.createRange();
                    const selection = window.getSelection();
                    if (input.childNodes.length > 0) {
                        const lastNode = input.childNodes[input.childNodes.length - 1];
                        if (lastNode.nodeType === Node.TEXT_NODE) {
                            range.setStart(lastNode, lastNode.textContent.length);
                        } else {
                            range.setStartAfter(lastNode);
                        }
                    } else {
                        range.setStart(input, 0);
                    }
                    range.collapse(true);
                    selection.removeAllRanges();
                    selection.addRange(range);
                } else {
                    input.focus();
                }
            }
        }
        
        // 重置@mention状态，确保功能不受影响
        mentionStateRef.current = {
            startPosition: null,
            isActive: false,
            query: ''
        };
        setIsReferenceDropdownOpen(false);
        setReferenceSearchQuery('');
        
        console.log('✅ [DEBUG] Reference removed and @mention state reset:', {
            removedRef: reference.reference_id,
            remainingVisualRefs: visualReferences.filter(ref => ref.reference_id !== reference.reference_id).length,
            remainingSelectedRefs: selectedReferences.filter(ref => ref.reference_id !== reference.reference_id).length
        });
    }, [visualReferences, selectedReferences]);

    // Clear all references
    const clearReferences = useCallback(() => {
        setSelectedReferences([]);
        setVisualReferences([]);

        // 直接清除@符号相关状态
        setIsReferenceDropdownOpen(false);
        setReferenceSearchQuery('');

        // 重置mention状态
        mentionStateRef.current = {
            startPosition: null,
            isActive: false,
            query: ''
        };

        // 强制清理输入框中的 @ 符号
        setTimeout(() => {
            forceCleanAtSymbol();
        }, 50);
    }, [forceCleanAtSymbol]);

    // Reset all state
    const reset = useCallback(() => {
        setIsReferenceDropdownOpen(false);
        setReferenceSearchQuery('');
        setSelectedReferences([]);
        setVisualReferences([]);
        mentionStateRef.current = {
            startPosition: null,
            isActive: false,
            query: ''
        };

        // 强制清理
        forceCleanAtSymbol();
    }, [forceCleanAtSymbol]);

    return {
        // State
        isReferenceDropdownOpen,
        referenceSearchQuery,
        dropdownPosition,
        selectedReferences,
        visualReferences,

        // Handlers
        handleInputChange,
        handleKeyDown,
        handleReferenceSelect,
        closeReferenceDropdown,
        removeReference,

        // Utilities
        parseReferences,
        processMessageForSubmission,
        clearReferences,
        reset,
        forceCleanAtSymbol,
        insertBatchReferences
    };
}; 