/**
 * 多媒体引用辅助工具
 * 集成新的Agent API和引用服务
 */

import axios from 'axios';

// 引用解析测试工具
export const testReferenceResolution = async (sessionId, content, token) => {
    try {
        const response = await axios.post('/api/chat/test-reference-resolution/', {
            session_id: sessionId,
            content: content
        }, {
            headers: {
                'Authorization': token ? `Token ${token}` : '',
                'Content-Type': 'application/json',
            },
            withCredentials: true,
            timeout: 10000
        });

        if (response.data.success) {
            return {
                success: true,
                data: {
                    originalContent: response.data.original_content,
                    processedContent: response.data.processed_content,
                    referencedMessages: response.data.referenced_messages,
                    referenceContext: response.data.reference_context,
                    referenceCount: response.data.reference_count
                }
            };
        } else {
            return {
                success: false,
                error: response.data.error || 'Reference resolution failed'
            };
        }
    } catch (error) {
        console.error('Reference resolution test failed:', error);
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Network error'
        };
    }
};

// 智能模式检测
export const detectOptimalMode = (references) => {
    const images = references.filter(ref => ref.type === 'image');
    const videos = references.filter(ref => ref.type === 'video');

    if (images.length > 1) {
        return 'multi_image_compose';
    } else if (images.length === 1 && videos.length === 0) {
        return 'image_edit';
    } else if (videos.length > 0) {
        return 'video_edit';
    } else {
        return 'image_gen';
    }
};

// 引用格式化
export const formatReferences = (references) => {
    return references.map((ref, index) => ({
        reference_id: ref.reference_id || `ref_${index + 1}`,
        type: ref.type,
        url: ref.url,
        display_name: ref.display_name || `${ref.type === 'image' ? 'Image' : 'Video'} ${index + 1}`,
        message_id: ref.message_id
    }));
};

// 引用验证
export const validateReferences = (references) => {
    const errors = [];

    references.forEach((ref, index) => {
        if (!ref.url) {
            errors.push(`Reference ${index + 1}: Missing URL`);
        }
        if (!ref.type || !['image', 'video'].includes(ref.type)) {
            errors.push(`Reference ${index + 1}: Invalid type`);
        }
        if (!ref.reference_id) {
            errors.push(`Reference ${index + 1}: Missing reference ID`);
        }
    });

    return {
        isValid: errors.length === 0,
        errors: errors
    };
};

// 引用统计
export const getReferenceStats = (references) => {
    const stats = {
        total: references.length,
        images: references.filter(ref => ref.type === 'image').length,
        videos: references.filter(ref => ref.type === 'video').length,
        types: {}
    };

    references.forEach(ref => {
        stats.types[ref.type] = (stats.types[ref.type] || 0) + 1;
    });

    return stats;
};

// 引用预览数据准备
export const prepareReferencePreview = (references) => {
    return references.map(ref => ({
        ...ref,
        preview_url: ref.url,
        is_valid: !!(ref.url && ref.type),
        metadata: {
            size: ref.size || 'unknown',
            format: ref.format || 'unknown',
            duration: ref.type === 'video' ? (ref.duration || 'unknown') : null
        }
    }));
};

export default {
    testReferenceResolution,
    detectOptimalMode,
    formatReferences,
    validateReferences,
    getReferenceStats,
    prepareReferencePreview
}; 