// Plan type mapping utility
const PLAN_MAPPING = {
    "TRIAL": "Member",
    "BASIC": "VIP",
    "PREMIUM": "SVIP",
    "ANNUAL": "SVIP",
    // Legacy compatibility
    "starter": "Member",
    "pro": "VIP",
    "premium": "SVIP",
    // Default fallback
    "Free": "Free"
};

export const mapPlanToDisplay = (planType) => {
    if (!planType) return "Free";

    // Remove "Plan" suffix if it exists
    const cleanPlanType = planType.replace(" Plan", "").trim();

    return PLAN_MAPPING[cleanPlanType] || "Free";
};

export const getDisplayPlan = (planType) => {
    const displayLevel = mapPlanToDisplay(planType);
    return displayLevel === "Free" ? "Free Plan" : `${displayLevel} Plan`;
};

export default { mapPlanToDisplay, getDisplayPlan }; 