// High-quality AI generation examples for the demo carousel
export const aiGenerationExamples = [
    {
        id: 1,
        title: "Cyberpunk City",
        description: "A futuristic neon-lit cityscape with flying cars",
        imageUrl: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Digital Art",
        prompt: "A futuristic cyberpunk city at night with neon lights"
    },
    {
        id: 2,
        title: "Abstract Portal",
        description: "Mystical dimensional gateway with flowing energy",
        imageUrl: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Abstract",
        prompt: "A magical portal with swirling energy and cosmic colors"
    },
    {
        id: 3,
        title: "Digital Landscape",
        description: "Surreal digital mountain landscape with aurora",
        imageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Landscape",
        prompt: "Digital mountain landscape with northern lights and crystals"
    },
    {
        id: 4,
        title: "AI Neural Network",
        description: "Visualization of artificial intelligence processing",
        imageUrl: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Technology",
        prompt: "AI neural network visualization with flowing data streams"
    },
    {
        id: 5,
        title: "Cosmic Garden",
        description: "Ethereal space garden with floating flowers",
        imageUrl: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Surreal",
        prompt: "A cosmic garden with floating flowers in zero gravity"
    },
    {
        id: 6,
        title: "Holographic Art",
        description: "3D holographic art installation with light beams",
        imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "3D Art",
        prompt: "Holographic art installation with laser light beams"
    },
    {
        id: 7,
        title: "Digital Ocean",
        description: "Fluid simulation of digital water waves",
        imageUrl: "https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Simulation",
        prompt: "Digital ocean waves with particle effects and foam"
    },
    {
        id: 8,
        title: "Neon Dreams",
        description: "Vibrant neon abstract composition",
        imageUrl: "https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=600&h=400&fit=crop&crop=center&auto=format&q=80",
        category: "Neon Art",
        prompt: "Vibrant neon abstract art with electric colors"
    }
];

// Categories for filtering
export const categories = [
    "All",
    "Digital Art",
    "Abstract",
    "Landscape",
    "Technology",
    "Surreal",
    "3D Art",
    "Simulation",
    "Neon Art"
];

// Get random selection of images for carousel
export const getRandomDemoImages = (count = 6) => {
    const shuffled = [...aiGenerationExamples].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count).map(item => item.imageUrl);
};

// Get images by category
export const getImagesByCategory = (category) => {
    if (category === "All") return aiGenerationExamples;
    return aiGenerationExamples.filter(item => item.category === category);
}; 