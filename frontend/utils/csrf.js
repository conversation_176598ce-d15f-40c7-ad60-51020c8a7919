// CSRF token utility functions

export const getCsrfToken = async () => {
    try {
        const response = await fetch('/api/auth/csrf-token/', {
            method: 'GET',
            credentials: 'include',
        });

        if (response.ok) {
            const data = await response.json();
            return data.csrfToken;
        }
    } catch (error) {
        console.error('Failed to get CSRF token:', error);
    }

    // Fallback: try to get from cookie
    if (typeof document !== 'undefined') {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
    }

    return null;
};

export const getCsrfTokenFromCookie = () => {
    if (typeof document === 'undefined') return null;

    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }
    return null;
}; 