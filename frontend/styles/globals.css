@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
    }
    50% {
        box-shadow: 0 0 30px rgba(168, 85, 247, 0.6);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 应用动画类 */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

.animate-slideUp {
    animation: slideUp 0.4s ease-out;
}

.animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
}

/* 改进滚动条样式 */
.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Rich text input placeholder support */
.rich-text-input:empty:before {
  content: attr(data-placeholder);
  color: rgba(156, 163, 175, 1); /* text-gray-400 */
  pointer-events: none;
  position: static;
  display: inline-block;
}

.rich-text-input:focus:empty:before {
  display: none;
}

/* Inline chip styles */
.inline-chip {
  display: inline-flex !important;
  align-items: center !important;
  user-select: none !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
}

.inline-chip img {
  pointer-events: none !important;
}

.inline-chip button {
  pointer-events: auto !important;
}

/* Prevent contentEditable from allowing inline chips to be edited */
.inline-chip[contenteditable="false"] {
  -webkit-user-modify: read-only !important;
  -moz-user-modify: read-only !important;
  user-modify: read-only !important;
}

/* Focus styles for rich text input */
.rich-text-input:focus {
  outline: none;
}

/* Ensure proper line height and spacing */
.rich-text-input {
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 侧边栏用户头像菜单定位 */
.sidebar-user-avatar > div > div:last-child {
    right: auto !important;
    left: 0 !important;
    transform: translateX(0) !important;
}

/* 确保菜单不会被遮挡 */
.sidebar-user-avatar {
    position: relative;
    z-index: 50;
    pointer-events: auto;
}

/* 确保按钮可点击 */
.sidebar-user-avatar button {
    pointer-events: auto !important;
    cursor: pointer !important;
}
