/* 聊天窗口媒体优化样式 */

/* 图片悬停效果 */
.chat-image {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

.chat-image:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 32px rgba(0, 210, 255, 0.15);
}

/* 图片容器 */
.chat-media-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.chat-media-container:hover .media-overlay {
  opacity: 1;
}

.chat-media-container:hover .media-download-btn {
  opacity: 1;
}

/* 媒体遮罩层 */
.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

/* 下载按钮 */
.media-download-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 36px;
  height: 36px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(8px);
}

.media-download-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

/* 加载状态样式 */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.loading-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.4;
  animation: loading-bounce 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0; }

@keyframes loading-bounce {
  0%, 80%, 100% { 
    opacity: 0.4;
    transform: scale(0.8);
  }
  40% { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-media-container {
    max-width: 100%;
  }
  
  .chat-image, 
  .chat-video {
    max-height: 250px;
  }
  
  .message-bubble {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .chat-image, 
  .chat-video {
    max-height: 200px;
  }
  
  .media-download-btn {
    width: 32px;
    height: 32px;
    top: 8px;
    right: 8px;
  }
}

/* 图片模态框优化 */
.image-modal {
  animation: modal-fade-in 0.3s ease-out;
}

.image-modal img {
  animation: modal-scale-in 0.3s ease-out;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes modal-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 聊天气泡文本优化 */
.chat-bubble {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.chat-bubble img,
.chat-bubble video {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* 平滑滚动 */
.chat-container {
  scroll-behavior: smooth;
}

/* 自定义滚动条优化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 234, 255, 0.3) 0%, rgba(127, 95, 255, 0.3) 100%);
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(0, 234, 255, 0.5) 0%, rgba(127, 95, 255, 0.5) 100%);
}

/* 聊天窗口媒体优化样式 */

/* 图片悬停效果 */
.chat-image {
  transition: all 0.3s ease;
  cursor: pointer;
}

.chat-image:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 图片容器 */
.chat-media-container {
  position: relative;
  display: inline-block;
  max-width: 400px;
}

.chat-media-container:hover .media-overlay {
  opacity: 1;
}

.chat-media-container:hover .media-download-btn {
  opacity: 1;
}

/* 媒体遮罩层 */
.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 210, 255, 0.1), rgba(122, 95, 250, 0.1));
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

/* 下载按钮 */
.media-download-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.media-download-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* 响应式媒体 */
@media (max-width: 768px) {
  .chat-media-container {
    max-width: 280px;
  }
  
  .chat-image, 
  .chat-video {
    max-height: 200px;
  }
}

@media (max-width: 480px) {
  .chat-media-container {
    max-width: 240px;
  }
  
  .chat-image, 
  .chat-video {
    max-height: 160px;
  }
}

/* 图片模态框动画 */
.image-modal {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 聊天气泡优化 */
.chat-bubble {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.chat-bubble img,
.chat-bubble video {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 媒体信息提示 */
.media-info {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 6px;
  text-align: center;
}

/* 用户消息的媒体信息 */
.user-message .media-info {
  color: rgba(255, 255, 255, 0.8);
}

/* AI消息的媒体信息 */
.ai-message .media-info {
  color: rgba(0, 0, 0, 0.6);
}

/* ChatGPT风格的聊天样式 */

/* 消息气泡优化 - 去掉背景，更简洁 */
.message-bubble {
  margin-bottom: 1.5rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.75;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.025em;
}

.message-bubble.user {
  text-align: right;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.message-bubble.assistant {
  text-align: left;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* 输入框字体优化 */
.chat-input {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.025em;
  line-height: 1.5;
}

/* 输入框焦点效果 */
.chat-input-container:focus-within {
  border-color: #00d2ff;
  box-shadow: 0 0 0 3px rgba(0, 210, 255, 0.1);
}

/* 文字渲染优化 */
.message-text {
  font-smooth: antialiased;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 发送按钮动画 */
.send-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
}

.send-button:active:not(:disabled) {
  transform: scale(0.95);
} 