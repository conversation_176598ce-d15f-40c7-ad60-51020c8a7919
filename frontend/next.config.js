/** @type {import('next').NextConfig} */
const nextConfig = {
    // 性能优化
    experimental: {
        // 优化包导入，减少 bundle 大小
        optimizePackageImports: [
            'axios',
            '@stripe/stripe-js',
            '@stripe/react-stripe-js'
        ],
    },

    // 启用 SWC minification (比 Terser 快 7x)
    swcMinify: true,

    // 图片优化配置
    images: {
        // 支持的设备尺寸
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
        // 图片尺寸
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
        // 支持的格式 (WebP 优先)
        formats: ['image/webp', 'image/avif'],
        // 最小缓存时间 (31天)
        minimumCacheTTL: 2678400,
        // 允许的远程图片域名 - 支持AI服务和媒体资源
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'example.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: '*.oss-cn-beijing.aliyuncs.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'dashscope-result-*.oss-cn-*.aliyuncs.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'ugen.oss-cn-beijing.aliyuncs.com',
                port: '',
                pathname: '/**',
            },
        ],
        // 取消域名限制以支持所有HTTPS图片
        unoptimized: false,
    },

    // 启用压缩
    compress: true,

    // 生产环境移除 console.log
    compiler: {
        removeConsole: process.env.NODE_ENV === 'production',
    },

    // HTTP Headers配置 - 处理CORS和安全策略
    async headers() {
        return [
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'Cross-Origin-Embedder-Policy',
                        value: 'unsafe-none'
                    },
                    {
                        key: 'Cross-Origin-Opener-Policy',
                        value: 'same-origin-allow-popups'
                    },
                    {
                        key: 'Cross-Origin-Resource-Policy',
                        value: 'cross-origin'
                    }
                ],
            },
        ];
    },

    // Docker独立构建输出
    output: 'standalone',

    // 启用React严格模式
    reactStrictMode: true,

    // 禁用静态优化以解决路由问题  
    trailingSlash: false,

    // 页面扩展名
    pageExtensions: ['js', 'jsx', 'ts', 'tsx'],

    // Webpack 优化
    webpack: (config, { dev, isServer }) => {
        // 生产环境优化
        if (!dev && !isServer) {
            // 启用分包
            config.optimization.splitChunks = {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10,
                    },
                    common: {
                        name: 'common',
                        minChunks: 2,
                        chunks: 'all',
                        enforce: true,
                        priority: 5,
                    },
                    // 将大型动画库分离
                    animations: {
                        test: /[\\/]node_modules[\\/](framer-motion)[\\/]/,
                        name: 'animations',
                        chunks: 'all',
                        priority: 20,
                    },
                },
            };
        }

        return config;
    },

    // 环境变量
    env: {
        ANALYZE: process.env.ANALYZE,
    },

    // 缓存优化
    onDemandEntries: {
        // period (in ms) where the server will keep pages in the buffer
        maxInactiveAge: 25 * 1000,
        // number of pages that should be kept simultaneously without being disposed
        pagesBufferLength: 2,
    },
};

// Bundle Analyzer 配置
// Bundle analyzer只在需要时启用
let config = nextConfig;

if (process.env.ANALYZE === 'true') {
    try {
        const withBundleAnalyzer = require('@next/bundle-analyzer')({
            enabled: true,
        });
        config = withBundleAnalyzer(nextConfig);
    } catch (error) {
        console.warn('Bundle analyzer not available:', error.message);
    }
}

module.exports = config; 