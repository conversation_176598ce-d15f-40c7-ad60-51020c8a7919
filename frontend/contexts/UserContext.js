'use client'

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { getDisplayPlan } from '../utils/planMapping';

// 初始状态
const initialState = {
    user: null,
    isLoading: false,
    isAuthenticated: false,
    error: null,
    profile: {
        balance: 0,
        plan: 'Free',
        usage: {
            current: 0,
            limit: 1000
        }
    }
};

// Action 类型
const ActionTypes = {
    SET_LOADING: 'SET_LOADING',
    SET_USER: 'SET_USER',
    SET_ERROR: 'SET_ERROR',
    UPDATE_PROFILE: 'UPDATE_PROFILE',
    CLEAR_USER: 'CLEAR_USER',
    UPDATE_BALANCE: 'UPDATE_BALANCE'
};

// Reducer
function userReducer(state, action) {
    switch (action.type) {
        case ActionTypes.SET_LOADING:
            return { ...state, isLoading: action.payload };

        case ActionTypes.SET_USER:
            return {
                ...state,
                user: action.payload,
                isAuthenticated: !!action.payload,
                isLoading: false,
                error: null
            };

        case ActionTypes.SET_ERROR:
            return {
                ...state,
                error: action.payload,
                isLoading: false
            };

        case ActionTypes.UPDATE_PROFILE:
            return {
                ...state,
                profile: { ...state.profile, ...action.payload }
            };

        case ActionTypes.UPDATE_BALANCE:
            return {
                ...state,
                profile: {
                    ...state.profile,
                    balance: action.payload
                }
            };

        case ActionTypes.CLEAR_USER:
            return {
                ...initialState
            };

        default:
            return state;
    }
}

// Context
const UserContext = createContext();

// Provider 组件
export function UserProvider({ children }) {
    const [state, dispatch] = useReducer(userReducer, initialState);

    // 获取用户信息
    const fetchUser = async () => {
        try {
            dispatch({ type: ActionTypes.SET_LOADING, payload: true });

            if (typeof window === 'undefined') return;

            const token = localStorage.getItem('token');
            if (!token) {
                dispatch({ type: ActionTypes.CLEAR_USER });
                return;
            }

            const response = await fetch('/api/auth/profile/', {
                headers: { 'Authorization': `Token ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('User profile data:', data); // Debug log
                if (data.user) {
                    // Cache user data
                    if (typeof window !== 'undefined') {
                        localStorage.setItem('user', JSON.stringify(data.user));
                    }

                    dispatch({ type: ActionTypes.SET_USER, payload: data.user });
                    const displayPlan = getDisplayPlan(data.user.current_plan || 'Free');
                    console.log('Plan mapping:', data.user.current_plan, '->', displayPlan); // Debug log
                    dispatch({
                        type: ActionTypes.UPDATE_PROFILE,
                        payload: {
                            balance: data.user.tokens || 0,
                            plan: displayPlan,
                        }
                    });
                }
            } else {
                console.error('API response not ok:', response.status, response.statusText);
                // Don't clear user data on server errors (5xx), only on auth errors (401)
                if (response.status === 401) {
                    if (typeof window !== 'undefined') {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                    dispatch({ type: ActionTypes.CLEAR_USER });
                } else {
                    // For other errors, keep existing user data but show error
                    dispatch({ type: ActionTypes.SET_ERROR, payload: `Server error: ${response.status}` });
                    dispatch({ type: ActionTypes.SET_LOADING, payload: false });
                }
            }
        } catch (error) {
            console.error('Error fetching user:', error);
            dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
            dispatch({ type: ActionTypes.SET_LOADING, payload: false });

            // Only clear user data on auth errors, not network errors
            if (error.status === 401) {
                if (typeof window !== 'undefined') {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
                dispatch({ type: ActionTypes.CLEAR_USER });
            }
        }
    };

    // 更新用户资料
    const updateProfile = (profileData) => {
        dispatch({ type: ActionTypes.UPDATE_PROFILE, payload: profileData });
    };

    // 更新余额
    const updateBalance = (newBalance) => {
        dispatch({ type: ActionTypes.UPDATE_BALANCE, payload: newBalance });
    };

    // 登出
    const logout = () => {
        if (typeof window !== 'undefined') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        }
        dispatch({ type: ActionTypes.CLEAR_USER });
    };

    // 初始化时获取用户信息
    useEffect(() => {
        if (typeof window === 'undefined') return; // 确保只在客户端运行

        const token = localStorage.getItem('token');
        if (token) {
            // Try to load cached user data first
            const cachedUser = localStorage.getItem('user');
            if (cachedUser) {
                try {
                    const userData = JSON.parse(cachedUser);
                    console.log('Loading cached user data:', userData);
                    dispatch({ type: ActionTypes.SET_USER, payload: userData });
                    dispatch({
                        type: ActionTypes.UPDATE_PROFILE,
                        payload: {
                            balance: userData.tokens || 0,
                            plan: getDisplayPlan(userData.current_plan || 'Free'),
                        }
                    });
                } catch (e) {
                    console.error('Error parsing cached user data:', e);
                }
            }
            // Then fetch fresh data
            fetchUser();
        }
    }, []);

    const value = {
        user: state.user,
        loading: state.isLoading,
        error: state.error,
        profile: state.profile,
        isAuthenticated: state.isAuthenticated,
        updateUser: (userData) => dispatch({ type: ActionTypes.SET_USER, payload: userData }),
        updateProfile,
        updateBalance,
        logout,
        actions: {
            fetchUser,
            updateProfile,
            updateBalance,
            logout
        }
    };

    return (
        <UserContext.Provider value={value}>
            {children}
        </UserContext.Provider>
    );
}

// Hook 用于使用 User Context
export function useUser() {
    const context = useContext(UserContext);
    if (context === undefined) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
}

// 别名导出以保持兼容性
export const useUserContext = useUser; 