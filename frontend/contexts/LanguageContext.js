import { createContext, useContext, useState, useEffect } from "react";

const LanguageContext = createContext();

export const translations = {
  en: {
    // Navigation
    "get.started": "Get Started",
    "sign.in": "Sign In",
    "sign.up": "Sign Up",
    "sign.out": "Sign Out",
    "enter.app": "Enter App",
    "home": "Home",

    // Homepage
    "hero.title": "Create Amazing AI Content",
    "hero.subtitle": "Join us to experience the powerful AI generation capabilities",
    "video.demo": "🎬 Demo Video Playing",
    "features.title": "Powerful Features",
    "feature.image.title": "AI Image Generation",
    "feature.image.desc": "Generate high-quality images from text descriptions with various art styles",
    "feature.video.title": "AI Video Creation",
    "feature.video.desc": "Create amazing video content from text or images, bring creativity to life",
    "feature.chat.title": "Smart Chat",
    "feature.chat.desc": "Natural conversation with AI for creative inspiration and professional advice",
    "feature.multimodal.title": "Multimodal Understanding",
    "feature.multimodal.desc": "Upload images or videos, AI understands content and provides related services",
    "stats.users": "Users",
    "stats.images": "Images Generated",
    "stats.models": "Models",
    "stats.characters": "AI Characters",
    "cta.title": "Start Your AI Creative Journey",
    "cta.subtitle": "Join millions of users and explore infinite creative possibilities",
    "cta.primary": "Start Creating",
    "cta.secondary": "Learn More",

    // Auth pages
    "auth.welcome.back": "Welcome Back",
    "auth.welcome.subtitle": "Sign in to your account to continue",
    "auth.create.account": "Create Your Account",
    "auth.create.subtitle": "Join us to experience powerful AI generation capabilities",
    "auth.social.signup": "Sign up with social account",
    "auth.social.signin": "Quick sign in",
    "auth.email.signup": "Or sign up with email",
    "auth.email.signin": "Or sign in with email",
    "auth.name": "Name",
    "auth.name.placeholder": "Enter your name",
    "auth.name.required": "Please enter your name",
    "auth.name.min.length": "Name must be at least 2 characters",
    "auth.email": "Email Address",
    "auth.email.placeholder": "Enter your email address",
    "auth.email.required": "Please enter your email address",
    "auth.email.invalid": "Please enter a valid email address",
    "auth.password": "Password",
    "auth.password.placeholder": "Enter password (at least 6 characters)",
    "auth.password.required": "Please enter your password",
    "auth.password.min.length": "Password must be at least 6 characters",
    "auth.confirm.password": "Confirm Password",
    "auth.confirm.password.placeholder": "Enter password again",
    "auth.confirm.password.required": "Please confirm your password",
    "auth.password.mismatch": "Passwords do not match",
    "auth.forgot.password": "Forgot Password?",
    "auth.signin.button": "Sign In",
    "auth.signup.button": "Sign Up",
    "auth.signing.in": "Signing in...",
    "auth.signing.up": "Signing up...",
    "auth.have.account": "Already have an account?",
    "auth.no.account": "Don't have an account?",
    "auth.signin.now": "Sign in now",
    "auth.signup.now": "Sign up now",
    "auth.login.failed": "Login failed, please try again",
    "auth.register.failed": "Registration failed, please try again",
    "auth.password.reset.success": "Password reset successful, please login with your new password",
    "auth.password.reset.failed": "Failed to send reset email, please try again",

    // Chat
    "chat.title": "AI Chat",
    "chat.placeholder": "Send a message...",
    "chat.send": "Send",
    "chat.new.session": "New Chat",
    "chat.history": "Chat History",
    "chat.current.session": "Current Session",
    "chat.generating": "Generating",
    "chat.welcome.title": "AIGC Multimodal Assistant",
    "chat.welcome.subtitle": "I can chat with you, generate images and videos, and understand uploaded images and videos. Type your question or upload a file below to get started!",
    "chat.welcome.login.note": "✨ Your chat history will be automatically saved and you can continue viewing it the next time you log in",
    "chat.advanced.mode": "Advanced Mode Enabled",
    "chat.file.image": "@Image",
    "chat.file.video": "@Video",
    "chat.file.preview": "Preview",
    "chat.video.not.supported": "Your browser does not support video playback.",
    "chat.error.feature.developing": "This feature is under development",
    "chat.enter.new.name": "Enter new session name",
    "chat.rename.session": "Rename",
    "chat.delete.session": "Delete",
    "chat.confirm.delete": "Are you sure you want to delete this session?",
    "chat.created": "Created",
    "chat.session.menu": "Chat Menu",

    // Profile
    "profile.title": "Profile",
    "profile.credits": "Credits",
    "profile.generations": "Total Generations",
    "profile.account.info": "Account Information",
    "profile.usage.stats": "Usage Statistics",
    "profile.account.actions": "Account Actions",
    "profile.user.id": "User ID",
    "profile.registration.date": "Registration Date",
    "profile.remaining.credits": "Remaining Credits",
    "profile.edit.profile": "Edit Profile",
    "profile.logout": "Sign Out",
    "profile.feature.developing": "Feature under development",
    "profile.welcome.back": "Welcome back, {name}",

    // Common
    "loading": "Loading...",
    "error": "Error",
    "success": "Success",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "save": "Save",
    "language": "Language",
    "english": "English",
    "chinese": "中文",

    // Image and Video
    "image.download": "Download Image",
    "image.click.to.enlarge": "Click to enlarge",
    "image.click.to.view": "Click to view full size",
    "video.click.to.play": "Click to play video",
    "video.download": "Download Video",

    // Chat content
    "chat.image.generation.complete": "Image generation complete",
    "chat.video.generation.complete": "Video generation completed",
    "chat.content.generated": "Content generated",
  },
  zh: {
    // Navigation
    "get.started": "立即体验",
    "sign.in": "登录",
    "sign.up": "注册",
    "sign.out": "退出登录",
    "enter.app": "进入应用",
    "home": "首页",

    // Homepage
    "hero.title": "创造令人惊叹的AI内容",
    "hero.subtitle": "加入我们，体验强大的AI生成能力",
    "video.demo": "🎬 演示视频循环播放中",
    "features.title": "强大功能",
    "feature.image.title": "AI图像生成",
    "feature.image.desc": "基于文本描述生成高质量图像，支持多种艺术风格",
    "feature.video.title": "AI视频创作",
    "feature.video.desc": "从文本或图片生成精彩视频内容，让创意动起来",
    "feature.chat.title": "智能对话",
    "feature.chat.desc": "与AI进行自然对话，获得创意灵感和专业建议",
    "feature.multimodal.title": "多模态理解",
    "feature.multimodal.desc": "上传图片或视频，AI能理解内容并提供相关服务",
    "stats.users": "用户",
    "stats.images": "生成图像",
    "stats.models": "模型",
    "stats.characters": "AI角色",
    "cta.title": "开始您的AI创意之旅",
    "cta.subtitle": "加入数百万用户，探索无限创意可能",
    "cta.primary": "开始创作",
    "cta.secondary": "了解更多",

    // Auth pages
    "auth.welcome.back": "欢迎回来",
    "auth.welcome.subtitle": "登录您的账户以继续使用",
    "auth.create.account": "创建您的账户",
    "auth.create.subtitle": "加入我们，体验强大的AI生成能力",
    "auth.social.signup": "使用社交账号快速注册",
    "auth.social.signin": "快速登录",
    "auth.email.signup": "或使用邮箱注册",
    "auth.email.signin": "或使用邮箱登录",
    "auth.name": "姓名",
    "auth.name.placeholder": "请输入您的姓名",
    "auth.name.required": "请输入您的姓名",
    "auth.name.min.length": "姓名至少需要2个字符",
    "auth.email": "邮箱地址",
    "auth.email.placeholder": "请输入邮箱地址",
    "auth.email.required": "请输入邮箱地址",
    "auth.email.invalid": "请输入有效的邮箱地址",
    "auth.password": "密码",
    "auth.password.placeholder": "请输入密码（至少6位）",
    "auth.password.required": "请输入密码",
    "auth.password.min.length": "密码至少需要6个字符",
    "auth.confirm.password": "确认密码",
    "auth.confirm.password.placeholder": "请再次输入密码",
    "auth.confirm.password.required": "请确认密码",
    "auth.password.mismatch": "两次输入的密码不一致",
    "auth.forgot.password": "忘记密码？",
    "auth.signin.button": "登录",
    "auth.signup.button": "注册",
    "auth.signing.in": "登录中...",
    "auth.signing.up": "注册中...",
    "auth.have.account": "已有账户？",
    "auth.no.account": "还没有账户？",
    "auth.signin.now": "立即登录",
    "auth.signup.now": "立即注册",
    "auth.login.failed": "登录失败，请重试",
    "auth.register.failed": "注册失败，请重试",
    "auth.password.reset.success": "密码重置成功，请用新密码登录",
    "auth.password.reset.failed": "发送重置邮件失败，请重试",

    // Chat
    "chat.title": "AI聊天",
    "chat.placeholder": "发送消息...",
    "chat.send": "发送",
    "chat.new.session": "新建聊天",
    "chat.history": "聊天记录",
    "chat.current.session": "当前会话",
    "chat.generating": "生成中",
    "chat.welcome.title": "AIGC多模态助手",
    "chat.welcome.subtitle": "我可以与您聊天、生成图像和视频，并理解上传的图像和视频。在下方输入问题或上传文件即可开始！",
    "chat.welcome.login.note": "✨ 您的聊天记录将自动保存，下次登录时可继续查看",
    "chat.advanced.mode": "高级模式已启用",
    "chat.file.image": "@图片",
    "chat.file.video": "@视频",
    "chat.file.preview": "预览",
    "chat.video.not.supported": "您的浏览器不支持视频播放。",
    "chat.error.feature.developing": "该功能正在开发中",
    "chat.enter.new.name": "输入新的会话名称",
    "chat.rename.session": "重命名",
    "chat.delete.session": "删除",
    "chat.confirm.delete": "确定要删除这个会话吗？",
    "chat.created": "创建时间",
    "chat.session.menu": "聊天菜单",

    // Profile
    "profile.title": "个人资料",
    "profile.credits": "积分",
    "profile.generations": "总生成次数",
    "profile.account.info": "账户信息",
    "profile.usage.stats": "使用统计",
    "profile.account.actions": "账户操作",
    "profile.user.id": "用户ID",
    "profile.registration.date": "注册日期",
    "profile.remaining.credits": "剩余积分",
    "profile.edit.profile": "编辑资料",
    "profile.logout": "退出登录",
    "profile.feature.developing": "功能开发中",
    "profile.welcome.back": "欢迎回来，{name}",

    // Common
    "loading": "加载中...",
    "error": "错误",
    "success": "成功",
    "cancel": "取消",
    "confirm": "确认",
    "save": "保存",
    "language": "语言",
    "english": "English",
    "chinese": "中文",

    // Image and Video
    "image.download": "下载图片",
    "image.click.to.enlarge": "点击放大",
    "image.click.to.view": "点击查看全尺寸",
    "video.click.to.play": "点击播放视频",
    "video.download": "下载视频",

    // Chat content
    "chat.image.generation.complete": "图像生成完成",
    "chat.video.generation.complete": "视频生成完成",
    "chat.content.generated": "内容已生成",
  }
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en'); // Default to English

  useEffect(() => {
    // Load saved language from localStorage only if user manually set it
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
      setLanguage(savedLanguage);
    } else {
      // Set default to English if no saved preference
      setLanguage('en');
      localStorage.setItem('language', 'en');
    }
  }, []);

  const changeLanguage = (newLanguage) => {
    if (newLanguage !== language) {
      setLanguage(newLanguage);
      localStorage.setItem('language', newLanguage);
    }
  };

  const t = (key) => {
    return translations[language][key] || translations['en'][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}; 