import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
    Elements,
    CardElement,
    useStripe,
    useElements
} from '@stripe/react-stripe-js';
import ErrorSuppressor from './ErrorSuppressor';
import StripeStatusChecker from './StripeStatusChecker';

// 初始化Stripe - 只在客户端运行，抑制广告拦截器错误
const stripePromise = typeof window !== 'undefined' ? (async () => {
    try {
        // 临时抑制特定错误
        const originalError = window.console.error;
        window.console.error = (...args) => {
            const message = args.join(' ');
            if (!message.includes('ERR_BLOCKED_BY_CLIENT') &&
                !message.includes('stripe.com') &&
                !message.includes('Failed to load resource')) {
                originalError.apply(console, args);
            }
        };

        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

        // 恢复console.error
        window.console.error = originalError;

        return stripe;
    } catch (error) {
        // 如果Stripe加载失败，返回null但不显示错误
        return null;
    }
})() : null;

// 调试信息 - 仅在开发环境显示
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    console.log('Stripe Configuration Debug:');
    console.log('- Environment:', process.env.NODE_ENV);
    console.log('- Stripe Key Available:', !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
    console.log('- Stripe Key Prefix:', process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY?.substring(0, 10) + '...');
    console.log('- Current URL:', window.location.href);

    // 检查是否有广告拦截器的简单方法
    try {
        const testDiv = document.createElement('div');
        testDiv.className = 'adsbox';
        testDiv.style.position = 'absolute';
        testDiv.style.left = '-9999px';
        document.body.appendChild(testDiv);
        setTimeout(() => {
            const adBlockDetected = testDiv.offsetHeight === 0;
            console.log('- Ad Blocker Detected:', adBlockDetected);
            document.body.removeChild(testDiv);
        }, 100);
    } catch (e) {
        console.log('- Ad Blocker Detection: Unable to test');
    }
}

// 卡片样式配置
const cardElementOptions = {
    style: {
        base: {
            fontSize: '16px',
            color: '#ffffff',
            fontFamily: '"Inter", sans-serif',
            fontSmoothing: 'antialiased',
            '::placeholder': {
                color: '#6b7280',
            },
            backgroundColor: 'transparent',
        },
        invalid: {
            color: '#ef4444',
            iconColor: '#ef4444',
        },
    },
    hidePostalCode: false,
};

// 广告拦截器检测组件
const AdBlockerWarning = () => {
    const [showWarning, setShowWarning] = useState(false);
    const [networkIssues, setNetworkIssues] = useState(false);
    const [hasDetected, setHasDetected] = useState(false);

    useEffect(() => {
        // 只检测一次，避免重复请求
        if (hasDetected) return;

        // 检测广告拦截器和网络问题
        const detectIssues = () => {
            try {
                // 静默检测广告拦截器，不产生网络请求
                const testDiv = document.createElement('div');
                testDiv.className = 'adsbox ads ad-banner';
                testDiv.style.position = 'absolute';
                testDiv.style.left = '-9999px';
                testDiv.style.height = '1px';
                testDiv.style.width = '1px';
                testDiv.style.visibility = 'hidden';
                document.body.appendChild(testDiv);

                setTimeout(() => {
                    try {
                        const isBlocked = testDiv.offsetHeight === 0 || testDiv.offsetWidth === 0;
                        setShowWarning(isBlocked);
                        document.body.removeChild(testDiv);

                        // 只有检测到广告拦截器时才进行进一步检测
                        if (isBlocked) {
                            // 检测是否能访问Stripe，但不产生实际请求
                            const stripeTestDiv = document.createElement('div');
                            stripeTestDiv.className = 'stripe-test-element';
                            stripeTestDiv.style.display = 'none';
                            document.body.appendChild(stripeTestDiv);

                            // 简单的连通性检查，不发送实际请求
                            setTimeout(() => {
                                try {
                                    document.body.removeChild(stripeTestDiv);
                                } catch (e) {
                                    // 忽略清理错误
                                }
                            }, 50);
                        }

                        setHasDetected(true);
                    } catch (e) {
                        // 静默处理错误，避免控制台噪音
                        setHasDetected(true);
                    }
                }, 100);

            } catch (e) {
                // 静默处理错误
                setHasDetected(true);
            }
        };

        detectIssues();
    }, [hasDetected]);

    if (!showWarning && !networkIssues) return null;

    return (
        <div className="mb-4 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <div className="flex items-start">
                <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                </div>
                <div className="ml-3 flex-1">
                    <h3 className="text-sm font-medium text-yellow-300 mb-2">
                        ⚠️ 支付可能受到干扰
                    </h3>
                    <div className="text-sm text-yellow-200 space-y-2">
                        {showWarning && (
                            <div>
                                <p className="font-medium">检测到广告拦截器:</p>
                                <ul className="list-disc list-inside mt-1 ml-4 space-y-1">
                                    <li>请暂时关闭广告拦截器 (uBlock Origin, AdBlock Plus等)</li>
                                    <li>将 *.stripe.com 和本站点添加到白名单</li>
                                    <li>尝试使用无痕/隐私模式</li>
                                </ul>
                            </div>
                        )}
                        {networkIssues && (
                            <div>
                                <p className="font-medium">网络连接问题:</p>
                                <ul className="list-disc list-inside mt-1 ml-4 space-y-1">
                                    <li>检查网络连接是否稳定</li>
                                    <li>尝试刷新页面</li>
                                    <li>如在公司网络，请联系IT部门</li>
                                </ul>
                            </div>
                        )}
                        <div className="mt-3 p-2 bg-blue-500/20 border border-blue-500/30 rounded">
                            <p className="text-blue-200 text-xs">
                                💡 <strong>快速解决:</strong> 如果支付失败，请尝试使用不同的浏览器或移动设备
                            </p>
                        </div>
                    </div>
                </div>
                <div className="ml-4">
                    <button
                        onClick={() => { setShowWarning(false); setNetworkIssues(false); }}
                        className="text-yellow-400 hover:text-yellow-600 transition-colors"
                        title="关闭提示"
                    >
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );
};

// 支付表单组件
const PaymentForm = ({ planDetails, onSuccess, onError, onCancel }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [customerInfo, setCustomerInfo] = useState({
        email: '',
        name: '',
        address: {
            line1: '',
            city: '',
            country: 'US',
            postal_code: ''
        }
    });

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!stripe || !elements) {
            return;
        }

        setLoading(true);
        setError(null);

        const cardElement = elements.getElement(CardElement);

        try {
            // 获取认证token
            const authToken = localStorage.getItem('token');
            if (!authToken) {
                throw new Error('Please log in first');
            }

            // 仅在开发环境输出详细日志
            if (process.env.NODE_ENV === 'development') {
                console.log('Creating Payment Intent for plan:', planDetails.type);
                console.log('Customer info:', customerInfo);
            }

            // 静默捕获广告拦截器错误
            const originalConsoleError = console.error;
            const suppressedErrors = [
                'net::ERR_BLOCKED_BY_CLIENT',
                'Failed to load resource',
                'ERR_BLOCKED_BY_CLIENT',
                'stripe.com',
                'js.stripe.com'
            ];

            console.error = (...args) => {
                const message = args.join(' ');
                if (!suppressedErrors.some(error => message.includes(error))) {
                    originalConsoleError.apply(console, args);
                }
            };

            // 创建Payment Intent
            const response = await fetch('/api/stripe/create-payment-intent/', {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${authToken}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    plan_type: planDetails.type,
                    customer_info: customerInfo
                }),
            });

            if (process.env.NODE_ENV === 'development') {
                console.log('Payment Intent response status:', response.status);
            }

            let responseData;
            try {
                responseData = await response.json();
                if (process.env.NODE_ENV === 'development') {
                    console.log('Payment Intent response data:', responseData);
                }
            } catch (parseError) {
                console.error('Failed to parse response JSON:', parseError);
                throw new Error('Server response format error');
            }

            if (!response.ok) {
                throw new Error(responseData?.error || `HTTP ${response.status}: Failed to create payment intent`);
            }

            if (!responseData.success) {
                throw new Error(responseData.error || 'Failed to create payment intent');
            }

            const client_secret = responseData.client_secret;

            if (!client_secret) {
                if (process.env.NODE_ENV === 'development') {
                    console.error('Response data:', responseData);
                }
                throw new Error('Did not receive valid client_secret');
            }

            if (process.env.NODE_ENV === 'development') {
                console.log('Client secret received, length:', client_secret.length);
            }

            // 确认支付
            const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(
                client_secret,
                {
                    payment_method: {
                        card: cardElement,
                        billing_details: {
                            name: customerInfo.name,
                            email: customerInfo.email,
                            address: customerInfo.address
                        },
                    }
                }
            );

            if (stripeError) {
                // 提供更友好的错误信息
                let errorMessage = stripeError.message;
                if (stripeError.code === 'card_declined') {
                    errorMessage = 'Your bank card has been rejected. Please check the card information or contact the bank';
                } else if (stripeError.code === 'insufficient_funds') {
                    errorMessage = 'The balance is insufficient. Please use another bank card';
                } else if (stripeError.code === 'network_error') {
                    errorMessage = 'Network connection issue. Please check the network or try refreshing the page';
                } else if (stripeError.message.includes('blocked')) {
                    errorMessage = 'The request was blocked. It might be due to interference from the AD blocker. Please try to turn off the AD blocker';
                }
                throw new Error(errorMessage);
            }

            if (paymentIntent.status === 'succeeded') {
                // 支付成功，通知后端
                const confirmResponse = await fetch('/api/stripe/confirm-payment/', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        payment_intent_id: paymentIntent.id
                    }),
                });

                const confirmData = await confirmResponse.json();

                if (confirmData.success) {
                    onSuccess({
                        success: true,
                        tokens_added: confirmData.tokens_added,
                        message: confirmData.message,
                        order_id: confirmData.order_id
                    });
                } else {
                    throw new Error(confirmData.error || 'Payment confirmation failed');
                }
            }

        } catch (err) {
            console.error('Payment error:', err);

            let errorMessage = err.message;

            // 特殊处理常见错误
            if (err.message.includes('ERR_BLOCKED_BY_CLIENT')) {
                errorMessage = '支付请求被浏览器拦截。请检查：\n1. 关闭广告拦截器\n2. 关闭隐私保护插件\n3. 尝试使用无痕模式\n4. 或尝试其他浏览器';
            } else if (err.message.includes('Failed to fetch')) {
                errorMessage = '网络连接失败，请检查网络连接后重试';
            } else if (err.message.includes('NetworkError')) {
                errorMessage = '网络错误，请检查网络连接或尝试刷新页面';
            } else if (err.message.includes('CORS')) {
                errorMessage = '跨域请求失败，请联系技术支持';
            }

            setError(errorMessage);
            onError?.(errorMessage);
        } finally {
            setLoading(false);

            // 恢复原始的console.error
            if (typeof originalConsoleError !== 'undefined') {
                console.error = originalConsoleError;
            }
        }
    };

    const handleInputChange = (field, value) => {
        if (field.includes('.')) {
            const [parent, child] = field.split('.');
            setCustomerInfo(prev => ({
                ...prev,
                [parent]: {
                    ...prev[parent],
                    [child]: value
                }
            }));
        } else {
            setCustomerInfo(prev => ({
                ...prev,
                [field]: value
            }));
        }
    };

    return (
        <div className="max-w-md mx-auto bg-[#241b3a] rounded-xl p-6 border border-[#3a2a5a]/50">
            {/* 广告拦截器警告 */}
            <AdBlockerWarning />

            <div className="mb-6">
                <h3 className="text-xl font-bold text-white mb-2">Payment Information</h3>
                <div className="bg-purple-600/20 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-300">{planDetails.name}</span>
                        <span className="text-white font-bold">${planDetails.price}</span>
                    </div>
                    <div className="text-sm text-purple-300">
                        Get {planDetails.tokens?.toLocaleString()} tokens
                    </div>
                </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
                {/* Customer Information */}
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                            Email Address *
                        </label>
                        <input
                            type="email"
                            required
                            value={customerInfo.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className="w-full px-3 py-2 bg-[#0f0a1d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                            placeholder="<EMAIL>"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                            Cardholder Name *
                        </label>
                        <input
                            type="text"
                            required
                            value={customerInfo.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            className="w-full px-3 py-2 bg-[#0f0a1d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                            placeholder="John Doe"
                        />
                    </div>

                    {/* Billing Address */}
                    <div className="grid grid-cols-2 gap-3">
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Address
                            </label>
                            <input
                                type="text"
                                value={customerInfo.address.line1}
                                onChange={(e) => handleInputChange('address.line1', e.target.value)}
                                className="w-full px-3 py-2 bg-[#0f0a1d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                                placeholder="123 Main St"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                City
                            </label>
                            <input
                                type="text"
                                value={customerInfo.address.city}
                                onChange={(e) => handleInputChange('address.city', e.target.value)}
                                className="w-full px-3 py-2 bg-[#0f0a1d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                                placeholder="New York"
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Country
                            </label>
                            <select
                                value={customerInfo.address.country}
                                onChange={(e) => handleInputChange('address.country', e.target.value)}
                                className="w-full px-3 py-2 bg-[#0f0a1d] border border-gray-600 rounded-lg text-white focus:outline-none focus:border-purple-500"
                            >
                                <option value="US">United States</option>
                                <option value="CA">Canada</option>
                                <option value="GB">United Kingdom</option>
                                <option value="AU">Australia</option>
                                <option value="DE">Germany</option>
                                <option value="FR">France</option>
                                <option value="IT">Italy</option>
                                <option value="ES">Spain</option>
                                <option value="NL">Netherlands</option>
                                <option value="SE">Sweden</option>
                                <option value="NO">Norway</option>
                                <option value="DK">Denmark</option>
                                <option value="FI">Finland</option>
                                <option value="CH">Switzerland</option>
                                <option value="AT">Austria</option>
                                <option value="BE">Belgium</option>
                                <option value="IE">Ireland</option>
                                <option value="PT">Portugal</option>
                                <option value="JP">Japan</option>
                                <option value="KR">South Korea</option>
                                <option value="SG">Singapore</option>
                                <option value="HK">Hong Kong</option>
                                <option value="TW">Taiwan</option>
                                <option value="MY">Malaysia</option>
                                <option value="TH">Thailand</option>
                                <option value="PH">Philippines</option>
                                <option value="VN">Vietnam</option>
                                <option value="ID">Indonesia</option>
                                <option value="IN">India</option>
                                <option value="CN">China</option>
                                <option value="BR">Brazil</option>
                                <option value="MX">Mexico</option>
                                <option value="AR">Argentina</option>
                                <option value="CL">Chile</option>
                                <option value="CO">Colombia</option>
                                <option value="PE">Peru</option>
                                <option value="ZA">South Africa</option>
                                <option value="EG">Egypt</option>
                                <option value="AE">United Arab Emirates</option>
                                <option value="SA">Saudi Arabia</option>
                                <option value="IL">Israel</option>
                                <option value="TR">Turkey</option>
                                <option value="RU">Russia</option>
                                <option value="UA">Ukraine</option>
                                <option value="PL">Poland</option>
                                <option value="CZ">Czech Republic</option>
                                <option value="HU">Hungary</option>
                                <option value="RO">Romania</option>
                                <option value="BG">Bulgaria</option>
                                <option value="HR">Croatia</option>
                                <option value="SI">Slovenia</option>
                                <option value="SK">Slovakia</option>
                                <option value="LT">Lithuania</option>
                                <option value="LV">Latvia</option>
                                <option value="EE">Estonia</option>
                                <option value="NZ">New Zealand</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Postal Code
                            </label>
                            <input
                                type="text"
                                value={customerInfo.address.postal_code}
                                onChange={(e) => handleInputChange('address.postal_code', e.target.value)}
                                className="w-full px-3 py-2 bg-[#0f0a1d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                                placeholder="10001"
                            />
                        </div>
                    </div>
                </div>

                {/* Credit Card Information */}
                <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                        Credit Card Information *
                    </label>
                    <div className="p-3 bg-[#0f0a1d] border border-gray-600 rounded-lg focus-within:border-purple-500">
                        <CardElement options={cardElementOptions} />
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="p-3 bg-red-500/20 border border-red-500/50 rounded-lg">
                        <p className="text-red-300 text-sm">{error}</p>
                    </div>
                )}

                {/* Security Notice */}
                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3">
                    <div className="flex items-center text-green-300 text-sm">
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                        SSL encrypted secure payment, your information is protected
                    </div>
                </div>

                {/* Buttons */}
                <div className="flex space-x-3 pt-4">
                    <button
                        type="button"
                        onClick={onCancel}
                        className="flex-1 py-3 px-4 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-600/20 transition-all"
                        disabled={loading}
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={!stripe || loading}
                        className="flex-1 py-3 px-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-lg hover:shadow-lg hover:shadow-purple-500/20 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                        {loading ? (
                            <>
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                Processing...
                            </>
                        ) : (
                            `Pay $${planDetails.price}`
                        )}
                    </button>
                </div>
            </form>

            {/* Stripe Logo */}
            <div className="mt-4 text-center">
                <div className="text-xs text-gray-500">
                    Powered by <span className="text-blue-400">Stripe</span>
                </div>
            </div>
        </div>
    );
};

// 主组件
const StripePaymentForm = ({ planDetails, onSuccess, onError, onCancel }) => {
    const [showPayment, setShowPayment] = useState(false);

    // 检查Stripe是否可用
    if (typeof window === 'undefined') {
        return null; // 服务端渲染时返回null
    }

    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
        !process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.startsWith('pk_')) {
        console.error('Stripe public key configuration:', process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
        return (
            <div className="max-w-md mx-auto bg-red-500/20 rounded-xl p-6 border border-red-500/50">
                <div className="text-center">
                    <h3 className="text-xl font-bold text-red-300 mb-2">Stripe Configuration Error</h3>
                    <p className="text-red-200 text-sm mb-4">
                        Stripe public key is not properly configured. Please contact administrator.
                    </p>
                    <button
                        onClick={onCancel}
                        className="w-full py-3 px-4 border border-red-500 text-red-300 rounded-lg hover:bg-red-500/10 transition-all"
                    >
                        Back
                    </button>
                </div>
            </div>
        );
    }

    return (
        <>
            <ErrorSuppressor />
            <Elements stripe={stripePromise}>
                <div className="space-y-4">
                    <StripeStatusChecker
                        onStatusChange={(isHealthy) => setShowPayment(isHealthy)}
                    />
                    <PaymentForm
                        planDetails={planDetails}
                        onSuccess={onSuccess}
                        onError={onError}
                        onCancel={onCancel}
                    />
                </div>
            </Elements>
        </>
    );
};

export default StripePaymentForm;
