import { useState } from 'react';
import Link from 'next/link';
import UserAvatar from './UserAvatar';

export default function ModernNavbar({
    user,
    onProfileClick,
    onUpgradeClick,
    navItems = [],
    startCreatingUrl = '/creative',
    onStartCreating,
    currentPage = ''
}) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    return (
        <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/20">
            <div className="container mx-auto px-6">
                <div className="flex items-center justify-between h-16">
                    {/* Logo */}
                    <Link href="/" className="flex items-center space-x-3 group">
                        <span className="text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-purple-300 text-transparent bg-clip-text">
                            MirageMakers AI
                        </span>
                    </Link>

                    {/* 右侧操作区域 */}
                    <div className="flex items-center space-x-6">
                        {user ? (
                            <>
                                {/* 登录状态：显示 Gallery、Chat 按钮和用户头像 */}
                                <div className="hidden md:flex items-center space-x-6">


                                    {/* Creative 按钮 - 仅在非Creative页面显示 */}
                                    {currentPage !== 'creative' && (
                                        <Link
                                            href="/creative"
                                            className="group flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300"
                                        >
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                            </svg>
                                            <span className="font-medium">Create</span>
                                        </Link>
                                    )}

                                    {/* 其他导航链接 */}
                                    {navItems.map((item, index) => {
                                        if (item.onClick) {
                                            return (
                                                <button
                                                    key={index}
                                                    onClick={item.onClick}
                                                    className="group flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300"
                                                >
                                                    {item.icon}
                                                    <span className="font-medium">{item.label}</span>
                                                </button>
                                            );
                                        }
                                        return (
                                            <Link
                                                key={index}
                                                href={item.path}
                                                className="group flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300"
                                            >
                                                {item.icon}
                                                <span className="font-medium">{item.label}</span>
                                            </Link>
                                        );
                                    })}
                                </div>

                                {/* 用户头像 */}
                                <UserAvatar
                                    user={user}
                                    size="sm"
                                    showOnlineStatus={true}
                                    onUpgrade={onUpgradeClick}
                                />
                            </>
                        ) : (
                            <>
                                {/* 非登录状态：只显示 Login 按钮 */}
                                <div className="hidden md:flex items-center space-x-6">
                                    {navItems.map((item, index) => {
                                        if (item.onClick) {
                                            return (
                                                <button
                                                    key={index}
                                                    onClick={item.onClick}
                                                    className="group flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300"
                                                >
                                                    {item.icon}
                                                    <span className="font-medium">{item.label}</span>
                                                </button>
                                            );
                                        }
                                        return (
                                            <Link
                                                key={index}
                                                href={item.path}
                                                className="group flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300"
                                            >
                                                {item.icon}
                                                <span className="font-medium">{item.label}</span>
                                            </Link>
                                        );
                                    })}
                                </div>

                                <Link
                                    href="/auth/login"
                                    className="text-white hover:text-purple-300 font-medium transition-colors duration-300"
                                >
                                    Login
                                </Link>
                            </>
                        )}

                        {/* 移动端菜单按钮 */}
                        <button
                            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                            className="md:hidden p-2 text-white hover:text-purple-300 transition-colors duration-300"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>

                {/* 移动端菜单 */}
                {isMobileMenuOpen && (
                    <div className="md:hidden border-t border-purple-500/20 py-4">
                        <div className="flex flex-col space-y-4">
                            {user ? (
                                <>
                                    {/* 登录状态：显示 Creative 按钮 */}

                                    {currentPage !== 'creative' && (
                                        <Link
                                            href="/creative"
                                            className="flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300 px-2 py-1"
                                            onClick={() => setIsMobileMenuOpen(false)}
                                        >
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                            </svg>
                                            <span className="font-medium">Create</span>
                                        </Link>
                                    )}

                                    {/* 其他导航链接 */}
                                    {navItems.map((item, index) => {
                                        if (item.onClick) {
                                            return (
                                                <button
                                                    key={index}
                                                    onClick={() => {
                                                        item.onClick();
                                                        setIsMobileMenuOpen(false);
                                                    }}
                                                    className="flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300 px-2 py-1"
                                                >
                                                    {item.icon}
                                                    <span className="font-medium">{item.label}</span>
                                                </button>
                                            );
                                        }
                                        return (
                                            <Link
                                                key={index}
                                                href={item.path}
                                                className="flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300 px-2 py-1"
                                                onClick={() => setIsMobileMenuOpen(false)}
                                            >
                                                {item.icon}
                                                <span className="font-medium">{item.label}</span>
                                            </Link>
                                        );
                                    })}
                                </>
                            ) : (
                                <>
                                    {/* 非登录状态：其他导航链接 + Login 按钮 */}
                                    {navItems.map((item, index) => {
                                        if (item.onClick) {
                                            return (
                                                <button
                                                    key={index}
                                                    onClick={() => {
                                                        item.onClick();
                                                        setIsMobileMenuOpen(false);
                                                    }}
                                                    className="flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300 px-2 py-1"
                                                >
                                                    {item.icon}
                                                    <span className="font-medium">{item.label}</span>
                                                </button>
                                            );
                                        }
                                        return (
                                            <Link
                                                key={index}
                                                href={item.path}
                                                className="flex items-center space-x-2 text-white hover:text-purple-300 transition-colors duration-300 px-2 py-1"
                                                onClick={() => setIsMobileMenuOpen(false)}
                                            >
                                                {item.icon}
                                                <span className="font-medium">{item.label}</span>
                                            </Link>
                                        );
                                    })}

                                    <Link
                                        href="/auth/login"
                                        className="text-white hover:text-purple-300 font-medium transition-colors duration-300 px-2 py-1"
                                        onClick={() => setIsMobileMenuOpen(false)}
                                    >
                                        Login
                                    </Link>
                                </>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </nav>
    );
}