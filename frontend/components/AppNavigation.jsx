import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';
import UserProfileMenu from './UserProfileMenu';

const AppNavigation = ({
    user,
    onUpgrade,
    showGallery = true,
    showChat = true,
    className = '',
    variant = 'default' // 'default', 'chat-sidebar', 'minimal'
}) => {
    const { language } = useLanguage();
    const router = useRouter();

    // Logo组件
    const LogoComponent = ({ className: logoClassName = '', showText = true, onClick }) => (
        <div
            className={`group flex items-center gap-3 text-white hover:opacity-80 transition-all duration-300 cursor-pointer ${logoClassName}`}
            onClick={onClick}
        >
            <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 via-violet-600 to-purple-700 rounded-xl flex items-center justify-center relative overflow-hidden group-hover:scale-110 transition-transform duration-300">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                    <div className="relative z-10">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-white">
                            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" fillOpacity="0.8" />
                            <circle cx="12" cy="12" r="3" fill="currentColor" fillOpacity="0.6" />
                            <path d="M12 1V5M12 19V23M23 12H19M5 12H1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" opacity="0.4" />
                        </svg>
                    </div>
                    <div className="absolute inset-0 rounded-xl border border-purple-400/30 group-hover:border-purple-300/50 transition-colors duration-300"></div>
                </div>
                <div className="absolute inset-0 rounded-xl bg-purple-500/20 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
            </div>
            {showText && (
                <div className="flex flex-col">
                    <span className="text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-purple-300 text-transparent bg-clip-text group-hover:from-purple-200 group-hover:to-white transition-all duration-300">
                        MirageMakers AI
                    </span>
                    <span className="text-xs text-purple-300/70 font-medium tracking-wider">
                        AI STUDIO
                    </span>
                </div>
            )}
        </div>
    );

    // 导航按钮组件
    const NavButton = ({ href, onClick, icon, label, active = false }) => (
        <button
            onClick={onClick || (() => router.push(href))}
            className={`group relative px-4 py-2 border rounded-lg transition-all duration-300 hover:shadow-lg ${active
                ? 'bg-[#6d28d9]/20 border-[#6d28d9]/50 text-white shadow-[#6d28d9]/20'
                : 'bg-[#241b3a]/20 hover:bg-[#6d28d9]/10 border-[#3a2a5a]/30 hover:border-[#6d28d9]/50 hover:shadow-[#6d28d9]/20'
                }`}
        >
            <span className={`flex items-center space-x-2 transition-colors ${active ? 'text-white' : 'text-[#6d28d9] group-hover:text-white'
                }`}>
                {icon}
                <span className="font-medium">{label}</span>
            </span>
        </button>
    );

    // Chat侧边栏变体 - 紧凑版Logo
    if (variant === 'chat-sidebar') {
        return (
            <div className={`flex items-center justify-between p-3 border-b border-[#3a2a5a] ${className}`}>
                <Link href="/">
                    <LogoComponent showText={true} />
                </Link>
            </div>
        );
    }

    // 最小版本 - 只有基础导航
    if (variant === 'minimal') {
        return (
            <div className={`flex items-center justify-between ${className}`}>
                <Link href="/">
                    <LogoComponent />
                </Link>
                <div className="flex items-center space-x-4">
                    {user && <UserProfileMenu onUpgrade={onUpgrade} />}
                </div>
            </div>
        );
    }

    // 默认导航栏
    return (
        <nav className={`bg-black/20 backdrop-blur-xl border-b border-white/5 ${className}`}>
            <div className="container mx-auto px-6 py-4">
                <div className="flex justify-between items-center">
                    {/* Logo */}
                    <Link href="/">
                        <LogoComponent />
                    </Link>

                    {/* 右侧菜单 */}
                    <div className="flex items-center space-x-4">
                        {user ? (
                            <div className="flex items-center space-x-3">
                                {/* Gallery按钮 */}
                                {showGallery && (
                                    <NavButton
                                        href="/gallery"
                                        active={router.pathname === '/gallery'}
                                        icon={
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        }
                                        label={language === 'zh' ? '画廊' : 'Gallery'}
                                    />
                                )}

                                {/* Creative按钮 */}
                                {showChat && (
                                    <NavButton
                                        href="/creative"
                                        active={router.pathname === '/creative'}
                                        icon={
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                            </svg>
                                        }
                                        label={language === 'zh' ? '创作' : 'Create'}
                                    />
                                )}

                                {/* 用户头像菜单 */}
                                <UserProfileMenu onUpgrade={onUpgrade} />
                            </div>
                        ) : (
                            /* 未登录状态 */
                            <div className="flex items-center space-x-3">
                                <Link href="/auth/login">
                                    <button className="px-4 py-2 text-[#b8a1ff] hover:text-white transition-colors hover:bg-[#3a2a5a]/50 rounded-lg">
                                        {language === 'zh' ? '登录' : 'Sign In'}
                                    </button>
                                </Link>
                                <Link href="/auth/beta-apply">
                                    <button className="px-4 py-2 bg-[#6d28d9] hover:bg-[#7c3aed] text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-[#6d28d9]/20 transition-all transform hover:scale-105">
                                        {language === 'zh' ? '内测申请' : 'Beta Apply'}
                                    </button>
                                </Link>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </nav>
    );
};

export default AppNavigation;
