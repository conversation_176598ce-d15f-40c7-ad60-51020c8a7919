import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { useLanguage } from '../contexts/LanguageContext';
import { useUser } from '../contexts/UserContext';

const UserProfileMenu = ({ onUpgrade, className = '' }) => {
    const [showUserMenu, setShowUserMenu] = useState(false);
    const { user, profile, actions } = useUser();
    const { language } = useLanguage();
    const router = useRouter();
    const menuRef = useRef(null);

    // 点击外部关闭菜单
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setShowUserMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Refresh user data when menu opens
    useEffect(() => {
        if (user && showUserMenu) {
            actions.fetchUser();
        }
    }, [showUserMenu]);

    const handleLogout = () => {
        actions.logout();
        router.push('/auth/login');
    };

    if (!user) return null;

    return (
        <div className={`relative user-menu-container ${className}`} ref={menuRef}>
            <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="group flex items-center space-x-2 px-3 py-2 bg-[#241b3a]/60 backdrop-blur-sm border border-[#3a2a5a]/50 rounded-lg hover:border-[#6d28d9]/50 transition-all duration-200 hover:shadow-lg hover:shadow-[#6d28d9]/20"
            >
                <div className="w-8 h-8 bg-gradient-to-r from-[#6d28d9] to-[#8b5cf6] rounded-full flex items-center justify-center text-white font-semibold group-hover:scale-110 transition-transform duration-200">
                    {((user.email && user.email.charAt(0)) || (user.username && user.username.charAt(0)) || 'U').toUpperCase()}
                </div>
                <div className="text-left hidden sm:block">
                    <div className="text-sm font-medium text-white">{user.username || user.email}</div>
                    <div className="text-xs text-[#6d28d9]">
                        {profile.balance !== null ? `${profile.balance.toLocaleString()} tokens` : '...'}
                    </div>
                </div>
                <svg className={`w-4 h-4 text-[#b8a1ff] group-hover:text-[#6d28d9] transition-colors ${showUserMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            {showUserMenu && (
                <div className="absolute right-0 mt-2 w-72 bg-[#241b3a]/95 backdrop-blur-lg border border-[#3a2a5a]/50 rounded-xl shadow-xl py-2 z-[9999] animate-fadeInUp">
                    {/* 用户信息头部 */}
                    <div className="px-4 py-3 border-b border-[#3a2a5a]/30">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-[#241b3a] to-[#3a2a5a] rounded-full flex items-center justify-center text-white font-semibold text-lg border border-[#6d28d9]/30">
                                {((user.email && user.email.charAt(0)) || (user.username && user.username.charAt(0)) || 'U').toUpperCase()}
                            </div>
                            <div className="flex-1">
                                <div className="text-sm font-medium text-white">{user.username || user.email}</div>
                                <div className="text-xs text-[#b8a1ff]">{user.email}</div>
                            </div>
                        </div>
                    </div>

                    {/* 会员状态 */}
                    <div className="px-4 py-3 border-b border-[#3a2a5a]/30">
                        <div className="flex items-center justify-between p-3 bg-[#241b3a]/40 rounded-lg border border-[#3a2a5a]/30">
                            <div>
                                <div className="text-sm font-medium text-white">
                                    {profile.plan}
                                </div>
                                <div className="text-xs text-[#b8a1ff]">Current membership status</div>
                            </div>
                            <button
                                onClick={() => {
                                    setShowUserMenu(false);
                                    if (onUpgrade) {
                                        onUpgrade();
                                    } else {
                                        router.push('/profile');
                                    }
                                }}
                                className="px-3 py-1 bg-gradient-to-r from-[#6d28d9] to-[#8b5cf6] text-white text-xs font-medium rounded-md hover:from-[#7c3aed] hover:to-[#a855f7] transition-all"
                            >
                                Upgrade
                            </button>
                        </div>
                    </div>

                    {/* Token余额信息 */}
                    <div className="px-4 py-3 border-b border-[#3a2a5a]/30">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="w-6 h-6 bg-gradient-to-r from-[#6d28d9] to-[#8b5cf6] rounded-md flex items-center justify-center">
                                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <span className="text-sm text-white font-medium">
                                    {(profile.balance || 0).toLocaleString()}
                                </span>
                            </div>
                            <span className="text-xs text-[#b8a1ff]">tokens</span>
                        </div>
                    </div>

                    {/* 菜单项 */}
                    <div className="py-1">
                        <button
                            className="w-full text-left px-4 py-2 text-sm text-[#b8a1ff] hover:bg-[#3a2a5a]/30 hover:text-[#6d28d9] transition-colors flex items-center space-x-3"
                            onClick={() => {
                                setShowUserMenu(false);
                                router.push('/profile');
                            }}
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>{language === 'zh' ? '个人资料' : 'Profile'}</span>
                        </button>

                        <button
                            className="w-full text-left px-4 py-2 text-sm text-[#b8a1ff] hover:bg-[#3a2a5a]/30 hover:text-[#6d28d9] transition-colors flex items-center space-x-3"
                            onClick={() => {
                                setShowUserMenu(false);
                                // Settings logic can be added here
                            }}
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span>{language === 'zh' ? '设置' : 'Settings'}</span>
                        </button>

                        <div className="border-t border-[#3a2a5a]/30 my-1"></div>

                        <button
                            onClick={() => {
                                setShowUserMenu(false);
                                handleLogout();
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-[#ef4444] hover:bg-[#3a2a5a]/30 hover:text-[#f87171] transition-colors flex items-center space-x-3"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            <span>{language === 'zh' ? '退出登录' : 'Sign Out'}</span>
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default UserProfileMenu;
