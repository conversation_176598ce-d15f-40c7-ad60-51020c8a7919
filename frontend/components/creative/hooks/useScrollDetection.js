import { useState, useEffect, useCallback } from 'react';

/**
 * Hook for smart floating message bar - 即梦风格智能悬浮消息栏
 * 简化版实现：页面滚动到底部时展开，向上滚动时收缩
 */
const useScrollDetection = (containerRef) => {
    // 核心状态：是否在底部
    const [isAtBottom, setIsAtBottom] = useState(true);
    // 消息栏状态：true=缩小，false=展开
    const [shouldMinimize, setShouldMinimize] = useState(false);

    // 检查是否在底部 - 简化版
    const checkIsAtBottom = useCallback((element) => {
        if (!element) return true;
        
        // 使用更大的阈值，让用户更容易触发底部状态
        const threshold = 100; // 100px容错范围
        const scrollTop = element.scrollTop;
        const scrollHeight = element.scrollHeight;
        const clientHeight = element.clientHeight;
        
        return (scrollHeight - scrollTop - clientHeight) <= threshold;
    }, []);

    // 简化的滚动事件处理器 - 按照您的方案实现
    const handleScroll = useCallback(() => {
        if (!containerRef?.current) return;

        const element = containerRef.current;
        const isAtBottomNow = checkIsAtBottom(element);

        // 更新是否在底部
        setIsAtBottom(isAtBottomNow);

        // 核心逻辑：页面滚动到底部时展开，向上滚动时收缩
        if (isAtBottomNow) {
            // 在底部：展开消息框
            setShouldMinimize(false);
        } else {
            // 不在底部：缩小消息框
            setShouldMinimize(true);
        }

        console.log('📱 Smart Message Bar:', {
            scrollTop: element.scrollTop,
            scrollHeight: element.scrollHeight,
            clientHeight: element.clientHeight,
            isAtBottom: isAtBottomNow,
            shouldMinimize: !isAtBottomNow
        });

    }, [containerRef, checkIsAtBottom]);

    // 手动滚动到底部
    const scrollToBottom = useCallback((smooth = true) => {
        if (!containerRef?.current) return;

        const element = containerRef.current;
        element.scrollTo({
            top: element.scrollHeight,
            behavior: smooth ? 'smooth' : 'auto'
        });

        // 立即更新状态
        setTimeout(() => {
            setIsAtBottom(true);
            setShouldMinimize(false);
        }, 100);
    }, [containerRef]);

    // 监听滚动事件
    useEffect(() => {
        const element = containerRef?.current;
        console.log('🔧 useScrollDetection初始化:', { 
            hasElement: !!element,
            elementHeight: element?.clientHeight,
            scrollHeight: element?.scrollHeight 
        });
        
        if (!element) return;

        // 添加滚动监听
        element.addEventListener('scroll', handleScroll, { passive: true });
        console.log('📡 滚动监听已添加');

        // 初始检查
        const isAtBottomInitial = checkIsAtBottom(element);
        setIsAtBottom(isAtBottomInitial);
        setShouldMinimize(!isAtBottomInitial); // 初始状态：在底部则展开，否则缩小
        console.log('🎯 初始状态:', { isAtBottom: isAtBottomInitial, shouldMinimize: !isAtBottomInitial });

        return () => {
            element.removeEventListener('scroll', handleScroll);
        };
    }, [containerRef, handleScroll, checkIsAtBottom]);

    // 当容器内容变化时，如果在底部则保持在底部
    const maintainScrollPosition = useCallback((shouldAutoScroll = true) => {
        if (!containerRef?.current) return;

        // 如果用户在底部，自动滚动到新内容
        if (isAtBottom && shouldAutoScroll) {
            setTimeout(() => {
                scrollToBottom(true);
            }, 50);
        }
    }, [isAtBottom, scrollToBottom]);

    return {
        // 核心状态
        isAtBottom,
        shouldMinimize, // 主要用于控制消息栏大小
        
        // 方法
        scrollToBottom,
        maintainScrollPosition,

        // 调试信息
        debug: {
            containerHeight: containerRef?.current?.clientHeight || 0,
            scrollHeight: containerRef?.current?.scrollHeight || 0,
            scrollTop: containerRef?.current?.scrollTop || 0
        }
    };
};

export default useScrollDetection;