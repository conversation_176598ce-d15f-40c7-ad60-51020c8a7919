import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useUserContext } from '../../contexts/UserContext';
import { useToast } from '../Toast';
import { useAtMention } from '../../hooks/useAtMention';

// 导入真实的chat hooks
import { useChatSessions } from '../chat/hooks/useChatSessions';
import { useChatMessages } from '../chat/hooks/useChatMessages';
import { useChatSubmit } from '../chat/hooks/useChatSubmit';
import { useWebSocket } from '../chat/hooks/useWebSocket';

// 导入组件
import MessageHistory from './components/MessageHistory';
import FloatingMessageBar from './components/FloatingMessageBar';

// 导入引用相关组件
import ReferenceSelector from '../ReferenceSelector';
import UserAvatar from '../UserAvatar';
import UpgradeModal from '../UpgradeModal';

import ImageModal from '../chat/components/ImageModal';

// 导入工具
import { messageUtils, TEXT_CHAT_MODE } from '../chat/utils/messageUtils';
import { useLanguage } from '../../contexts/LanguageContext';
import axios from 'axios';

// 会话项组件 - 复制自chat_backup.js
function SessionItem({ session, active, onClick, onDelete, onRename }) {
    const { language } = useLanguage();
    const [isRenaming, setIsRenaming] = useState(false);
    const [newTitle, setNewTitle] = useState(session.title);

    // 🔧 重要修复: 当session.title变化时，同步更新newTitle状态
    useEffect(() => {
        setNewTitle(session.title);
    }, [session.title]);

    const handleRename = (e) => {
        e.stopPropagation();
        if (isRenaming) {
            onRename(session.id, newTitle);
            setIsRenaming(false);
        } else {
            setIsRenaming(true);
        }
    };

    const handleDelete = (e) => {
        e.stopPropagation();
        onDelete(session.id);
    };

    const formatCreatedTime = (dateString) => {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffInHours = (now - date) / (1000 * 60 * 60);

            if (diffInHours < 24) {
                return language === 'zh' ? '今天' : 'Today';
            } else if (diffInHours < 48) {
                return language === 'zh' ? '昨天' : 'Yesterday';
            } else if (diffInHours < 168) { // 7 days
                const dayNames = language === 'zh'
                    ? ['日', '一', '二', '三', '四', '五', '六']
                    : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                return dayNames[date.getDay()];
            } else {
                return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', {
                    month: 'short',
                    day: 'numeric'
                });
            }
        } catch (error) {
            return '';
        }
    };

    return (
        <div
            className={`relative group p-4 rounded-2xl cursor-pointer transition-all duration-300 mb-2 ${active
                ? 'bg-purple-500/15 backdrop-blur-xl shadow-lg shadow-purple-500/10'
                : 'hover:bg-black/20 backdrop-blur-sm'
                }`}
            onClick={onClick}
        >
            <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                    {isRenaming ? (
                        <input
                            type="text"
                            value={newTitle}
                            onChange={(e) => setNewTitle(e.target.value)}
                            onBlur={() => setIsRenaming(false)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    onRename(session.id, newTitle);
                                    setIsRenaming(false);
                                }
                                if (e.key === 'Escape') {
                                    setNewTitle(session.title);
                                    setIsRenaming(false);
                                }
                            }}
                            className="w-full bg-transparent text-white text-sm font-medium focus:outline-none border-b border-purple-400"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                        />
                    ) : (
                        <div className="text-white text-sm font-medium truncate pr-2">
                            {session.title}
                        </div>
                    )}
                    <div className="text-white/60 text-xs mt-1">
                        {formatCreatedTime(session.created_at)}
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                        onClick={handleRename}
                        className="p-2 text-white hover:text-white hover:bg-purple-500/20 rounded-lg transition-all duration-200"
                        title={language === 'zh' ? '重命名' : 'Rename'}
                    >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                        </svg>
                    </button>
                    <button
                        onClick={handleDelete}
                        className="p-2 text-white hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200"
                        title={language === 'zh' ? '删除' : 'Delete'}
                    >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3,6 5,6 21,6" />
                            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );
}

const CreativePage = () => {
    // 添加客户端检查防止SSR错误
    const [isClient, setIsClient] = useState(false);
    const router = useRouter();
    const { user, loading: userLoading, error: userError, updateUser, updateBalance } = useUserContext();
    const { language } = useLanguage();
    const toast = useToast();

    // 基础状态管理
    const [currentPrompt, setCurrentPrompt] = useState('');
    const [currentFile, setCurrentFile] = useState(null);
    const [currentMode, setCurrentMode] = useState(TEXT_CHAT_MODE);
    const [authCheckComplete, setAuthCheckComplete] = useState(false);
    const [sessionMedia, setSessionMedia] = useState([]); // 会话媒体数据 - 用于@mention引用
    const [previewReference, setPreviewReference] = useState(null); // 用于引用预览模态框
    const [showUpgradeModal, setShowUpgradeModal] = useState(false); // 升级模态框状态
    const [sidebarOpen, setSidebarOpen] = useState(true); // 侧边栏状态

    // Refs
    const messageHistoryRef = useRef(null);
    const inputRef = useRef(null); // 输入框ref，用于@mention功能

    // 简单的滚动到底部函数
    const scrollToBottom = (smooth = true) => {
        if (!messageHistoryRef?.current) return;
        
        const element = messageHistoryRef.current;
        element.scrollTo({
            top: element.scrollHeight,
            behavior: smooth ? 'smooth' : 'auto'
        });
    };

    // 用户认证和会员信息
    const [membershipInfo, setMembershipInfo] = useState({
        current_plan: 'Free',
        current_balance: 0,
        is_expired: false
    });

    // 处理认证错误的回调
    const handleAuthError = () => {
        console.log('🔐 Authentication error, logging out...');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        updateUser(null);
        
        // 清除所有状态
        setCurrentPrompt('');
        setCurrentFile(null);
        
        toast.success('Logged out successfully');
        // 只在客户端执行路由操作
        if (isClient) {
            router.push('/');
        }
    };

    // @mention功能
    const {
        isReferenceDropdownOpen,
        referenceSearchQuery,
        dropdownPosition,
        selectedReferences,
        visualReferences,
        handleInputChange: handleAtMentionInputChange,
        handleKeyDown: handleAtMentionKeyDown,
        handleReferenceSelect,
        closeReferenceDropdown,
        removeReference,
        parseReferences,
        processMessageForSubmission,
        clearReferences,
        reset: resetAtMention,
        forceCleanAtSymbol,
        insertBatchReferences
    } = useAtMention(inputRef, setCurrentPrompt);

    // 使用真实的chat hooks
    const sessionHook = useChatSessions(user, handleAuthError);
    
    // WebSocket hooks - 实时通信
    const webSocketHook = useWebSocket();

    // 修复：useChatMessages不再需要轮询参数
    const messageHook = useChatMessages(sessionHook.activeSessionId, handleAuthError, null);

    // 配置WebSocket回调函数
    useEffect(() => {
        webSocketHook.setCallbacks({
            onTaskComplete: (taskId, result) => {
                console.log('✅ [WebSocket] 任务完成:', taskId, result);
                messageHook.updateMessageByTaskId(taskId, result);
            },
            onTaskError: (taskId, error, errorType) => {
                console.error('❌ [WebSocket] 任务失败:', taskId, error);
                const errorMessage = messageUtils.createErrorMessage(new Error(error));
                messageHook.addMessage(errorMessage);
            },
            onTaskProgress: (taskId, progressData) => {
                console.log('📊 [WebSocket] 任务进度:', taskId, progressData);
                // 这里可以添加进度更新逻辑
            }
        });
    }, [webSocketHook, messageHook]);

    // 用户登录时建立WebSocket连接
    useEffect(() => {
        if (user && !webSocketHook.isConnected && !webSocketHook.isConnecting) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🔗 [CreativePage] 建立WebSocket连接');
                webSocketHook.connect(token);
            }
        }

        // 用户登出时断开连接
        if (!user && webSocketHook.isConnected) {
            console.log('🔌 [CreativePage] 断开WebSocket连接');
            webSocketHook.disconnect();
        }
    }, [user, webSocketHook]);

    // 获取会话媒体数据，用于@引用功能 - 使用messageHook的sessionMedia
    useEffect(() => {
        // 直接使用messageHook提供的sessionMedia数据，始终保持同步
        console.log('🔄 [CreativePage] 同步sessionMedia数据:', {
            messageHookLength: messageHook.sessionMedia?.length || 0,
            currentLength: sessionMedia.length,
            data: messageHook.sessionMedia?.slice(0, 2).map(item => ({ id: item.reference_id, type: item.type }))
        });
        
        if (messageHook.sessionMedia !== undefined) {
            setSessionMedia(messageHook.sessionMedia);
        }
    }, [messageHook.sessionMedia]);

    // 专门监控sessionMedia的更新，用于调试
    useEffect(() => {
        console.log('📊 [CreativePage] sessionMedia已更新:', {
            总数: sessionMedia.length,
            图片: sessionMedia.filter(item => item.type === 'image').length,
            视频: sessionMedia.filter(item => item.type === 'video').length,
            引用列表: sessionMedia.map(item => `@${item.reference_id}`).join(', ')
        });
    }, [sessionMedia]);

    // 聊天提交hook
    const submitHook = useChatSubmit({
        activeSessionId: sessionHook.activeSessionId,
        onCreateSession: sessionHook.createSession,
        onAddMessage: messageHook.addMessage,
        onAddMessages: messageHook.addMessages,
        onRemoveLoadingMessages: messageHook.removeLoadingMessages,
        onUpdateBalance: updateBalance,
        onAuthError: handleAuthError,
        onShowUpgradeModal: () => {
            console.log('Show upgrade modal');
            setShowUpgradeModal(true);
        },
    });

    // 客户端检查 - 防止SSR路由错误
    useEffect(() => {
        setIsClient(true);
    }, []);

    // 处理用户登录状态检查和重定向 - 修复死循环问题
    useEffect(() => {
        // 只在客户端执行路由操作
        if (!isClient) return;

        const token = localStorage.getItem('token');
        console.log('🔍 [CreativePage] Auth check:', {
            hasToken: !!token,
            hasUser: !!user,
            userLoading,
            authCheckComplete
        });

        // 如果没有token，直接标记检查完成并重定向
        if (!token) {
            console.log('🔍 [CreativePage] No token, redirecting to home');
            setAuthCheckComplete(true);
            router.push('/');
            return;
        }

        // 如果有token且有用户，标记检查完成
        if (token && user && !userLoading) {
            console.log('🔍 [CreativePage] Auth successful, setting authCheckComplete = true');
            setAuthCheckComplete(true);
            return;
        }

        // 如果有token但没有用户且不在加载中，可能是token过期
        if (token && !user && !userLoading) {
            console.log('🔍 [CreativePage] Token expired, clearing and redirecting');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            updateUser(null);
            setAuthCheckComplete(true);
            router.push('/');
            return;
        }

        // 如果有token且正在加载，等待加载完成
        if (token && userLoading) {
            console.log('🔍 [CreativePage] User loading, waiting...');
            return;
        }

        // 设置一个超时保护，防止无限加载
        const timeoutId = setTimeout(() => {
            console.log('🔍 [CreativePage] Auth check timeout, marking complete');
            setAuthCheckComplete(true);
        }, 5000); // 5秒超时

        return () => clearTimeout(timeoutId);
    }, [isClient, user, userLoading]); // 移除router依赖避免循环

    // 同步用户token余额数据
    useEffect(() => {
        if (user && user.tokens !== undefined) {
            setMembershipInfo({
                current_plan: user?.current_plan || 'Free',
                current_balance: user.tokens,
                is_expired: user?.tokens_expires_at ? new Date(user.tokens_expires_at) < new Date() : false,
                tokens_expires_at: user?.tokens_expires_at,
                total_generations: user?.total_generations || 0
            });
        }
    }, [user]);

    // 监控会话切换并加载媒体数据 - 简化版
    useEffect(() => {
        if (sessionHook.activeSessionId && user) {
            console.log('🎯 Session switched, using messageHook sessionMedia');
        }
    }, [sessionHook.activeSessionId, user]);

    // 监控消息变化 - 优化用户体验：登录后自动滚动到底部并聚焦输入框
    useEffect(() => {
        const messageCount = messageHook.displayMessages?.length || 0;
        
        if (messageCount > 0) {
            console.log('🎯 Messages updated, count:', messageCount);
            
            // 如果用户已认证且有历史消息，自动滚动到底部
            if (user && authCheckComplete && messageCount > 0) {
                console.log('📜 Auto-scrolling to bottom for better UX');
                
                // 延迟滚动，确保DOM已更新
                setTimeout(() => {
                    scrollToBottom(true);
                    
                    // 自动聚焦到输入框，让用户可以立即开始工作
                    if (inputRef.current) {
                        inputRef.current.focus();
                        console.log('⌨️ Input focused for immediate productivity');
                    }
                }, 300); // 稍微延长延迟确保所有组件都已渲染
            }
        }
    }, [messageHook.displayMessages?.length, user, authCheckComplete]);

    // 专门监控用户认证完成后的首次消息加载
    useEffect(() => {
        // 只在认证刚完成且有消息时触发一次
        if (user && authCheckComplete && messageHook.displayMessages?.length > 0) {
            console.log('🎯 User authenticated with existing messages, optimizing UX');
            
            // 确保用户看到最新的对话内容
            setTimeout(() => {
                scrollToBottom(false); // 使用快速滚动避免干扰
                
                // 聚焦输入框让用户可以立即继续对话
                if (inputRef.current) {
                    // 确保输入框是contentEditable div时的正确聚焦方式
                    if (inputRef.current.contentEditable === 'true') {
                        inputRef.current.focus();
                        // 将光标移动到内容末尾
                        const range = document.createRange();
                        const selection = window.getSelection();
                        range.selectNodeContents(inputRef.current);
                        range.collapse(false);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    } else {
                        // 传统input的聚焦方式
                        inputRef.current.focus();
                    }
                    console.log('🎯 Ready for user input - UX optimized!');
                }
            }, 500);
        }
    }, [user, authCheckComplete, messageHook.displayMessages?.length > 0]); // 只在关键状态变化时触发

    // 调试：监控sessionMedia变化
    useEffect(() => {
        console.log('📚 SessionMedia updated:', sessionMedia?.length || 0, 'items');
        if (sessionMedia?.length > 0) {
            console.log('📚 Available media for @mention:', sessionMedia.map(m => m.display_name));
        }
    }, [sessionMedia]);

    // 处理升级 - 增加调试信息
    const handleUpgrade = () => {
        console.log('🔄 [CreativePage] handleUpgrade called');
        console.log('🔄 [CreativePage] Current showUpgradeModal state:', showUpgradeModal);
        setShowUpgradeModal(true);
        console.log('🔄 [CreativePage] Setting showUpgradeModal to true');
        
        // 延迟检查状态是否真的更新了
        setTimeout(() => {
            console.log('🔄 [CreativePage] showUpgradeModal after 100ms:', showUpgradeModal);
        }, 100);
    };

    // 处理会话删除
    const handleDeleteSession = async (sessionId) => {
        try {
            await sessionHook.deleteSession(sessionId);
            toast.success('Session deleted successfully');
        } catch (error) {
            console.error('Failed to delete session:', error);
            toast.error('Failed to delete session');
        }
    };

    // 处理会话重命名
    const handleRenameSession = async (sessionId, newTitle) => {
        try {
            await sessionHook.renameSession(sessionId, newTitle);
            toast.success('Session renamed successfully');
        } catch (error) {
            console.error('Failed to rename session:', error);
            toast.error('Failed to rename session');
        }
    };

    // 处理输入变化 - 支持富文本和传统输入
    const handleInputChange = (e) => {
        let newValue = '';
        
        // 处理不同类型的输入：事件对象、字符串、或其他格式
        if (typeof e === 'string') {
            // 直接传递字符串值（来自FloatingMessageBar的textContent）
            newValue = e;
            setCurrentPrompt(e);
            console.log('🔍 [CreativePage] 接收到字符串输入:', e);
            return;
        } else if (e && e.target && typeof e.target.value === 'string') {
            // 标准事件对象
            newValue = e.target.value;
        } else if (e && typeof e.value === 'string') {
            // 某些自定义事件对象
            newValue = e.value;
        } else {
            console.warn('🚨 [CreativePage] 无法处理的输入格式:', e);
            return;
        }

        // Integrate @mention functionality for event objects
        if (e && e.target) {
            handleAtMentionInputChange(e, (event) => {
                if (event && event.target && typeof event.target.value === 'string') {
                    setCurrentPrompt(event.target.value);
                }
            });
        } else {
            // 直接更新状态（用于非事件对象的输入）
            setCurrentPrompt(newValue);
        }
    };

    // 处理键盘事件
    const handleKeyDown = (e) => {
        handleAtMentionKeyDown(e, (event) => {
            // 原始键盘事件处理逻辑可以在这里添加
        });
    };

    // 处理提交
    const handleSubmit = async (e) => {
        console.log('🚀 [CreativePage] handleSubmit被调用:', { 
            e, 
            currentPrompt, 
            currentFile,
            eventType: typeof e,
            promptLength: currentPrompt?.length,
            hasFile: !!currentFile,
            fileType: currentFile?.type,
            fileName: currentFile?.name
        });
        
        // 安全检查：只有当e是事件对象时才调用preventDefault
        if (e && typeof e === 'object' && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }

        // 如果e是字符串，说明是从FloatingMessageBar直接传递的文本内容
        if (typeof e === 'string') {
            console.log('🔍 [CreativePage] 接收到文本提交:', e);
            // 更新当前提示词（如果传入的文本不为空）
            if (e.trim()) {
                console.log('🔍 [CreativePage] 更新提示词:', { old: currentPrompt, new: e.trim() });
                setCurrentPrompt(e.trim());
                // 等待状态更新
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // 再次检查更新后的状态
        const finalPrompt = typeof e === 'string' ? e.trim() : currentPrompt;
        console.log('🔍 [CreativePage] 最终检查状态:', { finalPrompt, currentFile, hasFile: !!currentFile });

        if (!finalPrompt.trim() && !currentFile) {
            console.log('⚠️ [CreativePage] 没有提示词和文件，跳过提交');
            return;
        }

        try {
            // 检查是否是明确的电商流程触发（需要同时满足条件）
            const isEcommerceIntent = finalPrompt.includes('提取卖点') || finalPrompt.includes('卖点文案') || finalPrompt.includes('产品文案');
            const hasUploadedFile = currentFile || (currentFiles && currentFiles.length > 0);
            
            console.log('🔍 [CreativePage] 电商流程检查:', { 
                isEcommerceIntent, 
                hasUploadedFile, 
                prompt: finalPrompt,
                triggerEcommerce: isEcommerceIntent && hasUploadedFile
            });

            // 只有明确的电商意图 + 上传文件才触发电商流程
            if (isEcommerceIntent && hasUploadedFile) {
                console.log('🛒 [CreativePage] 触发电商流程');
                
                // 使用多文件状态进行检查
                const filesToCheck = currentFiles.length > 0 ? currentFiles : (currentFile ? [currentFile] : []);
                
                // 检查电商流程
                const ecommerceResult = await checkEcommerceFlow(finalPrompt, filesToCheck);
                console.log('🔍 [CreativePage] 电商流程检测结果:', ecommerceResult);
                
                if (ecommerceResult.is_ecommerce_flow) {
                    console.log('✅ [CreativePage] 电商流程成功，添加文字结果到聊天');
                    
                    // 创建电商结果消息（文字形式，不是UI组件）
                    const ecommerceTextResult = `📊 **产品卖点提取结果**

${ecommerceResult.result.data.extracted_texts?.map((text, index) => 
    `**图片 ${index + 1}:** ${text.texts?.length > 0 ? text.texts.join(', ') : '未检测到文字内容'}`
).join('\n\n') || ''}

${ecommerceResult.result.data.selling_points?.length > 0 ? 
    `**建议卖点:**\n${ecommerceResult.result.data.selling_points.map((point, index) => `${index + 1}. ${point}`).join('\n')}` : 
    '**建议卖点:** 可以根据产品特点自定义卖点文案'
}

💡 **下一步:** 您可以复制合适的卖点，上传或引用logo图片，然后输入指令生成营销图片。`;

                    // 添加到消息历史
                    messageHook.addMessage({
                        id: `ecommerce_${Date.now()}`,
                        sender: 'bot',
                        content: ecommerceTextResult,
                        timestamp: new Date()
                    });

                    // 清空输入状态
                    setCurrentPrompt('');
                    setCurrentFile(null);
                    setCurrentFiles([]);
                    clearReferences();

                    // 滚动到底部
                    setTimeout(() => scrollToBottom(), 100);
                    return;
                }
            }

            // 常规消息发送逻辑
            console.log('📨 [CreativePage] 发送常规消息');
            await sendRegularMessage();

        } catch (error) {
            console.error('Submit error:', error);
            // 使用现有的toast来显示错误
            toast.error(error.message || 'Failed to send message');
        }
    };

    // 处理文件变化（支持多文件）
    const handleFileChange = (files) => {
        console.log('📎 [CreativePage] 接收到文件变化:', { files, isArray: Array.isArray(files) });
        
        // 处理单文件兼容性
        if (files && !Array.isArray(files)) {
            setCurrentFile(files);
            return;
        }
        
        // 处理多文件
        if (Array.isArray(files) && files.length > 0) {
            // 为了兼容现有系统，currentFile设为第一个文件
            setCurrentFile(files[0]);
            // 存储所有文件用于电商流程等多文件场景
            setCurrentFiles(files);
        } else {
            setCurrentFile(null);
            setCurrentFiles([]);
        }
    };

    // 处理文件移除
    const handleFileRemove = () => {
        setCurrentFile(null);
        setCurrentFiles([]);
    };

    // 新增：多文件状态
    const [currentFiles, setCurrentFiles] = useState([]);

    // 检查是否触发电商流程 - 支持多文件
    const checkEcommerceFlow = async (message, uploadedFiles) => {
        console.log('🔍 [EcomFlow] 检测电商流程:', { message, uploadedFiles, isArray: Array.isArray(uploadedFiles) });
        
        // 处理文件参数的不同格式
        let filesToProcess = [];
        
        if (!uploadedFiles) {
            console.log('🔍 [EcomFlow] 没有上传文件，跳过电商流程');
            return { is_ecommerce_flow: false };
        }
        
        // 统一处理为数组格式
        if (Array.isArray(uploadedFiles)) {
            filesToProcess = uploadedFiles;
        } else {
            filesToProcess = [uploadedFiles];
        }
        
        console.log('🔍 [EcomFlow] 处理文件列表:', { count: filesToProcess.length, files: filesToProcess.map(f => f?.name || 'unknown') });

        // 批量上传文件获取真实URL
        const uploadedImageUrls = [];
        
        for (let i = 0; i < filesToProcess.length; i++) {
            const file = filesToProcess[i];
            let imageUrl = '';
            
            if (typeof file === 'string') {
                imageUrl = file;
            } else if (file && file.url) {
                imageUrl = file.url;
            } else if (file && file.preview) {
                imageUrl = file.preview;
            } else if (file instanceof File) {
                // 上传File对象获得真实URL
                console.log(`🔍 [EcomFlow] 上传第${i+1}个文件:`, file.name);
                try {
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    const uploadResponse = await fetch('/api/file/upload/', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'Authorization': `Token ${localStorage.getItem('token')}`,
                        }
                    });
                    
                    if (uploadResponse.ok) {
                        const uploadResult = await uploadResponse.json();
                        imageUrl = uploadResult.url;
                        console.log(`✅ [EcomFlow] 第${i+1}个文件上传成功:`, imageUrl);
                    } else {
                        const errorText = await uploadResponse.text();
                        console.error(`❌ [EcomFlow] 第${i+1}个文件上传失败:`, errorText);
                        continue; // 跳过失败的文件
                    }
                } catch (uploadError) {
                    console.error(`❌ [EcomFlow] 第${i+1}个文件上传异常:`, uploadError);
                    continue; // 跳过异常的文件
                }
            }
            
            if (imageUrl) {
                uploadedImageUrls.push(imageUrl);
            }
        }

        console.log('🔍 [EcomFlow] 最终图片URL列表:', uploadedImageUrls);

        if (uploadedImageUrls.length === 0) {
            console.log('🔍 [EcomFlow] 无法获取有效的图片URL');
            return { is_ecommerce_flow: false };
        }

        try {
            const response = await fetch('/api/ecommerce/trigger/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${localStorage.getItem('token')}`,
                },
                body: JSON.stringify({
                    message: message,
                    uploaded_images: uploadedImageUrls // 传递所有图片URL
                })
            });

            console.log('🔍 [EcomFlow] API响应状态:', response.status);
            
            if (response.ok) {
                const result = await response.json();
                console.log('🔍 [EcomFlow] API响应结果:', result);
                return result;
            } else {
                const errorText = await response.text();
                console.error('🔍 [EcomFlow] API错误响应:', errorText);
            }
            
            return { is_ecommerce_flow: false };
        } catch (error) {
            console.error('电商流程检测失败:', error);
            return { is_ecommerce_flow: false };
        }
    };

    // 发送常规消息（原有逻辑）
    const sendRegularMessage = async () => {
        // 处理@mentions
        const { message: processedMessage, references: messageReferences } = processMessageForSubmission(currentPrompt);

        try {
            const result = await submitHook.submitMessage({
                prompt: processedMessage,
                file: currentFile,
                mode: currentMode,
                references: messageReferences
            });

            // 清除输入
            setCurrentPrompt('');
            setCurrentFile(null);
            setCurrentFiles([]);  // 同时清空多文件状态
            clearReferences();

            // 滚动到底部
            setTimeout(() => scrollToBottom(), 100);

        } catch (error) {
            console.error('Submit failed:', error);
            if (error.response?.status === 402) {
                setShowUpgradeModal(true);
            }
            throw error; // 重新抛出错误，让handleSubmit处理
        }
    };

    // 处理引用移除
    const handleReferenceRemove = (reference, index) => {
        removeReference(reference, inputRef);
    };

    // 处理引用预览
    const handleReferencePreview = (reference) => {
        setPreviewReference(reference);
    };

    // 显示加载状态
    if (!isClient || userLoading || !authCheckComplete) {
        return (
            <div className="min-h-screen bg-[#0f0a1d] flex items-center justify-center">
                <div className="text-white">Loading...</div>
            </div>
        );
    }

    // 如果没有用户，重定向
    if (!user) {
        return null;
    }

    return (
        <>
            <Head>
                <title>Creative Studio - MirageMakers AI</title>
                <meta name="description" content="AI-powered creative studio for generating images and videos" />
            </Head>
            
            <div className="h-screen flex bg-[#0f0a1d] overflow-hidden">
                {/* 左侧侧边栏 - 类似chat_backup.js的会话历史栏 */}
                <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-black/25 backdrop-blur-xl flex-shrink-0 relative z-0`}>
                    <div className="w-80 h-full flex flex-col relative">
                        {/* 侧边栏头部 - GPT风格设计 */}
                        <div className="p-4 border-b border-white/10">
                            {/* 顶部品牌区域 - GPT风格 */}
                            <div className="flex items-center justify-between mb-3">
                                {/* Logo - 更大尺寸 */}
                                <div className="flex items-center">
                                    <span className="text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-purple-300 text-transparent bg-clip-text">
                                        MirageMakers AI
                                    </span>
                                </div>

                                {/* 收起按钮 - GPT风格 */}
                                <button
                                    onClick={() => setSidebarOpen(false)}
                                    className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                                    title={language === 'zh' ? '收起侧边栏' : 'Collapse sidebar'}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M15 18l-6-6 6-6" />
                                    </svg>
                                </button>
                            </div>

                            {/* New Chat 按钮 - GPT风格 */}
                            <button
                                onClick={sessionHook.createSession}
                                disabled={submitHook.isSubmitting}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-white hover:bg-white/10 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {/* 新建会话图标 - 编辑笔图标 */}
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                    <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                </svg>

                                {/* 文字 */}
                                <span className="font-medium text-sm">
                                    {language === 'zh' ? '新建对话' : 'New chat'}
                                </span>

                                {/* 加载指示器 */}
                                {submitHook.isSubmitting && (
                                    <div className="ml-auto">
                                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                    </div>
                                )}
                            </button>
                        </div>

                        {/* 会话列表 - 改进样式 */}
                        <div className="flex-1 overflow-y-auto px-4 pb-4">
                            <div className="space-y-2">
                                {sessionHook.sessions.map(s => (
                                    <SessionItem
                                        key={s.id}
                                        session={s}
                                        active={s.id === sessionHook.activeSessionId}
                                        onClick={() => sessionHook.switchToSession(s.id)}
                                        onDelete={handleDeleteSession}
                                        onRename={handleRenameSession}
                                    />
                                ))}
                            </div>
                        </div>

                        {/* 升级按钮 - GPT风格 */}
                        <div className="p-4 mt-auto border-t border-white/10">
                            <button
                                onClick={() => {
                                    console.log('🔄 [CreativePage] Sidebar upgrade button clicked');
                                    handleUpgrade();
                                }}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                            >
                                {/* 闪电升级图标 */}
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-amber-400">
                                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                                </svg>
                                <span className="font-medium text-sm">
                                    {language === 'zh' ? '升级计划' : 'Upgrade plan'}
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* 主内容区域 */}
                <div className="flex-1 flex flex-col min-w-0">
                    {/* 顶部导航栏 - 去掉边框 */}
                    <div className="flex items-center justify-between p-4 bg-black/10 backdrop-blur-xl flex-shrink-0 relative z-10">
                        <div className="flex items-center gap-3">
                            {/* 侧边栏切换按钮 */}
                            {!sidebarOpen && (
                                <button
                                    onClick={() => setSidebarOpen(true)}
                                    className="p-2.5 text-white/70 hover:text-white hover:bg-purple-500/10 rounded-xl transition-all duration-300"
                                    title={language === 'zh' ? '显示侧边栏' : 'Show sidebar'}
                                >
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M3 12h18M3 6h18M3 18h18" />
                                    </svg>
                                </button>
                            )}
                        </div>

                        <div className="flex items-center gap-3">
                            {/* 用户头像菜单 */}
                            {user && (
                                <UserAvatar
                                    user={user}
                                    size="md"
                                    showOnlineStatus={true}
                                    onUpgrade={() => {
                                        console.log('🔄 [CreativePage] UserAvatar onUpgrade triggered');
                                        handleUpgrade();
                                    }}
                                />
                            )}
                        </div>
                    </div>

                    {/* 聊天内容区域 */}
                    <div className="flex-1 flex flex-col min-h-0 relative z-0">
                        {/* 消息历史显示区域 */}
                        <MessageHistory
                            ref={messageHistoryRef}
                            messages={messageHook.displayMessages || []}
                            isLoading={submitHook.isSubmitting || messageHook.loading}
                            onScrollToBottom={scrollToBottom}
                            className="flex-1 overflow-y-auto"
                        />

                        {/* 智能悬浮消息栏 */}
                        <FloatingMessageBar
                            value={currentPrompt}
                            onChange={handleInputChange}
                            onSubmit={handleSubmit}
                            onKeyDown={handleKeyDown}
                            placeholder="Ask anything..."
                            isLoading={submitHook.isSubmitting}
                            userBalance={membershipInfo.current_balance}
                            currentFile={currentFile}
                            currentFiles={currentFiles}
                            onFileChange={handleFileChange}
                            onFileRemove={handleFileRemove}
                            inputRef={inputRef}
                            scrollContainerRef={messageHistoryRef}
                            selectedReferences={selectedReferences}
                            visualReferences={visualReferences}
                            onReferenceRemove={handleReferenceRemove}
                            onReferencePreview={handleReferencePreview}
                        />

                        {/* Reference Selector for @mentions - 引用选择器 */}
                        {console.log('🔍 [CreativePage] ReferenceSelector渲染状态:', {
                            isOpen: isReferenceDropdownOpen,
                            sessionMediaCount: sessionMedia?.length || 0,
                            sessionMediaFirst3: sessionMedia?.slice(0, 3).map(m => ({
                                reference_id: m.reference_id,
                                type: m.type,
                                hasUrl: !!m.url
                            })) || [],
                            searchQuery: referenceSearchQuery,
                            selectedReferencesCount: selectedReferences?.length || 0
                        })}
                        <ReferenceSelector
                            isOpen={isReferenceDropdownOpen}
                            onClose={closeReferenceDropdown}
                            onSelect={insertBatchReferences}
                            searchQuery={referenceSearchQuery}
                            position={dropdownPosition}
                            sessionMedia={sessionMedia}
                            selectedReferences={selectedReferences}
                        />

                        {/* Reference Preview Modal - 引用预览模态框 */}
                        {previewReference && (
                            <ImageModal
                                src={previewReference.url}
                                alt={previewReference.display_name}
                                isOpen={!!previewReference}
                                onClose={() => setPreviewReference(null)}
                            />
                        )}

                        {/* Upgrade Modal - 升级模态框 */}
                        {console.log('🔍 [CreativePage] Rendering UpgradeModal:', { showUpgradeModal, isOpen: showUpgradeModal })}
                        <UpgradeModal
                            isOpen={showUpgradeModal}
                            onClose={() => {
                                console.log('🔄 [CreativePage] UpgradeModal onClose called');
                                setShowUpgradeModal(false);
                            }}
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default CreativePage;