import React, { forwardRef } from 'react';
import MessageBubble from '../../chat/components/MessageBubble';
import EcommerceStreamFlow from './EcommerceStreamFlow';

/**
 * Message History Component
 * 消息历史显示组件 - 复用chat的MessageBubble
 */
const MessageHistory = forwardRef(({
    messages = [],
    isLoading = false,
    onScrollToBottom,
    className = ''
}, ref) => {

    // 不再显示欢迎界面，直接显示聊天界面

    // 调试函数 - 检查消息的sender分布 (简化版)
    React.useEffect(() => {
        console.log('📊 MessageHistory收到消息更新:', {
            total: messages.length,
            isLoading,
            messages: messages.slice(0, 2).map(m => ({ id: m.id, sender: m.sender, content: m.content?.substring(0, 30) }))
        });
        
        if (messages.length > 0) {
            const distribution = messages.reduce((acc, msg) => {
                acc[msg.sender] = (acc[msg.sender] || 0) + 1;
                return acc;
            }, {});
            console.log('📊 MessageHistory消息分布:', {
                total: messages.length,
                distribution
            });
        }
    }, [messages, messages.length, isLoading]);

    // 渲染单条消息
    const renderMessage = (message, index) => {
        // 常规消息处理 - 移除电商流程的特殊处理
        let isUser = message.sender === 'user';
        
        // 备用判断逻辑：如果sender不是明确的'user'或'bot'，则基于内容推断
        if (!isUser && message.sender !== 'bot') {
            const content = message.content || '';
            const isLikelyUserMessage = 
                content.startsWith('Media_') || // @引用格式
                (content.length < 200 && // 较短的消息
                 !content.includes('生成成功') && 
                 !content.includes('Processing') && 
                 !content.includes('成功') &&
                 !content.includes('错误') &&
                 !content.includes('Sorry, we cannot process') &&
                 !message.image_url && 
                 !message.video_url); // 没有AI生成的媒体内容
            
            if (isLikelyUserMessage) {
                isUser = true;
                console.log(`🔍 Detected user message by content: "${content.substring(0, 30)}..."`);
            }
        }
        
        // 调试日志 - 只记录前2条消息
        if (index < 2) {
            console.log(`💬 Msg ${index}: ${message.sender}→${isUser ? 'RIGHT' : 'LEFT'}`);
        }
        
        return (
            <MessageBubble
                key={`${message.id || index}-${message.timestamp || Date.now()}`}
                message={message}
                isUser={isUser}
            />
        );
    };

    // 显示消息历史
    return (
        <div 
            ref={ref}
            className={`h-full overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent ${className}`}
        >
            {/* 消息列表 - 现代化布局 */}
            <div className="max-w-5xl mx-auto px-6 py-8 space-y-2">
                {/* 加载状态 - 现代化设计 */}
                {isLoading && messages.length === 0 && (
                    <div className="flex items-center justify-center h-96">
                        <div className="text-center text-white/60">
                            <div className="relative mb-6">
                                <div className="w-12 h-12 border-4 border-white/10 border-t-purple-500 rounded-full animate-spin mx-auto"></div>
                                <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-t-blue-400 rounded-full animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '0.8s' }}></div>
                            </div>
                            <p className="text-base font-medium">Loading messages...</p>
                            <p className="text-sm text-white/40 mt-2">Please wait while we fetch your conversation</p>
                        </div>
                    </div>
                )}

                {/* 空状态 - 精美设计 */}
                {messages.length === 0 && !isLoading && (
                    <div className="flex items-center justify-center h-96">
                        <div className="text-center text-white/60 max-w-md">
                            <div className="relative mb-8">
                                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-2xl flex items-center justify-center backdrop-blur-sm ring-1 ring-white/10">
                                    <svg className="w-10 h-10 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                </div>
                                <div className="absolute -inset-2 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-3xl blur-xl"></div>
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-white/80">Start Your Creative Journey</h3>
                            <p className="text-base text-white/50 leading-relaxed">Share your ideas, describe what you want to create, and watch AI bring your vision to life with stunning images and videos.</p>
                        </div>
                    </div>
                )}

                {messages.map((message, index) => {
                    return renderMessage(message, index);
                })}

                {/* 加载状态 */}
                {isLoading && (
                    <div className="flex justify-start mb-8">
                        <div className="bg-black/40 backdrop-blur-xl text-white p-6 rounded-[2rem] rounded-bl-lg shadow-lg max-w-[85%]">
                            <div className="flex items-center space-x-3">
                                <div className="relative">
                                    <div className="w-6 h-6 bg-gradient-to-br from-purple-400 via-violet-500 to-blue-500 rounded-lg flex items-center justify-center shadow-lg">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" className="text-white">
                                            <path d="M12 2L2 7V17L12 22L22 17V7L12 2Z" stroke="currentColor" strokeWidth="2" />
                                        </svg>
                                    </div>
                                    <div className="absolute -inset-1 rounded-lg border-2 border-purple-400/40 animate-spin" style={{ animationDuration: '2s' }}></div>
                                </div>
                                <div className="text-white/90 text-sm font-medium">
                                    Generating your content...
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* 底部占位，确保消息不被输入框遮挡 */}
                <div className="h-96"></div>
            </div>
        </div>
    );
});

MessageHistory.displayName = 'MessageHistory';

export default MessageHistory; 