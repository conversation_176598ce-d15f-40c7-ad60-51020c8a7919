import React from 'react';

const CreativeSidebar = ({
    activeItem,
    onNavigate,
    user,
    membershipInfo,
    onUpgrade,
    collapsed = false,
    onToggleCollapse
}) => {
    // 导航项目配置 - 使用emoji图标简化
    const navigationItems = [
        {
            id: 'home',
            icon: '🏠',
            label: 'Home',
            tooltip: '返回首页'
        },
        {
            id: 'creative',
            icon: '✨',
            label: 'Create',
            tooltip: '创意工作台'
        },
        {
            id: 'assets',
            icon: '🗂️',
            label: 'Assets',
            tooltip: '素材管理'
        },
        {
            id: 'canvas',
            icon: '📚',
            label: 'Canvas',
            tooltip: '画布工具'
        }
    ];

    return (
        <div className="w-20 h-full bg-gradient-to-b from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] border-r border-white/10 flex flex-col items-center py-6 relative overflow-hidden">
            {/* 背景装饰层 */}
            <div className="absolute inset-0 bg-gradient-to-b from-purple-900/5 via-transparent to-blue-900/5"></div>
            <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white/5 to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-purple-600/10 to-transparent"></div>

            {/* Logo区域 */}
            <div className="relative z-10 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 group cursor-pointer">
                    <span className="text-white font-bold text-xl group-hover:scale-110 transition-transform duration-300">M</span>

                    {/* Logo悬浮提示 */}
                    <div className="absolute left-full ml-3 top-1/2 -translate-y-1/2 px-3 py-2 bg-black/90 backdrop-blur-md text-white text-sm rounded-lg border border-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                        MirageMakers AI
                        <div className="absolute right-full top-1/2 -translate-y-1/2 border-4 border-transparent border-r-black/90"></div>
                    </div>
                </div>
            </div>

            {/* 导航按钮区域 */}
            <div className="relative z-10 flex-1 flex flex-col gap-4">
                {navigationItems.map((item) => (
                    <button
                        key={item.id}
                        onClick={() => onNavigate(item.id)}
                        className={`group relative w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
                            activeItem === item.id
                                ? 'bg-gradient-to-br from-purple-600 to-pink-600 shadow-lg shadow-purple-500/30 text-white scale-105'
                                : 'bg-white/5 hover:bg-white/10 text-white/60 hover:text-white hover:scale-105 backdrop-blur-sm border border-white/10 hover:border-purple-500/30'
                        }`}
                    >
                        <span className="text-xl group-hover:scale-110 transition-transform duration-300">
                            {item.icon}
                        </span>

                        {/* 活动状态指示器 */}
                        {activeItem === item.id && (
                            <div className="absolute -left-3 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-purple-400 to-pink-400 rounded-full"></div>
                        )}

                        {/* 悬浮提示 */}
                        <div className="absolute left-full ml-3 top-1/2 -translate-y-1/2 px-3 py-2 bg-black/90 backdrop-blur-md text-white text-sm rounded-lg border border-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                            {item.tooltip}
                            <div className="absolute right-full top-1/2 -translate-y-1/2 border-4 border-transparent border-r-black/90"></div>
                        </div>
                    </button>
                ))}
            </div>

            {/* Token余额显示 */}
            <div className="relative z-10 mb-6">
                <div className="w-12 h-12 bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl flex flex-col items-center justify-center group cursor-pointer hover:border-purple-500/30 transition-all duration-300">
                    <div className="text-purple-400 text-xs font-medium">💎</div>
                    <div className="text-white text-xs font-bold">
                        {membershipInfo?.current_balance > 999
                            ? `${Math.floor(membershipInfo.current_balance / 1000)}k`
                            : membershipInfo?.current_balance || 0
                        }
                    </div>

                    {/* Tokens 悬浮提示 */}
                    <div className="absolute left-full ml-3 top-1/2 -translate-y-1/2 px-3 py-2 bg-black/90 backdrop-blur-md text-white text-sm rounded-lg border border-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                        Token Balance: {membershipInfo?.current_balance?.toLocaleString() || 0}
                        <div className="absolute right-full top-1/2 -translate-y-1/2 border-4 border-transparent border-r-black/90"></div>
                    </div>
                </div>
            </div>

            {/* 升级按钮 */}
            <div className="relative z-10">
                <button
                    onClick={onUpgrade}
                    className="group w-12 h-10 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-xl flex items-center justify-center transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/30 relative overflow-hidden"
                >
                    {/* 升级图标 */}
                    <svg className="w-5 h-5 text-white group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>

                    {/* 光效动画 */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%]"></div>

                    {/* 悬浮提示 */}
                    <div className="absolute left-full ml-3 top-1/2 -translate-y-1/2 px-3 py-2 bg-black/90 backdrop-blur-md text-white text-sm rounded-lg border border-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                        升级会员
                        <div className="absolute right-full top-1/2 -translate-y-1/2 border-4 border-transparent border-r-black/90"></div>
                    </div>
                </button>
            </div>
        </div>
    );
};

export default CreativeSidebar;
