import React, { useState, useRef } from 'react';
import { Send, Upload, Image as ImageIcon, Video, Wand2, AtSign, Paperclip, Mic, Settings } from 'lucide-react';

const CreationPanel = ({ 
    currentPrompt, 
    onPromptChange, 
    onSubmit, 
    isLoading, 
    onFileUpload,
    uploadedFiles = [],
    onRemoveFile 
}) => {
    const [mode, setMode] = useState('auto'); // auto, image, video, text
    const [showMentions, setShowMentions] = useState(false);
    const [isRecording, setIsRecording] = useState(false);
    const fileInputRef = useRef(null);
    const textareaRef = useRef(null);

    const modes = [
        { id: 'auto', name: '智能模式', icon: Wand2, color: 'from-purple-500 to-pink-500' },
        { id: 'image', name: '图片生成', icon: ImageIcon, color: 'from-blue-500 to-cyan-500' },
        { id: 'video', name: '视频生成', icon: Video, color: 'from-green-500 to-teal-500' },
    ];

    const mentionOptions = [
        { id: 'style', name: '@风格', description: '艺术风格模板' },
        { id: 'template', name: '@模板', description: '快速模板' },
        { id: 'history', name: '@历史', description: '历史提示词' },
    ];

    const handleFileSelect = (event) => {
        const files = Array.from(event.target.files);
        files.forEach(file => {
            if (onFileUpload) {
                onFileUpload(file);
            }
        });
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (currentPrompt.trim() && !isLoading) {
                onSubmit();
            }
        }
        
        if (e.key === '@') {
            setShowMentions(true);
        }
    };

    const insertMention = (mention) => {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newValue = currentPrompt.slice(0, start) + mention.name + ' ' + currentPrompt.slice(end);
        onPromptChange(newValue);
        setShowMentions(false);
        
        // 重新聚焦并设置光标位置
        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(start + mention.name.length + 1, start + mention.name.length + 1);
        }, 0);
    };

    return (
        <div className="bg-black/30 backdrop-blur-lg border-t border-white/10 p-6">
            <div className="max-w-6xl mx-auto">
                {/* 模式选择 */}
                <div className="flex items-center gap-3 mb-4">
                    <span className="text-white/60 text-sm font-medium">创作模式:</span>
                    <div className="flex gap-2">
                        {modes.map((modeOption) => {
                            const Icon = modeOption.icon;
                            return (
                                <button
                                    key={modeOption.id}
                                    onClick={() => setMode(modeOption.id)}
                                    className={`flex items-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                                        mode === modeOption.id
                                            ? `bg-gradient-to-r ${modeOption.color} text-white shadow-lg shadow-purple-500/25`
                                            : 'bg-white/10 text-white/70 hover:bg-white/20'
                                    }`}
                                >
                                    <Icon className="w-4 h-4" />
                                    {modeOption.name}
                                </button>
                            );
                        })}
                    </div>
                </div>

                {/* 上传的文件 */}
                {uploadedFiles.length > 0 && (
                    <div className="flex gap-2 mb-4">
                        {uploadedFiles.map((file, index) => (
                            <div key={index} className="relative bg-black/40 rounded-lg p-3 border border-white/20">
                                <div className="flex items-center gap-2">
                                    <Paperclip className="w-4 h-4 text-white/60" />
                                    <span className="text-white text-sm truncate max-w-32">{file.name}</span>
                                    <button 
                                        onClick={() => onRemoveFile && onRemoveFile(index)}
                                        className="text-white/60 hover:text-white ml-2"
                                    >
                                        ×
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}

                {/* 主输入区域 */}
                <div className="relative">
                    <div className="bg-black/40 backdrop-blur-sm rounded-2xl border border-white/20 p-4 focus-within:border-purple-500/50 transition-colors">
                        <textarea
                            ref={textareaRef}
                            value={currentPrompt}
                            onChange={(e) => onPromptChange(e.target.value)}
                            onKeyDown={handleKeyDown}
                            placeholder="描述你想要创建的内容... (支持 @mention 快速插入模板)"
                            className="w-full bg-transparent text-white placeholder-white/40 border-none outline-none resize-none min-h-[80px] max-h-[200px] text-sm leading-relaxed"
                            style={{ scrollbarWidth: 'thin' }}
                        />

                        {/* @mention 下拉菜单 */}
                        {showMentions && (
                            <div className="absolute bottom-full left-4 right-4 mb-2 bg-black/80 backdrop-blur-lg rounded-xl border border-white/20 shadow-2xl z-50">
                                {mentionOptions.map((option) => (
                                    <button
                                        key={option.id}
                                        onClick={() => insertMention(option)}
                                        className="w-full text-left px-4 py-3 hover:bg-white/10 transition-colors first:rounded-t-xl last:rounded-b-xl"
                                    >
                                        <div className="text-white font-medium text-sm">{option.name}</div>
                                        <div className="text-white/60 text-xs">{option.description}</div>
                                    </button>
                                ))}
                            </div>
                        )}

                        {/* 底部工具栏 */}
                        <div className="flex items-center justify-between mt-4 pt-4 border-t border-white/10">
                            <div className="flex items-center gap-3">
                                {/* 文件上传 */}
                                <button 
                                    onClick={() => fileInputRef.current?.click()}
                                    className="flex items-center gap-2 px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white/70 hover:text-white transition-all text-sm"
                                >
                                    <Upload className="w-4 h-4" />
                                    上传文件
                                </button>

                                {/* 语音输入 */}
                                <button 
                                    onClick={() => setIsRecording(!isRecording)}
                                    className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-all ${
                                        isRecording 
                                            ? 'bg-red-500/20 text-red-400 border border-red-500/30' 
                                            : 'bg-white/10 hover:bg-white/20 text-white/70 hover:text-white'
                                    }`}
                                >
                                    <Mic className="w-4 h-4" />
                                    {isRecording ? '录音中...' : '语音输入'}
                                </button>

                                {/* @ 快捷键提示 */}
                                <button 
                                    onClick={() => setShowMentions(!showMentions)}
                                    className="flex items-center gap-2 px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white/70 hover:text-white transition-all text-sm"
                                >
                                    <AtSign className="w-4 h-4" />
                                    快速插入
                                </button>
                            </div>

                            {/* 发送按钮 */}
                            <button 
                                onClick={onSubmit}
                                disabled={!currentPrompt.trim() || isLoading}
                                className="flex items-center gap-2 px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium text-sm transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-purple-500/25"
                            >
                                {isLoading ? (
                                    <>
                                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                        创作中...
                                    </>
                                ) : (
                                    <>
                                        <Send className="w-4 h-4" />
                                        开始创作
                                    </>
                                )}
                            </button>
                        </div>
                    </div>

                    {/* 隐藏的文件输入 */}
                    <input
                        ref={fileInputRef}
                        type="file"
                        multiple
                        accept="image/*,video/*"
                        onChange={handleFileSelect}
                        className="hidden"
                    />
                </div>

                {/* 提示信息 */}
                <div className="flex items-center justify-between mt-3 text-xs text-white/40">
                    <div className="flex items-center gap-4">
                        <span>按 Enter 发送，Shift+Enter 换行</span>
                        <span>支持上传图片和视频文件</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <span>当前模式: {modes.find(m => m.id === mode)?.name}</span>
                        <Settings className="w-3 h-3" />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreationPanel;
