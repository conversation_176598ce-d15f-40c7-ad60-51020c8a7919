import React, { useState, useEffect } from 'react';

const CreationOptions = ({ onOptionsChange, isVisible }) => {
    // 创作选项状态
    const [options, setOptions] = useState({
        creationType: 'image', // 'image' | 'video'
        model: 'auto', // 'auto' | 'pro' | 'max'
        aspectRatio: '16:9', // '21:9' | '16:9' | '4:3' | '1:1' | '3:4' | '9:16'
        resolution: '1080p', // '720p' | '1080p' | '4k'
        duration: '5s' // '5s' | '10s'
    });

    // 模型配置
    const modelConfigs = {
        auto: {
            name: 'Auto',
            subtitle: 'gpt-image-1veo3',
            supportedTypes: ['image'],
            supportedRatios: ['1:1', '4:3', '16:9'],
            supportedResolutions: ['720p', '1080p'],
            defaultRatio: '16:9',
            defaultResolution: '1080p',
            color: 'from-blue-500 to-blue-600'
        },
        pro: {
            name: 'Pro',
            subtitle: 'Seedance',
            supportedTypes: ['video'],
            supportedRatios: ['16:9', '9:16', '1:1'],
            supportedResolutions: ['1080p', '4k'],
            supportedDurations: ['5s', '10s'],
            defaultRatio: '16:9',
            defaultResolution: '1080p',
            defaultDuration: '5s',
            color: 'from-purple-500 to-purple-600'
        },
        max: {
            name: 'Max',
            subtitle: 'veo3',
            supportedTypes: ['video'],
            supportedRatios: ['21:9', '16:9', '9:16', '1:1'],
            supportedResolutions: ['4k', '1080p'],
            supportedDurations: ['5s', '10s'],
            defaultRatio: '21:9',
            defaultResolution: '4k',
            defaultDuration: '10s',
            color: 'from-orange-500 to-orange-600'
        }
    };

    // 当模型变化时自动调整其他选项
    useEffect(() => {
        const config = modelConfigs[options.model];
        const newOptions = { ...options };

        // 调整创作类型
        if (config.supportedTypes.length === 1) {
            newOptions.creationType = config.supportedTypes[0];
        }

        // 调整比例
        if (!config.supportedRatios.includes(options.aspectRatio)) {
            newOptions.aspectRatio = config.defaultRatio;
        }

        // 调整分辨率
        if (!config.supportedResolutions.includes(options.resolution)) {
            newOptions.defaultResolution = config.defaultResolution;
        }

        // 调整时长
        if (newOptions.creationType === 'video' && config.supportedDurations) {
            if (!config.supportedDurations.includes(options.duration)) {
                newOptions.duration = config.defaultDuration;
            }
        }

        setOptions(newOptions);
    }, [options.model]);

    // 选项改变处理
    const handleOptionChange = (key, value) => {
        const newOptions = { ...options, [key]: value };
        setOptions(newOptions);
        onOptionsChange?.(newOptions);
    };

    // 检查选项是否可用
    const isOptionAvailable = (type, value) => {
        const config = modelConfigs[options.model];
        switch (type) {
            case 'ratio':
                return config.supportedRatios.includes(value);
            case 'resolution':
                return config.supportedResolutions.includes(value);
            case 'duration':
                return config.supportedDurations?.includes(value) || false;
            default:
                return true;
        }
    };

    if (!isVisible) return null;

    return (
        <div className="w-full bg-gray-900/95 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 space-y-4">
            {/* 模型选择 */}
            <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">AI 模型</label>
                <div className="flex gap-2">
                    {Object.entries(modelConfigs).map(([key, config]) => (
                        <button
                            key={key}
                            onClick={() => handleOptionChange('model', key)}
                            className={`flex-1 p-3 rounded-lg border-2 transition-all duration-200 ${
                                options.model === key
                                    ? `bg-gradient-to-r ${config.color} border-transparent text-white shadow-lg`
                                    : 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500 hover:bg-gray-700/50'
                            }`}
                        >
                            <div className="text-sm font-semibold">{config.name}</div>
                            <div className="text-xs opacity-80">{config.subtitle}</div>
                        </button>
                    ))}
                </div>
            </div>

            {/* 创作类型 - 仅在Auto模型显示，其他自动隐藏 */}
            {options.model === 'auto' && (
                <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">创作类型</label>
                    <div className="flex gap-2">
                        {['image', 'video'].map((type) => (
                            <button
                                key={type}
                                onClick={() => handleOptionChange('creationType', type)}
                                disabled={!modelConfigs[options.model].supportedTypes.includes(type)}
                                className={`flex-1 py-2 px-4 rounded-lg border-2 transition-all duration-200 text-sm ${
                                    options.creationType === type
                                        ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                                        : modelConfigs[options.model].supportedTypes.includes(type)
                                        ? 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
                                        : 'border-gray-700 bg-gray-800/20 text-gray-500 cursor-not-allowed opacity-50'
                                }`}
                            >
                                {type === 'image' ? '🖼️ 图片生成' : '🎬 视频生成'}
                            </button>
                        ))}
                    </div>
                </div>
            )}

            {/* 比例选择 */}
            <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">画面比例</label>
                <div className="grid grid-cols-3 gap-2">
                    {['21:9', '16:9', '4:3', '1:1', '3:4', '9:16'].map((ratio) => {
                        const available = isOptionAvailable('ratio', ratio);
                        return (
                            <button
                                key={ratio}
                                onClick={() => available && handleOptionChange('aspectRatio', ratio)}
                                disabled={!available}
                                className={`py-2 px-3 rounded-lg border-2 transition-all duration-200 text-sm ${
                                    options.aspectRatio === ratio
                                        ? 'border-purple-500 bg-purple-500/20 text-purple-400'
                                        : available
                                        ? 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
                                        : 'border-gray-700 bg-gray-800/20 text-gray-500 cursor-not-allowed opacity-40'
                                }`}
                            >
                                {ratio}
                            </button>
                        );
                    })}
                </div>
            </div>

            {/* 分辨率选择 */}
            <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">分辨率</label>
                <div className="flex gap-2">
                    {['720p', '1080p', '4k'].map((res) => {
                        const available = isOptionAvailable('resolution', res);
                        return (
                            <button
                                key={res}
                                onClick={() => available && handleOptionChange('resolution', res)}
                                disabled={!available}
                                className={`flex-1 py-2 px-4 rounded-lg border-2 transition-all duration-200 text-sm ${
                                    options.resolution === res
                                        ? 'border-green-500 bg-green-500/20 text-green-400'
                                        : available
                                        ? 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
                                        : 'border-gray-700 bg-gray-800/20 text-gray-500 cursor-not-allowed opacity-40'
                                }`}
                            >
                                {res.toUpperCase()}
                            </button>
                        );
                    })}
                </div>
            </div>

            {/* 时长选择 - 仅视频显示 */}
            {options.creationType === 'video' && (
                <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">视频时长</label>
                    <div className="flex gap-2">
                        {['5s', '10s'].map((duration) => {
                            const available = isOptionAvailable('duration', duration);
                            return (
                                <button
                                    key={duration}
                                    onClick={() => available && handleOptionChange('duration', duration)}
                                    disabled={!available}
                                    className={`flex-1 py-2 px-4 rounded-lg border-2 transition-all duration-200 text-sm ${
                                        options.duration === duration
                                            ? 'border-orange-500 bg-orange-500/20 text-orange-400'
                                            : available
                                            ? 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
                                            : 'border-gray-700 bg-gray-800/20 text-gray-500 cursor-not-allowed opacity-40'
                                    }`}
                                >
                                    {duration}
                                </button>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* 当前配置摘要 */}
            <div className="pt-2 border-t border-gray-700/50">
                <div className="text-xs text-gray-400">
                    当前配置: {modelConfigs[options.model].name} • {options.aspectRatio} • {options.resolution}
                    {options.creationType === 'video' && ` • ${options.duration}`}
                </div>
            </div>
        </div>
    );
};

export default CreationOptions; 