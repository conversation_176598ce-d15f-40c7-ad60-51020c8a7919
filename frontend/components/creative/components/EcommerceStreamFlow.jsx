import React, { useState, useEffect } from 'react';
import { ShoppingCart, Eye, Download, Check, RefreshCw, Image, Type, Palette } from 'lucide-react';

const EcommerceStreamFlow = ({ stream_id, initial_step, initial_data, onComplete }) => {
    const [currentStep, setCurrentStep] = useState(initial_step);
    const [streamData, setStreamData] = useState(initial_data || {});
    const [loading, setLoading] = useState(false);
    const [selectedCopy, setSelectedCopy] = useState('');
    const [selectedImageType, setSelectedImageType] = useState('');
    const [referenceImage, setReferenceImage] = useState(null);

    const stepTitles = {
        'user_confirm': '确认卖点文案',
        'image_type_select': '选择图片类型',
        'image_generate': '图像生成中',
        'image_enhance': '图像增强',
        'completed': '流程完成'
    };

    const imageTypes = {
        'main': '主图',
        'selling_points': '卖点图',
        'lifestyle': '生活场景图',
        'specs': '参数汇总图'
    };

    // 处理步骤
    const processStep = async (step, userInput = {}) => {
        setLoading(true);
        try {
            const response = await fetch(`/api/ecommerce/stream/${stream_id}/step/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${localStorage.getItem('token')}`,
                },
                body: JSON.stringify({
                    step: step,
                    user_input: userInput
                })
            });

            if (response.ok) {
                const result = await response.json();
                setCurrentStep(result.result.step);
                setStreamData(result.result.data);
            }
        } catch (error) {
            console.error('流程处理失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 确认文案
    const handleConfirmCopy = async () => {
        await processStep('user_confirm', {
            confirmed_copy: selectedCopy,
            reference_image: referenceImage
        });
    };

    // 选择图片类型
    const handleImageTypeSelect = async (imageType) => {
        setSelectedImageType(imageType);
        await processStep('image_type_select', {
            image_type: imageType
        });
    };

    // 渲染不同步骤的UI
    const renderStepContent = () => {
        switch (currentStep) {
            case 'user_confirm':
                return (
                    <div className="space-y-4">
                        <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-4">
                            <h4 className="text-white/90 font-medium mb-3 flex items-center gap-2">
                                <Type className="w-5 h-5 text-purple-400" />
                                提取的卖点文案
                            </h4>
                            
                            {streamData.selling_points?.map((point, index) => (
                                <div
                                    key={index}
                                    className={`bg-black/20 rounded-lg p-3 border cursor-pointer mb-2 transition-all
                                        ${selectedCopy === point.title 
                                            ? 'border-purple-500 bg-purple-500/10' 
                                            : 'border-white/10 hover:border-purple-500/50'
                                        }`}
                                    onClick={() => setSelectedCopy(point.title)}
                                >
                                    <div className="flex items-start gap-3">
                                        <div className={`w-4 h-4 rounded-full border-2 mt-1 
                                            ${selectedCopy === point.title 
                                                ? 'border-purple-500 bg-purple-500' 
                                                : 'border-white/30'
                                            }`}>
                                            {selectedCopy === point.title && (
                                                <Check className="w-2 h-2 text-white m-0.5" />
                                            )}
                                        </div>
                                        <div className="flex-1">
                                            <p className="text-white/90 text-sm font-medium">{point.title}</p>
                                            {point.description && (
                                                <p className="text-white/60 text-xs mt-1">{point.description}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}

                            <div className="mt-4">
                                <label className="block text-white/70 text-sm mb-2">或输入自定义文案:</label>
                                <textarea
                                    className="w-full bg-white/5 rounded-lg p-3 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                                    rows="3"
                                    value={selectedCopy}
                                    onChange={(e) => setSelectedCopy(e.target.value)}
                                    placeholder="输入您的产品卖点文案..."
                                />
                            </div>

                            <div className="mt-4">
                                <label className="block text-white/70 text-sm mb-2">参考图片 (可选):</label>
                                <input
                                    type="file"
                                    accept="image/*"
                                    className="w-full text-white/70 text-sm"
                                    onChange={(e) => setReferenceImage(e.target.files[0])}
                                />
                            </div>

                            <button
                                onClick={handleConfirmCopy}
                                disabled={!selectedCopy.trim() || loading}
                                className="w-full mt-4 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-500 
                                         text-white py-2 px-4 rounded-lg transition-colors"
                            >
                                {loading ? '处理中...' : '确认文案'}
                            </button>
                        </div>
                    </div>
                );

            case 'image_type_select':
                return (
                    <div className="space-y-4">
                        <div className="bg-gradient-to-r from-blue-500/20 to-green-500/20 rounded-xl p-4">
                            <h4 className="text-white/90 font-medium mb-3 flex items-center gap-2">
                                <Image className="w-5 h-5 text-blue-400" />
                                选择图片类型
                            </h4>
                            
                            <div className="grid grid-cols-2 gap-3">
                                {Object.entries(imageTypes).map(([type, name]) => (
                                    <button
                                        key={type}
                                        onClick={() => handleImageTypeSelect(type)}
                                        disabled={loading}
                                        className="bg-black/20 hover:bg-black/30 border border-white/10 
                                                 hover:border-blue-500/50 rounded-lg p-4 text-center transition-all"
                                    >
                                        <div className="text-white/90 font-medium">{name}</div>
                                        <div className="text-white/60 text-xs mt-1">
                                            {type === 'main' && '电商主图，白底产品'}
                                            {type === 'selling_points' && '图文结合，突出卖点'}
                                            {type === 'lifestyle' && '生活场景，真实使用'}
                                            {type === 'specs' && '参数规格，数据展示'}
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                );

            case 'image_generate':
                return (
                    <div className="space-y-4">
                        <div className="bg-gradient-to-r from-green-500/20 to-yellow-500/20 rounded-xl p-4">
                            <h4 className="text-white/90 font-medium mb-3 flex items-center gap-2">
                                <RefreshCw className="w-5 h-5 text-green-400 animate-spin" />
                                图像生成中
                            </h4>
                            
                            <div className="text-center py-8">
                                <div className="w-16 h-16 border-4 border-white/30 border-t-green-400 rounded-full animate-spin mx-auto mb-4"></div>
                                <p className="text-white/80">正在为您生成 {imageTypes[selectedImageType]} ...</p>
                                <p className="text-white/60 text-sm mt-2">预计需要 30-60 秒</p>
                            </div>
                        </div>
                    </div>
                );

            case 'completed':
                return (
                    <div className="space-y-4">
                        <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl p-4">
                            <h4 className="text-white/90 font-medium mb-3 flex items-center gap-2">
                                <Check className="w-5 h-5 text-green-400" />
                                流程完成
                            </h4>
                            
                            <div className="grid grid-cols-2 gap-3">
                                {streamData.final_images?.map((image, index) => (
                                    <div key={index} className="bg-black/20 rounded-lg p-3">
                                        <img
                                            src={image.enhanced_url}
                                            alt={`生成图片 ${index + 1}`}
                                            className="w-full h-32 object-cover rounded-lg mb-2"
                                        />
                                        <div className="flex gap-2">
                                            <button className="flex-1 bg-blue-500/30 hover:bg-blue-500/50 
                                                             text-blue-300 py-1 px-2 rounded text-xs transition-colors flex items-center justify-center gap-1">
                                                <Eye className="w-3 h-3" />
                                                预览
                                            </button>
                                            <button className="flex-1 bg-green-500/30 hover:bg-green-500/50 
                                                             text-green-300 py-1 px-2 rounded text-xs transition-colors flex items-center justify-center gap-1">
                                                <Download className="w-3 h-3" />
                                                下载
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                );

            default:
                return <div className="text-white/60">加载中...</div>;
        }
    };

    return (
        <div className="bg-gradient-to-br from-purple-900/30 to-blue-900/30 rounded-xl p-6 border border-white/10">
            <div className="flex items-center gap-3 mb-4">
                <ShoppingCart className="w-6 h-6 text-purple-400" />
                <div>
                    <h3 className="text-white font-medium">电商流程</h3>
                    <p className="text-white/60 text-sm">{stepTitles[currentStep] || '处理中'}</p>
                </div>
            </div>

            {renderStepContent()}
        </div>
    );
};

export default EcommerceStreamFlow; 