import React, { useState, useEffect } from 'react';

const GenerationModal = ({ 
    isOpen, 
    taskType = 'image', // 'image' or 'video'
    prompt = '',
    onClose,
    startTime = null,
    progress = 0 // 0-100
}) => {
    const [elapsedTime, setElapsedTime] = useState(0);
    const [displayProgress, setDisplayProgress] = useState(0);

    // 计算经过时间
    useEffect(() => {
        if (!isOpen || !startTime) return;
        
        const interval = setInterval(() => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            setElapsedTime(elapsed);
        }, 1000);
        
        return () => clearInterval(interval);
    }, [isOpen, startTime]);

    // 平滑进度动画
    useEffect(() => {
        if (progress > displayProgress) {
            const increment = Math.min(2, progress - displayProgress);
            const timer = setTimeout(() => {
                setDisplayProgress(prev => Math.min(progress, prev + increment));
            }, 50);
            return () => clearTimeout(timer);
        }
    }, [progress, displayProgress]);

    if (!isOpen) return null;

    const taskConfig = {
        image: {
            icon: '🎨',
            title: '图片生成中',
            description: 'AI正在创作您的图片...',
            color: 'from-purple-500 to-pink-500',
            estimatedTime: 30
        },
        video: {
            icon: '🎬',
            title: '视频生成中', 
            description: 'AI正在制作您的视频...',
            color: 'from-blue-500 to-cyan-500',
            estimatedTime: 60
        }
    };

    const config = taskConfig[taskType] || taskConfig.image;
    const progressPercent = Math.min(displayProgress, 100);
    const timeProgress = Math.min((elapsedTime / config.estimatedTime) * 100, 95);
    const actualProgress = Math.max(progressPercent, timeProgress);

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm">
            <div className="bg-[#1a1a2e] border border-purple-500/30 rounded-3xl p-8 w-[480px] shadow-2xl">
                {/* 头部 */}
                <div className="text-center mb-8">
                    <div className="text-6xl mb-4 animate-bounce">{config.icon}</div>
                    <h2 className="text-2xl font-bold text-white mb-2">{config.title}</h2>
                    <p className="text-purple-200/80 text-sm">{config.description}</p>
                </div>

                {/* 进度环 */}
                <div className="relative w-48 h-48 mx-auto mb-6">
                    {/* 背景圆环 */}
                    <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                        <circle
                            cx="50"
                            cy="50"
                            r="45"
                            stroke="rgba(139, 92, 246, 0.2)"
                            strokeWidth="4"
                            fill="none"
                        />
                        {/* 进度圆环 */}
                        <circle
                            cx="50"
                            cy="50"
                            r="45"
                            stroke="url(#gradient)"
                            strokeWidth="4"
                            fill="none"
                            strokeLinecap="round"
                            strokeDasharray={`${2 * Math.PI * 45}`}
                            strokeDashoffset={`${2 * Math.PI * 45 * (1 - actualProgress / 100)}`}
                            className="transition-all duration-500 ease-out"
                        />
                        <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stopColor="#8B5CF6" />
                                <stop offset="100%" stopColor="#EC4899" />
                            </linearGradient>
                        </defs>
                    </svg>
                    
                    {/* 中心内容 */}
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <div className="text-3xl font-bold text-white mb-1">
                            {Math.round(actualProgress)}%
                        </div>
                        <div className="text-purple-300 text-sm">
                            {elapsedTime}s / {config.estimatedTime}s
                        </div>
                    </div>
                </div>

                {/* 提示词显示 */}
                {prompt && (
                    <div className="bg-purple-500/10 border border-purple-500/20 rounded-2xl p-4 mb-6">
                        <div className="text-purple-300 text-xs font-medium mb-2">生成提示词</div>
                        <div className="text-white/90 text-sm line-clamp-2">
                            {prompt}
                        </div>
                    </div>
                )}

                {/* 底部信息 */}
                <div className="text-center">
                    <div className="flex justify-center items-center space-x-4 text-xs text-purple-200/60">
                        <span className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span>AI处理中</span>
                        </span>
                        <span>•</span>
                        <span>请耐心等待</span>
                    </div>
                    
                    {/* 取消按钮 */}
                    {onClose && (
                        <button 
                            onClick={onClose}
                            className="mt-4 px-6 py-2 bg-white/10 hover:bg-white/20 text-white/80 rounded-xl text-sm transition-all duration-200"
                        >
                            取消生成
                        </button>
                    )}
                </div>

                {/* 装饰性动画 */}
                <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-8 left-8 w-3 h-3 bg-purple-400/40 rounded-full animate-ping"></div>
                    <div className="absolute top-12 right-12 w-2 h-2 bg-pink-400/40 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                    <div className="absolute bottom-16 left-16 w-4 h-4 bg-blue-400/40 rounded-full animate-ping" style={{ animationDelay: '2s' }}></div>
                </div>
            </div>
        </div>
    );
};

export default GenerationModal; 