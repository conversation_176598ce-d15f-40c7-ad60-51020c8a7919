import React, { useState } from 'react';
import { Heart, MessageCircle, Share2, RotateCcw, Download, Play, Image as ImageIcon, Filter } from 'lucide-react';

const ContentCanvas = ({ contentItems = [], isGenerating, onRegenerate, onLike, onComment, onShare }) => {
    const [filter, setFilter] = useState('all'); // all, images, videos
    const [viewMode, setViewMode] = useState('grid'); // grid, list

    const filteredItems = contentItems.filter(item => {
        if (filter === 'all') return true;
        if (filter === 'images') return item.type === 'image';
        if (filter === 'videos') return item.type === 'video';
        return true;
    });

    const ContentCard = ({ item }) => (
        <div className="group bg-black/20 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105">
            {/* 内容预览 */}
            <div className="relative aspect-video bg-gradient-to-br from-purple-500/20 to-pink-500/20">
                {item.type === 'image' ? (
                    <div className="absolute inset-0 flex items-center justify-center">
                        <ImageIcon className="w-12 h-12 text-white/40" />
                        {item.preview && (
                            <img 
                                src={item.preview} 
                                alt={item.prompt}
                                className="absolute inset-0 w-full h-full object-cover"
                            />
                        )}
                    </div>
                ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                        <Play className="w-12 h-12 text-white/40" />
                        {item.preview && (
                            <video 
                                src={item.preview}
                                className="absolute inset-0 w-full h-full object-cover"
                                poster={item.thumbnail}
                            />
                        )}
                    </div>
                )}
                
                {/* 悬浮操作按钮 */}
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="flex gap-2">
                        <button className="w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <Download className="w-4 h-4" />
                        </button>
                        <button 
                            onClick={() => onRegenerate(item.id)}
                            className="w-8 h-8 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors"
                        >
                            <RotateCcw className="w-4 h-4" />
                        </button>
                    </div>
                </div>

                {/* 类型标识 */}
                <div className="absolute bottom-3 left-3">
                    <span className="bg-black/50 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-white font-medium">
                        {item.type === 'image' ? '图片' : '视频'}
                    </span>
                </div>
            </div>

            {/* 内容信息 */}
            <div className="p-4">
                <p className="text-white text-sm mb-3 line-clamp-2">{item.prompt}</p>
                
                {/* 交互按钮 */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <button 
                            onClick={() => onLike(item.id)}
                            className={`flex items-center gap-1 text-sm transition-colors ${
                                item.liked ? 'text-red-400' : 'text-white/60 hover:text-white'
                            }`}
                        >
                            <Heart className={`w-4 h-4 ${item.liked ? 'fill-current' : ''}`} />
                            <span>{item.likes || 0}</span>
                        </button>
                        
                        <button 
                            onClick={() => onComment(item.id)}
                            className="flex items-center gap-1 text-sm text-white/60 hover:text-white transition-colors"
                        >
                            <MessageCircle className="w-4 h-4" />
                            <span>{item.comments || 0}</span>
                        </button>
                        
                        <button 
                            onClick={() => onShare(item.id)}
                            className="flex items-center gap-1 text-sm text-white/60 hover:text-white transition-colors"
                        >
                            <Share2 className="w-4 h-4" />
                        </button>
                    </div>
                    
                    <div className="text-xs text-white/40">
                        {item.createdAt ? new Date(item.createdAt).toLocaleDateString() : '刚刚'}
                    </div>
                </div>
            </div>
        </div>
    );

    return (
        <div className="flex-1 p-6 overflow-y-auto">
            {/* 头部工具栏 */}
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                    <h1 className="text-2xl font-bold text-white">创作画布</h1>
                    <div className="flex items-center gap-2">
                        <Filter className="w-4 h-4 text-white/60" />
                        <select 
                            value={filter}
                            onChange={(e) => setFilter(e.target.value)}
                            className="bg-black/30 border border-white/20 rounded-lg px-3 py-1 text-white text-sm"
                        >
                            <option value="all">全部内容</option>
                            <option value="images">图片</option>
                            <option value="videos">视频</option>
                        </select>
                    </div>
                </div>
                
                <div className="text-white/60 text-sm">
                    共 {filteredItems.length} 个作品
                </div>
            </div>

            {/* 生成中状态 */}
            {isGenerating && (
                <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl border border-purple-500/30 p-6 mb-6">
                    <div className="flex items-center gap-3">
                        <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <div className="text-white">
                            <div className="font-medium">正在创作中...</div>
                            <div className="text-sm text-white/60">AI正在根据您的描述生成内容</div>
                        </div>
                    </div>
                </div>
            )}

            {/* 内容网格 */}
            {filteredItems.length > 0 ? (
                <div className={`grid gap-6 ${
                    viewMode === 'grid' 
                        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                        : 'grid-cols-1'
                }`}>
                    {filteredItems.map((item) => (
                        <ContentCard key={item.id} item={item} />
                    ))}
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                    <div className="w-24 h-24 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl flex items-center justify-center mb-4">
                        <ImageIcon className="w-12 h-12 text-white/40" />
                    </div>
                    <h3 className="text-white text-lg font-medium mb-2">还没有创作内容</h3>
                    <p className="text-white/60 text-sm">在下方输入框中描述您想要创建的内容，开始您的创作之旅吧！</p>
                </div>
            )}
        </div>
    );
};

export default ContentCanvas;
