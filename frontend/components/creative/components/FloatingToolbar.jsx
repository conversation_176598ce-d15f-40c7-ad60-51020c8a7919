import React from 'react';
import { User, Crown, Coins, Settings } from 'lucide-react';

const FloatingToolbar = ({ userInfo, onUpgrade }) => {
    return (
        <div className="fixed top-6 right-6 z-50">
            <div className="bg-black/40 backdrop-blur-lg rounded-2xl border border-white/10 p-4 shadow-2xl">
                {/* 用户信息 */}
                <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-white" />
                    </div>
                    <div className="text-white">
                        <div className="text-sm font-medium">{userInfo?.username || '用户'}</div>
                        <div className="text-xs text-white/60">{userInfo?.plan || 'Basic'}</div>
                    </div>
                </div>

                {/* 余额信息 */}
                <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl p-3 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                        <Coins className="w-4 h-4 text-yellow-400" />
                        <span className="text-yellow-400 text-sm font-medium">创作币</span>
                    </div>
                    <div className="text-white text-lg font-bold">{userInfo?.balance || 0}</div>
                    <div className="text-white/60 text-xs">可生成 {Math.floor((userInfo?.balance || 0) / 10)} 次</div>
                </div>

                {/* 升级按钮 */}
                <button 
                    onClick={onUpgrade}
                    className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl py-3 px-4 font-medium text-sm transition-all duration-300 flex items-center justify-center gap-2 mb-3"
                >
                    <Crown className="w-4 h-4" />
                    升级会员
                </button>

                {/* 快捷设置 */}
                <button className="w-full bg-white/10 hover:bg-white/20 text-white rounded-xl py-2 px-4 text-sm transition-all duration-300 flex items-center justify-center gap-2">
                    <Settings className="w-4 h-4" />
                    设置
                </button>
            </div>
        </div>
    );
};

export default FloatingToolbar;