import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Star, TrendingUp, Zap, Palette, Camera, Film, Wand2, Edit, Bot, RefreshCw, Copy, Check } from 'lucide-react';

const PromptOptimizer = ({ 
    onClose, 
    onPromptSelect, 
    recentPrompts = [], 
    currentImages = [], 
    onTextEnhance,
    currentPrompt = '' // 新增：接收当前输入的提示词
}) => {
    const [activeTab, setActiveTab] = useState('ai-optimize'); // 默认显示AI优化标签页
    
    // AI提示词优化相关状态
    const [optimizing, setOptimizing] = useState(false);
    const [optimizedPrompts, setOptimizedPrompts] = useState([]);
    const [originalPrompt, setOriginalPrompt] = useState('');
    const [optimization_type, setOptimizationType] = useState('creative'); // creative, detailed, artistic, commercial
    const [copiedIndex, setCopiedIndex] = useState(-1);
    
    // 文案提取相关状态
    const [scenario, setScenario] = useState('auto'); // auto, ecommerce
    const [analyzing, setAnalyzing] = useState(false);
    const [sellingPoints, setSellingPoints] = useState([]);
    const [extractedTexts, setExtractedTexts] = useState([]);

    // 监听传入的提示词变化
    useEffect(() => {
        if (currentPrompt && currentPrompt.trim()) {
            setOriginalPrompt(currentPrompt.trim());
        }
    }, [currentPrompt]);

    // AI提示词优化函数
    const handleOptimizePrompt = async () => {
        if (!originalPrompt.trim()) {
            return;
        }

        setOptimizing(true);
        setOptimizedPrompts([]);

        try {
            const response = await fetch('/api/prompt/optimize/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${localStorage.getItem('token')}`,
                },
                body: JSON.stringify({
                    prompt: originalPrompt,
                    optimization_type: optimization_type,
                    count: 4 // 生成4个优化版本
                })
            });

            if (response.ok) {
                const data = await response.json();
                setOptimizedPrompts(data.optimized_prompts || []);
            } else {
                console.error('优化请求失败');
            }
        } catch (error) {
            console.error('提示词优化错误:', error);
        } finally {
            setOptimizing(false);
        }
    };

    // 复制提示词到剪贴板
    const handleCopyPrompt = async (prompt, index) => {
        try {
            await navigator.clipboard.writeText(prompt);
            setCopiedIndex(index);
            setTimeout(() => setCopiedIndex(-1), 2000);
        } catch (error) {
            console.error('复制失败:', error);
        }
    };

    // 使用优化后的提示词
    const handleUseOptimizedPrompt = (prompt) => {
        onPromptSelect?.(prompt);
        onClose?.();
    };

    const suggestionCategories = [
        {
            id: 'art',
            name: '艺术风格',
            icon: Palette,
            prompts: [
                '梵高风格的星空夜景，油画质感，浓烈色彩',
                '莫奈印象派花园，光影斑驳，柔和笔触',
                '中国水墨画山水，意境深远，黑白渐变',
                '日本浮世绘风格，精细线条，传统色彩'
            ]
        },
        {
            id: 'photography',
            name: '摄影风格',
            icon: Camera,
            prompts: [
                '专业人像摄影，柔和光线，背景虚化',
                '街头摄影，黑白风格，强烈对比',
                '风景摄影，黄金时刻，广角视角',
                '微距摄影，细节丰富，浅景深'
            ]
        },
        {
            id: 'video',
            name: '视频概念',
            icon: Film,
            prompts: [
                '时光倒流效果，慢镜头，科幻感',
                '城市延时摄影，车流光轨，夜景',
                '自然纪录片风格，动物特写，环境音',
                '音乐MV风格，节奏感强，视觉冲击'
            ]
        }
    ];

    const trendingPrompts = [
        { text: 'AI数字艺术，未来主义，霓虹色彩', popularity: 95 },
        { text: '赛博朋克城市，雨夜街道，反光路面', popularity: 89 },
        { text: '极简主义设计，几何图形，现代风格', popularity: 84 },
        { text: '古风仙侠，飘逸服饰，山川背景', popularity: 78 }
    ];

    // 标签页导航
    const tabs = [
        { id: 'ai-optimize', name: 'AI 优化', icon: Bot },
        { id: 'suggestions', name: '创意建议', icon: Sparkles },
        { id: 'enhance', name: '文本增强', icon: Wand2 },
        { id: 'extract', name: '文案提取', icon: Edit },
        { id: 'history', name: '历史记录', icon: Clock },
        { id: 'trending', name: '热门趋势', icon: TrendingUp }
    ];

    // 图片分析处理函数
    const handleAnalyzeImages = async () => {
        if (!currentImages || currentImages.length === 0) {
            console.warn('没有图片可分析');
            return;
        }

        setAnalyzing(true);
        try {
            // 构建图片URL数组（假设currentImages是File对象数组）
            const imageUrls = currentImages.map(file => {
                if (typeof file === 'string') return file; // 已经是URL
                return URL.createObjectURL(file); // File对象转URL
            });

            const response = await fetch('/api/image/analyze/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${localStorage.getItem('token')}`,
                },
                body: JSON.stringify({
                    image_urls: imageUrls,
                    scenario: scenario
                })
            });

            if (response.ok) {
                const data = await response.json();
                const taskId = data.task_id;

                // 轮询任务状态
                const pollStatus = async () => {
                    const statusResponse = await fetch(`/api/task/status/${taskId}/`, {
                        headers: {
                            'Authorization': `Token ${localStorage.getItem('token')}`,
                        }
                    });

                    if (statusResponse.ok) {
                        const statusData = await statusResponse.json();
                        
                        if (statusData.status === 'SUCCESS') {
                            const result = statusData.result.result;
                            setSellingPoints(result.selling_points || []);
                            setExtractedTexts(result.extracted_texts || []);
                            setAnalyzing(false);
                        } else if (statusData.status === 'FAILURE') {
                            console.error('分析失败:', statusData.error);
                            setAnalyzing(false);
                        } else {
                            // 继续轮询
                            setTimeout(pollStatus, 2000);
                        }
                    }
                };

                setTimeout(pollStatus, 2000);
            } else {
                console.error('提交分析任务失败');
                setAnalyzing(false);
            }
        } catch (error) {
            console.error('图片分析错误:', error);
            setAnalyzing(false);
        }
    };

    // 编辑卖点
    const handleEditPoint = (index) => {
        const point = sellingPoints[index];
        const newTitle = prompt('编辑卖点标题:', point.title);
        if (newTitle && newTitle !== point.title) {
            const newPoints = [...sellingPoints];
            newPoints[index] = { ...point, title: newTitle };
            setSellingPoints(newPoints);
        }
    };

    const PromptCard = ({ prompt, category, onSelect, popularity }) => (
        <div className="group bg-black/20 hover:bg-black/30 rounded-xl p-3 border border-white/10 hover:border-purple-500/30 transition-all duration-300 cursor-pointer"
             onClick={() => onSelect(prompt)}>
            <div className="flex items-start justify-between mb-2">
                <p className="text-white text-sm leading-relaxed flex-1">{prompt}</p>
                {popularity && (
                    <div className="flex items-center gap-1 ml-2">
                        <TrendingUp className="w-3 h-3 text-green-400" />
                        <span className="text-green-400 text-xs">{popularity}%</span>
                    </div>
                )}
            </div>
            <div className="flex items-center justify-between">
                {category && (
                    <span className="text-purple-400 text-xs bg-purple-500/20 px-2 py-1 rounded-full">
                        {category}
                    </span>
                )}
                <button className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <Zap className="w-4 h-4 text-yellow-400" />
                </button>
            </div>
        </div>
    );

    return (
        <div className="w-80 bg-black/30 backdrop-blur-xl border-l border-white/10 flex flex-col h-full">
            {/* 头部 */}
            <div className="flex items-center justify-between p-4 border-b border-white/10">
                <h3 className="text-white font-semibold flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-purple-400" />
                    Prompt 优化器
                </h3>
                <button 
                    onClick={onClose} 
                    className="text-white/60 hover:text-white transition-colors p-1 hover:bg-white/10 rounded-lg"
                >
                    <X className="w-4 h-4" />
                </button>
            </div>

            {/* 标签页 */}
            <div className="flex p-2 bg-black/20">
                {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                                activeTab === tab.id
                                    ? 'bg-purple-500/30 text-purple-300 border border-purple-500/50'
                                    : 'text-white/60 hover:text-white hover:bg-white/10'
                            }`}
                        >
                            <Icon className="w-4 h-4" />
                            {tab.name}
                        </button>
                    );
                })}
            </div>

            {/* 内容区域 */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {activeTab === 'ai-optimize' && (
                    <div className="space-y-4">
                        <div className="text-center py-8">
                            <Bot className="w-12 h-12 text-purple-400 mx-auto mb-3" />
                            <h4 className="text-white/90 text-sm font-medium mb-2">AI 提示词优化</h4>
                            <p className="text-white/60 text-xs">让您的提示词更加生动、专业</p>
                        </div>
                        
                        <div className="bg-black/20 rounded-xl p-4 border border-white/10">
                            <h5 className="text-white/90 text-sm font-medium mb-3">
                                <div className="flex items-center gap-2">
                                    <RefreshCw className="w-4 h-4 text-purple-400" />
                                    当前提示词
                                </div>
                            </h5>
                            <div className="flex items-center gap-2 mb-3">
                                <textarea
                                    className="flex-1 bg-white/5 rounded-lg p-3 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                                    rows="3"
                                    value={originalPrompt}
                                    onChange={(e) => setOriginalPrompt(e.target.value)}
                                    placeholder="请输入您的提示词"
                                />
                                <button
                                    onClick={handleOptimizePrompt}
                                    disabled={optimizing || !originalPrompt.trim()}
                                    className="bg-purple-500/30 text-purple-300 hover:bg-purple-500/50 rounded-lg p-2 transition-colors"
                                >
                                    {optimizing ? (
                                        <div className="flex items-center justify-center gap-2">
                                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                            优化中...
                                        </div>
                                    ) : (
                                        <div className="flex items-center gap-2">
                                            <Bot className="w-4 h-4 text-purple-400" />
                                            优化
                                        </div>
                                    )}
                                </button>
                            </div>

                            {optimizedPrompts.length > 0 && (
                                <div className="space-y-3">
                                    <h5 className="text-white/90 text-sm font-medium">优化建议</h5>
                                    {optimizedPrompts.map((prompt, index) => (
                                        <div
                                            key={index}
                                            className="bg-black/20 rounded-xl p-3 border border-white/10 flex items-center justify-between gap-2"
                                        >
                                            <p className="text-white/80 text-sm flex-1">{prompt}</p>
                                            <div className="flex items-center gap-2">
                                                <button
                                                    onClick={() => handleCopyPrompt(prompt, index)}
                                                    className="text-white/60 hover:text-white transition-colors"
                                                >
                                                    {copiedIndex === index ? (
                                                        <Check className="w-4 h-4 text-green-400" />
                                                    ) : (
                                                        <Copy className="w-4 h-4 text-purple-400" />
                                                    )}
                                                </button>
                                                <button
                                                    onClick={() => handleUseOptimizedPrompt(prompt)}
                                                    className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                                                >
                                                    使用此文案
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {activeTab === 'suggestions' && (
                    <div className="space-y-6">
                        {suggestionCategories.map((category) => {
                            const Icon = category.icon;
                            return (
                                <div key={category.id}>
                                    <div className="flex items-center gap-2 mb-3">
                                        <Icon className="w-4 h-4 text-purple-400" />
                                        <h4 className="text-white font-medium text-sm">{category.name}</h4>
                                    </div>
                                    <div className="space-y-2">
                                        {category.prompts.map((prompt, index) => (
                                            <PromptCard
                                                key={index}
                                                prompt={prompt}
                                                category={category.name}
                                                onSelect={onPromptSelect}
                                            />
                                        ))}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}

                {activeTab === 'history' && (
                    <div className="space-y-3">
                        <div className="flex items-center justify-between">
                            <h4 className="text-white/90 text-sm font-medium">最近使用</h4>
                            <button className="text-white/60 hover:text-white text-xs">清空</button>
                        </div>
                        {recentPrompts.length > 0 ? (
                            recentPrompts.map((prompt, index) => (
                                <PromptCard
                                    key={index}
                                    prompt={prompt}
                                    onSelect={onPromptSelect}
                                />
                            ))
                        ) : (
                            <div className="text-center py-8">
                                <Clock className="w-12 h-12 text-white/20 mx-auto mb-3" />
                                <p className="text-white/40 text-sm">暂无历史记录</p>
                                <p className="text-white/30 text-xs mt-1">开始创作后会显示历史提示词</p>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'enhance' && (
                    <div className="space-y-4">
                        <div className="text-center py-8">
                            <Wand2 className="w-12 h-12 text-purple-400 mx-auto mb-3" />
                            <h4 className="text-white/90 text-sm font-medium mb-2">文本增强</h4>
                            <p className="text-white/60 text-xs">自动优化您的提示词，提升生成效果</p>
                        </div>
                        
                        <div className="bg-black/20 rounded-xl p-4 border border-white/10">
                            <h5 className="text-white/90 text-sm font-medium mb-3">快速增强选项</h5>
                            <div className="space-y-2">
                                <button 
                                    onClick={() => onTextEnhance && onTextEnhance('高质量，专业摄影，4K分辨率')}
                                    className="w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg text-white/80 text-sm transition-colors"
                                >
                                    ✨ 添加质量描述词
                                </button>
                                <button 
                                    onClick={() => onTextEnhance && onTextEnhance('电商风格，产品展示，营销海报')}
                                    className="w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg text-white/80 text-sm transition-colors"
                                >
                                    🛍️ 电商风格增强
                                </button>
                                <button 
                                    onClick={() => onTextEnhance && onTextEnhance('艺术创作，创意设计，视觉冲击')}
                                    className="w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg text-white/80 text-sm transition-colors"
                                >
                                    🎨 创意风格增强
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {activeTab === 'extract' && (
                    <div className="space-y-4">
                        {/* 场景选择 */}
                        <div className="space-y-2">
                            <h4 className="text-white/90 text-sm font-medium">选择分析场景</h4>
                            <div className="flex gap-2">
                                <button
                                    onClick={() => setScenario('auto')}
                                    className={`px-3 py-2 rounded-lg text-xs transition-colors ${
                                        scenario === 'auto'
                                            ? 'bg-purple-500/30 text-purple-300 border border-purple-500/50'
                                            : 'bg-white/10 text-white/60 hover:bg-white/20'
                                    }`}
                                >
                                    Auto
                                </button>
                                <button
                                    onClick={() => setScenario('ecommerce')}
                                    className={`px-3 py-2 rounded-lg text-xs transition-colors ${
                                        scenario === 'ecommerce'
                                            ? 'bg-purple-500/30 text-purple-300 border border-purple-500/50'
                                            : 'bg-white/10 text-white/60 hover:bg-white/20'
                                    }`}
                                >
                                    电商
                                </button>
                            </div>
                        </div>

                        {/* 分析按钮 */}
                        <button
                            onClick={handleAnalyzeImages}
                            disabled={!currentImages?.length || analyzing}
                            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl py-3 px-4 font-medium text-sm transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {analyzing ? (
                                <div className="flex items-center justify-center gap-2">
                                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                    分析中...
                                </div>
                            ) : (
                                `分析图片 (${currentImages?.length || 0})`
                            )}
                        </button>

                        {/* 卖点展示（限制3个）*/}
                        {sellingPoints.length > 0 && (
                            <div className="space-y-3">
                                <h4 className="text-white/90 text-sm font-medium">提取的卖点</h4>
                                {sellingPoints.slice(0, 3).map((point, index) => (
                                    <div key={index} className="bg-black/20 rounded-xl p-3 border border-white/10">
                                        <div className="flex items-start justify-between mb-2">
                                            <h5 className="text-purple-300 text-sm font-medium flex-1">
                                                {point.title}
                                            </h5>
                                            <button
                                                onClick={() => handleEditPoint(index)}
                                                className="text-white/40 hover:text-white/60 transition-colors"
                                            >
                                                <Edit className="w-3 h-3" />
                                            </button>
                                        </div>
                                        <p className="text-white/80 text-xs leading-relaxed mb-2">
                                            {point.description}
                                        </p>
                                        <button
                                            onClick={() => onPromptSelect(point.title)}
                                            className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                                        >
                                            使用此文案
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* 没有图片时的提示 */}
                        {(!currentImages || currentImages.length === 0) && (
                            <div className="text-center py-8">
                                <Camera className="w-12 h-12 text-white/20 mx-auto mb-3" />
                                <p className="text-white/40 text-sm">请先上传图片</p>
                                <p className="text-white/30 text-xs mt-1">上传图片后即可提取文案卖点</p>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'trending' && (
                    <div className="space-y-3">
                        <div className="flex items-center gap-2 mb-4">
                            <TrendingUp className="w-4 h-4 text-green-400" />
                            <h4 className="text-white/90 text-sm font-medium">热门趋势</h4>
                            <span className="text-green-400 text-xs bg-green-500/20 px-2 py-1 rounded-full">实时</span>
                        </div>
                        {trendingPrompts.map((item, index) => (
                            <PromptCard
                                key={index}
                                prompt={item.text}
                                popularity={item.popularity}
                                onSelect={onPromptSelect}
                            />
                        ))}
                    </div>
                )}
            </div>

            {/* 底部提示 */}
            <div className="p-4 border-t border-white/10">
                <div className="text-xs text-white/40 text-center">
                    <Star className="w-3 h-3 inline mr-1" />
                    点击任意提示词直接使用
                </div>
            </div>
        </div>
    );
};

export default PromptOptimizer;
