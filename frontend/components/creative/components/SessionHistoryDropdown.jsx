import React, { useState, useRef, useEffect } from 'react';

/**
 * Session History Dropdown Component
 * 会话历史下拉菜单组件 - 类似即梦风格
 */
const SessionHistoryDropdown = ({
    sessions = [],
    activeSessionId,
    onSessionSelect,
    onNewSession,
    className = ''
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    // 点击外部关闭下拉菜单
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    // 格式化会话标题
    const formatSessionTitle = (session) => {
        if (session.custom_title) {
            return session.custom_title;
        }
        // 使用第一条消息作为标题，限制长度
        const firstMessage = session.preview_message || 'New Conversation';
        return firstMessage.length > 20 ? firstMessage.substring(0, 20) + '...' : firstMessage;
    };

    // 格式化时间
    const formatTime = (timestamp) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} min${minutes !== 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hr${hours !== 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }
    };

    const handleSessionClick = (sessionId) => {
        console.log('🎯 SessionHistoryDropdown: 选择会话', {
            sessionId,
            currentActiveId: activeSessionId,
            onSessionSelectType: typeof onSessionSelect,
            sessionsList: sessions.map(s => ({ id: s.id, title: s.custom_title || s.preview_message?.substring(0, 20) }))
        });
        
        if (typeof onSessionSelect === 'function') {
            onSessionSelect(sessionId);
            console.log('✅ SessionHistoryDropdown: onSessionSelect called successfully');
        } else {
            console.error('❌ SessionHistoryDropdown: onSessionSelect is not a function:', onSessionSelect);
        }
        
        setIsOpen(false);
    };

    const handleNewSession = () => {
        onNewSession();
        setIsOpen(false);
    };

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            {/* 历史记录按钮 */}
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center space-x-2 px-4 py-3 rounded-2xl bg-gradient-to-r from-white/5 via-white/10 to-white/5 hover:from-white/10 hover:via-white/15 hover:to-white/10 backdrop-blur-xl border border-white/20 shadow-lg transition-all duration-200 text-white/90 hover:text-white ring-1 ring-white/10"
            >
                <svg 
                    className="w-5 h-5" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
                    />
                </svg>
                <span className="text-sm font-medium">History</span>
                <svg 
                    className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M19 9l-7 7-7-7" 
                    />
                </svg>
            </button>

            {/* 下拉菜单 */}
            {isOpen && (
                <div className="absolute right-0 top-full mt-3 w-80 bg-gradient-to-b from-black/95 via-black/90 to-black/95 backdrop-blur-2xl rounded-3xl border border-white/20 shadow-2xl shadow-black/50 z-50 max-h-96 overflow-hidden ring-1 ring-white/10">
                    {/* 头部 */}
                    <div className="p-4 border-b border-white/10">
                        <div className="flex items-center justify-between">
                            <h3 className="text-white font-medium">Chat History</h3>
                            <button
                                onClick={handleNewSession}
                                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white text-sm rounded-xl transition-all duration-200 shadow-lg hover:shadow-purple-500/25 ring-1 ring-purple-400/20"
                            >
                                New Chat
                            </button>
                        </div>
                    </div>

                    {/* 会话列表 */}
                    <div className="max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                        {sessions.length === 0 ? (
                            <div className="p-6 text-center text-white/60">
                                <svg className="w-12 h-12 mx-auto mb-3 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                <p>No conversation history</p>
                                <p className="text-sm mt-1">Start your first creative session!</p>
                            </div>
                        ) : (
                            sessions.map((session, index) => (
                                <button
                                    key={session.id}
                                    onClick={() => handleSessionClick(session.id)}
                                    className={`w-full text-left p-4 hover:bg-white/5 transition-colors duration-200 border-b border-white/5 last:border-b-0 ${
                                        session.id === activeSessionId ? 'bg-purple-600/20 border-l-4 border-l-purple-500' : ''
                                    }`}
                                >
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1 min-w-0">
                                            <h4 className="text-white font-medium text-sm truncate">
                                                {formatSessionTitle(session)}
                                            </h4>
                                            <p className="text-white/60 text-xs mt-1">
                                                {formatTime(session.updated_at || session.created_at)}
                                            </p>
                                        </div>
                                        {session.id === activeSessionId && (
                                            <div className="ml-2 flex-shrink-0">
                                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                            </div>
                                        )}
                                    </div>
                                </button>
                            ))
                        )}
                    </div>

                    {/* 底部信息 */}
                    {sessions.length > 0 && (
                        <div className="p-3 border-t border-white/10 text-center">
                            <p className="text-white/40 text-xs">
                                {sessions.length} conversation{sessions.length !== 1 ? 's' : ''}
                            </p>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default SessionHistoryDropdown; 