import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import PromptOptimizer from './PromptOptimizer';

/**
 * Floating Message Bar Component - 简化版本
 * textarea + 可视化引用chip
 */
const FloatingMessageBar = ({
    value = '',
    onChange,
    onSubmit,
    onKeyDown,
    placeholder = "Ask anything...",
    isLoading = false,
    userBalance = 0,
    currentFile = null,
    currentFiles = [], // 新增：接收多文件状态
    onFileChange,
    onFileRemove,
    inputRef = null,
    className = '',
    scrollContainerRef = null,
    selectedReferences = [],
    visualReferences = [],
    onReferenceRemove,
    onReferencePreview
}) => {
    const { language } = useLanguage();

    // 内联编辑器状态
    const editorRef = useRef(null);
    const [editorContent, setEditorContent] = useState('');

    // 已插入的引用列表（用于上方缩略图显示）
    const [insertedReferences, setInsertedReferences] = useState([]);

    // 滚动检测状态
    const [isAtBottom, setIsAtBottom] = useState(true);
    const [isFocused, setIsFocused] = useState(false);

    // 创建选项状态
    const [creationType, setCreationType] = useState('image');
    const [selectedModel, setSelectedModel] = useState('auto');
    const [selectedRatio, setSelectedRatio] = useState('16:9');
    const [selectedResolution, setSelectedResolution] = useState('1080p');
    const [selectedDuration, setSelectedDuration] = useState('5s');

    // 多文件上传状态
    const [currentFilesState, setCurrentFilesState] = useState([]);
    const [showPromptOptimizer, setShowPromptOptimizer] = useState(false);
    const maxFiles = 10;

    // 显示状态
    const [showTypeDropdown, setShowTypeDropdown] = useState(false);
    const [showModelDropdown, setShowModelDropdown] = useState(false);
    const [showRatioDropdown, setShowRatioDropdown] = useState(false);
    const [showResolutionDropdown, setShowResolutionDropdown] = useState(false);
    const [showDurationDropdown, setShowDurationDropdown] = useState(false);

    // Tooltip 状态
    const [hoveredTooltip, setHoveredTooltip] = useState(null);

    // 预览状态
    const [hoveredPreview, setHoveredPreview] = useState(null);
    const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });

    // 同步editorRef到inputRef，保持@mention功能兼容
    useEffect(() => {
        if (inputRef && editorRef.current) {
            inputRef.current = editorRef.current;
        }
    }, [inputRef]);

    // 同步value到编辑器
    useEffect(() => {
        if (editorRef.current && editorContent !== value) {
            // 如果value是纯文本且编辑器为空，设置初始内容
            if (typeof value === 'string' && !editorRef.current.innerHTML.trim()) {
                editorRef.current.textContent = value;
                setEditorContent(value);
            }
        }
    }, [value, editorContent]);

    // 同步insertedReferences与visualReferences - 简化版
    useEffect(() => {
        if (visualReferences && visualReferences.length === 0) {
            // 如果visualReferences被清空，也清空insertedReferences和编辑器中的tokens
            setInsertedReferences([]);

            // 清理编辑器中的所有tokens
            if (editorRef.current) {
                const tokens = editorRef.current.querySelectorAll('.ai-token');
                tokens.forEach(token => token.remove());
            }
        }
    }, [visualReferences]);

    // 监听Token预览事件
    useEffect(() => {
        const editor = editorRef.current;
        if (!editor) return;

        const handleTokenPreview = (e) => {
            const { reference } = e.detail;
            // 触发引用预览
            onReferencePreview?.(reference);
        };

        editor.addEventListener('tokenPreview', handleTokenPreview);
        return () => {
            editor.removeEventListener('tokenPreview', handleTokenPreview);
        };
    }, [onReferencePreview]);

    // 智能解析和自动完成功能
    const handleSmartCompletion = useCallback((text) => {
        // 智能识别常见模式并建议引用
        const patterns = [
            { match: /图片[一二三四五六七八九十]/, suggest: 'image' },
            { match: /视频[一二三四五六七八九十]/, suggest: 'video' },
            { match: /第[一二三四五六七八九十]+张图/, suggest: 'image' },
            { match: /最新的图片/, suggest: 'latest_image' },
            { match: /最新的视频/, suggest: 'latest_video' }
        ];

        for (const pattern of patterns) {
            if (pattern.match.test(text)) {
                // 可以在这里触发智能建议
                console.log(`🤖 AI建议: 检测到"${pattern.suggest}"类型的引用需求`);
                break;
            }
        }
    }, []);

    // 从编辑器更新内容 - 增强版
    const updateContentFromEditor = () => {
        if (!editorRef.current) return;

        // 提取纯文本内容，将Token转换为@引用
        const editor = editorRef.current;
        let textContent = '';

        console.log('🔍 [Debug] 开始提取编辑器内容，子节点数量:', editor.childNodes.length);

        for (const node of editor.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                const text = node.textContent;
                textContent += text;
                console.log('🔍 [Debug] 文本节点:', `"${text}"`);
            } else if (node.classList && node.classList.contains('ai-token')) {
                // 从Token中提取@引用文本
                const label = node.querySelector('span:nth-child(2)');
                if (label && label.textContent.startsWith('@')) {
                    const referenceText = label.textContent;
                    textContent += referenceText;
                    console.log('🔍 [Debug] Token节点提取:', `"${referenceText}"`);
                } else {
                    console.warn('⚠️ [Debug] Token节点未找到有效label:', node);
                }
            } else {
                // 处理其他元素
                const otherText = node.textContent || '';
                textContent += otherText;
                console.log('🔍 [Debug] 其他节点:', `"${otherText}"`, node.nodeName);
            }
        }

        console.log('🔍 [Debug] 最终提取的文本内容:', `"${textContent}"`);

        // 触发智能完成检查
        handleSmartCompletion(textContent);

        // 更新父组件状态
        onChange?.(textContent);

        // 更新插入的引用列表 - 从实际的DOM tokens中读取
        const tokens = editor.querySelectorAll('.ai-token');
        const currentReferences = Array.from(tokens).map(token => {
            const referenceData = {
                instance_id: token.dataset.instanceId,
                reference_id: token.dataset.referenceId,
                type: token.dataset.type,
                url: token.querySelector('img')?.src || '',
                display_name: token.dataset.referenceId
            };
            console.log('🔍 [Debug] Token引用数据:', referenceData);
            return referenceData;
        });

        setInsertedReferences(currentReferences);
        console.log('🔍 [Debug] 更新引用列表，数量:', currentReferences.length);
    };

    // 处理编辑器输入事件
    const handleEditorInput = (e) => {
        updateContentFromEditor();
        
        // 集成@mention功能：创建符合useAtMention期待的事件对象
        if (onChange && editorRef.current) {
            const currentText = editorRef.current.textContent || '';
            
            // 创建标准的事件对象结构
            const syntheticEvent = {
                target: {
                    value: currentText,
                    selectionStart: currentText.length,
                    selectionEnd: currentText.length
                },
                preventDefault: () => {},
                stopPropagation: () => {}
            };
            
            console.log('🔍 [FloatingMessageBar] 触发@mention检测:', { 
                text: currentText, 
                textLength: currentText.length,
                hasAtSymbol: currentText.includes('@')
            });
            
            // 调用父组件的onChange，这会触发@mention检测
            onChange(syntheticEvent);
        }
    };

    // 创建现代AI风格的Token元素
    const createChipElement = (reference) => {
        // 创建Token组件 - 采用现代AI工具风格
        const token = document.createElement('span');
        token.className = 'ai-token';
        token.contentEditable = 'false';
        token.dataset.type = reference.type || 'image';
        token.dataset.referenceId = reference.reference_id;
        token.dataset.instanceId = reference.instance_id;

        // Token样式 - 参考Notion/Cursor风格
        token.style.cssText = `
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            margin: 0 2px;
            background: rgba(255, 255, 255, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 12px;
            font-size: 13px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.95);
            vertical-align: middle;
            cursor: pointer;
            user-select: none;
            white-space: nowrap;
            max-width: 180px;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        `;

        // 悬停效果
        token.onmouseenter = () => {
            token.style.background = 'rgba(255, 255, 255, 0.2)';
            token.style.transform = 'translateY(-1px)';
            token.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
        };
        token.onmouseleave = () => {
            token.style.background = 'rgba(255, 255, 255, 0.12)';
            token.style.transform = 'translateY(0)';
            token.style.boxShadow = 'none';
        };

        // 类型图标
        const icon = document.createElement('span');
        icon.style.cssText = 'font-size: 14px; line-height: 1;';

        if (reference.type === 'image') {
            // 使用缩略图
            const thumbnail = document.createElement('img');
            thumbnail.src = reference.url;
            thumbnail.alt = '';
            thumbnail.style.cssText = `
                width: 16px;
                height: 16px;
                border-radius: 50%;
                object-fit: cover;
                border: 1px solid rgba(255, 255, 255, 0.3);
            `;
            thumbnail.onerror = () => {
                thumbnail.style.display = 'none';
                icon.textContent = '🖼️';
                token.insertBefore(icon, token.firstChild);
            };
            icon.appendChild(thumbnail);
        } else if (reference.type === 'video') {
            icon.textContent = '🎥';
        } else {
            icon.textContent = '📎';
        }

        // 标签文本
        const label = document.createElement('span');
        label.textContent = `@${reference.reference_id || reference.display_name}`;
        label.style.cssText = 'font-weight: 500; letter-spacing: 0.01em;';

        // 删除按钮 - 现代化设计
        const removeBtn = document.createElement('button');
        removeBtn.innerHTML = '×';
        removeBtn.style.cssText = `
            margin-left: 4px;
            width: 18px;
            height: 18px;
            background: rgba(0, 0, 0, 0.3);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            font-size: 11px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.7;
            backdrop-filter: blur(4px);
            transition: all 0.15s ease;
        `;

        removeBtn.onmouseenter = () => {
            removeBtn.style.opacity = '1';
            removeBtn.style.background = 'rgba(239, 68, 68, 0.8)';
            removeBtn.style.transform = 'scale(1.1)';
        };
        removeBtn.onmouseleave = () => {
            removeBtn.style.opacity = '0.7';
            removeBtn.style.background = 'rgba(0, 0, 0, 0.3)';
            removeBtn.style.transform = 'scale(1)';
        };

        removeBtn.onclick = (e) => {
            e.stopPropagation();
            token.remove();
            updateContentFromEditor();
        };

        // 点击预览功能
        token.onclick = (e) => {
            if (e.target === removeBtn) return;
            // 触发预览功能
            onReferencePreview?.(reference);
        };

        // 组装Token
        token.appendChild(icon);
        token.appendChild(label);
        token.appendChild(removeBtn);

        return token;
    };

    // 从缩略图区域移除引用
    const removeReferenceFromThumbnails = (instanceId) => {
        // 从insertedReferences列表中移除
        setInsertedReferences(prev =>
            prev.filter(ref => ref.instance_id !== instanceId)
        );

        // 从编辑器中移除对应的chip
        if (editorRef.current) {
            const chips = editorRef.current.querySelectorAll('.inline-chip');
            chips.forEach(chip => {
                const chipText = chip.querySelector('span:not(button)').textContent;
                const chipRef = insertedReferences.find(ref => {
                    const expectedText = `@${ref.reference_id || ref.display_name}`;
                    return chipText === expectedText && ref.instance_id === instanceId;
                });
                if (chipRef) {
                    chip.remove();
                }
            });
            updateContentFromEditor();
        }

        // 通知父组件
        onReferenceRemove?.({ instance_id: instanceId });
    };

    // 处理鼠标悬停预览
    const handlePreviewMouseEnter = (item, event) => {
        if (item.type === 'image' || (item.type === undefined && item.url)) {
            setHoveredPreview(item);

            const rect = event.currentTarget.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const previewWidth = 200;
            const previewHeight = 200;

            let x = rect.left + (rect.width - previewWidth) / 2;
            let y = rect.top - previewHeight - 10;

            if (x < 10) x = 10;
            if (x + previewWidth > viewportWidth - 10) x = viewportWidth - previewWidth - 10;
            if (y < 10) y = rect.bottom + 10;

            setPreviewPosition({ x, y });
        }
    };

    const handlePreviewMouseLeave = () => {
        setHoveredPreview(null);
    };

    // 判断是否应该展开
    const isExpanded = isAtBottom || isFocused;

    // 模型配置
    const modelConfigs = {
        auto: {
            name: 'Auto',
            subtitle: 'Agent识别使用什么模型',
            actualModel: 'gpt-image-1',
            supportedTypes: ['image'],
            supportedRatios: ['1:1', '4:3', '16:9'],
            supportedResolutions: ['720p', '1080p'],
            defaultRatio: '16:9',
            defaultResolution: '1080p'
        },
        pro: {
            name: 'Pro',
            subtitle: 'Seedance',
            actualModel: 'seedance',
            supportedTypes: ['video'],
            supportedRatios: ['16:9', '9:16', '1:1'],
            supportedResolutions: ['1080p', '4K'],
            supportedDurations: ['5s', '10s'],
            defaultRatio: '16:9',
            defaultResolution: '1080p',
            defaultDuration: '5s'
        },
        max: {
            name: 'Max',
            subtitle: 'Veo3',
            actualModel: 'veo3',
            supportedTypes: ['video'],
            supportedRatios: ['21:9', '16:9', '9:16', '1:1'],
            supportedResolutions: ['1080p', '4K'],
            supportedDurations: ['5s', '8s'],
            defaultRatio: '16:9',
            defaultResolution: '1080p',
            defaultDuration: '8s'
        }
    };

    // 所有可用选项
    const allRatios = ['21:9', '16:9', '4:3', '1:1', '3:4', '9:16'];
    const allResolutions = ['1080p', '4K'];
    const allDurations = ['5s', '8s', '10s'];

    // 获取当前创作类型可用的模型
    const getAvailableModels = () => {
        return Object.keys(modelConfigs).filter(key =>
            modelConfigs[key].supportedTypes.includes(creationType)
        );
    };

    // 检查选项是否可用
    const isOptionAvailable = (type, option) => {
        const config = modelConfigs[selectedModel];
        switch (type) {
            case 'ratio':
                return config.supportedRatios.includes(option);
            case 'resolution':
                return config.supportedResolutions.includes(option);
            case 'duration':
                return config.supportedDurations?.includes(option) || false;
            default:
                return false;
        }
    };

    // 处理创作类型切换
    const handleTypeChange = (type) => {
        setCreationType(type);
        const availableModels = Object.keys(modelConfigs).filter(key =>
            modelConfigs[key].supportedTypes.includes(type)
        );

        if (availableModels.length > 0 && !availableModels.includes(selectedModel)) {
            const newModel = availableModels[0];
            const config = modelConfigs[newModel];
            setSelectedModel(newModel);
            setSelectedRatio(config.defaultRatio);
            setSelectedResolution(config.defaultResolution);
            if (type === 'video' && config.defaultDuration) {
                setSelectedDuration(config.defaultDuration);
            }
        }
        setShowTypeDropdown(false);
    };

    // 处理模型切换
    const handleModelChange = (model) => {
        const config = modelConfigs[model];
        setSelectedModel(model);

        if (config.defaultRatio && !config.supportedRatios.includes(selectedRatio)) {
            setSelectedRatio(config.defaultRatio);
        }
        if (config.defaultResolution && !config.supportedResolutions.includes(selectedResolution)) {
            setSelectedResolution(config.defaultResolution);
        }
        if (config.defaultDuration && creationType === 'video') {
            setSelectedDuration(config.defaultDuration);
        }
        setShowModelDropdown(false);
    };

    // 处理选项变化
    const handleOptionChange = (type, value) => {
        if (type === 'resolution' && value === '4K') {
            // Not Supported
            return;
        }

        if (type !== 'resolution' && !isOptionAvailable(type, value)) return;

        switch (type) {
            case 'ratio':
                setSelectedRatio(value);
                setShowRatioDropdown(false);
                break;
            case 'resolution':
                setSelectedResolution(value);
                setShowResolutionDropdown(false);
                break;
            case 'duration':
                setSelectedDuration(value);
                setShowDurationDropdown(false);
                break;
        }
    };

    // 滚动到底部
    const scrollToBottom = () => {
        const container = scrollContainerRef?.current;
        if (container) {
            container.scrollTo({
                top: container.scrollHeight,
                behavior: 'smooth'
            });
        }
    };

    // 容器级滚动检测
    useEffect(() => {
        const handleScroll = () => {
            const container = scrollContainerRef?.current;
            if (!container) return;

            const threshold = 100;
            const scrollTop = container.scrollTop;
            const scrollHeight = container.scrollHeight;
            const clientHeight = container.clientHeight;
            const atBottom = (scrollHeight - scrollTop - clientHeight) <= threshold;

            setIsAtBottom(atBottom);
        };

        const container = scrollContainerRef?.current;
        if (!container) {
            const retryTimer = setTimeout(() => {
                if (scrollContainerRef?.current) {
                    scrollContainerRef.current.addEventListener('scroll', handleScroll, { passive: true });
                    handleScroll();
                }
            }, 100);
            return () => clearTimeout(retryTimer);
        }

        container.addEventListener('scroll', handleScroll, { passive: true });
        handleScroll();
        return () => container.removeEventListener('scroll', handleScroll);
    }, [scrollContainerRef]);

    // 同步传入的currentFiles prop
    useEffect(() => {
        if (currentFiles && currentFiles.length > 0) {
            setCurrentFilesState(currentFiles);
        }
    }, [currentFiles]);

    // 多文件处理函数
    const handleMultiFileChange = (e) => {
        const files = Array.from(e.target.files || []);
        if (files.length === 0) return;

        const totalFiles = currentFilesState.length + files.length;
        
        if (totalFiles > maxFiles) {
            // 可以添加toast提示
            console.warn(`最多只能上传${maxFiles}个文件，当前尝试上传${totalFiles}个`);
            return;
        }

        // 添加新文件到现有列表
        const newFiles = [...currentFilesState, ...files.slice(0, maxFiles - currentFilesState.length)];
        setCurrentFilesState(newFiles);

        // 通知父组件多文件变化
        if (onFileChange) {
            onFileChange(newFiles); // 传递整个文件数组
        }
    };

    // 移除单个文件
    const handleRemoveFile = (index) => {
        const newFiles = currentFilesState.filter((_, i) => i !== index);
        setCurrentFilesState(newFiles);

        // 通知父组件文件变化
        if (newFiles.length > 0) {
            onFileChange?.(newFiles); // 传递剩余文件数组
        } else {
            onFileRemove?.(); // 如果没有文件了，调用移除回调
        }
    };

    // 清空所有文件
    const handleClearAllFiles = () => {
        setCurrentFilesState([]);
        onFileRemove?.(); // 通知父组件清空文件
    };

    // 处理键盘事件
    const handleKeyDown = (e) => {
        console.log('⌨️ [FloatingMessageBar] 键盘事件:', { 
            key: e.key, 
            hasOnKeyDown: !!onKeyDown,
            isReferenceOpen: selectedReferences?.length > 0
        });
        
        // 优先处理@mention相关的键盘事件
        if (onKeyDown) {
            try {
                onKeyDown(e);
            } catch (error) {
                console.error('❌ [FloatingMessageBar] @mention keydown error:', error);
            }
        }
        
        // 如果事件没有被@mention处理，继续处理其他键盘事件
        if (e.defaultPrevented) {
            return;
        }
        
        if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
            e.preventDefault();
            handleSubmit();
            return;
        }
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const textValue = editorContent || value;
            onSubmit?.(textValue);
            // 清空编辑器
            if (editorRef.current) {
                editorRef.current.innerHTML = '';
                setEditorContent('');
                setInsertedReferences([]);
            }
            return;
        }
    };

    // 处理提交
    const handleSubmit = () => {
        const textValue = editorContent || value;
        if ((textValue.trim() || currentFile || currentFiles?.length > 0) && !isLoading) {
            onSubmit?.(textValue);
            // 清空输入状态
            if (editorRef.current) {
                editorRef.current.innerHTML = '';
                setEditorContent('');
                setInsertedReferences([]);
            }
        }
    };

    // 处理最小化状态的点击
    const handleMinimizedClick = () => {
        setIsFocused(true);
        setTimeout(() => {
            if (inputRef?.current) {
                inputRef.current.focus();
            }
        }, 100);
    };

    // 焦点处理
    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);

    // 翻译文本
    const translations = {
        zh: {
            placeholder: placeholder === "Ask anything..." ? '输入文字，描述你想创作的画面内容、运动方式等。例如：一个3D形象的小男孩，在公园滑滑板。' : placeholder,
            send: '发送',
            sending: '发送中...',
            image: '图片',
            video: '视频',
            attachFile: '添加附件',
            removeFile: '移除文件',
            scrollToBottom: '回到底部',
            removeReference: '移除引用',
            // 模型选项翻译
            auto: '自动',
            pro: '专业版',
            max: '最高版',
            videoPro: '视频专业版',
            videoMax: '视频最高版',
            // 比例翻译
            ratio: '比例',
            resolution: '分辨率',
            duration: '时长',
            // 其他选项
            notSupported: '暂不支持'
        },
        en: {
            placeholder: placeholder,
            send: 'Send',
            sending: 'Sending...',
            image: 'Image',
            video: 'Video',
            attachFile: 'Attach File',
            removeFile: 'Remove File',
            scrollToBottom: 'Scroll to Bottom',
            removeReference: 'Remove Reference',
            // 模型选项翻译
            auto: 'Auto',
            pro: 'Pro',
            max: 'Max',
            videoPro: 'Video Pro',
            videoMax: 'Video Max',
            // 比例翻译
            ratio: 'Ratio',
            resolution: 'Resolution',
            duration: 'Duration',
            // 其他选项
            notSupported: 'Not Supported'
        }
    };

    const t = translations[language] || translations.en;

    // 从当前输入中解析Media_N引用
    const parseMediaReferences = (text) => {
        const mediaRegex = /Media_(\d+)/g;
        const matches = [];
        let match;
        while ((match = mediaRegex.exec(text)) !== null) {
            matches.push({
                fullMatch: match[0],
                index: parseInt(match[1]) - 1, // Media_1 对应索引 0
                start: match.index,
                end: match.index + match[0].length
            });
        }
        return matches;
    };

    // 获取当前选中的引用媒体
    const getCurrentReferences = () => {
        const mediaRefs = parseMediaReferences(value);
        return mediaRefs.map(ref => {
            if (ref.index < selectedReferences.length) {
                return {
                    ...selectedReferences[ref.index],
                    displayIndex: ref.index + 1
                };
            }
            return null;
        }).filter(Boolean);
    };

    // 当前显示的引用媒体
    const currentReferences = getCurrentReferences();

    // 移除引用
    const handleRemoveReference = (index) => {
        // 获取要移除的引用
        const currentReferences = getCurrentReferences();
        const referenceToRemove = currentReferences[index];
        if (referenceToRemove && onReferenceRemove) {
            // 调用外部的移除处理器
            onReferenceRemove(referenceToRemove, index);
        } else {
            // 回退处理：从输入文本中移除对应的Media_N
            const mediaPattern = new RegExp(`Media_${index + 1}\\s*`, 'g');
            const newValue = value.replace(mediaPattern, '').trim();

            // 更新文本，重新编号剩余的Media引用
            let updatedValue = newValue;
            const remaining = parseMediaReferences(newValue);

            // 重新编号
            remaining.forEach((ref, newIndex) => {
                const oldPattern = `Media_${ref.index + 1}`;
                const newPattern = `Media_${newIndex + 1}`;
                updatedValue = updatedValue.replace(oldPattern, newPattern);
            });

            if (onChange) {
                const event = { target: { value: updatedValue } };
                onChange(event);
            }
        }
    };

    // Tooltip 组件
    const Tooltip = ({ text, children, position = 'top' }) => {
        return (
            <div className="relative">
                {children}
                {hoveredTooltip === text && (
                    <div
                        className={`absolute z-50 px-2 py-1 text-xs text-white bg-black/80 backdrop-blur-sm rounded-md whitespace-nowrap
                            transition-all duration-200 opacity-100 scale-100
                            ${position === 'top' ? 'bottom-full mb-2 left-1/2 transform -translate-x-1/2' : ''}
                            ${position === 'bottom' ? 'top-full mt-2 left-1/2 transform -translate-x-1/2' : ''}
                        `}
                    >
                        {text}
                        <div className={`absolute w-1 h-1 bg-black/80 transform rotate-45
                            ${position === 'top' ? 'top-full left-1/2 -translate-x-1/2 -mt-0.5' : ''}
                            ${position === 'bottom' ? 'bottom-full left-1/2 -translate-x-1/2 -mb-0.5' : ''}
                        `} />
                    </div>
                )}
            </div>
        );
    };

    // 下拉菜单组件
    const DropdownMenu = ({ isOpen, onClose, children, className = '' }) => {
        if (!isOpen) return null;

        return (
            <div className={`absolute bottom-full left-0 mb-2 bg-black/90 backdrop-blur-xl
                rounded-xl shadow-2xl z-50 min-w-[160px] pointer-events-auto
                transform transition-all duration-200 scale-100 opacity-100 ${className}`}>
                {children}
            </div>
        );
    };

    return (
        <div className={`fixed bottom-0 left-0 right-0 z-50 pointer-events-none ${className}`} style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-end',
            height: '100vh',
            pointerEvents: 'none'
        }}>
            {/* 大幅增加顶部边距，避免与内容重叠 */}
            <div className={`mx-auto px-4 pb-6 pt-20 transition-all duration-300 ease-in-out max-w-7xl`}>
                <div className={`backdrop-blur-md bg-white/5 rounded-2xl shadow-xl transition-all duration-300 ease-in-out relative pointer-events-auto
                    ${isExpanded ? 'p-4' : 'p-3'}`}>

                    {/* 回到底部按钮 - 右上角 */}
                    {!isAtBottom && (
                        <div className="absolute -top-16 right-4 pointer-events-auto">
                            <Tooltip text={t.scrollToBottom}>
                                <button
                                    onClick={scrollToBottom}
                                    className="p-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-full text-white/70 hover:text-white transition-all duration-200 shadow-lg"
                                    onMouseEnter={() => setHoveredTooltip(t.scrollToBottom)}
                                    onMouseLeave={() => setHoveredTooltip(null)}
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                    </svg>
                                </button>
                            </Tooltip>
                        </div>
                    )}



                    {/* 缩小状态 - 只显示输入框和发送按钮 */}
                    {!isExpanded && (
                        <div
                            className="flex items-center gap-3 cursor-pointer"
                            onClick={handleMinimizedClick}
                        >
                            {/* 缩小版附件按钮 - 触发主文件输入 */}
                            <Tooltip text={t.attachFile}>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        document.getElementById('file-upload-main')?.click();
                                    }}
                                    className="flex items-center justify-center h-12 w-12 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 cursor-pointer group flex-shrink-0"
                                    onMouseEnter={() => setHoveredTooltip(t.attachFile)}
                                    onMouseLeave={() => setHoveredTooltip(null)}
                                    disabled={isLoading}
                                >
                                    <svg className="w-4 h-4 text-white/70 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                </button>
                            </Tooltip>

                            {/* 缩小版输入框显示 */}
                            <div className="flex-1 h-12 px-4 bg-white/5 rounded-xl border border-white/10 text-white/70 text-sm flex items-center">
                                {value || t.placeholder.substring(0, 50) + '...'}
                            </div>

                            {/* 缩小版发送按钮 - 与展开状态保持一致 */}
                            <Tooltip text={isLoading ? t.sending : t.send}>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleSubmit();
                                    }}
                                    disabled={(!value.trim() && !currentFile) || isLoading}
                                    className={`h-12 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 active:scale-95 flex-shrink-0 flex items-center justify-center
                                        ${(!value.trim() && !currentFile) || isLoading
                                            ? 'text-white/30 cursor-not-allowed bg-white/5'
                                            : 'text-white hover:bg-white/15 bg-white/10'
                                        }`}
                                    onMouseEnter={() => setHoveredTooltip(isLoading ? t.sending : t.send)}
                                    onMouseLeave={() => setHoveredTooltip(null)}
                                >
                                    {isLoading ? (
                                        <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                    ) : (
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4l8 8-8 8M4 12h16" />
                                        </svg>
                                    )}
                                </button>
                            </Tooltip>
                        </div>
                    )}

                    {/* 展开状态 - 显示完整功能 */}
                    {isExpanded && (
                        <div className="flex flex-col">
                            {/* 引用预览区域 - 独立显示在最上方 */}
                            {insertedReferences.length > 0 && (
                                <div className="flex items-center gap-2 flex-wrap p-3 mb-3 bg-transparent">
                                    {insertedReferences.map((ref) => (
                                        <div
                                            key={ref.instance_id}
                                            className="relative group"
                                        >
                                            <div
                                                className="w-12 h-12 rounded-lg overflow-hidden bg-gray-800 border border-purple-500/30 hover:border-purple-500/60 transition-all duration-200 cursor-pointer hover:scale-105"
                                                onClick={() => onReferencePreview?.(ref)}
                                                onMouseEnter={(e) => handlePreviewMouseEnter(ref, e)}
                                                onMouseLeave={handlePreviewMouseLeave}
                                                title={`@${ref.reference_id || ref.display_name} - Click to enlarge`}
                                            >
                                                {ref.type === 'image' ? (
                                                    <img
                                                        src={ref.url}
                                                        alt={ref.display_name}
                                                        className="w-full h-full object-cover"
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                            e.target.nextSibling.style.display = 'flex';
                                                        }}
                                                    />
                                                ) : (
                                                    <div className="w-full h-full flex items-center justify-center text-lg bg-gradient-to-br from-gray-700 to-gray-800">
                                                        🎥
                                                    </div>
                                                )}
                                                {/* Fallback icon */}
                                                <div className="hidden w-full h-full items-center justify-center text-lg bg-gray-800">
                                                    {ref.type === 'image' ? '🖼️' : '🎥'}
                                                </div>
                                            </div>
                                            {/* 引用移除按钮 - 一直显示，参考图2样式 */}
                                            <button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    removeReferenceFromThumbnails(ref.instance_id);
                                                }}
                                                className="absolute -top-1 -right-1 w-5 h-5 bg-black/60 hover:bg-black/80 text-white text-sm rounded-full flex items-center justify-center transition-all duration-200 z-10 hover:scale-110 border border-white/20"
                                                title="Remove reference"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {/* 输入区域 - 向上扩展 */}
                            <div className="flex items-start gap-3 mb-4">
                                {/* 全局唯一的文件输入 - 隐藏但可被任何按钮触发 */}
                                <input
                                    id="file-upload-main"
                                    type="file"
                                    className="hidden"
                                    onChange={handleMultiFileChange}
                                    onClick={(e) => {
                                        e.target.value = null; // 清空input值，确保可以选择相同文件
                                    }}
                                    accept="image/*,video/*"
                                    multiple
                                    disabled={isLoading}
                                />

                                {/* 输入框容器 */}
                                <div className="flex-1 flex flex-col gap-2">
                                    {/* 多文件预览区域 */}
                                    {(currentFilesState.length > 0 || currentFile) && (
                                        <div className="space-y-2">
                                            {/* 文件计数和清空按钮 */}
                                            {currentFilesState.length > 1 && (
                                                <div className="flex items-center justify-between p-2 bg-white/5 rounded-lg border border-white/10">
                                                    <span className="text-white/70 text-sm">
                                                        已选择 {currentFilesState.length} 个文件 (最多{maxFiles}个)
                                                    </span>
                                                    <button
                                                        onClick={handleClearAllFiles}
                                                        className="text-red-400 hover:text-red-300 text-xs px-2 py-1 rounded transition-colors"
                                                    >
                                                        清空全部
                                                    </button>
                                                </div>
                                            )}
                                            
                                            {/* 文件预览网格 - 横向滚动 */}
                                            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-thin">
                                                {(currentFilesState.length > 0 ? currentFilesState : [currentFile]).filter(Boolean).map((file, index) => (
                                                    <div key={`${file.name}-${index}`} className="flex-shrink-0">
                                                        <div className="relative group">
                                                            <div
                                                                className="w-16 h-16 rounded-lg overflow-hidden bg-white/10 border border-white/20 hover:border-white/40 transition-all duration-200 cursor-pointer group-hover:scale-105 flex items-center justify-center"
                                                                onMouseEnter={(e) => handlePreviewMouseEnter({
                                                                    type: file.type.startsWith('image/') ? 'image' : 'video',
                                                                    url: URL.createObjectURL(file),
                                                                    name: file.name
                                                                }, e)}
                                                                onMouseLeave={handlePreviewMouseLeave}
                                                                title={file.name}
                                                            >
                                                                {file.type.startsWith('image/') ? (
                                                                    <img
                                                                        src={URL.createObjectURL(file)}
                                                                        alt={`File ${index + 1}`}
                                                                        className="w-full h-full object-cover"
                                                                    />
                                                                ) : file.type.startsWith('video/') ? (
                                                                    <video
                                                                        src={URL.createObjectURL(file)}
                                                                        className="w-full h-full object-cover"
                                                                        preload="metadata"
                                                                    />
                                                                ) : (
                                                                    <div className="text-white/60 text-xl">📄</div>
                                                                )}
                                                            </div>
                                                            
                                                            {/* 文件序号 */}
                                                            <div className="absolute -top-1 -left-1 w-5 h-5 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                                                                {index + 1}
                                                            </div>
                                                            
                                                            {/* 删除按钮 */}
                                                            <button
                                                                onClick={() => currentFilesState.length > 0 ? handleRemoveFile(index) : onFileRemove?.()}
                                                                className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white text-xs rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10 hover:scale-110"
                                                                title="删除文件"
                                                            >
                                                                ×
                                                            </button>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {/* 内联编辑器 */}
                                    <div
                                        ref={editorRef}
                                        contentEditable="true"
                                        onInput={handleEditorInput}
                                        onKeyDown={handleKeyDown}
                                        onFocus={handleFocus}
                                        onBlur={handleBlur}
                                        data-placeholder={t.placeholder}
                                        suppressContentEditableWarning={true}
                                        className="bg-transparent text-white placeholder-gray-400 px-0 py-3 outline-none transition-all duration-200 resize-none min-h-[48px] max-h-[240px] text-base rich-text-input overflow-y-auto"
                                        style={{
                                            minHeight: '48px',
                                            maxHeight: '240px',
                                            width: '100%',
                                            wordBreak: 'break-word',
                                            overflowWrap: 'break-word',
                                            overflowY: 'auto',
                                            lineHeight: '24px'
                                        }}
                                    ></div>
                                </div>
                            </div>

                            {/* 操作选项区域 - 固定在底部 */}
                            <div className="flex items-center justify-between order-last">
                                {/* 左侧：选项按钮组 */}
                                <div className="flex items-center gap-1 min-w-[600px]">
                                    {/* 上传按钮 - 移到功能按钮行 */}
                                    <Tooltip text={t.attachFile}>
                                        <label htmlFor="file-upload-main" className="flex items-center justify-center w-9 h-9 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 cursor-pointer group"
                                            onMouseEnter={() => setHoveredTooltip(t.attachFile)}
                                            onMouseLeave={() => setHoveredTooltip(null)}
                                        >
                                            <svg className="w-4 h-4 text-white/70 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </label>
                                    </Tooltip>

                                    {/* 文本增强按钮 */}
                                    <Tooltip text="文本增强">
                                        <button
                                            onClick={() => setShowPromptOptimizer(true)}
                                            className="flex items-center justify-center w-9 h-9 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 group"
                                            onMouseEnter={() => setHoveredTooltip("文本增强")}
                                            onMouseLeave={() => setHoveredTooltip(null)}
                                            disabled={isLoading}
                                        >
                                            <svg className="w-4 h-4 text-purple-400 group-hover:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                            </svg>
                                        </button>
                                    </Tooltip>

                                    {/* 创作类型选择 */}
                                    <div className="relative">
                                        <button
                                            onClick={() => setShowTypeDropdown(!showTypeDropdown)}
                                            className="flex items-center gap-1 px-4 py-2 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 text-white text-sm"
                                        >
                                            {creationType === 'image' ? (
                                                <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            ) : (
                                                <svg className="w-4 h-4 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            )}
                                            <span className="font-medium">{creationType === 'image' ? t.image : t.video}</span>
                                            <svg className="w-3 h-3 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>

                                        <DropdownMenu isOpen={showTypeDropdown} onClose={() => setShowTypeDropdown(false)}>
                                            <div className="p-2">
                                                {[
                                                    { key: 'image', label: t.image, icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z', color: 'text-blue-400' },
                                                    { key: 'video', label: t.video, icon: 'M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z', color: 'text-cyan-400' }
                                                ].map(({ key, label, icon, color }) => (
                                                    <button
                                                        key={key}
                                                        onClick={() => handleTypeChange(key)}
                                                        className={`w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 text-left
                                                            ${creationType === key
                                                                ? 'bg-white/20 text-white'
                                                                : 'text-white/70 hover:bg-white/10 hover:text-white'
                                                            }`}
                                                    >
                                                        <svg className={`w-4 h-4 flex-shrink-0 ${color}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={icon} />
                                                        </svg>
                                                        <span className="font-medium">{label}</span>
                                                    </button>
                                                ))}
                                            </div>
                                        </DropdownMenu>
                                    </div>

                                    {/* 根据选择类型显示对应选项 */}
                                    {creationType === 'image' && (
                                        <>
                                            {/* 模型选择 - 图片模式只显示Pro */}
                                            <div className="flex items-center gap-1 px-4 py-2 bg-white/10 rounded-xl text-white text-sm">
                                                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                                </svg>
                                                <span className="font-medium">{t.pro}</span>
                                            </div>

                                            {/* 图片比例选择 */}
                                            <div className="relative">
                                                <button
                                                    onClick={() => setShowRatioDropdown(!showRatioDropdown)}
                                                    className="flex items-center gap-1 px-4 py-2 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 text-white text-sm"
                                                >
                                                    <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                                    </svg>
                                                    <span className="font-medium">{selectedRatio}</span>
                                                    <svg className="w-3 h-3 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                    </svg>
                                                </button>

                                                <DropdownMenu isOpen={showRatioDropdown} onClose={() => setShowRatioDropdown(false)}>
                                                    <div className="p-2 space-y-1">
                                                        {allRatios.map((ratio) => (
                                                            <button
                                                                key={ratio}
                                                                onClick={() => handleOptionChange('ratio', ratio)}
                                                                disabled={!isOptionAvailable('ratio', ratio)}
                                                                className={`w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 text-left
                                                                    ${!isOptionAvailable('ratio', ratio)
                                                                        ? 'text-white/30 cursor-not-allowed'
                                                                        : selectedRatio === ratio
                                                                            ? 'bg-white/20 text-white'
                                                                            : 'text-white/70 hover:bg-white/10 hover:text-white'
                                                                    }`}
                                                            >
                                                                {ratio}
                                                            </button>
                                                        ))}
                                                    </div>
                                                </DropdownMenu>
                                            </div>

                                            {/* 占位按钮 - 保持与视频模式按钮数量一致 */}
                                            <div className="flex items-center gap-1 px-4 py-2 bg-white/5 rounded-xl text-white/50 text-sm cursor-default">
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                </svg>
                                                <span className="font-medium">{t.auto}</span>
                                            </div>
                                        </>
                                    )}

                                    {creationType === 'video' && (
                                        <>
                                            {/* 模型选择 - 视频模式显示Pro/Max */}
                                            <div className="relative">
                                                <button
                                                    onClick={() => setShowModelDropdown(!showModelDropdown)}
                                                    className="flex items-center gap-1 px-4 py-2 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 text-white text-sm"
                                                >
                                                    <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                                    </svg>
                                                    <span className="font-medium">{selectedModel === 'pro' ? t.videoPro : selectedModel === 'max' ? t.videoMax : `Video ${modelConfigs[selectedModel].name}`}</span>
                                                    <svg className="w-3 h-3 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                    </svg>
                                                </button>

                                                <DropdownMenu isOpen={showModelDropdown} onClose={() => setShowModelDropdown(false)}>
                                                    <div className="p-2 space-y-1">
                                                        {getAvailableModels().map((model) => {
                                                            const config = modelConfigs[model];
                                                            const displayName = model === 'pro' ? t.videoPro : model === 'max' ? t.videoMax : `Video ${config.name}`;
                                                            return (
                                                                <button
                                                                    key={model}
                                                                    onClick={() => handleModelChange(model)}
                                                                    className={`w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 text-left
                                                                        ${selectedModel === model
                                                                            ? 'bg-white/20 text-white'
                                                                            : 'text-white/70 hover:bg-white/10 hover:text-white'
                                                                        }`}
                                                                >
                                                                    {displayName}
                                                                </button>
                                                            );
                                                        })}
                                                    </div>
                                                </DropdownMenu>
                                            </div>

                                            {/* 比例和分辨率合并选择 */}
                                            <div className="relative">
                                                <button
                                                    onClick={() => setShowRatioDropdown(!showRatioDropdown)}
                                                    className="flex items-center gap-1 px-4 py-2 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 text-white text-sm"
                                                >
                                                    <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                                    </svg>
                                                    <span className="font-medium">{selectedRatio} • {selectedResolution}</span>
                                                    <svg className="w-3 h-3 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                    </svg>
                                                </button>

                                                <DropdownMenu isOpen={showRatioDropdown} onClose={() => setShowRatioDropdown(false)}>
                                                    <div className="p-2">
                                                        {/* 比例选择区域 */}
                                                        <div className="mb-3">
                                                            <div className="text-xs text-white/50 mb-2 px-2">{t.ratio}</div>
                                                            <div className="space-y-1">
                                                                {allRatios.map((ratio) => (
                                                                    <button
                                                                        key={ratio}
                                                                        onClick={() => handleOptionChange('ratio', ratio)}
                                                                        disabled={!isOptionAvailable('ratio', ratio)}
                                                                        className={`w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 text-left
                                                                            ${!isOptionAvailable('ratio', ratio)
                                                                                ? 'text-white/30 cursor-not-allowed'
                                                                                : selectedRatio === ratio
                                                                                    ? 'bg-white/20 text-white'
                                                                                    : 'text-white/70 hover:bg-white/10 hover:text-white'
                                                                            }`}
                                                                    >
                                                                        {ratio}
                                                                    </button>
                                                                ))}
                                                            </div>
                                                        </div>

                                                        {/* 分隔线 */}
                                                        <div className="h-px bg-white/10 my-3"></div>

                                                        {/* 分辨率选择区域 */}
                                                        <div>
                                                            <div className="text-xs text-white/50 mb-2 px-2">{t.resolution}</div>
                                                            <div className="space-y-1">
                                                                {allResolutions.map((resolution) => (
                                                                    <button
                                                                        key={resolution}
                                                                        onClick={() => handleOptionChange('resolution', resolution)}
                                                                        disabled={resolution === '4K'}
                                                                        className={`w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 text-left
                                                                    ${resolution === '4K'
                                                                        ? 'text-white/30 cursor-not-allowed'
                                                                        : selectedResolution === resolution
                                                                            ? 'bg-white/20 text-white'
                                                                            : 'text-white/70 hover:bg-white/10 hover:text-white'
                                                                            }`}
                                                                    >
                                                                        {resolution}
                                                                        {resolution === '4K' && (
                                                                            <span className="text-xs text-white/40 ml-2">({t.notSupported})</span>
                                                                        )}
                                                                    </button>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </DropdownMenu>
                                            </div>

                                            {/* 时长选择 */}
                                            <div className="relative">
                                                <button
                                                    onClick={() => setShowDurationDropdown(!showDurationDropdown)}
                                                    className="flex items-center gap-1 px-4 py-2 bg-white/10 hover:bg-white/15 rounded-xl transition-all duration-200 text-white text-sm"
                                                >
                                                    <svg className="w-4 h-4 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <span className="font-medium">{selectedDuration}</span>
                                                    <svg className="w-3 h-3 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                    </svg>
                                                </button>

                                                <DropdownMenu isOpen={showDurationDropdown} onClose={() => setShowDurationDropdown(false)}>
                                                    <div className="p-2 space-y-1">
                                                        {allDurations.map((duration) => (
                                                            <button
                                                                key={duration}
                                                                onClick={() => handleOptionChange('duration', duration)}
                                                                disabled={!isOptionAvailable('duration', duration)}
                                                                className={`w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 text-left
                                                                    ${!isOptionAvailable('duration', duration)
                                                                        ? 'text-white/30 cursor-not-allowed'
                                                                        : selectedDuration === duration
                                                                            ? 'bg-white/20 text-white'
                                                                            : 'text-white/70 hover:bg-white/10 hover:text-white'
                                                                    }`}
                                                            >
                                                                {duration}
                                                            </button>
                                                        ))}
                                                    </div>
                                                </DropdownMenu>
                                            </div>
                                        </>
                                    )}
                                </div>

                                {/* 右侧：发送按钮 */}
                                <Tooltip text={isLoading ? t.sending : t.send}>
                                    <button
                                        onClick={handleSubmit}
                                        disabled={(!value.trim() && !currentFile) || isLoading}
                                        className={`px-4 py-2 rounded-xl transition-all duration-200 transform hover:scale-105 active:scale-95
                                            ${(!value.trim() && !currentFile) || isLoading
                                                ? 'text-white/30 cursor-not-allowed bg-white/5'
                                                : 'text-white hover:bg-white/15 bg-white/10'
                                            }`}
                                        onMouseEnter={() => setHoveredTooltip(isLoading ? t.sending : t.send)}
                                        onMouseLeave={() => setHoveredTooltip(null)}
                                    >
                                        {isLoading ? (
                                            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                        ) : (
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4l8 8-8 8M4 12h16" />
                                            </svg>
                                        )}
                                    </button>
                                </Tooltip>
                            </div>
                        </div>
                    )}

                </div>
            </div>

            {/* 鼠标悬停预览窗体 */}
            {hoveredPreview && hoveredPreview.type === 'image' && (
                <div
                    className="fixed z-50 pointer-events-none"
                    style={{
                        left: previewPosition.x,
                        top: previewPosition.y,
                    }}
                >
                    <div className="bg-black/95 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl p-2 animate-in fade-in zoom-in-95 duration-200">
                        <img
                            src={hoveredPreview.url}
                            alt={hoveredPreview.name || hoveredPreview.display_name || 'Preview'}
                            className="w-48 h-48 object-cover rounded-lg"
                            style={{ width: '200px', height: '200px' }}
                        />
                        <div className="mt-2 text-white text-xs text-center opacity-80">
                            {hoveredPreview.name || hoveredPreview.display_name || 'Image Preview'}
                        </div>
                    </div>
                </div>
            )}

            {/* 右侧滑出的PromptOptimizer */}
            {showPromptOptimizer && (
                <div className="fixed top-0 right-0 h-full z-50 w-96 transform transition-transform duration-300 ease-out">
                    <PromptOptimizer
                        onClose={() => setShowPromptOptimizer(false)}
                        onPromptSelect={(prompt) => {
                            onChange?.({ target: { value: prompt } });
                            setShowPromptOptimizer(false);
                        }}
                        currentPrompt={value}
                        currentImages={currentFilesState.length > 0 ? currentFilesState : (currentFile ? [currentFile] : [])}
                        onTextEnhance={(enhancedText) => {
                            onChange?.({ target: { value: enhancedText } });
                        }}
                    />
                </div>
            )}

            {/* 全局样式 */}
            <style jsx>{`
                .backdrop-blur-md {
                    backdrop-filter: blur(12px);
                }
                .scrollbar-thin::-webkit-scrollbar {
                    height: 4px;
                }
                .scrollbar-thin::-webkit-scrollbar-track {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 2px;
                }
                .scrollbar-thin::-webkit-scrollbar-thumb {
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 2px;
                }
                .scrollbar-thin::-webkit-scrollbar-thumb:hover {
                    background: rgba(255, 255, 255, 0.5);
                }
            `}</style>
        </div>
    );
};

export default FloatingMessageBar;