import React from 'react';

export default function Message({ message, isUser }) {
  const { content, imageUrl, videoUrl, logs, error } = message;

  return (
    <div className={`my-4 flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-lg p-4 rounded-2xl shadow-lg transition-all duration-300
        ${isUser ? 'bg-gradient-to-r from-blue-700 to-blue-400 text-white' : 'bg-gradient-to-r from-gray-800 to-gray-600 text-white'}
        border border-blue-300/30 backdrop-blur-md relative`}
        style={{ minWidth: 120 }}>
        {/* Text content */}
        {content && (
          <div className="text-base leading-relaxed" dangerouslySetInnerHTML={{ __html: content }} />
        )}
        {/* Image content */}
        {imageUrl && (
          <img src={imageUrl} alt="Generated image" className="mt-2 rounded-xl border border-blue-200 shadow-md max-w-xs" />
        )}
        {/* Video content */}
        {videoUrl && (
          <video src={videoUrl} controls className="mt-2 rounded-xl border border-blue-200 shadow-md max-w-xs" />
        )}
        {/* Log content */}
        {logs && logs.length > 0 && (
          <div className="mt-2 text-xs text-blue-200 font-mono whitespace-pre-line bg-blue-900/40 p-2 rounded">
            {logs.map((line, idx) => <div key={idx}>{line}</div>)}
          </div>
        )}
        {/* Error content */}
        {error && (
          <div className="mt-2 text-red-400 font-bold">{error}</div>
        )}
      </div>
    </div>
  );
} 
