'use client'

import React from 'react';

// Main Loading component
const Loading = ({
    type = 'spinner',
    size = 'md',
    color = 'blue',
    fullScreen = false,
    overlay = false,
    text = '',
    className = ''
}) => {
    // Size configuration
    const sizeConfig = {
        sm: 'w-4 h-4',
        md: 'w-8 h-8',
        lg: 'w-12 h-12',
        xl: 'w-16 h-16'
    };

    // Color configuration
    const colorConfig = {
        blue: 'text-blue-500 border-blue-500',
        purple: 'text-purple-500 border-purple-500',
        green: 'text-green-500 border-green-500',
        gray: 'text-gray-500 border-gray-500',
        white: 'text-white border-white'
    };

    // Loading type components
    const LoadingTypes = {
        spinner: (
            <div className={`${sizeConfig[size]} ${colorConfig[color]} border-2 border-current border-t-transparent rounded-full animate-spin`} />
        ),

        dots: (
            <div className="flex space-x-1">
                {[0, 1, 2].map((i) => (
                    <div
                        key={i}
                        className={`${size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : 'w-3 h-3'} ${colorConfig[color].split(' ')[0]} rounded-full animate-bounce`}
                        style={{ animationDelay: `${i * 0.1}s` }}
                    />
                ))}
            </div>
        ),

        pulse: (
            <div className={`${sizeConfig[size]} ${colorConfig[color].split(' ')[0]} rounded-full animate-pulse`} />
        ),

        bars: (
            <div className="flex space-x-1 items-end">
                {[0, 1, 2, 3].map((i) => (
                    <div
                        key={i}
                        className={`${size === 'sm' ? 'w-1' : size === 'md' ? 'w-2' : 'w-3'} ${colorConfig[color].split(' ')[0]} rounded-sm animate-pulse`}
                        style={{
                            height: size === 'sm' ? '8px' : size === 'md' ? '16px' : '24px',
                            animationDelay: `${i * 0.15}s`
                        }}
                    />
                ))}
            </div>
        )
    };

    const loadingComponent = (
        <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
            {LoadingTypes[type]}
            {text && (
                <p className={`${colorConfig[color].split(' ')[0]} text-sm font-medium animate-pulse`}>
                    {text}
                </p>
            )}
        </div>
    );

    // Full screen Loading
    if (fullScreen) {
        return (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900">
                {loadingComponent}
            </div>
        );
    }

    // Overlay Loading
    if (overlay) {
        return (
            <div className="absolute inset-0 z-40 flex items-center justify-center bg-black/20 backdrop-blur-sm">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl border border-gray-200 dark:border-gray-700">
                    {loadingComponent}
                </div>
            </div>
        );
    }

    return loadingComponent;
};

// Specialized Loading components
export const ButtonLoading = ({ size = 'sm', color = 'white' }) => (
    <Loading type="spinner" size={size} color={color} />
);

export const PageLoading = ({ text = 'Loading...' }) => (
    <Loading type="spinner" size="lg" color="blue" fullScreen text={text} />
);

export const CardLoading = () => (
    <div className="animate-pulse">
        <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48 mb-4"></div>
        <div className="space-y-2">
            <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-3/4"></div>
            <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-1/2"></div>
        </div>
    </div>
);

export const AvatarLoading = ({ size = 'md' }) => {
    const sizeConfig = {
        sm: 'w-8 h-8',
        md: 'w-10 h-10',
        lg: 'w-12 h-12'
    };

    return (
        <div className={`${sizeConfig[size]} bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse`} />
    );
};

export const TextLoading = ({ lines = 3, className = '' }) => (
    <div className={`space-y-2 ${className}`}>
        {Array.from({ length: lines }).map((_, i) => (
            <div
                key={i}
                className={`bg-gray-200 dark:bg-gray-700 rounded h-4 animate-pulse ${i === lines - 1 ? 'w-2/3' : 'w-full'
                    }`}
            />
        ))}
    </div>
);

// Toast loading component
export const ToastLoading = ({ message = 'Loading...', type = 'info' }) => {
    const typeConfig = {
        info: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800',
        success: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800',
        warning: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800',
        error: 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800'
    };

    return (
        <div className={`fixed top-4 right-4 z-50 flex items-center space-x-3 px-4 py-3 rounded-lg border ${typeConfig[type]} shadow-lg animate-fadeInUp`}>
            <Loading type="spinner" size="sm" color={type === 'info' ? 'blue' : type} />
            <span className="text-sm font-medium">{message}</span>
        </div>
    );
};

// List loading component
export const ListLoading = ({ items = 5, className = '' }) => (
    <div className={`space-y-4 ${className}`}>
        {Array.from({ length: items }).map((_, i) => (
            <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1 space-y-2">
                    <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-3/4"></div>
                    <div className="bg-gray-200 dark:bg-gray-700 rounded h-3 w-1/2"></div>
                </div>
            </div>
        ))}
    </div>
);

// Grid loading component
export const GridLoading = ({
    items = 6,
    cols = 3,
    className = ''
}) => {
    const gridConfig = {
        1: 'grid-cols-1',
        2: 'grid-cols-2',
        3: 'grid-cols-3',
        4: 'grid-cols-4',
        6: 'grid-cols-6'
    };

    return (
        <div className={`grid ${gridConfig[cols]} gap-4 ${className}`}>
            {Array.from({ length: items }).map((_, i) => (
                <CardLoading key={i} />
            ))}
        </div>
    );
};

export default Loading; 