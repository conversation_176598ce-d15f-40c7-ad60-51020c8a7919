import { useState, useEffect } from 'react';

/**
 * 可重用的加载点动画组件
 * 遵循React最佳实践，避免重复代码
 */
const LoadingDots = ({ className = '', dotClassName = '' }) => {
    const [dots, setDots] = useState('');

    useEffect(() => {
        const interval = setInterval(() => {
            setDots(prev => prev.length >= 3 ? '' : prev + '.');
        }, 500);

        return () => clearInterval(interval);
    }, []);

    return (
        <span className={className}>
            <span className={dotClassName}>{dots}</span>
        </span>
    );
};

export default LoadingDots; 