import React from 'react';
import { useRouter } from 'next/router';

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null, errorInfo: null };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Log the error to console in development
        if (process.env.NODE_ENV === 'development') {
            console.error('ErrorBoundary caught an error:', error, errorInfo);
        }

        // Update state with error details
        this.setState({
            error: error,
            errorInfo: errorInfo
        });
    }

    render() {
        if (this.state.hasError) {
            return <ErrorFallback error={this.state.error} errorInfo={this.state.errorInfo} />;
        }

        return this.props.children;
    }
}

function ErrorFallback({ error, errorInfo }) {
    const router = useRouter();

    const handleGoHome = () => {
        router.push('/');
    };

    const handleRefresh = () => {
        window.location.reload();
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-[#0f0a1d] via-[#1a0b2e] to-[#16213e] flex items-center justify-center px-4">
            {/* Background Effects */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
                <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-blue-500/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
            </div>

            {/* Error Content */}
            <div className="relative z-10 text-center max-w-2xl mx-auto">
                {/* Error Icon */}
                <div className="mb-8">
                    <div className="w-24 h-24 mx-auto bg-gradient-to-br from-red-500/20 to-orange-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-red-500/30">
                        <div className="text-4xl">⚠️</div>
                    </div>
                </div>

                {/* Error Title */}
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-white via-red-100 to-orange-100 bg-clip-text text-transparent">
                    Application Error
                </h1>

                {/* Error Message */}
                <p className="text-lg text-purple-200/80 mb-8 leading-relaxed">
                    Something went wrong with the application. Please try refreshing the page or return to the homepage.
                </p>

                {/* Error Details */}
                {error && (
                    <div className="mb-8">
                        <div className="inline-block px-4 py-2 bg-red-500/10 border border-red-500/30 rounded-full">
                            <span className="text-red-300 text-sm font-medium">
                                {error.name}: {error.message}
                            </span>
                        </div>
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button
                        onClick={handleRefresh}
                        className="px-8 py-3 bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white font-medium rounded-2xl transition-all duration-300 shadow-lg shadow-purple-500/25 hover:scale-105 hover:shadow-purple-500/40"
                    >
                        Refresh Page
                    </button>

                    <button
                        onClick={handleGoHome}
                        className="px-8 py-3 bg-white/5 hover:bg-white/10 text-white font-medium rounded-2xl transition-all duration-300 border border-white/10 hover:border-white/20 backdrop-blur-sm"
                    >
                        Go to Homepage
                    </button>
                </div>

                {/* Help Section */}
                <div className="mt-12 p-6 bg-purple-500/5 border border-purple-500/20 rounded-2xl backdrop-blur-sm">
                    <h3 className="text-white font-medium mb-2">Troubleshooting</h3>
                    <div className="text-purple-200/70 text-sm space-y-1">
                        <p>• Try refreshing the page</p>
                        <p>• Clear your browser cache and cookies</p>
                        <p>• Check your internet connection</p>
                        <p>• Contact support if the problem persists</p>
                    </div>
                </div>

                {/* Development Error Details */}
                {process.env.NODE_ENV === 'development' && error && (
                    <div className="mt-8 p-4 bg-red-500/10 border border-red-500/30 rounded-xl backdrop-blur-sm text-left">
                        <h4 className="text-red-300 font-medium mb-2">Development Error Details:</h4>
                        <div className="space-y-2">
                            <div>
                                <h5 className="text-red-200 text-sm font-medium">Error:</h5>
                                <pre className="text-red-200/80 text-xs bg-black/20 p-2 rounded overflow-auto">
                                    {error.stack || error.toString()}
                                </pre>
                            </div>
                            {errorInfo && (
                                <div>
                                    <h5 className="text-red-200 text-sm font-medium">Component Stack:</h5>
                                    <pre className="text-red-200/80 text-xs bg-black/20 p-2 rounded overflow-auto">
                                        {errorInfo.componentStack}
                                    </pre>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default ErrorBoundary; 