import React, { useState, useEffect, useRef, useCallback } from 'react';

const ReferenceSelector = ({
    isOpen,
    position,
    searchQuery = '',
    onSelect,
    onClose,
    sessionMedia = [],
    selectedReferences = [] // 新增：已选择的引用列表
}) => {
    const [filteredMedia, setFilteredMedia] = useState([]);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [localSelectedReferences, setLocalSelectedReferences] = useState([]); // 本地选择状态
    const dropdownRef = useRef(null);

    // 初始化本地选择状态 - 每次打开时重置为空状态，确保干净的选择体验
    useEffect(() => {
        if (isOpen) {
            setLocalSelectedReferences([]); // 每次打开都重置为空，避免状态污染
        }
    }, [isOpen]);

    // Filter media based on search query
    useEffect(() => {
        console.log('🔍 [ReferenceSelector] Filtering media:', {
            sessionMediaCount: sessionMedia?.length || 0,
            sessionMediaFirst3: sessionMedia?.slice(0, 3).map(m => ({ id: m.reference_id, type: m.type, hasUrl: !!m.url })) || [],
            searchQuery,
            searchQueryLength: searchQuery?.length || 0
        });

        if (!sessionMedia || sessionMedia.length === 0) {
            console.log('⚠️ [ReferenceSelector] No sessionMedia available');
            setFilteredMedia([]);
            return;
        }

        // Create searchable media items with proper reference IDs
        const searchableMedia = sessionMedia.map(media => {
            const result = {
                reference_id: media.reference_id,
                type: media.type,
                url: media.url,
                message_id: media.id || media.message_id,
                display_name: media.display_name || media.reference_id,
                searchText: `${media.reference_id} ${media.content || ''}`.toLowerCase()
            };
            console.log('🔍 [ReferenceSelector] Creating searchable media:', {
                original: { reference_id: media.reference_id, type: media.type, hasUrl: !!media.url },
                searchable: { reference_id: result.reference_id, type: result.type, hasUrl: !!result.url }
            });
            return result;
        });

        // Filter based on search query
        let filtered = searchableMedia;
        if (searchQuery.trim()) {
            const query = searchQuery.toLowerCase();
            filtered = searchableMedia.filter(media =>
                media.searchText.includes(query) ||
                media.reference_id.includes(query)
            );
            console.log('🔍 [ReferenceSelector] Search filtering:', {
                query,
                originalCount: searchableMedia.length,
                filteredCount: filtered.length
            });
        }

        console.log('✅ [ReferenceSelector] Final filtered media:', {
            count: filtered.length,
            items: filtered.map(m => ({ id: m.reference_id, type: m.type, hasUrl: !!m.url }))
        });

        setFilteredMedia(filtered);
        setSelectedIndex(0);
    }, [sessionMedia, searchQuery]);

    // 监控isOpen状态变化
    useEffect(() => {
        console.log('🔍 [ReferenceSelector] isOpen changed:', {
            isOpen,
            sessionMediaCount: sessionMedia?.length || 0,
            filteredMediaCount: filteredMedia?.length || 0
        });
    }, [isOpen, sessionMedia?.length, filteredMedia?.length]);

    // 切换单个引用的选择状态
    const handleReferenceToggle = useCallback((reference) => {
        if (!reference) {
            console.error('ReferenceSelector: No reference provided');
            return;
        }

        // 检查是否已经选中
        const isSelected = localSelectedReferences.some(ref => ref.reference_id === reference.reference_id);

        if (isSelected) {
            // 取消选择
            setLocalSelectedReferences(prev =>
                prev.filter(ref => ref.reference_id !== reference.reference_id)
            );
        } else {
            // 创建新的引用实例 - 支持多重引用
            const completeReference = {
                reference_id: reference.reference_id,
                type: reference.type,
                url: reference.url,
                message_id: reference.message_id,
                display_name: reference.display_name || reference.reference_id,
                // 添加唯一实例ID，支持多重引用
                instance_id: `${reference.reference_id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            };

            // 添加到选择列表
            setLocalSelectedReferences(prev => [...prev, completeReference]);
        }
    }, [localSelectedReferences]);

    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (!isOpen || filteredMedia.length === 0) return;

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    setSelectedIndex(prev =>
                        prev < filteredMedia.length - 1 ? prev + 1 : 0
                    );
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    setSelectedIndex(prev =>
                        prev > 0 ? prev - 1 : filteredMedia.length - 1
                    );
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (filteredMedia[selectedIndex]) {
                        handleReferenceToggle(filteredMedia[selectedIndex]);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    onClose();
                    break;
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleKeyDown);
            return () => document.removeEventListener('keydown', handleKeyDown);
        }
    }, [isOpen, filteredMedia, selectedIndex, onClose, handleReferenceToggle]);

    // Handle click outside to close
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [isOpen, onClose]);

    // 处理插入选中的引用
    const handleInsertSelected = useCallback(() => {
        if (localSelectedReferences.length > 0 && onSelect) {
            console.log('🔧 [DEBUG] Inserting selected references:', localSelectedReferences);
            onSelect(localSelectedReferences);
        }

        // 插入后关闭选择器
        if (onClose) {
            onClose();
        }
    }, [localSelectedReferences, onSelect, onClose]);

    // 清空所有选择
    const handleClearAll = () => {
        setLocalSelectedReferences([]);
    };

    if (!isOpen) {
        return null;
    }

    return (
        <div
            ref={dropdownRef}
            className="fixed z-50 backdrop-blur-md bg-white/5 border border-white/20 rounded-2xl shadow-xl w-96 max-h-96 overflow-hidden transition-all duration-300 ease-in-out"
            style={{
                top: position.top,
                left: position.left,
            }}
        >
            {/* Header */}
            <div className="px-4 py-3 border-b border-white/10 bg-white/10">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        {/* 简化的标识 */}
                        <div className="flex items-center gap-2">
                            <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span className="text-sm text-white/70 font-medium">{filteredMedia.length} items</span>
                        </div>

                        {/* 显示选择计数 */}
                        {localSelectedReferences.length > 0 && (
                            <div className="flex items-center gap-1 bg-purple-600/20 border border-purple-500/30 text-purple-300 text-xs px-2 py-1 rounded-lg">
                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                                {localSelectedReferences.length} selected
                            </div>
                        )}
                    </div>

                    {/* 右侧操作按钮 */}
                    <div className="flex items-center gap-2">
                        {/* 清空按钮 */}
                        {localSelectedReferences.length > 0 && (
                            <button
                                onClick={handleClearAll}
                                className="text-white/50 hover:text-white/80 text-xs px-2 py-1 rounded-lg hover:bg-white/10 transition-all duration-200"
                            >
                                Clear
                            </button>
                        )}

                        {/* 插入按钮 */}
                        {localSelectedReferences.length > 0 && (
                            <button
                                onClick={handleInsertSelected}
                                className="flex items-center gap-1.5 px-3 py-1.5 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-lg transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-purple-500/25 transform hover:scale-105"
                            >
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M5 12h14M12 5l7 7-7 7" />
                                </svg>
                                Insert {localSelectedReferences.length}
                            </button>
                        )}
                    </div>
                </div>

                {searchQuery && (
                    <div className="text-xs text-gray-400 mt-1">
                        searching: &quot;{searchQuery}&quot;
                    </div>
                )}
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto max-h-64">
                <div className="p-2">
                    {filteredMedia.length === 0 ? (
                        <div className="text-center py-8 text-gray-400">
                            {sessionMedia.length === 0 ? 'No media in this session' : 'No matching references found'}
                        </div>
                    ) : (
                        <div className="space-y-2">
                            {filteredMedia.map((media, index) => (
                                <div
                                    key={`${media.reference_id}-${index}`}
                                    className={`flex items-center p-3 rounded-xl cursor-pointer transition-all duration-200 ${index === selectedIndex
                                        ? 'bg-white/10 border border-white/20'
                                        : 'hover:bg-white/5 border border-transparent hover:border-white/10'
                                        }`}
                                    onClick={() => handleReferenceToggle(media)}
                                >
                                    {/* Media Preview */}
                                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-800 flex-shrink-0">
                                        {media.type === 'image' ? (
                                            <img
                                                src={media.url}
                                                alt={media.reference_id}
                                                className="w-full h-full object-cover"
                                                onError={(e) => {
                                                    e.target.style.display = 'none';
                                                    e.target.nextSibling.style.display = 'flex';
                                                }}
                                            />
                                        ) : (
                                            <video
                                                src={media.url}
                                                className="w-full h-full object-cover"
                                                preload="metadata"
                                            />
                                        )}

                                        {/* Fallback icon */}
                                        <div className="hidden w-full h-full items-center justify-center text-2xl">
                                            {media.type === 'image' ? '🖼️' : '🎥'}
                                        </div>
                                    </div>

                                    {/* Media Info */}
                                    <div className="ml-3 flex-1 min-w-0">
                                        <div className="text-sm font-medium text-white truncate">
                                            @{media.reference_id}
                                        </div>
                                        <div className="text-xs text-gray-400 truncate">
                                            {media.display_name}
                                        </div>
                                        <div className="text-xs text-purple-400 mt-1">
                                            {media.type.toUpperCase()}
                                        </div>
                                    </div>

                                    {/* Add reference button */}
                                    <div className="ml-2 flex items-center space-x-2">
                                        {(() => {
                                            // 计算这个媒体被选中了多少次
                                            const selectedCount = localSelectedReferences.filter(ref =>
                                                ref.reference_id === media.reference_id
                                            ).length;

                                            return selectedCount > 0 ? (
                                                /* 显示选中状态和数量 */
                                                <div className="w-5 h-5 rounded-full bg-purple-500 text-white flex items-center justify-center text-xs font-bold">
                                                    {selectedCount}
                                                </div>
                                            ) : (
                                                /* 显示添加按钮 */
                                                <div className="w-5 h-5 rounded border-2 border-white/30 hover:border-purple-500 hover:bg-purple-500/20 flex items-center justify-center transition-all cursor-pointer">
                                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                                    </svg>
                                                </div>
                                            );
                                        })()}

                                        {/* Keyboard navigation indicator */}
                                        {index === selectedIndex && (
                                            <div className="text-purple-400">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z" />
                                                </svg>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Footer with keyboard shortcuts */}
            <div className="px-4 py-2 border-t border-white/10 bg-white/10">
                <div className="text-xs text-white/50 text-center">
                    ↑↓ Navigate • Enter Select • Esc Close
                </div>
            </div>
        </div>
    );
};

export default ReferenceSelector;