import React from 'react';
import Link from 'next/link';

// 统一的Logo组件
const Logo = ({
    variant = 'default', // 'default', 'compact', 'minimal', 'large'
    showText = true,
    showSubtext = true,
    className = '',
    href = '/',
    onClick = null
}) => {
    const variants = {
        default: {
            iconSize: 'w-10 h-10',
            iconClass: 'rounded-xl',
            textSize: 'text-xl',
            subtextSize: 'text-xs',
            spacing: 'space-x-3'
        },
        compact: {
            iconSize: 'w-8 h-8',
            iconClass: 'rounded-lg',
            textSize: 'text-lg',
            subtextSize: 'text-xs',
            spacing: 'space-x-2'
        },
        minimal: {
            iconSize: 'w-7 h-7',
            iconClass: 'rounded-lg',
            textSize: 'text-base',
            subtextSize: 'text-xs hidden',
            spacing: 'space-x-2'
        },
        large: {
            iconSize: 'w-12 h-12',
            iconClass: 'rounded-2xl',
            textSize: 'text-2xl',
            subtextSize: 'text-sm',
            spacing: 'space-x-4'
        }
    };

    const config = variants[variant] || variants.default;

    const LogoComponent = () => (
        <div className={`group flex items-center text-white hover:opacity-80 transition-all duration-300 ${className}`}>
            {showText && (
                <div className="flex flex-col">
                    <span className={`font-bold ${config.textSize} leading-none bg-gradient-to-r from-[#6d28d9] to-[#8b5cf6] text-transparent bg-clip-text`}
                        style={{
                            fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                            letterSpacing: '-0.02em'
                        }}>
                        MirageMakers AI
                    </span>
                </div>
            )}
        </div>
    );

    if (href && !onClick) {
        return (
            <Link href={href} className="no-underline">
                <LogoComponent />
            </Link>
        );
    }

    return <LogoComponent onClick={onClick} />;
};

// 导出各种变体的快捷组件
export const LogoCompact = (props) => <Logo {...props} variant="compact" />;
export const LogoMinimal = (props) => <Logo {...props} variant="minimal" />;
export const LogoLarge = (props) => <Logo {...props} variant="large" />;

export default Logo;
