import React, { useState } from 'react';

const FlipCard = ({ children, className = '' }) => {
    const [isFlipped, setIsFlipped] = useState(false);

    const handleClick = () => {
        setIsFlipped(!isFlipped);
    };

    // 提取default和back插槽内容
    const defaultContent = React.Children.toArray(children).find(
        child => child.props?.slot === 'default' || !child.props?.slot
    );
    const backContent = React.Children.toArray(children).find(
        child => child.props?.slot === 'back'
    );

    return (
        <div className={`group ${className}`} style={{ perspective: '1000px' }}>
            <div
                className={`relative h-80 w-full cursor-pointer transition-transform duration-700 ${isFlipped ? '' : ''
                    }`}
                style={{
                    transformStyle: 'preserve-3d',
                    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
                }}
                onClick={handleClick}
            >
                {/* Front Side */}
                <div
                    className="absolute inset-0 w-full h-full"
                    style={{ backfaceVisibility: 'hidden' }}
                >
                    <div className="relative h-full w-full rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800 border border-white/10 shadow-xl">
                        {defaultContent}
                    </div>
                </div>

                {/* Back Side */}
                <div
                    className="absolute inset-0 w-full h-full"
                    style={{
                        backfaceVisibility: 'hidden',
                        transform: 'rotateY(180deg)'
                    }}
                >
                    <div className="relative h-full w-full rounded-2xl overflow-hidden bg-gradient-to-br from-purple-900/90 to-black/90 backdrop-blur-sm border border-purple-500/30 p-6 shadow-xl">
                        {backContent}
                    </div>
                </div>
            </div>
        </div>
    );
};

// 插槽组件
export const FlipCardDefault = ({ children, ...props }) => (
    <div {...props} slot="default">{children}</div>
);

export const FlipCardBack = ({ children, ...props }) => (
    <div {...props} slot="back">{children}</div>
);

export default FlipCard;
