import React from 'react';
import { CardContainer, CardBody, CardItem } from './card-container';
import { cn } from '../../utils/cn';

const FeatureCard3D = ({ feature, index }) => {
    return (
        <CardContainer className="inter-var w-full">
            <CardBody className="bg-transparent relative group/card hover:shadow-2xl hover:shadow-purple-500/[0.3] border-[#3a2a5a]/20 hover:border-[#6d28d9]/60 w-full h-auto rounded-xl p-6 border backdrop-blur-sm transition-all duration-500">
                <CardItem
                    translateZ="50"
                    className="text-xl font-bold text-white group-hover/card:text-[#b8a1ff] transition-colors duration-300"
                >
                    {feature.title}
                </CardItem>

                <CardItem
                    as="p"
                    translateZ="60"
                    className="text-[#b8a1ff] text-sm max-w-sm mt-2 group-hover/card:text-[#e5deff] transition-colors duration-300"
                >
                    {feature.description}
                </CardItem>

                <CardItem
                    translateZ="100"
                    rotateX={20}
                    rotateZ={-10}
                    className="w-full mt-4"
                >
                    <div className="relative h-60 w-full rounded-xl bg-gradient-to-br from-purple-500 via-violet-500 to-blue-500 overflow-hidden group-hover/card:scale-110 transition-transform duration-500 shadow-2xl">
                        {/* Dynamic background based on feature type */}
                        {feature.backgroundImage ? (
                            <div className="relative h-full w-full">
                                <img
                                    src={feature.backgroundImage}
                                    className="h-full w-full object-cover rounded-xl group-hover/card:brightness-110 transition-all duration-500"
                                    alt={feature.title}
                                />
                                {/* Enhanced overlay effects for better image prominence */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/30 via-transparent to-blue-600/30 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500"></div>

                                {/* Floating particles effect */}
                                <div className="absolute inset-0 opacity-0 group-hover/card:opacity-100 transition-opacity duration-700">
                                    <div className="absolute top-4 left-4 w-2 h-2 bg-white rounded-full animate-ping"></div>
                                    <div className="absolute top-8 right-6 w-1 h-1 bg-purple-300 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                                    <div className="absolute bottom-6 left-6 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
                                    <div className="absolute bottom-4 right-4 w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '1.5s' }}></div>
                                </div>

                                {/* Enhanced glow ring */}
                                <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500 via-violet-500 to-blue-500 rounded-xl opacity-0 group-hover/card:opacity-30 blur-sm transition-all duration-500"></div>
                            </div>
                        ) : (
                            <div className="h-full w-full relative">
                                {/* AI-themed background pattern */}
                                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/80 via-violet-600/80 to-blue-600/80" />
                                <div className="absolute inset-0 opacity-30">
                                    <div className="grid grid-cols-8 grid-rows-8 h-full">
                                        {Array.from({ length: 64 }).map((_, i) => (
                                            <div
                                                key={i}
                                                className="border border-white/20 flex items-center justify-center"
                                                style={{
                                                    animationDelay: `${i * 0.1}s`,
                                                    animation: 'pulse 2s infinite'
                                                }}
                                            >
                                                {Math.random() > 0.7 && (
                                                    <div className="w-1 h-1 bg-white rounded-full animate-pulse" />
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Feature icon overlay */}
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <CardItem
                                        translateZ="80"
                                        rotateZ={5}
                                        className="text-6xl opacity-80"
                                    >
                                        {feature.icon}
                                    </CardItem>
                                </div>

                                {/* Glow effect */}
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
                            </div>
                        )}
                    </div>
                </CardItem>

                {/* 移除按钮，让3D效果更突出 */}
            </CardBody>
        </CardContainer>
    );
};

// Enhanced feature data with AI-specific content
export const aiFeatures = [
    {
        icon: "🎨",
        title: "AI Image Generation",
        description: "Generate high-quality images from text descriptions with various art styles and professional-grade results.",
        backgroundImage: "/pictures/MirageMakers-image-1750504916069.jpg"
    },
    {
        icon: "🎬",
        title: "AI Video Creation",
        description: "Create amazing video content from text or images, bringing creativity to life with advanced AI technology.",
        backgroundImage: "/pictures/MirageMakers-image-1750507256438.jpg"
    },
    {
        icon: "💬",
        title: "Smart Chat",
        description: "Natural conversation with AI for creative inspiration and professional advice with intelligent responses.",
        backgroundImage: "/pictures/MirageMakers-image-1750513942846.jpg"
    },
    {
        icon: "🔄",
        title: "Multimodal Understanding",
        description: "Upload images or videos, AI understands content and provides related services with advanced analysis.",
        backgroundImage: "/pictures/MirageMakers-image-1750514277830.jpg"
    }
];

export default FeatureCard3D; 