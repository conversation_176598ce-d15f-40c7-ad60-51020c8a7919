import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { cn } from '../../utils/cn';

const ThreeDCarousel = ({
    images = [],
    className,
    autoRotate = true,
    rotationSpeed = 0.01
}) => {
    const mountRef = useRef(null);
    const sceneRef = useRef(null);
    const rendererRef = useRef(null);
    const cameraRef = useRef(null);
    const groupRef = useRef(null);
    const animationRef = useRef(null);
    const [currentIndex, setCurrentIndex] = useState(0);

    // Default images if none provided
    const defaultImages = [
        '/api/placeholder/400/300',
        '/api/placeholder/400/300',
        '/api/placeholder/400/300',
        '/api/placeholder/400/300',
        '/api/placeholder/400/300',
        '/api/placeholder/400/300'
    ];

    const displayImages = images.length > 0 ? images : defaultImages;

    useEffect(() => {
        if (!mountRef.current) return;

        // Scene setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x0f0a1d);
        sceneRef.current = scene;

        // Camera setup
        const camera = new THREE.PerspectiveCamera(75, 16 / 9, 0.1, 1000);
        camera.position.z = 8;
        cameraRef.current = camera;

        // Renderer setup
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        mountRef.current.appendChild(renderer.domElement);
        rendererRef.current = renderer;

        // Group for carousel items
        const group = new THREE.Group();
        scene.add(group);
        groupRef.current = group;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x6d28d9, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0x8b5cf6, 0.8);
        directionalLight.position.set(5, 5, 5);
        scene.add(directionalLight);

        // Add rim lighting for better visual appeal
        const rimLight = new THREE.DirectionalLight(0xa855f7, 0.4);
        rimLight.position.set(-5, 3, -5);
        scene.add(rimLight);

        // Create carousel items
        const radius = 4;
        const itemCount = displayImages.length;
        const angleStep = (Math.PI * 2) / itemCount;

        displayImages.forEach((imageSrc, index) => {
            // Create geometry and material
            const geometry = new THREE.PlaneGeometry(2, 1.5);

            // Load texture
            const loader = new THREE.TextureLoader();
            const texture = loader.load(
                imageSrc,
                // onLoad
                () => {
                    console.log(`Texture ${index} loaded successfully`);
                },
                // onProgress
                undefined,
                // onError
                (error) => {
                    console.error(`Error loading texture ${index}:`, error);
                    // Use a colored material as fallback
                    const fallbackMaterial = new THREE.MeshLambertMaterial({
                        color: new THREE.Color().setHSL((index * 0.2) % 1, 0.7, 0.5)
                    });
                    mesh.material = fallbackMaterial;
                }
            );

            const material = new THREE.MeshLambertMaterial({ map: texture });
            const mesh = new THREE.Mesh(geometry, material);

            // Position items in a circle
            const angle = index * angleStep;
            mesh.position.x = Math.sin(angle) * radius;
            mesh.position.z = Math.cos(angle) * radius;
            mesh.position.y = 0;

            // Rotate to face center
            mesh.lookAt(0, 0, 0);

            // Add glow effect
            const glowGeometry = new THREE.PlaneGeometry(2.2, 1.7);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0x6d28d9,
                transparent: true,
                opacity: 0.2,
                side: THREE.DoubleSide
            });
            const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
            glowMesh.position.copy(mesh.position);
            glowMesh.lookAt(0, 0, 0);
            glowMesh.position.z -= 0.01; // Slightly behind

            // Store reference for animation
            glowMesh.userData = { originalOpacity: 0.2, index };

            group.add(glowMesh);
            group.add(mesh);
        });

        // Animation loop
        const animate = () => {
            animationRef.current = requestAnimationFrame(animate);

            if (autoRotate && groupRef.current) {
                groupRef.current.rotation.y += rotationSpeed;
            }

            // Add subtle floating animation
            if (groupRef.current) {
                groupRef.current.position.y = Math.sin(Date.now() * 0.001) * 0.1;
            }

            renderer.render(scene, camera);
        };

        animate();

        // Handle resize
        const handleResize = () => {
            if (!mountRef.current || !camera || !renderer) return;

            const width = mountRef.current.clientWidth;
            const height = mountRef.current.clientHeight;

            camera.aspect = width / height;
            camera.updateProjectionMatrix();
            renderer.setSize(width, height);
        };

        window.addEventListener('resize', handleResize);

        // Mouse interaction
        let mouseX = 0;
        let mouseY = 0;
        let targetRotationX = 0;
        let targetRotationY = 0;

        const handleMouseMove = (event) => {
            if (!mountRef.current) return;

            const rect = mountRef.current.getBoundingClientRect();
            mouseX = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouseY = -((event.clientY - rect.top) / rect.height) * 2 + 1;

            targetRotationY = mouseX * 0.5;
            targetRotationX = mouseY * 0.3;
        };

        const updateMouseInteraction = () => {
            if (groupRef.current && !autoRotate) {
                groupRef.current.rotation.y += (targetRotationY - groupRef.current.rotation.y) * 0.05;
                groupRef.current.rotation.x += (targetRotationX - groupRef.current.rotation.x) * 0.05;
            }
        };

        // Add mouse interaction to animation loop
        const animateWithMouse = () => {
            animationRef.current = requestAnimationFrame(animateWithMouse);

            updateMouseInteraction();

            if (autoRotate && groupRef.current) {
                groupRef.current.rotation.y += rotationSpeed;
            }

            if (groupRef.current) {
                groupRef.current.position.y = Math.sin(Date.now() * 0.001) * 0.1;

                // Animate glow effects
                groupRef.current.children.forEach((child, index) => {
                    if (child.userData && child.userData.originalOpacity !== undefined) {
                        const time = Date.now() * 0.002 + child.userData.index * 0.5;
                        child.material.opacity = child.userData.originalOpacity + Math.sin(time) * 0.1;
                    }
                });
            }

            renderer.render(scene, camera);
        };

        // Replace the simple animate with the mouse-interactive version
        cancelAnimationFrame(animationRef.current);
        animateWithMouse();

        mountRef.current.addEventListener('mousemove', handleMouseMove);

        // Cleanup
        return () => {
            window.removeEventListener('resize', handleResize);
            if (mountRef.current) {
                mountRef.current.removeEventListener('mousemove', handleMouseMove);
                if (renderer.domElement && mountRef.current.contains(renderer.domElement)) {
                    mountRef.current.removeChild(renderer.domElement);
                }
            }
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
            if (renderer) {
                renderer.dispose();
            }
        };
    }, [displayImages, autoRotate, rotationSpeed]);

    return (
        <div
            ref={mountRef}
            className={cn("w-full h-full min-h-[400px] relative", className)}
            style={{ aspectRatio: '16/9' }}
        />
    );
};

export default ThreeDCarousel; 