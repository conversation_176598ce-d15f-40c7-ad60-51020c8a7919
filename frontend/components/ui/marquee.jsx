import React, { useRef, useEffect } from 'react';
import { cn } from '../../utils/cn';

const Marquee = ({
    children,
    className,
    reverse = false,
    pauseOnHover = false,
    ...props
}) => {
    return (
        <div
            className={cn(
                "group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem]",
                className
            )}
            {...props}
        >
            <div
                className={cn(
                    "flex shrink-0 justify-around [gap:var(--gap)] animate-marquee flex-row",
                    reverse && "animation-direction-reverse",
                    pauseOnHover && "group-hover:[animation-play-state:paused]"
                )}
            >
                {children}
            </div>
            <div
                className={cn(
                    "flex shrink-0 justify-around [gap:var(--gap)] animate-marquee flex-row",
                    reverse && "animation-direction-reverse",
                    pauseOnHover && "group-hover:[animation-play-state:paused]"
                )}
                aria-hidden="true"
            >
                {children}
            </div>
        </div>
    );
};

const ReviewCard = ({ img, name, username, body, className, ...props }) => {
    return (
        <figure
            className={cn(
                "relative w-64 cursor-pointer overflow-hidden rounded-xl border p-4",
                "border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]",
                "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]",
                "backdrop-blur-sm bg-white/5 border-white/10",
                className
            )}
            {...props}
        >
            <div className="flex flex-row items-center gap-2">
                <img className="rounded-full" width="32" height="32" alt="" src={img} />
                <div className="flex flex-col">
                    <figcaption className="text-sm font-medium text-white">
                        {name}
                    </figcaption>
                    <p className="text-xs font-medium text-white/60">{username}</p>
                </div>
            </div>
            <blockquote className="mt-2 text-sm text-white/80">{body}</blockquote>
        </figure>
    );
};

export { Marquee, ReviewCard }; 