import React from 'react';

// 导航栏统一配置和UX优化
export const NavigationConfig = {
    // 品牌信息
    brand: {
        name: 'MirageMakers AI',
        description: 'AI驱动的多模态创意平台'
    },

    // 导航项配置 - 简化版本，移除中间导航
    navigationItems: (language, router) => [],

    // 右侧导航按钮配置 - 使用类似中间导航的样式
    rightNavItems: (language, router, isAuthenticated) => [
        ...(isAuthenticated ? [] : [
            {
                id: 'login',
                href: '/auth/login',
                label: language === 'zh' ? '登录' : 'Login',
                active: router.pathname === '/auth/login',
                style: 'nav-button'
            },
        ])
    ],

    // 导航栏样式配置
    styles: {
        navbar: {
            base: 'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
            scrolled: 'backdrop-blur-md bg-gray-900/90 border-b border-gray-800',
            transparent: 'bg-transparent'
        },
        container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
        content: 'flex items-center justify-between h-16',
        nav: 'hidden md:flex items-center space-x-8',
        mobile: 'md:hidden'
    },

    // 用户头像配置 - 透明样式，显示邮箱首字母
    userAvatar: {
        getInitials: (email) => {
            if (!email) return 'U';
            return email.charAt(0).toUpperCase();
        },
        style: {
            base: 'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300',
            background: 'bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20',
            text: 'text-white font-medium text-sm'
        }
    },

    // 用户菜单配置
    userMenu: {
        items: (language, user) => [
            {
                id: 'profile',
                label: language === 'zh' ? '个人资料' : 'Profile',
                href: '/profile',
                icon: '👤'
            },
            {
                id: 'tokens',
                label: language === 'zh' ? `代币余额: ${user?.tokens || 0}` : `Tokens: ${user?.tokens || 0}`,
                action: 'upgrade',
                icon: '💎',
                highlight: user?.tokens < 100
            },
            {
                id: 'settings',
                label: language === 'zh' ? '设置' : 'Settings',
                href: '/settings',
                icon: '⚙️'
            },
            {
                id: 'logout',
                label: language === 'zh' ? '退出登录' : 'Logout',
                action: 'logout',
                icon: '🚪'
            }
        ]
    }
};

// 响应式断点配置
export const breakpoints = {
    mobile: '768px',
    tablet: '1024px',
    desktop: '1280px'
};

// 动画配置
export const animations = {
    fadeIn: 'animate-in fade-in duration-200',
    slideIn: 'animate-in slide-in-from-top-2 duration-300',
    bounce: 'animate-bounce'
};

export default NavigationConfig;
