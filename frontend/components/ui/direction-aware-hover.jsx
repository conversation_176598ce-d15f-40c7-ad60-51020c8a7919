"use client";

import { motion, useMotionTemplate, useMotionValue, animate } from "framer-motion";
import { useState, useRef, useEffect } from "react";

const DirectionAwareHover = ({
    imageUrl,
    children,
    className = "",
    imageClassName = "",
    childrenClassName = "",
}) => {
    const ref = useRef(null);
    const [isMobile, setIsMobile] = useState(false);
    const [direction, setDirection] = useState("center");
    const x = useMotionValue(0);
    const y = useMotionValue(0);
    const rotateX = useMotionValue(0);
    const rotateY = useMotionValue(0);

    // 检测是否是移动设备
    useEffect(() => {
        const checkMobile = () => setIsMobile(window.innerWidth < 768);
        checkMobile();
        window.addEventListener("resize", checkMobile);
        return () => window.removeEventListener("resize", checkMobile);
    }, []);

    const handleMouseMove = (e) => {
        if (!ref.current || isMobile) return;

        const rect = ref.current.getBoundingClientRect();
        const width = rect.width;
        const height = rect.height;
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // 计算方向
        const xPercentage = mouseX / width;
        const yPercentage = mouseY / height;

        if (xPercentage < 0.33) setDirection("left");
        else if (xPercentage > 0.66) setDirection("right");
        else if (yPercentage < 0.33) setDirection("top");
        else if (yPercentage > 0.66) setDirection("bottom");
        else setDirection("center");

        // 更新动画值
        const xVal = (mouseX - width / 2) / 20;
        const yVal = (mouseY - height / 2) / 20;
        animate(x, xVal, { duration: 0.3 });
        animate(y, yVal, { duration: 0.3 });
        animate(rotateX, -yVal / 2, { duration: 0.3 });
        animate(rotateY, xVal / 2, { duration: 0.3 });
    };

    const handleMouseLeave = () => {
        animate(x, 0, { duration: 0.5 });
        animate(y, 0, { duration: 0.5 });
        animate(rotateX, 0, { duration: 0.5 });
        animate(rotateY, 0, { duration: 0.5 });
        setDirection("center");
    };

    const transform = useMotionTemplate`perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translate(${x}px, ${y}px)`;

    return (
        <motion.div
            ref={ref}
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
            onClick={() => isMobile && setDirection(prev => prev === "center" ? "top" : "center")}
            style={{ transform }}
            className={`relative overflow-hidden transition-all duration-500 ease-out ${className}`}
        >
            {/* 图片层 */}
            <motion.img
                src={imageUrl}
                alt="Gallery Image"
                className={`w-full h-full object-cover transition-all duration-700 ${direction !== "center" ? "scale-110" : "scale-100"
                    } ${imageClassName}`}
            />

            {/* 内容层（文字/按钮） */}
            <motion.div
                className={`absolute inset-0 flex flex-col justify-end p-4 transition-all duration-300 ${direction === "top" ? "items-start" :
                        direction === "bottom" ? "items-end" :
                            direction === "left" ? "justify-start" :
                                direction === "right" ? "justify-end" : "justify-center"
                    } ${childrenClassName}`}
                initial={{ opacity: 0 }}
                animate={{
                    opacity: direction === "center" ? 0 : 1,
                    backdropFilter: direction === "center" ? "blur(0px)" : "blur(4px)"
                }}
            >
                {children}
            </motion.div>
        </motion.div>
    );
};

export default DirectionAwareHover; 