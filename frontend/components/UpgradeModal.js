import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// 动态导入Stripe组件，避免SSR问题
const StripePaymentForm = dynamic(() => import('./StripePaymentForm'), {
    ssr: false,
    loading: () => (
        <div className="max-w-md mx-auto bg-[#241b3a] rounded-xl p-6 border border-[#3a2a5a]/50">
            <div className="text-center">
                <div className="w-16 h-16 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-white">Loading payment component...</p>
            </div>
        </div>
    )
});

// 套餐功能特性配置
const PLAN_FEATURES = {
    'TRIAL': {
        creations: [
            '20 images or',
            '10 videos or', 
            '2-3 image-to-video'
        ],
        features: [
            'LLM',
            'Veo3: Max',
            'GPT-image-1',
        ]
    },
    'BASIC': {
        creations: [
            '50 images or',
            '25 videos or',
            '6-7 image-to-video'
        ],
        features: [
            'LLM',
            'Veo3: Max',
            'GPT-image-1',
        ]
    },
    'PREMIUM': {
        creations: [
            '165 images or',
            '82 videos or',
            '22 image-to-video'
        ],
        features: [
            'LLM',
            'Veo3: Max',
            'GPT-image-1',
        ]
    },
    'ANNUAL': {
        creations: [
            '600 images or',
            '300 videos or', 
            '80 image-to-video'
        ],
        features: [
            'LLM',
            'Veo3: Max',
            'GPT-image-1',
        ]
    }
};

const UpgradeModal = ({ isOpen, onClose }) => {
    const [plans, setPlans] = useState([]);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [showPaymentForm, setShowPaymentForm] = useState(false);
    const [paymentResult, setPaymentResult] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (isOpen) {
            fetchMembershipPlans();
        }
    }, [isOpen]);

    const fetchMembershipPlans = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await fetch('/api/membership/plans/');
            if (response.ok) {
                const data = await response.json();
                setPlans(data.plans || []);
            } else {
                setError('无法加载套餐信息，请刷新重试');
            }
        } catch (error) {
            console.error('Failed to fetch plans:', error);
            setError('网络连接异常，请检查网络后重试');
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (amount) => {
        return `$${parseFloat(amount).toFixed(2)}`;
    };

    const resetPaymentFlow = () => {
        setShowPaymentForm(false);
        setPaymentResult(null);
        setSelectedPlan(null);
        setLoading(false);
        setError(null);
    };

    const handleClose = () => {
        resetPaymentFlow();
        onClose();
    };

    const handleSelectPlan = (plan) => {
        setSelectedPlan(plan);
        setShowPaymentForm(true);
    };

    const handlePaymentSuccess = async (result) => {
        try {
            console.log('Payment successful:', result);

            // Update user info in localStorage
            const token = localStorage.getItem('token');
            if (token) {
                await updateUserData(token);
            }

            setPaymentResult({
                success: true,
                tokens_added: result.tokens_added,
                plan_type: result.plan_type,
                message: 'Payment successful! Your tokens have been added.'
            });
        } catch (error) {
            console.error('Error handling payment success:', error);
            setPaymentResult({
                success: false,
                error: 'Payment was successful, but there was an error updating your account. Please refresh the page.'
            });
        }
    };

    const handlePaymentError = (error) => {
        console.error('Payment error:', error);
        setPaymentResult({
            success: false,
            error: 'Payment failed. Please try again or contact support if the problem persists.'
        });
    };

    const updateUserData = async (token) => {
        try {
            const updatedUserResponse = await fetch('/api/auth/profile/', {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (updatedUserResponse.ok) {
                const userData = await updatedUserResponse.json();
                const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
                if (userData.user) {
                    currentUser.tokens = userData.user.tokens;
                    currentUser.current_plan = userData.user.current_plan;
                    localStorage.setItem('user', JSON.stringify(currentUser));
                }
            }
        } catch (error) {
            console.error('Error updating user data:', error);
        }
    };

    const handleRetry = () => {
        fetchMembershipPlans();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-[#1a1a2e] rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="p-6 border-b border-[#3a2a5a]/50">
                    <div className="flex justify-between items-center">
                        <h2 className="text-2xl font-bold text-white">Upgrade Your Plan</h2>
                        <button
                            onClick={handleClose}
                            className="text-gray-400 hover:text-white transition-colors"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    {paymentResult ? (
                        <PaymentResultDisplay result={paymentResult} onClose={handleClose} />
                    ) : showPaymentForm && selectedPlan ? (
                        <PaymentFormDisplay
                            plan={selectedPlan}
                            onBack={() => setShowPaymentForm(false)}
                            onSuccess={handlePaymentSuccess}
                            onError={handlePaymentError}
                        />
                    ) : error ? (
                        <ErrorDisplay error={error} onRetry={handleRetry} onClose={handleClose} />
                    ) : (
                        <PlanSelectionDisplay
                            plans={plans}
                            loading={loading}
                            onSelectPlan={handleSelectPlan}
                            formatCurrency={formatCurrency}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

// Error Display Component
const ErrorDisplay = ({ error, onRetry, onClose }) => {
    return (
        <div className="text-center py-12">
            <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">加载失败</h3>
            <p className="text-gray-400 mb-6">{error}</p>
            <div className="flex justify-center space-x-4">
                <button
                    onClick={onRetry}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
                >
                    重试
                </button>
                <button
                    onClick={onClose}
                    className="bg-gray-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-gray-700 transition-all duration-300"
                >
                    关闭
                </button>
            </div>
        </div>
    );
};

// Plan Selection Component
const PlanSelectionDisplay = ({ plans, loading, onSelectPlan, formatCurrency }) => {
    if (loading) {
        return (
            <div className="text-center py-12">
                <div className="w-16 h-16 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-white">Loading plans...</p>
            </div>
        );
    }

    if (plans.length === 0) {
        return (
            <div className="text-center py-12">
                <p className="text-gray-400">暂无可用套餐</p>
            </div>
        );
    }

    return (
        <div>
            <div className="text-center mb-8">
                <h3 className="text-xl text-white mb-2">Choose Your Token Package</h3>
                <p className="text-gray-400">Select a plan to continue creating amazing content</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {plans.map((plan) => {
                    const features = PLAN_FEATURES[plan.plan_type] || { creations: [], features: [] };
                    
                    return (
                        <div
                            key={plan.plan_type}
                            className={`bg-[#241b3a] rounded-xl p-6 border border-[#3a2a5a]/50 hover:border-purple-500/50 transition-all duration-300 cursor-pointer transform hover:scale-105 ${plan.plan_type === 'PREMIUM' ? 'ring-2 ring-purple-500/50' : ''
                                }`}
                            onClick={() => onSelectPlan(plan)}
                        >
                            {plan.plan_type === 'PREMIUM' && (
                                <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full mb-4 text-center">
                                    MOST POPULAR
                                </div>
                            )}

                            <div className="text-center">
                                <h4 className="text-lg font-bold text-white mb-2">{plan.name}</h4>
                                <div className="text-3xl font-bold text-purple-400 mb-2">
                                    {formatCurrency(plan.price_usd)}
                                </div>
                                <p className="text-gray-400 text-sm mb-4">
                                    {plan.duration_days === 365 ? 'per year' : 'per month'}
                                </p>

                                {/* Token数量 */}
                                <div className="mb-6">
                                    <span className="text-3xl font-bold text-white">
                                        {plan.tokens?.toLocaleString() || 'N/A'}
                                    </span>
                                    <span className="text-gray-400 text-sm block">Tokens</span>
                                </div>

                                {/* 创作能力 */}
                                {features.creations && features.creations.length > 0 && (
                                    <div className="mb-4 text-left">
                                        <h4 className="text-white text-sm font-semibold mb-2">What you can create:</h4>
                                        <ul className="space-y-2">
                                            {features.creations.map((creation, index) => (
                                                <li key={index} className="flex items-start text-gray-300 text-xs">
                                                    <svg className="w-3 h-3 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>{creation}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                {/* 功能特性 */}
                                {features.features && features.features.length > 0 && (
                                    <div className="mb-4 text-left">
                                        <h4 className="text-white text-sm font-semibold mb-2">Features & Benefits:</h4>
                                        <ul className="space-y-2">
                                            {features.features.slice(0, 3).map((feature, index) => (
                                                <li key={index} className="flex items-start text-gray-300 text-xs">
                                                    <svg className="w-3 h-3 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>{feature}</span>
                                                </li>
                                            ))}
                                            {features.features.length > 3 && (
                                                <li className="text-gray-400 text-xs text-center">
                                                    +{features.features.length - 3} more features
                                                </li>
                                            )}
                                        </ul>
                                    </div>
                                )}

                                {plan.discount_info && (
                                    <div className="bg-green-600/20 text-green-400 text-xs font-bold px-3 py-1 rounded-full mb-4">
                                        {plan.discount_info}
                                    </div>
                                )}

                                <button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-3 px-4 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300">
                                    Select Plan
                                </button>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

// Payment Form Component
const PaymentFormDisplay = ({ plan, onBack, onSuccess, onError }) => {
    return (
        <div>
            <div className="flex items-center mb-6">
                <button
                    onClick={onBack}
                    className="text-gray-400 hover:text-white transition-colors mr-4"
                >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <h3 className="text-xl font-bold text-white">Complete Your Purchase</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Plan Summary */}
                <div className="bg-[#241b3a] rounded-xl p-6 border border-[#3a2a5a]/50">
                    <h4 className="text-lg font-bold text-white mb-4">Order Summary</h4>
                    <div className="space-y-3">
                        <div className="flex justify-between">
                            <span className="text-gray-400">Plan:</span>
                            <span className="text-white font-medium">{plan.name}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-400">Tokens:</span>
                            <span className="text-white font-medium">{plan.tokens?.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-400">Duration:</span>
                            <span className="text-white font-medium">{plan.duration_days} days</span>
                        </div>
                        <div className="border-t border-[#3a2a5a]/50 pt-3">
                            <div className="flex justify-between text-lg font-bold">
                                <span className="text-white">Total:</span>
                                <span className="text-purple-400">${parseFloat(plan.price_usd).toFixed(2)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Payment Form */}
                <div>
                    <StripePaymentForm
                        planDetails={{
                            type: plan.plan_type,
                            name: plan.name,
                            price: parseFloat(plan.price_usd),
                            tokens: plan.tokens
                        }}
                        onSuccess={onSuccess}
                        onError={onError}
                        onCancel={onBack}
                    />
                </div>
            </div>
        </div>
    );
};

// Payment Result Component
const PaymentResultDisplay = ({ result, onClose }) => {
    return (
        <div className="text-center py-12">
            {result.success ? (
                <div>
                    <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">Payment Successful!</h3>
                    <p className="text-gray-400 mb-6">{result.message}</p>
                    {result.tokens_added && (
                        <div className="bg-[#241b3a] rounded-xl p-6 border border-[#3a2a5a]/50 mb-6 max-w-md mx-auto">
                            <div className="text-3xl font-bold text-purple-400 mb-2">
                                +{result.tokens_added?.toLocaleString()}
                            </div>
                            <div className="text-gray-400">Tokens Added</div>
                        </div>
                    )}
                    <button
                        onClick={onClose}
                        className="bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-3 px-8 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
                    >
                        Continue Creating
                    </button>
                </div>
            ) : (
                <div>
                    <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">Payment Failed</h3>
                    <p className="text-gray-400 mb-6">{result.error}</p>
                    <button
                        onClick={onClose}
                        className="bg-gray-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-gray-700 transition-all duration-300"
                    >
                        Try Again
                    </button>
                </div>
            )}
        </div>
    );
};

export default UpgradeModal; 