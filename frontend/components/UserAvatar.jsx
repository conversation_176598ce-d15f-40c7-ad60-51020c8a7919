'use client'

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useUser } from '../contexts/UserContext';
import { useLanguage } from '../contexts/LanguageContext';
import { getDisplayPlan } from '../utils/planMapping';

// User avatar component - Reference GPT website design
const UserAvatar = ({
    size = 'md',
    showDropdown = true,
    className = '',
    onUpgrade
}) => {
    const { user, profile, actions } = useUser();
    const { language, setLanguage } = useLanguage();
    const router = useRouter();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Size configuration
    const sizeConfig = {
        sm: {
            avatar: 'w-8 h-8 text-sm',
            dropdown: 'w-64'
        },
        md: {
            avatar: 'w-10 h-10 text-base',
            dropdown: 'w-72'
        },
        lg: {
            avatar: 'w-12 h-12 text-lg',
            dropdown: 'w-80'
        }
    };

    // Get user email initials
    const getInitials = () => {
        if (!user) return 'U';

        if (user.email) {
            return user.email.charAt(0).toUpperCase();
        }

        if (user.username) {
            return user.username.charAt(0).toUpperCase();
        }

        return 'U';
    };

    // Click outside to close dropdown menu
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsDropdownOpen(false);
            }
        };

        if (isDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isDropdownOpen]);

    // Handle logout
    const handleLogout = async () => {
        setIsDropdownOpen(false);
        actions.logout();
        router.push('/auth/login');
    };

    // Handle navigation
    const handleNavigation = (path) => {
        setIsDropdownOpen(false);
        router.push(path);
    };

    if (!user) {
        return null;
    }

    return (
        <div className={`relative ${className} z-[99999]`} ref={dropdownRef}>
            {/* Avatar button */}
            <button
                onClick={() => showDropdown && setIsDropdownOpen(!isDropdownOpen)}
                disabled={!showDropdown}
                className={`
          group relative flex items-center justify-center
          ${sizeConfig[size].avatar}
          bg-transparent
          backdrop-blur-sm
          hover:backdrop-blur-md
          rounded-full
          text-white font-semibold
          border-2 border-purple-400/30 hover:border-purple-400/50
          shadow-lg hover:shadow-xl
          transition-all duration-200
          opacity-80 hover:opacity-100
          ${showDropdown ? 'cursor-pointer hover:scale-105' : 'cursor-default'}
          focus:outline-none focus:ring-2 focus:ring-purple-400/50 focus:ring-offset-2 focus:ring-offset-transparent
        `}
            >
                {getInitials()}

                {/* Natural breathing online status indicator */}
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3">
                    <div className="w-3 h-3 bg-green-500 border-2 border-black rounded-full animate-pulse" style={{ animationDuration: '2s' }}></div>
                    <div className="absolute inset-0.5 w-2 h-2 bg-green-300 rounded-full opacity-80" style={{ animation: 'pulse 3s infinite' }}></div>
                </div>

                {/* Hover light effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-purple-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>

            {/* Dropdown menu */}
            {showDropdown && isDropdownOpen && (
                <div className={`
          absolute right-0 top-full mt-3 
          ${sizeConfig[size].dropdown}
          bg-black/90 backdrop-blur-3xl
          border border-white/10
          rounded-3xl shadow-2xl shadow-black/80
          py-6 z-[99999]
          animate-fadeInUp
          overflow-hidden
          before:absolute before:inset-0 before:rounded-3xl before:bg-gradient-to-br before:from-white/5 before:via-transparent before:to-transparent before:pointer-events-none
        `}>
                    {/* User info section - compact book-like design */}
                    <div className="px-6 py-4 mb-4">
                        <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-transparent border border-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm flex-shrink-0">
                                <span className="text-white/90 font-bold text-lg">
                                    {getInitials()}
                                </span>
                            </div>
                            <div className="flex-1 min-w-0">
                                <div className="text-white font-semibold text-base mb-1 truncate">
                                    {user.username || user.email?.split('@')[0] || 'User'}
                                </div>
                                <div className="text-white/60 text-sm mb-2 word-break-keep-all overflow-wrap-anywhere">
                                    {user.email}
                                </div>
                                <div className="flex items-center space-x-2">
                                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                                    <span className="text-green-400 text-xs font-medium">
                                        {language === 'zh' ? '在线' : 'Online'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Plan and balance info - simplified list style */}
                    <div className="px-6 py-2 mb-4 space-y-3">
                        <div className="flex items-center justify-between">
                            <span className="text-white/60 text-sm">
                                {language === 'zh' ? '当前计划' : 'Current Plan'}
                            </span>
                            <span className="text-white font-medium text-sm">
                                {getDisplayPlan(profile.current_plan) || 'Free Plan'}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-white/60 text-sm">
                                {language === 'zh' ? 'Token余额' : 'Token Balance'}
                            </span>
                            <span className="text-white font-medium text-sm">
                                {profile.balance !== null ? profile.balance.toLocaleString() : '...'}
                            </span>
                        </div>
                    </div>

                    {/* Divider */}
                    <div className="mx-6 h-px bg-white/10 mb-4"></div>

                    {/* Menu items - clean list style */}
                    <div className="space-y-1">
                        <MenuButton
                            icon={ProfileIcon}
                            label={language === 'zh' ? '个人资料' : 'Profile'}
                            onClick={() => handleNavigation('/profile')}
                        />
                        <MenuButton
                            icon={SettingsIcon}
                            label={language === 'zh' ? '设置' : 'Settings'}
                            onClick={() => handleNavigation('/profile?tab=settings')}
                        />

                        <div className="my-3 mx-6 h-px bg-white/10"></div>

                        <MenuButton
                            icon={LogoutIcon}
                            label={language === 'zh' ? '退出登录' : 'Sign out'}
                            onClick={handleLogout}
                            variant="danger"
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

// 菜单按钮组件
const MenuButton = ({ icon: Icon, label, onClick, variant = 'default' }) => {
    const baseClasses = "w-full px-6 py-2.5 text-left flex items-center space-x-3 text-sm font-medium transition-all duration-200 rounded-xl";
    const variantClasses = {
        default: "text-white/70 hover:text-white hover:bg-white/8 backdrop-blur-sm",
        danger: "text-red-400/80 hover:text-red-300 hover:bg-red-500/10 backdrop-blur-sm"
    };

    return (
        <button
            onClick={onClick}
            className={`${baseClasses} ${variantClasses[variant]}`}
        >
            <Icon className="w-4 h-4 flex-shrink-0" />
            <span className="flex-1">{label}</span>
        </button>
    );
};

// 图标组件
const ProfileIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
);

const SettingsIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
);

const GalleryIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
);



const LogoutIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
    </svg>
);

export default UserAvatar; 