import React, { useState, useEffect } from 'react';

const StripeStatusChecker = ({ onStatusChange }) => {
    const [status, setStatus] = useState({
        stripeJs: null,
        adBlocker: null,
        networkAccess: null,
        configValid: null
    });

    const [showDetails, setShowDetails] = useState(false);

    useEffect(() => {
        checkStripeStatus();
    }, []);

    const checkStripeStatus = async () => {
        // 1. 检查配置
        const configValid = !!(
            process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY &&
            process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.startsWith('pk_')
        );
        setStatus(prev => ({ ...prev, configValid }));

        // 2. 检查广告拦截器
        const adBlockerDetected = await detectAdBlocker();
        setStatus(prev => ({ ...prev, adBlocker: !adBlockerDetected }));

        // 3. 检查Stripe JS加载
        if (configValid) {
            try {
                const { loadStripe } = await import('@stripe/stripe-js');
                const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
                setStatus(prev => ({ ...prev, stripeJs: !!stripe }));
            } catch (error) {
                setStatus(prev => ({ ...prev, stripeJs: false }));
            }
        }

        // 4. 检查网络访问（简单检查）
        try {
            // 不发送实际请求，只检查能否创建请求
            const testRequest = new Request('https://api.stripe.com');
            setStatus(prev => ({ ...prev, networkAccess: true }));
        } catch (error) {
            setStatus(prev => ({ ...prev, networkAccess: false }));
        }

        // 通知父组件状态变化
        const allGood = configValid && !adBlockerDetected;
        if (onStatusChange) {
            onStatusChange(allGood);
        }
    };

    const detectAdBlocker = () => {
        return new Promise((resolve) => {
            try {
                const testDiv = document.createElement('div');
                testDiv.className = 'adsbox ad-banner ads';
                testDiv.style.position = 'absolute';
                testDiv.style.left = '-9999px';
                testDiv.style.height = '1px';
                testDiv.style.width = '1px';
                testDiv.style.visibility = 'hidden';
                document.body.appendChild(testDiv);

                setTimeout(() => {
                    try {
                        const isBlocked = testDiv.offsetHeight === 0 || testDiv.offsetWidth === 0;
                        document.body.removeChild(testDiv);
                        resolve(isBlocked);
                    } catch (e) {
                        resolve(false);
                    }
                }, 100);
            } catch (e) {
                resolve(false);
            }
        });
    };

    const getStatusIcon = (statusValue) => {
        if (statusValue === null) return '⏳';
        return statusValue ? '✅' : '❌';
    };

    const getOverallStatus = () => {
        const { configValid, adBlocker, stripeJs } = status;
        if (configValid === false) return 'error';
        if (adBlocker === false) return 'warning';
        if (stripeJs === false) return 'warning';
        if (configValid && adBlocker && stripeJs) return 'success';
        return 'checking';
    };

    const overallStatus = getOverallStatus();

    // 如果一切正常，不显示任何提示
    if (overallStatus === 'success') {
        return null;
    }

    return (
        <div className={`mb-4 p-4 rounded-lg border ${overallStatus === 'error'
            ? 'bg-red-500/10 border-red-500/30'
            : overallStatus === 'warning'
                ? 'bg-yellow-500/10 border-yellow-500/30'
                : 'bg-blue-500/10 border-blue-500/30'
            }`}>
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <span className="text-lg mr-2">
                        {overallStatus === 'error' ? '🚫' : overallStatus === 'warning' ? '⚠️' : '🔍'}
                    </span>
                    <span className={`font-medium ${overallStatus === 'error' ? 'text-red-300' :
                        overallStatus === 'warning' ? 'text-yellow-300' : 'text-blue-300'
                        }`}>
                        {overallStatus === 'error' ? '支付配置错误' :
                            overallStatus === 'warning' ? '支付可能受到干扰' : '检查支付状态中...'}
                    </span>
                </div>
                <button
                    onClick={() => setShowDetails(!showDetails)}
                    className="text-sm px-3 py-1 rounded bg-white/10 hover:bg-white/20 transition-colors"
                >
                    {showDetails ? '隐藏详情' : '查看详情'}
                </button>
            </div>

            {showDetails && (
                <div className="mt-4 space-y-2">
                    <div className="text-sm space-y-2">
                        <div className="flex justify-between items-center">
                            <span>Stripe配置</span>
                            <span>{getStatusIcon(status.configValid)} {status.configValid ? '正常' : '错误'}</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span>广告拦截器状态</span>
                            <span>{getStatusIcon(status.adBlocker)} {status.adBlocker ? '未检测到' : '已检测到'}</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span>Stripe JS加载</span>
                            <span>{getStatusIcon(status.stripeJs)} {status.stripeJs ? '成功' : '失败'}</span>
                        </div>
                    </div>

                    {status.adBlocker === false && (
                        <div className="mt-3 p-3 bg-black/20 rounded border border-white/10">
                            <p className="text-sm text-gray-300 mb-2">
                                <strong>检测到广告拦截器，请尝试：</strong>
                            </p>
                            <ul className="text-xs text-gray-400 space-y-1 list-disc list-inside ml-2">
                                <li>暂时禁用广告拦截器 (uBlock Origin, AdBlock Plus等)</li>
                                <li>将本站点和 *.stripe.com 添加到白名单</li>
                                <li>使用无痕/隐私浏览模式</li>
                                <li>尝试其他浏览器或移动设备</li>
                            </ul>
                        </div>
                    )}

                    <div className="mt-3 flex space-x-2">
                        <button
                            onClick={checkStripeStatus}
                            className="text-xs px-3 py-1 bg-blue-500/20 hover:bg-blue-500/30 rounded transition-colors"
                        >
                            🔄 重新检查
                        </button>
                        <a
                            href="/stripe-troubleshoot"
                            className="text-xs px-3 py-1 bg-gray-500/20 hover:bg-gray-500/30 rounded transition-colors"
                        >
                            📖 故障排除指南
                        </a>
                    </div>
                </div>
            )}
        </div>
    );
};

export default StripeStatusChecker; 