import { useEffect } from 'react';

const ErrorSuppressor = () => {
    useEffect(() => {
        // 保存原始的console方法
        const originalError = console.error;
        const originalWarn = console.warn;
        const originalLog = console.log;

        // 需要抑制的错误模式
        const suppressedPatterns = [
            'ERR_BLOCKED_BY_CLIENT',
            'Failed to load resource: net::ERR_BLOCKED_BY_CLIENT',
            'stripe.com',
            'stripe.network',
            'hooks.stripe.com',
            'api.stripe.com',
            'js.stripe.com',
            'shared-f871b6574',
            'FetchError: Error fetching',
            'Uncaught (in promise) FetchError',
            'private-state-token-redemption',
            'private-state-token-issuance'
        ];

        // 智能错误过滤函数
        const shouldSuppressMessage = (message) => {
            const messageStr = String(message);
            return suppressedPatterns.some(pattern =>
                messageStr.includes(pattern)
            );
        };

        // 重写console.error
        console.error = (...args) => {
            const message = args.join(' ');
            if (!shouldSuppressMessage(message)) {
                originalError.apply(console, args);
            }
        };

        // 重写console.warn  
        console.warn = (...args) => {
            const message = args.join(' ');
            if (!shouldSuppressMessage(message)) {
                originalWarn.apply(console, args);
            }
        };

        // 添加全局错误处理器
        const handleGlobalError = (event) => {
            if (shouldSuppressMessage(event.message || '')) {
                event.preventDefault();
                return false;
            }
        };

        const handleUnhandledRejection = (event) => {
            if (shouldSuppressMessage(event.reason?.message || event.reason || '')) {
                event.preventDefault();
                return false;
            }
        };

        // 监听全局错误
        window.addEventListener('error', handleGlobalError);
        window.addEventListener('unhandledrejection', handleUnhandledRejection);

        // 清理函数
        return () => {
            console.error = originalError;
            console.warn = originalWarn;
            window.removeEventListener('error', handleGlobalError);
            window.removeEventListener('unhandledrejection', handleUnhandledRejection);
        };
    }, []);

    return null; // 这个组件不渲染任何内容
};

export default ErrorSuppressor; 