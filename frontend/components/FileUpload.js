import { useRef } from 'react';

export default function FileUpload({ onFileSelect, file, onClear, disabled }) {
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    onFileSelect(file || null);
  };

  const handleClear = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
    onClear();
  };

  return (
    <div className="flex items-center space-x-2">
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*,video/*"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled}
      />
      <button
        type="button"
        className="px-3 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-400 text-white font-bold shadow hover:scale-105 transition"
        onClick={() => fileInputRef.current && fileInputRef.current.click()}
        disabled={disabled}
      >
        📎 Select File
      </button>
      {file && (
        <>
          <span className="text-blue-200 bg-blue-900/40 px-2 py-1 rounded text-xs max-w-xs truncate">{file.name}</span>
          <button
            type="button"
            onClick={handleClear}
            className="px-2 py-1 bg-red-500 text-white rounded text-xs font-bold hover:bg-red-600 transition"
            disabled={disabled}
          >Clear</button>
        </>
      )}
    </div>
  );
} 
