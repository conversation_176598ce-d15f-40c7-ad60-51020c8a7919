import React, { useState } from 'react';

const ReferencePreview = ({ references, onRemove, onPreview }) => {
    const [hoveredImage, setHoveredImage] = useState(null);
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

    console.log('🔧 [DEBUG] ReferencePreview received:', {
        references: references,
        hasReferences: !!references,
        length: references?.length || 0,
        firstRef: references?.[0]
    });

    if (!references || references.length === 0) {
        console.log('🔧 [DEBUG] ReferencePreview: No references, returning null');
        return null;
    }

    const handleMouseEnter = (ref, event) => {
        if (ref.type === 'image') {
            setHoveredImage(ref);
            const rect = event.currentTarget.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const previewWidth = 280;
            const previewHeight = 220;

            // Smart positioning - avoid covering the original image
            let x = rect.right + 16; // Default to right side
            let y = rect.top - 10;

            // If not enough space on the right, show on the left
            if (x + previewWidth > viewportWidth - 16) {
                x = rect.left - previewWidth - 16;
            }

            // If still not enough space, use center positioning
            if (x < 16) {
                x = Math.max(16, (viewportWidth - previewWidth) / 2);
                y = rect.top - previewHeight - 16; // Show above
            }

            // Adjust vertical position to stay within viewport
            if (y + previewHeight > viewportHeight - 16) {
                y = Math.max(16, viewportHeight - previewHeight - 16);
            }

            // Ensure minimum distance from original image
            if (Math.abs(x - rect.left) < previewWidth && Math.abs(y - rect.top) < previewHeight) {
                y = rect.bottom + 16; // Show below if too close
                if (y + previewHeight > viewportHeight - 16) {
                    y = rect.top - previewHeight - 16; // Show above if below doesn't fit
                }
            }

            setMousePosition({ x, y });
        }
    };

    const handleMouseLeave = () => {
        setHoveredImage(null);
    };

    return (
        <>
            {/* Compact reference bar */}
            <div className="flex items-center gap-2 p-2 bg-purple-500/8 border border-purple-500/20 rounded-xl mb-3 backdrop-blur-sm">
                <div className="flex items-center gap-1.5 flex-wrap">
                    {references.map((ref, index) => (
                        <div
                            key={`${ref.reference_id}-${index}`}
                            className="relative group"
                        >
                            {/* Compact thumbnail */}
                            <div
                                className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-800 border border-purple-500/30 hover:border-purple-500/60 transition-all duration-200 cursor-pointer hover:scale-105"
                                onMouseEnter={(e) => handleMouseEnter(ref, e)}
                                onMouseLeave={handleMouseLeave}
                                onClick={() => onPreview && onPreview(ref)}
                                title={`${ref.display_name} - Click to enlarge, hover to preview`}
                            >
                                {ref.type === 'image' ? (
                                    <img
                                        src={ref.url}
                                        alt={ref.display_name}
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                            e.target.style.display = 'none';
                                            e.target.nextSibling.style.display = 'flex';
                                        }}
                                    />
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center text-lg bg-gradient-to-br from-gray-700 to-gray-800">
                                        🎥
                                    </div>
                                )}

                                {/* Fallback icon */}
                                <div className="hidden w-full h-full items-center justify-center text-lg bg-gray-800">
                                    {ref.type === 'image' ? '🖼️' : '🎥'}
                                </div>

                                {/* Remove button - only show on hover */}
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onRemove && onRemove(ref, index);
                                    }}
                                    className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 hover:bg-red-600 text-white text-xs rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10 hover:scale-110"
                                    title="Remove reference"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Reference count and info */}
                <div className="flex items-center space-x-2 ml-auto">
                    <span className="text-purple-300 text-xs bg-purple-500/20 px-2 py-1 rounded-lg">
                        {references.length} reference{references.length > 1 ? 's' : ''}
                    </span>
                </div>
            </div>

            {/* Enhanced hover preview */}
            {hoveredImage && (
                <div
                    className="fixed z-50 pointer-events-none"
                    style={{
                        left: mousePosition.x,
                        top: mousePosition.y,
                    }}
                >
                    <div className="bg-gray-900/98 backdrop-blur-xl border border-purple-400/40 rounded-2xl shadow-2xl shadow-purple-500/30 p-3 max-w-xs animate-in fade-in zoom-in-95 duration-200">
                        <div className="relative">
                            <img
                                src={hoveredImage.url}
                                alt={hoveredImage.display_name}
                                className="w-full max-w-64 h-auto rounded-xl object-cover"
                                style={{ maxHeight: '180px' }}
                            />
                            <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded-lg">
                                @{hoveredImage.reference_id}
                            </div>
                        </div>
                        <div className="mt-2 space-y-1">
                            <div className="text-white text-sm font-medium">
                                {hoveredImage.display_name}
                            </div>
                            <div className="flex items-center text-purple-300 text-xs">
                                <span className="mr-1">👁️</span>
                                <span>Hover preview • Click to enlarge</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default ReferencePreview; 