'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useUser } from '../contexts/UserContext';
import { useLanguage } from '../contexts/LanguageContext';
import UserAvatar from './UserAvatar';
import { Logo, LogoCompact } from './ui/logo';

const ModernNavbar = ({
    className = '',
    onUpgrade,
    variant = 'default' // 'default', 'minimal', 'transparent'
}) => {
    const { user, isAuthenticated, isLoading } = useUser();
    const { language } = useLanguage();
    const router = useRouter();
    const [isScrolled, setIsScrolled] = useState(false);
    const [showUserMenu, setShowUserMenu] = useState(false);

    // Listen to scroll events
    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    // Navigation items configuration - Remove Home and Gallery, keep only Chat
    const navigationItems = [];

    // Filter navigation items that require authentication
    const filteredNavItems = navigationItems.filter(item =>
        !item.requireAuth || isAuthenticated
    );

    // Unified purple theme background
    const getNavbarBackground = () => {
        const baseClasses = 'bg-black/20 backdrop-blur-xl border-b border-purple-500/20';
        if (isScrolled) {
            return `${baseClasses} shadow-lg shadow-purple-500/10`;
        }
        return baseClasses;
    };

    return (
        <nav className={`
      sticky top-0 z-50 transition-all duration-300
      ${getNavbarBackground()}
      ${className}
    `}>
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
                {/* Logo */}
                <Link href="/" className="flex items-center">
                    <LogoCompact
                        className="hover:scale-105 transition-transform duration-300"
                        showText={true}
                    />
                </Link>

                {/* Center Navigation - Hidden on smaller screens */}
                <div className="hidden lg:flex items-center space-x-8">
                    {filteredNavItems.map((item) => (
                        <NavItem key={item.href} {...item} />
                    ))}
                </div>

                {/* Right user area */}
                <div className="flex items-center space-x-4">
                    {/* User authentication status */}
                    {isLoading ? (
                        <div className="w-8 h-8 rounded-full bg-purple-500/20 animate-pulse"></div>
                    ) : isAuthenticated ? (
                        <div className="flex items-center space-x-3">
                            {/* Creative button moved to left of avatar */}
                            <Link href="/creative">
                                <button className="group relative p-2 text-purple-300 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300 border border-transparent hover:border-purple-500/20">
                                    <CreateIcon className="w-5 h-5" />
                                </button>
                            </Link>

                            {/* Transparent avatar with blinking green online indicator */}
                            <div className="relative">
                                <UserAvatar
                                    onUpgrade={onUpgrade}
                                    className="bg-transparent hover:ring-2 hover:ring-purple-400/50 transition-all duration-300 opacity-80 hover:opacity-100"
                                />
                                {/* Blinking green online indicator */}
                                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-black animate-pulse"></div>
                            </div>
                        </div>
                    ) : (
                        <div className="flex items-center space-x-3">
                            <Link href="/auth/login">
                                <button className="px-4 py-2 text-sm font-medium text-purple-300 hover:text-white bg-white/5 hover:bg-white/10 rounded-lg border border-purple-500/20 hover:border-purple-400/40 transition-all duration-300">
                                    {language === 'zh' ? '登录' : 'Sign in'}
                                </button>
                            </Link>
                            <Link href="/auth/beta-apply">
                                <button className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-[#6d28d9] to-[#8b5cf6] hover:from-[#7c3aed] hover:to-[#a855f7] rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/25 transform hover:scale-105">
                                    {language === 'zh' ? '内测申请' : 'Beta Apply'}
                                </button>
                            </Link>
                        </div>
                    )}

                    {/* Mobile menu button */}
                    <MobileMenuButton
                        navigationItems={filteredNavItems}
                        isAuthenticated={isAuthenticated}
                        language={language}
                    />
                </div>
            </div>
        </nav>
    );
};

// Enhanced navigation item component
const NavItem = ({ href, label, icon: Icon, active }) => {
    return (
        <Link href={href}>
            <button className={`
        relative flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 group
        ${active
                    ? 'text-white bg-gradient-to-r from-[#6d28d9]/30 to-[#8b5cf6]/30 border border-purple-400/30'
                    : 'text-purple-300 hover:text-white hover:bg-white/5 border border-transparent hover:border-purple-500/20'
                }
      `}>
                <Icon className="w-4 h-4" />
                <span>{label}</span>
                {active && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-purple-400 rounded-full animate-pulse"></div>
                )}
            </button>
        </Link>
    );
};

// Enhanced mobile menu button
const MobileMenuButton = ({ navigationItems, isAuthenticated, language }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="lg:hidden relative">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="p-2 text-purple-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 border border-transparent hover:border-purple-500/20"
            >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {isOpen ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    )}
                </svg>
            </button>

            {/* Enhanced mobile dropdown menu */}
            {isOpen && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-black/40 backdrop-blur-xl border border-purple-500/20 rounded-xl shadow-xl py-2 z-50">
                    {navigationItems.map((item) => (
                        <Link key={item.href} href={item.href}>
                            <button
                                onClick={() => setIsOpen(false)}
                                className={`
                  w-full px-4 py-3 text-left flex items-center space-x-3 text-sm transition-all duration-200
                  ${item.active
                                        ? 'text-white bg-gradient-to-r from-[#6d28d9]/30 to-[#8b5cf6]/30 border-l-2 border-purple-400'
                                        : 'text-purple-300 hover:text-white hover:bg-white/5'
                                    }
                `}
                            >
                                <item.icon className="w-4 h-4" />
                                <span>{item.label}</span>
                            </button>
                        </Link>
                    ))}
                </div>
            )}
        </div>
    );
};

// Icon components
const HomeIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
    </svg>
);

const ChatIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
    </svg>
);

const CreateIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
    </svg>
);

const GalleryIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
);

export default ModernNavbar; 