'use client'

import React, { useState, useEffect, createContext, useContext } from 'react';

// Toast Context
const ToastContext = createContext();

// Toast Types
const TOAST_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// Toast Hook
export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};

// Toast Provider
export const ToastProvider = ({ children }) => {
    const [toasts, setToasts] = useState([]);

    const addToast = (message, type = TOAST_TYPES.INFO, duration = 5000, options = {}) => {
        const id = Date.now() + Math.random();
        const toast = {
            id,
            message,
            type,
            duration,
            ...options
        };

        setToasts(prev => [...prev, toast]);

        if (duration > 0) {
            setTimeout(() => {
                removeToast(id);
            }, duration);
        }

        return id;
    };

    const removeToast = (id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    };

    const removeAllToasts = () => {
        setToasts([]);
    };

    // 通用showToast方法
    const showToast = (message, type = TOAST_TYPES.INFO, duration = 5000, options = {}) => {
        return addToast(message, type, duration, options);
    };

    // 便捷方法
    const toast = {
        success: (message, options) => addToast(message, TOAST_TYPES.SUCCESS, 4000, options),
        error: (message, options) => addToast(message, TOAST_TYPES.ERROR, 6000, options),
        warning: (message, options) => addToast(message, TOAST_TYPES.WARNING, 5000, options),
        info: (message, options) => addToast(message, TOAST_TYPES.INFO, 4000, options),
        custom: addToast,
        remove: removeToast,
        clear: removeAllToasts,
        showToast // 添加showToast方法
    };

    return (
        <ToastContext.Provider value={toast}>
            {children}
            <ToastContainer toasts={toasts} onRemove={removeToast} />
        </ToastContext.Provider>
    );
};

// Toast Container
const ToastContainer = ({ toasts, onRemove }) => {
    if (toasts.length === 0) return null;

    return (
        <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
            {toasts.map(toast => (
                <ToastItem
                    key={toast.id}
                    toast={toast}
                    onRemove={() => onRemove(toast.id)}
                />
            ))}
        </div>
    );
};

// Toast Item
const ToastItem = ({ toast, onRemove }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isLeaving, setIsLeaving] = useState(false);

    useEffect(() => {
        // 延迟显示动画
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
    }, []);

    const handleRemove = () => {
        setIsLeaving(true);
        setTimeout(onRemove, 300); // 等待动画完成
    };

    // 配置样式
    const typeConfig = {
        success: {
            bg: 'bg-green-50 dark:bg-green-900/20',
            border: 'border-green-200 dark:border-green-800',
            text: 'text-green-800 dark:text-green-200',
            icon: <CheckIcon className="w-5 h-5 text-green-500" />
        },
        error: {
            bg: 'bg-red-50 dark:bg-red-900/20',
            border: 'border-red-200 dark:border-red-800',
            text: 'text-red-800 dark:text-red-200',
            icon: <ErrorIcon className="w-5 h-5 text-red-500" />
        },
        warning: {
            bg: 'bg-yellow-50 dark:bg-yellow-900/20',
            border: 'border-yellow-200 dark:border-yellow-800',
            text: 'text-yellow-800 dark:text-yellow-200',
            icon: <WarningIcon className="w-5 h-5 text-yellow-500" />
        },
        info: {
            bg: 'bg-blue-50 dark:bg-blue-900/20',
            border: 'border-blue-200 dark:border-blue-800',
            text: 'text-blue-800 dark:text-blue-200',
            icon: <InfoIcon className="w-5 h-5 text-blue-500" />
        }
    };

    const config = typeConfig[toast.type] || typeConfig.info;

    return (
        <div
            className={`
        relative flex items-start p-4 rounded-lg border shadow-lg backdrop-blur-sm
        ${config.bg} ${config.border}
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving
                    ? 'translate-x-0 opacity-100 scale-100'
                    : 'translate-x-full opacity-0 scale-95'
                }
      `}
        >
            {/* 图标 */}
            <div className="flex-shrink-0 mr-3">
                {config.icon}
            </div>

            {/* 内容 */}
            <div className="flex-1 min-w-0">
                {toast.title && (
                    <h3 className={`text-sm font-semibold ${config.text} mb-1`}>
                        {toast.title}
                    </h3>
                )}
                <p className={`text-sm ${config.text} ${toast.title ? 'opacity-90' : ''}`}>
                    {toast.message}
                </p>

                {toast.action && (
                    <div className="mt-3">
                        <button
                            onClick={toast.action.onClick}
                            className={`text-sm font-medium ${config.text} hover:underline focus:outline-none`}
                        >
                            {toast.action.label}
                        </button>
                    </div>
                )}
            </div>

            {/* 关闭按钮 */}
            <button
                onClick={handleRemove}
                className={`flex-shrink-0 ml-3 p-1 rounded-md ${config.text} hover:bg-black/5 dark:hover:bg-white/5 transition-colors focus:outline-none`}
            >
                <CloseIcon className="w-4 h-4" />
            </button>

            {/* 进度条 (如果有持续时间) */}
            {toast.duration > 0 && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/10 dark:bg-white/10 rounded-b-lg overflow-hidden">
                    <div
                        className={`h-full ${toast.type === 'success' ? 'bg-green-500' :
                            toast.type === 'error' ? 'bg-red-500' :
                                toast.type === 'warning' ? 'bg-yellow-500' :
                                    'bg-blue-500'
                            } animate-shrink-width`}
                        style={{ animationDuration: `${toast.duration}ms` }}
                    />
                </div>
            )}
        </div>
    );
};

// 图标组件
const CheckIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
);

const ErrorIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);

const WarningIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
);

const InfoIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

const CloseIcon = ({ className }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);

// 独立的Toast组件 (不依赖Provider)
export const Toast = ({
    type = 'info',
    message,
    title,
    onClose,
    action,
    className = ''
}) => {
    return (
        <ToastItem
            toast={{ type, message, title, action }}
            onRemove={onClose}
        />
    );
};

export default ToastProvider; 