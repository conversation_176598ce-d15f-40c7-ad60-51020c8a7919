import React from 'react';

/**
 * WebSocket连接状态指示器
 * 显示实时连接状态和断线重连提示
 */
const WebSocketStatus = ({ 
    connectionStatus, 
    isVisible = true, 
    showDetails = false 
}) => {
    // 不显示时返回null
    if (!isVisible) return null;

    // 状态配置
    const statusConfig = {
        connected: {
            icon: '🟢',
            text: '已连接',
            color: 'text-green-600',
            bgColor: 'bg-green-50',
            borderColor: 'border-green-200'
        },
        connecting: {
            icon: '🟡',
            text: '连接中',
            color: 'text-yellow-600',
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-200'
        },
        reconnecting: {
            icon: '🔄',
            text: '重连中',
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
            borderColor: 'border-blue-200'
        },
        disconnected: {
            icon: '🔴',
            text: '已断开',
            color: 'text-red-600',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200'
        },
        error: {
            icon: '❌',
            text: '连接错误',
            color: 'text-red-600',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200'
        },
        failed: {
            icon: '💥',
            text: '连接失败',
            color: 'text-red-700',
            bgColor: 'bg-red-100',
            borderColor: 'border-red-300'
        }
    };

    const config = statusConfig[connectionStatus] || statusConfig.disconnected;

    return (
        <div className={`
            inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
            ${config.color} ${config.bgColor} border ${config.borderColor}
            transition-all duration-300 ease-in-out
        `}>
            <span className="mr-2 animate-pulse">
                {config.icon}
            </span>
            
            <span className="mr-2">
                实时连接
            </span>
            
            <span className={`
                ${connectionStatus === 'connecting' || connectionStatus === 'reconnecting' 
                    ? 'animate-spin' : ''
                }
            `}>
                {config.text}
            </span>
            
            {showDetails && (
                <span className="ml-2 text-xs opacity-75">
                    ({connectionStatus})
                </span>
            )}
        </div>
    );
};

/**
 * 紧凑版WebSocket状态指示器
 * 用于在导航栏或工具栏中显示
 */
export const WebSocketStatusCompact = ({ connectionStatus, onClick }) => {
    const statusConfig = {
        connected: { icon: '🟢', title: 'WebSocket已连接' },
        connecting: { icon: '🟡', title: 'WebSocket连接中...' },
        reconnecting: { icon: '🔄', title: 'WebSocket重连中...' },
        disconnected: { icon: '🔴', title: 'WebSocket已断开' },
        error: { icon: '❌', title: 'WebSocket连接错误' },
        failed: { icon: '💥', title: 'WebSocket连接失败' }
    };

    const config = statusConfig[connectionStatus] || statusConfig.disconnected;

    return (
        <button
            onClick={onClick}
            title={config.title}
            className={`
                w-6 h-6 flex items-center justify-center rounded-full 
                hover:bg-gray-100 transition-colors duration-200
                ${connectionStatus === 'connecting' || connectionStatus === 'reconnecting' 
                    ? 'animate-pulse' : ''
                }
            `}
        >
            <span className="text-sm">
                {config.icon}
            </span>
        </button>
    );
};

/**
 * WebSocket状态详情模态框
 * 显示详细的连接信息和故障排除建议
 */
export const WebSocketStatusModal = ({ 
    isOpen, 
    onClose, 
    connectionStatus, 
    lastMessage,
    activeTasks = [],
    reconnectAttempts = 0
}) => {
    if (!isOpen) return null;

    const getStatusDescription = (status) => {
        switch (status) {
            case 'connected':
                return '您的设备已成功连接到实时服务，可以接收任务状态更新。';
            case 'connecting':
                return '正在建立与服务器的实时连接...';
            case 'reconnecting':
                return `连接中断，正在尝试重新连接... (尝试 ${reconnectAttempts}/5)`;
            case 'disconnected':
                return '与服务器的实时连接已断开。您可能无法收到最新的任务状态更新。';
            case 'error':
                return '连接过程中发生错误。请检查网络连接或刷新页面重试。';
            case 'failed':
                return '多次尝试连接失败。请检查网络状况或联系技术支持。';
            default:
                return '未知连接状态。';
        }
    };

    const getTroubleshootingTips = (status) => {
        const baseTips = [
            '检查网络连接是否正常',
            '尝试刷新页面',
            '检查浏览器是否阻止了WebSocket连接'
        ];

        switch (status) {
            case 'error':
            case 'failed':
            case 'disconnected':
                return [
                    ...baseTips,
                    '如果使用代理或VPN，请尝试关闭',
                    '清除浏览器缓存和Cookie',
                    '尝试使用其他浏览器'
                ];
            case 'reconnecting':
                return [
                    '请稍等，系统正在自动重连',
                    '避免频繁刷新页面',
                    ...baseTips.slice(0, 2)
                ];
            default:
                return baseTips;
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
                <div className="p-6">
                    {/* 标题 */}
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">
                            实时连接状态
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* 状态信息 */}
                    <div className="mb-6">
                        <WebSocketStatus 
                            connectionStatus={connectionStatus} 
                            showDetails={true} 
                        />
                        <p className="text-gray-600 mt-2 text-sm">
                            {getStatusDescription(connectionStatus)}
                        </p>
                    </div>

                    {/* 活动任务 */}
                    {activeTasks.length > 0 && (
                        <div className="mb-6">
                            <h4 className="font-medium text-gray-900 mb-2">
                                当前活动任务 ({activeTasks.length})
                            </h4>
                            <div className="space-y-2">
                                {activeTasks.map(task => (
                                    <div key={task.taskId} className="text-sm bg-gray-50 p-2 rounded">
                                        <div className="font-medium">{task.taskId}</div>
                                        <div className="text-gray-600">{task.status}: {task.message}</div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* 最后消息 */}
                    {lastMessage && (
                        <div className="mb-6">
                            <h4 className="font-medium text-gray-900 mb-2">最后收到的消息</h4>
                            <div className="text-sm bg-gray-50 p-2 rounded">
                                <div className="font-medium">类型: {lastMessage.type}</div>
                                <div className="text-gray-600">
                                    时间: {new Date(lastMessage.timestamp).toLocaleString()}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 故障排除 */}
                    {(connectionStatus === 'error' || connectionStatus === 'failed' || connectionStatus === 'disconnected') && (
                        <div className="mb-6">
                            <h4 className="font-medium text-gray-900 mb-2">故障排除建议</h4>
                            <ul className="text-sm text-gray-600 space-y-1">
                                {getTroubleshootingTips(connectionStatus).map((tip, index) => (
                                    <li key={index} className="flex items-start">
                                        <span className="text-blue-500 mr-2">•</span>
                                        {tip}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}

                    {/* 操作按钮 */}
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => window.location.reload()}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                        >
                            刷新页面
                        </button>
                        <button
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                        >
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WebSocketStatus; 