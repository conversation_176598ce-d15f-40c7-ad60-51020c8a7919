// 消息处理工具函数 - 从chat.js重构而来

// 常量定义
export const CHAT_MODES = {
    TEXT_CHAT_MODE: 'text_chat',
    TEXT_TO_IMG_MODE: 'text_to_image',
    IMG_TO_IMG_MODE: 'image_to_image',
    TEXT_TO_VIDEO_MODE: 'text_to_video',
    IMG_TO_VIDEO_MODE: 'image_to_video'
};

export const { TEXT_CHAT_MODE, TEXT_TO_IMG_MODE, IMG_TO_IMG_MODE, TEXT_TO_VIDEO_MODE, IMG_TO_VIDEO_MODE } = CHAT_MODES;

// 消息处理工具
export const messageUtils = {
    // 创建用户消息对象
    createUserMessage(content, currentFile = null, referencedMedia = []) {
        const userMessage = {
            id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // 用户消息使用临时ID
            sender: 'user',
            content: content,
            image_url: currentFile && currentFile.type.startsWith('image/') 
                ? URL.createObjectURL(currentFile) 
                : null,
            video_url: currentFile && currentFile.type.startsWith('video/') 
                ? URL.createObjectURL(currentFile) 
                : null,
            referenced_media: referencedMedia.length > 0 ? referencedMedia.map((ref, index) => ({
                reference_id: `${index + 1}`, // 使用简化编号格式
                type: ref.type,
                url: ref.url,
                display_name: ref.display_name || `Reference ${index + 1}`,
                message_id: ref.message_id // 保留原始消息ID
            })) : [],
            timestamp: Date.now()
        };
        
        console.log('👤 新用户消息创建');
        
        return userMessage;
    },

    // 创建加载状态消息
    createLoadingMessage(taskType = 'unknown') {
        return {
            id: `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // 加载消息使用临时ID
            sender: 'bot',
            isLoading: true,
            content: null,
            startTime: Date.now(), // 记录开始时间用于进度计算
            taskType: taskType, // 任务类型，将由响应更新
            timestamp: Date.now()
        };
    },

    // 创建异步任务处理消息 - 完全按照原始index.js的格式
    createAsyncTaskMessage(agentResult, currentMode, taskType) {
        console.log('🔄 [MessageUtils] 创建异步任务消息 (按原始index.js格式):', {
            taskId: agentResult.task_id,
            messageId: agentResult.message_id,
            status: agentResult.status,
            chatResponse: agentResult.chat_response
        });
        
        // 🔧 关键修复：确保异步任务消息始终使用后端返回的message_id作为唯一标识
        const asyncMessage = {
            id: agentResult.message_id, // ✅ 使用后端返回的message_id作为id字段
            sender: 'bot',
            content: agentResult.chat_response || 'Video is being generated...',
            taskId: agentResult.task_id,
            messageId: agentResult.message_id, // 也添加messageId字段以保持一致性
            isTaskProcessing: true,
            isLoading: true, // 显示加载动画
            needsTaskResume: true, // 🔧 新增：标记需要恢复轮询
            estimated_time: agentResult.estimated_time,
            timestamp: Date.now(),
            metadata: {
                // 🔧 新增：保存关键信息用于调试
                backend_message_id: agentResult.message_id,
                backend_task_id: agentResult.task_id,
                creation_time: new Date().toISOString(),
                task_type: taskType,
                requires_polling: true
            }
        };
        
        console.log('✅ [MessageUtils] 异步任务消息创建完成:', {
            messageId: asyncMessage.id,
            taskId: asyncMessage.taskId,
            needsTaskResume: asyncMessage.needsTaskResume
        });
        
        return asyncMessage;
    },

    // 创建成功结果消息
    createSuccessMessage(agentResult) {
        return {
            id: agentResult.message_id || `success_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // 🔧 修复：使用后端返回的message_id或临时ID
            sender: 'bot',
            content: agentResult.chat_response || agentResult.message || '',
            image_url: agentResult.image_url || null,
            video_url: agentResult.video_url || null,
            taskId: agentResult.task_id || null,
            messageId: agentResult.message_id || null,
            timestamp: Date.now()
        };
    },

    // 创建错误消息
    createErrorMessage(error, isTokenError = false) {
        return {
            id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // 🔧 修复：为错误消息生成唯一ID
            sender: 'bot',
            content: error.message || 'An error occurred',
            isError: true,
            isTokenError,
            timestamp: Date.now(),
            metadata: {
                error: true,
                errorType: isTokenError ? 'token_insufficient' : 'general',
                originalError: error.message || 'Unknown error'
            }
        };
    },

    // 判断是否是视频任务
    isVideoTask(taskType) {
        return ['video_gen', 'img2video', 'text_to_video', 'image_to_video', 'multi_step_composite'].includes(taskType);
    },

    // 判断是否应该显示按钮
    shouldShowModeButton(buttonMode, advancedMode, currentFile) {
        if (!advancedMode) return false;

        if (!currentFile) {
            // 无文件状态下，只显示文本生成相关按钮
            return [TEXT_CHAT_MODE, TEXT_TO_IMG_MODE, TEXT_TO_VIDEO_MODE].includes(buttonMode);
        }

        if (currentFile.type.startsWith('image/')) {
            // 图片文件状态下，显示图像处理相关按钮
            return [TEXT_CHAT_MODE, IMG_TO_IMG_MODE, IMG_TO_VIDEO_MODE].includes(buttonMode);
        }

        // 视频文件状态下，只显示聊天按钮
        return buttonMode === TEXT_CHAT_MODE;
    },

    // 处理会话媒体数据
    processSessionMedia(messages) {
        const sessionMedia = [];
        
        messages.forEach(msg => {
            if (msg.image_url) {
                sessionMedia.push({
                    type: 'image',
                    url: msg.image_url,
                    message_id: msg.id,
                    display_name: `Image ${sessionMedia.length + 1}`
                });
            }
            if (msg.video_url) {
                sessionMedia.push({
                    type: 'video',
                    url: msg.video_url,
                    message_id: msg.id,
                    display_name: `Video ${sessionMedia.length + 1}`
                });
            }
        });

        return sessionMedia;
    },

    // 过滤显示的消息（去除系统消息和临时消息）
    filterDisplayMessages(messages) {
        const filtered = messages.filter(msg => {
            // 对于历史消息，如果没有设置isSystem和isTemporary字段，应该显示
            if (msg.isSystem === undefined && msg.isTemporary === undefined) {
                return true; // 显示历史消息
            }
            return !msg.isSystem && !msg.isTemporary;
        });
        
        console.log('🔍 filterDisplayMessages:', {
            input: messages.length,
            output: filtered.length,
            sampleMessage: filtered[0] ? { id: filtered[0].id, sender: filtered[0].sender } : null
        });
        
        return filtered;
    },

    // 更新消息中的任务状态 - 简化版本，参考原始index.js
    updateMessageWithTaskResult(messages, taskId, result) {
        // 🔧 关键修复：优先通过message_id进行精确匹配
        console.log('🔍 [MessageUtils] 开始匹配消息:', {
            taskId,
            resultMessageId: result.messageId || result.message_id,
            totalMessages: messages.length,
            processingMessages: messages.filter(m => m.isTaskProcessing).length
        });
        
        const targetMessageIndex = messages.findIndex(msg => {
            // ✅ 策略1：优先通过message_id直接匹配（最可靠）
            const resultMsgId = result.messageId || result.message_id;
            if (resultMsgId && msg.id === resultMsgId) {
                console.log('✅ [MessageUtils] 通过message_id匹配成功:', {
                    messageId: msg.id,
                    resultMessageId: resultMsgId
                });
                return true;
            }
            
            // 策略2：通过taskId匹配（备选）
            if (taskId && msg.taskId === taskId) {
                console.log('✅ [MessageUtils] 通过taskId匹配成功:', {
                    messageId: msg.id,
                    taskId: taskId
                });
                return true;
            }
            
            // 策略3：通过isTaskProcessing状态匹配（最后的备选）
            if (msg.isTaskProcessing && msg.taskId === taskId) {
                console.log('✅ [MessageUtils] 通过processing状态匹配成功:', {
                    messageId: msg.id,
                    taskId: taskId
                });
                return true;
            }
            
            return false;
        });
        
        if (targetMessageIndex === -1) {
            console.error('❌ [MessageUtils] 找不到匹配的消息:', {
                taskId,
                resultMessageId: result.messageId || result.message_id,
                totalMessages: messages.length,
                allMessageIds: messages.map(m => ({ id: m.id, taskId: m.taskId, isProcessing: m.isTaskProcessing })),
                processingMessages: messages.filter(m => m.isTaskProcessing).map(m => ({ id: m.id, taskId: m.taskId }))
            });
            
            return messages;
        }
        
        console.log(`✅ [MessageUtils] 找到匹配消息，准备更新:`, {
            messageIndex: targetMessageIndex,
            messageId: messages[targetMessageIndex].id,
            resultMessageId: result.messageId || result.message_id,
            resultContent: result.content ? result.content.substring(0, 50) + '...' : 'null'
        });
        
        return messages.map((msg, index) => {
            if (index === targetMessageIndex) {
                const updatedMessage = {
                    ...msg,
                    isLoading: false,
                    isTaskProcessing: false,
                    needsTaskResume: false, // 🔧 新增：清除恢复标记
                    content: result.content || result.chat_response || result.message || msg.content,
                    image_url: result.image_url || msg.image_url,
                    video_url: result.video_url || msg.video_url,
                    tokens_consumed: result.tokens_consumed || 0,
                    metadata: {
                        ...msg.metadata,
                        // 🔧 新增：更新完成状态
                        task_completed: true,
                        completion_time: new Date().toISOString(),
                        final_result: {
                            success: true,
                            image_url: result.image_url,
                            video_url: result.video_url,
                            tokens_consumed: result.tokens_consumed
                        }
                    }
                };
                
                console.log('✅ [MessageUtils] 消息更新完成:', {
                    messageId: updatedMessage.id,
                    hasImage: !!updatedMessage.image_url,
                    hasVideo: !!updatedMessage.video_url,
                    tokensConsumed: updatedMessage.tokens_consumed
                });
                
                return updatedMessage;
            }
            return msg;
        });
    },

    // 移除加载状态消息
    removeLoadingMessages(messages) {
        return messages.filter(msg => !msg.isLoading);
    },

    // 获取任务进度阶段（用于ThinkingAnimation）
    getTaskProgressStage(elapsedTime, taskType) {
        const isVideoTask = this.isVideoTask(taskType);
        
        if (isVideoTask) {
            // 视频生成有更长的阶段
            if (elapsedTime >= 480) return 5; // 8分钟 - 视频渲染最终阶段
            if (elapsedTime >= 300) return 4; // 5分钟 - 视频合成中
            if (elapsedTime >= 180) return 3; // 3分钟 - 视频渲染中
            if (elapsedTime >= 120) return 2; // 2分钟 - 图像分析完成，开始视频生成
            if (elapsedTime >= 60) return 1;  // 1分钟 - 图像分析中
            return 0; // 初始分析
        } else {
            // 普通生成任务
            if (elapsedTime >= 30) return 3; // 长时间等待
            if (elapsedTime >= 15) return 2; // 复杂处理
            if (elapsedTime >= 8) return 1;  // 深度分析
            return 0; // 初始思考
        }
    }
};

export default messageUtils;