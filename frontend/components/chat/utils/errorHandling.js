// 错误处理工具函数
export const errorHandling = {
    // 处理任务轮询错误
    handleTaskPollingError(error, taskId) {
        console.error('Task polling error:', error);
        
        if (error.response) {
            const status = error.response.status;
            const errorData = error.response.data || {};
            
            switch (status) {
                case 401:
                    return {
                        type: 'auth_error',
                        message: '登录已过期，请重新登录',
                        action: 'redirect_login',
                        shouldStopPolling: true // 🔧 新增：停止轮询标记
                    };
                    
                case 402:
                    return {
                        type: 'insufficient_balance',
                        message: `余额不足：需要 ${errorData.tokens_required || 'N/A'} tokens，当前 ${errorData.current_balance || 0} tokens`,
                        action: 'show_upgrade',
                        shouldStopPolling: true
                    };
                    
                case 500:
                    // 🔧 针对数据库连接问题的特殊处理
                    if (errorData.error && (
                        errorData.error.includes('database') || 
                        errorData.error.includes('MySQL') ||
                        errorData.error.includes('timeout')
                    )) {
                        return {
                            type: 'database_timeout',
                            message: '数据库繁忙，任务可能已完成，请刷新页面查看结果',
                            action: 'suggest_refresh',
                            isRecoverable: true,
                            shouldStopPolling: false, // 🔧 数据库超时不停止轮询，可能恢复
                            quietMode: true // 🔧 新增：静默模式，减少日志输出
                        };
                    }
                    
                    return {
                        type: 'server_error',
                        message: '服务器暂时繁忙，您的任务可能已完成。请刷新页面查看结果，或稍后重试。',
                        action: 'suggest_refresh',
                        isRecoverable: true,
                        shouldStopPolling: false
                    };
                    
                case 503:
                    return {
                        type: 'service_unavailable',
                        message: '服务暂时不可用，请稍后重试',
                        action: 'retry_later',
                        shouldStopPolling: false
                    };
                    
                default:
                    return {
                        type: 'unknown_http_error',
                        message: `请求失败 (${status})：${errorData.error || error.message}`,
                        action: 'retry',
                        shouldStopPolling: false
                    };
            }
        } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
            // 🔧 优化超时错误处理
            return {
                type: 'timeout',
                message: '请求超时，任务可能仍在进行中',
                action: 'continue_polling',
                isRecoverable: true,
                shouldStopPolling: false,
                quietMode: true // 减少超时错误日志
            };
        } else if (error.code === 'ERR_NETWORK') {
            return {
                type: 'network_error',
                message: '网络连接问题，请检查网络设置',
                action: 'check_network',
                shouldStopPolling: false
            };
        } else {
            return {
                type: 'unknown_error',
                message: `未知错误：${error.message}`,
                action: 'retry',
                shouldStopPolling: false
            };
        }
    },
    
    // 处理API错误的通用函数
    handleApiError(error, context = '') {
        console.error(`API Error ${context}:`, error);
        
        const errorResult = this.handleTaskPollingError(error, null);
        
        // 为不同错误类型提供特定的恢复建议
        switch (errorResult.type) {
            case 'server_error':
                return {
                    ...errorResult,
                    suggestions: [
                        '🔄 刷新页面查看是否有新内容',
                        '⏳ 等待1-2分钟后重试',
                        '📱 检查生成结果是否已保存',
                        '🆘 如问题持续，请联系技术支持'
                    ]
                };
                
            case 'network_error':
                return {
                    ...errorResult,
                    suggestions: [
                        '🌐 检查网络连接',
                        '🔄 刷新页面重试',
                        '📶 尝试切换网络'
                    ]
                };
                
            case 'insufficient_balance':
                return {
                    ...errorResult,
                    suggestions: [
                        '💳 升级您的套餐',
                        '🎁 邀请朋友获得免费tokens',
                        '📅 等待tokens自动刷新'
                    ]
                };
                
            default:
                return {
                    ...errorResult,
                    suggestions: [
                        '🔄 刷新页面重试',
                        '⏳ 稍后重试',
                        '🆘 如问题持续，请联系支持'
                    ]
                };
        }
    },
    
    // 🔧 新增：专门处理数据库连接相关的错误
    handleDatabaseConnectionError(taskId) {
        return {
            type: 'database_connection_error',
            message: '数据保存出现问题，但您的内容可能已生成完成',
            action: 'suggest_refresh',
            isRecoverable: true,
            suggestions: [
                '🔄 刷新页面查看生成结果',
                '⏳ 等待1-2分钟系统自动恢复',
                '📋 记录任务ID以便查询：' + (taskId ? taskId.slice(-8) : 'N/A'),
                '🆘 如结果丢失，请联系技术支持'
            ],
            technicalInfo: {
                taskId: taskId,
                timestamp: new Date().toISOString(),
                issue: 'Database connection timeout during result saving',
                recovery: 'Task likely completed successfully, refresh to see results'
            }
        };
    },
    
    // 创建用户友好的错误消息
    createErrorMessage(error, taskId = null) {
        const errorInfo = taskId ? 
            this.handleDatabaseConnectionError(taskId) : 
            this.handleApiError(error, 'general');
            
        return {
            id: Date.now().toString(),
            type: 'error',
            content: errorInfo.message,
            suggestions: errorInfo.suggestions || [],
            isRecoverable: errorInfo.isRecoverable || false,
            technicalInfo: errorInfo.technicalInfo || null,
            timestamp: new Date().toISOString()
        };
    },
    
    // 检查错误是否可以恢复
    isRecoverableError(error) {
        const errorInfo = this.handleApiError(error);
        return errorInfo.isRecoverable || false;
    },
    
    // 获取错误的恢复建议
    getRecoverySuggestions(error, taskId = null) {
        const errorInfo = taskId ? 
            this.handleDatabaseConnectionError(taskId) : 
            this.handleApiError(error);
            
        return errorInfo.suggestions || [];
    }
};