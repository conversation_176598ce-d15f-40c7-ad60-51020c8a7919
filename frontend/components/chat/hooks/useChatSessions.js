import { useState, useEffect } from 'react';
import { sessionApi } from '../services/sessionApi';
import { errorHandling } from '../utils/errorHandling';

// 聊天会话管理Hook - 从chat.js重构而来
export const useChatSessions = (user, onAuthError) => {
    const [sessions, setSessions] = useState([]);
    const [activeSessionId, setActiveSessionId] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // 加载会话列表
    const loadSessions = async (token) => {
        if (!token || !user) {
            console.log('❌ No token or user, skipping session load');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            console.log('🔄 Loading chat sessions for user:', user.id);
            const result = await sessionApi.loadChatSessions(token);
            
            if (result.success) {
                setSessions(result.sessions);
                
                console.log('✅ Sessions loaded successfully:', {
                    count: result.sessions.length,
                    currentActiveSessionId: activeSessionId,
                    firstSessionId: result.sessions.length > 0 ? result.sessions[0].id : null
                });
                
                // 如果有会话且没有活动会话，选择第一个 - 参考原chat.js逻辑
                if (result.sessions.length > 0 && !activeSessionId) {
                    const firstSessionId = result.sessions[0].id;
                    console.log('🎯 Auto-selecting first session:', firstSessionId);
                    setActiveSessionId(firstSessionId);
                }
            }
        } catch (error) {
            console.error('❌ Failed to load sessions:', error);
            
            const errorResult = errorHandling.handleSessionError(error);
            setError(errorResult.message);
            
            if (errorResult.shouldLogout && onAuthError) {
                onAuthError();
            }
        } finally {
            setLoading(false);
        }
    };

    // 创建新会话
    const createSession = async (title = '新对话') => {
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('❌ No token available for creating session');
            return null;
        }

        try {
            console.log('🔄 Creating new session...');
            const result = await sessionApi.createSession(token, title);
            
            if (result.success) {
                const newSession = result.session;
                
                // 添加到会话列表顶部
                setSessions(prev => [newSession, ...prev]);
                setActiveSessionId(newSession.id);
                
                console.log('✅ Session created successfully:', newSession.id);
                return newSession;
            }
        } catch (error) {
            console.error('❌ Failed to create session:', error);
            
            const errorResult = errorHandling.handleSessionError(error);
            setError(errorResult.message);
            
            if (errorResult.shouldLogout && onAuthError) {
                onAuthError();
            }
            
            return null;
        }
    };

    // 删除会话
    const deleteSession = async (sessionId) => {
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('❌ No token available for deleting session');
            return false;
        }

        try {
            console.log('🗑️ Deleting session:', sessionId);
            const result = await sessionApi.deleteSession(token, sessionId);
            
            if (result.success) {
                // 从会话列表中移除
                setSessions(prev => prev.filter(session => session.id !== sessionId));
                
                // 如果删除的是当前活动会话，清除活动会话ID
                if (activeSessionId === sessionId) {
                    setActiveSessionId(null);
                }
                
                console.log('✅ Session deleted successfully:', sessionId);
                return true;
            }
        } catch (error) {
            console.error('❌ Failed to delete session:', error);
            
            const errorResult = errorHandling.handleSessionError(error);
            setError(errorResult.message);
            
            if (errorResult.shouldLogout && onAuthError) {
                onAuthError();
            }
            
            return false;
        }
    };

    // 更新会话标题
    const updateSessionTitle = async (sessionId, newTitle) => {
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('❌ No token available for updating session title');
            return false;
        }

        try {
            console.log('✏️ Updating session title:', sessionId, '->', newTitle);
            const result = await sessionApi.updateSessionTitle(token, sessionId, newTitle);
            
            if (result.success) {
                // 更新本地会话列表
                setSessions(prev => prev.map(session => 
                    session.id === sessionId 
                        ? { ...session, title: newTitle }
                        : session
                ));
                
                console.log('✅ Session title updated successfully');
                return true;
            }
        } catch (error) {
            console.error('❌ Failed to update session title:', error);
            
            const errorResult = errorHandling.handleSessionError(error);
            setError(errorResult.message);
            
            if (errorResult.shouldLogout && onAuthError) {
                onAuthError();
            }
            
            return false;
        }
    };

    // 切换活动会话
    const switchToSession = (sessionId) => {
        console.log(`🎯 useChatSessions: switchToSession called`, {
            sessionId,
            currentActiveSessionId: activeSessionId,
            willSwitch: sessionId !== activeSessionId
        });
        
        if (sessionId !== activeSessionId) {
            console.log(`🔄 useChatSessions: Switching from ${activeSessionId} to ${sessionId}`);
            setActiveSessionId(sessionId);
        } else {
            console.log(`✅ useChatSessions: Session ${sessionId} is already active`);
        }
    };

    // 当用户发生变化时重新加载会话 - 参考原chat.js双重检查逻辑
    useEffect(() => {
        console.log('🔄 User effect triggered:', {
            user: !!user,
            sessionsLength: sessions.length,
            activeSessionId: activeSessionId
        });

        // 只有当用户已登录且还没有加载会话时才加载 - 参考原chat.js逻辑
        if (user && sessions.length === 0 && !activeSessionId) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🚀 Loading chat sessions for user:', user.id);
                loadSessions(token);
            }
        }
    }, [user]); // 只依赖user，避免循环依赖

    // 获取当前活动会话
    const activeSession = sessions.find(session => session.id === activeSessionId);

    return {
        // 状态
        sessions,
        activeSessionId,
        activeSession,
        loading,
        error,
        
        // 操作方法
        loadSessions,
        createSession,
        deleteSession,
        updateSessionTitle,
        switchToSession,
        
        // 辅助方法
        clearError: () => setError(null),
        refreshSessions: () => {
            const token = localStorage.getItem('token');
            if (token) {
                loadSessions(token);
            }
        }
    };
};

export default useChatSessions;