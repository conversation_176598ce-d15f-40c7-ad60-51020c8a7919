import { useState, useEffect, useRef } from 'react';
import { chatApi } from '../services/chatApi';
import { messageUtils } from '../utils/messageUtils';
import { errorHandling } from '../utils/errorHandling';

// 新增：导入轮询恢复依赖
// 需要父组件传入startTaskPolling

// 聊天消息管理Hook - 从chat.js重构而来
export const useChatMessages = (activeSessionId, onAuthError, startTaskPolling) => {
    const [messages, setMessages] = useState([]);
    const [sessionMedia, setSessionMedia] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // 用于跟踪上一次消息数量的ref
    const prevMessageCountRef = useRef(0);
    const messagesEndRef = useRef(null);

    // 加载会话消息
    const loadMessages = async (sessionId, token) => {
        if (!sessionId || !token) {
            console.log('❌ [useChatMessages] No sessionId or token, skipping message load:', {
                sessionId: !!sessionId,
                token: !!token
            });
            return;
        }

        console.log('🔄 [useChatMessages] 开始加载消息:', {
            sessionId,
            tokenPrefix: token.substring(0, 10) + '...',
            timestamp: new Date().toISOString()
        });

        setLoading(true);
        setError(null);

        try {
            console.log(`🔄 [useChatMessages] 调用 chatApi.loadChatMessages...`);
            const result = await chatApi.loadChatMessages(sessionId, token);
            
            console.log('📨 [useChatMessages] chatApi 返回结果:', {
                success: result.success,
                messagesCount: result.messages?.length || 0,
                sessionMediaCount: result.sessionMedia?.length || 0,
                resultKeys: Object.keys(result),
                messagesSample: result.messages?.slice(0, 2).map(m => ({
                    id: m.id,
                    type: m.type || m.sender,
                    hasContent: !!m.content,
                    hasImageUrl: !!m.image_url,
                    hasVideoUrl: !!m.video_url
                })) || []
            });
            
            if (result.success) {
                console.log('✅ [useChatMessages] 设置状态 - messages:', {
                    count: result.messages.length,
                    firstMessage: result.messages[0] ? {
                        id: result.messages[0].id,
                        type: result.messages[0].type,
                        sender: result.messages[0].sender,
                        content: result.messages[0].content?.substring(0, 50) + '...'
                    } : 'No messages'
                });

                console.log('✅ [useChatMessages] 设置状态 - sessionMedia:', {
                    count: result.sessionMedia.length,
                    items: result.sessionMedia.map(m => ({
                        reference_id: m.reference_id,
                        type: m.type,
                        hasUrl: !!m.url
                    }))
                });

                setMessages(result.messages);
                setSessionMedia(result.sessionMedia);
                
                console.log('📨 [useChatMessages] Messages loaded successfully:', {
                    messageCount: result.messages.length,
                    mediaCount: result.sessionMedia.length,
                    tasksToResume: result.messages.filter(msg => msg.needsTaskResume).length
                });

                // === 自动恢复异步任务轮询 ===
                if (startTaskPolling) {
                    let resumeCount = 0;
                    
                    result.messages.forEach(msg => {
                        // 🔧 关键修复：完善恢复条件判断
                        const shouldResume = (
                            // 条件1：消息标记需要恢复轮询
                            msg.needsTaskResume ||
                            // 条件2：消息正在处理任务
                            msg.isTaskProcessing ||
                            // 条件3：消息有taskId且为UUID格式的id（后端message_id格式）
                            (msg.taskId && msg.id && /^[0-9a-fA-F-]{36}$/.test(msg.id))
                        );
                        
                        if (shouldResume && msg.taskId && msg.id) {
                            console.log('🔄 [ChatMessages] 恢复异步任务轮询:', {
                                taskId: msg.taskId, 
                                messageId: msg.id,
                                needsTaskResume: msg.needsTaskResume,
                                isTaskProcessing: msg.isTaskProcessing,
                                content: msg.content ? msg.content.substring(0, 30) + '...' : 'null'
                            });
                            
                            // 开始轮询，使用message_id作为messageId参数
                            try {
                                startTaskPolling(msg.taskId, msg.id, { messageIndex: null });
                                resumeCount++;
                            } catch (error) {
                                console.error('❌ [ChatMessages] 恢复轮询失败:', {
                                    taskId: msg.taskId,
                                    messageId: msg.id,
                                    error: error.message
                                });
                            }
                        }
                    });
                    
                    console.log(`✅ [ChatMessages] 轮询恢复完成: ${resumeCount}个任务恢复轮询`);
                }
                // === END ===
            }
        } catch (error) {
            if (error.message !== 'AUTHENTICATION_FAILED') {
                console.error('❌ Failed to load messages:', error.message);
            }
            
            const errorResult = errorHandling.handleApiError(error, 'Loading messages');
            setError(errorResult.userMessage);
            
            if (errorResult.isAuthError && onAuthError) {
                onAuthError();
            }
            
            // 设置空消息以避免无限重试
            setMessages([]);
            setSessionMedia([]);
        } finally {
            setLoading(false);
        }
    };

    // 添加消息到列表
    const addMessage = (message) => {
        setMessages(prev => [...prev, message]);
    };

    // 添加多个消息
    const addMessages = (newMessages) => {
        setMessages(prev => [...prev, ...newMessages]);
    };

    // 更新指定消息
    const updateMessage = (messageId, updates) => {
        setMessages(prev => prev.map(msg => 
            msg.id === messageId ? { ...msg, ...updates } : msg
        ));
    };

    // 移除加载状态的消息
    const removeLoadingMessages = () => {
        setMessages(prev => messageUtils.removeLoadingMessages(prev));
    };

    // 根据taskId更新消息
    const updateMessageByTaskId = (taskId, result) => {
        setMessages(prev => messageUtils.updateMessageWithTaskResult(prev, taskId, result));
    };

    // 清空消息
    const clearMessages = () => {
        setMessages([]);
        setSessionMedia([]);
        prevMessageCountRef.current = 0;
    };

    // 获取过滤后的显示消息
    const displayMessages = messageUtils.filterDisplayMessages(messages);

    // 当活动会话变化时加载消息 - 参考原chat.js逻辑
    useEffect(() => {
        console.log('🎯 useChatMessages: activeSessionId changed', {
            activeSessionId,
            hasToken: !!localStorage.getItem('token'),
            currentMessageCount: messages.length
        });
        
        if (activeSessionId) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🔄 useChatMessages: Loading messages for session', activeSessionId);
                // 先清空当前消息
                clearMessages();
                // 添加简单的防抖，避免重复调用
                const timeoutId = setTimeout(() => {
                    loadMessages(activeSessionId, token);
                }, 100);
                
                return () => clearTimeout(timeoutId);
            } else {
                console.warn('❌ useChatMessages: No token found, cannot load messages');
            }
        } else {
            console.log('🧹 useChatMessages: No active session, clearing messages');
            // 没有活动会话时清空消息
            clearMessages();
        }
    }, [activeSessionId]);

    // 专门处理有会话但没有消息的情况 - 参考原chat.js的第二个useEffect
    useEffect(() => {
        // 如果有活动会话但没有消息，且不在加载中，则加载消息
        if (activeSessionId && messages.length === 0 && !loading) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🔄 useChatMessages: Ensuring messages loaded for session:', activeSessionId);
                // 添加防抖避免重复调用
                const timeoutId = setTimeout(() => {
                    loadMessages(activeSessionId, token);
                }, 200);
                
                return () => clearTimeout(timeoutId);
            }
        }
    }, [activeSessionId, messages.length, loading]); // 依赖这些特定状态

    // 自动滚动到底部 - 只有当消息数量真正增加时才滚动
    useEffect(() => {
        if (messages.length > prevMessageCountRef.current) {
            const timeoutId = setTimeout(() => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            prevMessageCountRef.current = messages.length;
            return () => clearTimeout(timeoutId);
        } else {
            // 更新计数但不滚动（消息内容更新）
            prevMessageCountRef.current = messages.length;
        }
    }, [messages]);

    // 手动滚动到底部
    const scrollToBottom = (smooth = true) => {
        messagesEndRef.current?.scrollIntoView({ 
            behavior: smooth ? 'smooth' : 'auto' 
        });
    };

    // 重新加载当前会话的消息
    const refreshMessages = () => {
        if (activeSessionId) {
            const token = localStorage.getItem('token');
            if (token) {
                loadMessages(activeSessionId, token);
            }
        }
    };

    // 添加引用媒体到会话媒体列表
    const addToSessionMedia = (mediaItem) => {
        setSessionMedia(prev => {
            // 避免重复添加
            const exists = prev.some(item => 
                item.url === mediaItem.url && item.message_id === mediaItem.message_id
            );
            
            if (exists) {
                return prev;
            }
            
            return [...prev, {
                ...mediaItem,
                display_name: mediaItem.display_name || `${mediaItem.type} ${prev.length + 1}`
            }];
        });
    };

    // 统计信息
    const messageStats = {
        total: messages.length,
        display: displayMessages.length,
        images: sessionMedia.filter(item => item.type === 'image').length,
        videos: sessionMedia.filter(item => item.type === 'video').length,
        loading: messages.filter(msg => msg.isLoading).length,
        errors: messages.filter(msg => msg.isError).length
    };

    return {
        // 状态
        messages,
        displayMessages,
        sessionMedia,
        loading,
        error,
        messageStats,
        
        // Refs
        messagesEndRef,
        
        // 操作方法
        loadMessages,
        addMessage,
        addMessages,
        updateMessage,
        updateMessageByTaskId,
        removeLoadingMessages,
        clearMessages,
        refreshMessages,
        addToSessionMedia,
        
        // 工具方法
        scrollToBottom,
        clearError: () => setError(null)
    };
};

export default useChatMessages;