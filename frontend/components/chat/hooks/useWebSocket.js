import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * WebSocket Hook - 替换轮询系统，实现实时通信
 * 提供AI任务状态的实时推送功能
 */
export const useWebSocket = () => {
    const [connectionStatus, setConnectionStatus] = useState('disconnected'); // 连接状态
    const [lastMessage, setLastMessage] = useState(null); // 最后收到的消息
    const [activeTasks, setActiveTasks] = useState(new Map()); // 活动任务状态
    const [taskProgress, setTaskProgress] = useState(new Map()); // 任务进度状态
    
    // WebSocket 引用
    const wsRef = useRef(null);
    const reconnectTimeoutRef = useRef(null);
    const heartbeatIntervalRef = useRef(null);
    
    // 回调函数引用
    const onTaskCompleteRef = useRef(null);
    const onTaskErrorRef = useRef(null);
    const onTaskProgressRef = useRef(null);
    
    // 重连配置
    const reconnectAttempts = useRef(0);
    const maxReconnectAttempts = 5;
    const reconnectDelay = 3000; // 3秒重连延迟
    
    /**
     * 建立WebSocket连接
     */
    const connect = useCallback((token) => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            console.log('🔗 [WebSocket] 连接已存在，跳过连接');
            return;
        }
        
        try {
            // 开发环境直接连接Daphne服务
            const isDevelopment = process.env.NODE_ENV === 'development' || window.location.port === '3000';
            
            let wsUrl;
            if (isDevelopment) {
                // 开发环境：直接连接到Daphne服务器
                wsUrl = `ws://localhost:8001/ws/ai-tasks/?token=${encodeURIComponent(token)}`;
            } else {
                // 生产环境：通过Nginx代理
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const host = window.location.host;
                wsUrl = `${protocol}//${host}/ws/ai-tasks/?token=${encodeURIComponent(token)}`;
            }
            
            console.log('🚀 [WebSocket] 建立连接:', wsUrl, isDevelopment ? '(开发模式)' : '(生产模式)');
            console.log('🔑 [WebSocket] Token信息:', token ? `Token存在，长度: ${token.length}` : '❌ Token不存在');
            setConnectionStatus('connecting');
            
            // 创建WebSocket连接
            const ws = new WebSocket(wsUrl);
            wsRef.current = ws;
            
            // 连接成功
            ws.onopen = () => {
                console.log('✅ [WebSocket] 连接成功');
                setConnectionStatus('connected');
                reconnectAttempts.current = 0;
                
                // 启动心跳检测
                startHeartbeat();
            };
            
            // 接收消息
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('📨 [WebSocket] 收到消息:', data.type, data);
                    
                    setLastMessage(data);
                    handleWebSocketMessage(data);
                    
                } catch (error) {
                    console.error('❌ [WebSocket] 消息解析失败:', error);
                }
            };
            
            // 连接关闭
            ws.onclose = (event) => {
                console.log('🔌 [WebSocket] 连接关闭:', event.code, event.reason);
                setConnectionStatus('disconnected');
                wsRef.current = null;
                
                stopHeartbeat();
                
                // 如果不是主动关闭，尝试重连
                if (event.code !== 1000 && event.code !== 1001) {
                    attemptReconnect(token);
                }
            };
            
            // 连接错误
            ws.onerror = (error) => {
                console.error('❌ [WebSocket] 连接错误:', error);
                setConnectionStatus('error');
            };
            
        } catch (error) {
            console.error('❌ [WebSocket] 创建连接失败:', error);
            setConnectionStatus('error');
        }
    }, []);
    
    /**
     * 处理WebSocket消息
     */
    const handleWebSocketMessage = useCallback((data) => {
        const { type, data: messageData, timestamp } = data;
        
        switch (type) {
            case 'connection_established':
                console.log('🔗 [WebSocket] 连接确认:', messageData);
                break;
                
            case 'task_update':
                handleTaskUpdate(messageData);
                break;
                
            case 'task_progress':
                handleTaskProgress(messageData);
                break;
                
            case 'task_completed':
                handleTaskCompleted(messageData);
                break;
                
            case 'task_failed':
                handleTaskFailed(messageData);
                break;
                
            case 'pong':
                // 心跳响应，无需处理
                break;
                
            default:
                console.log('📨 [WebSocket] 未知消息类型:', type, messageData);
        }
    }, []);
    
    /**
     * 处理任务状态更新
     */
    const handleTaskUpdate = useCallback((data) => {
        const { task_id, status, message } = data;
        
        console.log('🔄 [WebSocket] 任务更新:', task_id, status);
        
        setActiveTasks(prev => {
            const newMap = new Map(prev);
            newMap.set(task_id, {
                taskId: task_id,
                status: status,
                message: message,
                lastUpdate: Date.now()
            });
            return newMap;
        });
        
        // 调用进度回调
        if (onTaskProgressRef.current) {
            onTaskProgressRef.current(task_id, { status, message });
        }
    }, []);
    
    /**
     * 处理任务进度更新
     */
    const handleTaskProgress = useCallback((data) => {
        const { task_id, progress, stage, message } = data;
        
        console.log('📊 [WebSocket] 任务进度:', task_id, progress + '%');
        
        setTaskProgress(prev => {
            const newMap = new Map(prev);
            newMap.set(task_id, {
                taskId: task_id,
                progress: progress,
                stage: stage,
                message: message,
                lastUpdate: Date.now()
            });
            return newMap;
        });
        
        // 调用进度回调
        if (onTaskProgressRef.current) {
            onTaskProgressRef.current(task_id, { progress, stage, message });
        }
    }, []);
    
    /**
     * 处理任务完成
     */
    const handleTaskCompleted = useCallback((data) => {
        const { task_id, result } = data;
        
        console.log('✅ [WebSocket] 任务完成:', task_id);
        
        // 清理任务状态
        setActiveTasks(prev => {
            const newMap = new Map(prev);
            newMap.delete(task_id);
            return newMap;
        });
        
        setTaskProgress(prev => {
            const newMap = new Map(prev);
            newMap.delete(task_id);
            return newMap;
        });
        
        // 调用完成回调
        if (onTaskCompleteRef.current) {
            onTaskCompleteRef.current(task_id, result);
        }
    }, []);
    
    /**
     * 处理任务失败
     */
    const handleTaskFailed = useCallback((data) => {
        const { task_id, error, error_type } = data;
        
        console.error('❌ [WebSocket] 任务失败:', task_id, error);
        
        // 清理任务状态
        setActiveTasks(prev => {
            const newMap = new Map(prev);
            newMap.delete(task_id);
            return newMap;
        });
        
        setTaskProgress(prev => {
            const newMap = new Map(prev);
            newMap.delete(task_id);
            return newMap;
        });
        
        // 调用错误回调
        if (onTaskErrorRef.current) {
            onTaskErrorRef.current(task_id, error, error_type);
        }
    }, []);
    
    /**
     * 启动心跳检测
     */
    const startHeartbeat = useCallback(() => {
        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current);
        }
        
        heartbeatIntervalRef.current = setInterval(() => {
            if (wsRef.current?.readyState === WebSocket.OPEN) {
                wsRef.current.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000); // 30秒心跳
    }, []);
    
    /**
     * 停止心跳检测
     */
    const stopHeartbeat = useCallback(() => {
        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current);
            heartbeatIntervalRef.current = null;
        }
    }, []);
    
    /**
     * 尝试重连
     */
    const attemptReconnect = useCallback((token) => {
        if (reconnectAttempts.current >= maxReconnectAttempts) {
            console.error('❌ [WebSocket] 重连次数达到上限，停止重连');
            setConnectionStatus('failed');
            return;
        }
        
        reconnectAttempts.current++;
        console.log(`🔄 [WebSocket] 尝试重连 (${reconnectAttempts.current}/${maxReconnectAttempts})`);
        
        setConnectionStatus('reconnecting');
        
        reconnectTimeoutRef.current = setTimeout(() => {
            connect(token);
        }, reconnectDelay * reconnectAttempts.current); // 递增延迟
    }, [connect]);
    
    /**
     * 断开连接
     */
    const disconnect = useCallback(() => {
        console.log('🔌 [WebSocket] 主动断开连接');
        
        // 清理定时器
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
        }
        
        stopHeartbeat();
        
        // 关闭WebSocket
        if (wsRef.current) {
            wsRef.current.close(1000, 'Normal closure');
            wsRef.current = null;
        }
        
        setConnectionStatus('disconnected');
        
        // 清理状态
        setActiveTasks(new Map());
        setTaskProgress(new Map());
        
        reconnectAttempts.current = 0;
    }, [stopHeartbeat]);
    
    /**
     * 发送消息
     */
    const sendMessage = useCallback((message) => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify(message));
            return true;
        } else {
            console.warn('⚠️ [WebSocket] 连接未就绪，无法发送消息');
            return false;
        }
    }, []);
    
    /**
     * 订阅任务
     */
    const subscribeTask = useCallback((taskId) => {
        return sendMessage({
            type: 'subscribe_task',
            task_id: taskId
        });
    }, [sendMessage]);
    
    /**
     * 取消订阅任务
     */
    const unsubscribeTask = useCallback((taskId) => {
        return sendMessage({
            type: 'unsubscribe_task',
            task_id: taskId
        });
    }, [sendMessage]);
    
    /**
     * 设置回调函数
     */
    const setCallbacks = useCallback((callbacks) => {
        onTaskCompleteRef.current = callbacks.onTaskComplete;
        onTaskErrorRef.current = callbacks.onTaskError;
        onTaskProgressRef.current = callbacks.onTaskProgress;
    }, []);
    
    /**
     * 获取任务状态
     */
    const getTaskStatus = useCallback((taskId) => {
        return activeTasks.get(taskId);
    }, [activeTasks]);
    
    /**
     * 获取任务进度
     */
    const getTaskProgress = useCallback((taskId) => {
        return taskProgress.get(taskId);
    }, [taskProgress]);
    
    /**
     * 检查是否有活动任务
     */
    const hasActiveTasks = useCallback(() => {
        return activeTasks.size > 0;
    }, [activeTasks]);
    
    // 组件卸载时清理
    useEffect(() => {
        return () => {
            disconnect();
        };
    }, [disconnect]);
    
    return {
        // 连接控制
        connect,
        disconnect,
        
        // 状态
        connectionStatus,
        lastMessage,
        activeTasks: Array.from(activeTasks.values()),
        taskProgress: Array.from(taskProgress.values()),
        
        // 任务操作
        subscribeTask,
        unsubscribeTask,
        getTaskStatus,
        getTaskProgress,
        hasActiveTasks,
        
        // 消息发送
        sendMessage,
        
        // 回调设置
        setCallbacks,
        
        // 工具函数
        isConnected: connectionStatus === 'connected',
        isConnecting: connectionStatus === 'connecting',
        isReconnecting: connectionStatus === 'reconnecting'
    };
}; 