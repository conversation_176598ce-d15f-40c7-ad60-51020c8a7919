import { useState, useCallback } from 'react';
import { chatApi } from '../services/chatApi';
import { messageUtils } from '../utils/messageUtils';
import { errorHandling } from '../utils/errorHandling';

// 聊天提交管理Hook - 从chat.js重构而来
export const useChatSubmit = ({ 
    activeSessionId, 
    onCreateSession,
    onAddMessage,
    onAddMessages,
    onRemoveLoadingMessages,
    onUpdateBalance,
    onAuthError,
    onShowUpgradeModal,
    startTaskPolling
}) => {
    const [submissionLoading, setSubmissionLoading] = useState(false);
    const [submissionError, setSubmissionError] = useState(null);

    // 核心提交逻辑
    const submitMessage = useCallback(async (options) => {
        const {
            prompt,
            currentFile,
            currentMode,
            selectedReferences = [],
            processedMessage = null,
            onProgress = null
        } = options;

        if ((!prompt.trim() && !currentFile) || submissionLoading) {
            return null;
        }

        setSubmissionLoading(true);
        setSubmissionError(null);

        try {
            // 创建会话（如果需要）
            let sessionId = activeSessionId;
            if (!sessionId) {
                console.log('🔄 No active session, creating new one...');
                const newSession = await onCreateSession?.('新对话');
                if (!newSession) {
                    throw new Error('Failed to create session');
                }
                sessionId = newSession.id;
            }

            // 处理@mentions（如果没有预处理的话）
            const finalMessage = processedMessage || prompt;

            // 创建用户消息
            const userMessage = messageUtils.createUserMessage(
                finalMessage, 
                currentFile, 
                selectedReferences
            );

            console.log('👤 User message with references:', {
                content: finalMessage.substring(0, 50) + '...',
                referencesCount: selectedReferences.length,
                hasFile: !!currentFile
            });

            // 获取token和CSRF token
            const token = localStorage.getItem('token');
            const csrfResult = await chatApi.fetchCsrfToken();
            
            if (!csrfResult.success) {
                throw new Error('Failed to get CSRF token');
            }

            // 初始化为未知任务类型，将由后端响应确定
            let taskType = 'unknown';

            // 添加用户消息和加载状态消息
            const loadingMessage = messageUtils.createLoadingMessage(taskType);
            onAddMessages?.([userMessage, loadingMessage]);

            // 进度回调
            onProgress?.({ stage: 'sending', message: 'Sending request...' });

            // 发送请求到后端
            const response = await chatApi.submitGenerate({
                sessionId,
                prompt: finalMessage,
                mode: currentMode,
                currentFile,
                selectedReferences,
                token,
                csrfToken: csrfResult.token
            });

            console.log('✅ Submit response:', response.data);

            if (!response.success) {
                throw new Error(response.data?.error || 'Submit failed');
            }

            const agentResult = response.data;

            // 🔧 关键修复：不更新用户消息ID，保持用户消息使用临时ID
            // 只记录后端返回的message_id用于调试，但用户消息保持独立的ID
            console.log('📝 Backend returned message_id:', agentResult.message_id);
            console.log('👤 User message ID remains:', userMessage.id);

            // 从后端响应获取任务类型
            taskType = agentResult.task_type || 'text_to_image';
            const hasVideoResult = agentResult.video_url;
            const isVideoTask = messageUtils.isVideoTask(taskType) || hasVideoResult;

            // 移除加载状态消息
            onRemoveLoadingMessages?.();

            // 处理响应 - 检查是否是异步任务（后端返回async_task: true）
            if (agentResult.async_task && agentResult.task_id) {
                // 异步任务处理
                console.log('🔄 [ChatSubmit] 处理异步任务响应:', {
                    taskId: agentResult.task_id,
                    messageId: agentResult.message_id,
                    taskType: taskType,
                    estimatedTime: agentResult.estimated_time
                });
                
                const processingMessage = messageUtils.createAsyncTaskMessage(
                    agentResult, 
                    currentMode, 
                    taskType
                );

                onAddMessage?.(processingMessage);

                // 进度回调
                onProgress?.({ 
                    stage: 'processing', 
                    message: 'Processing your request...',
                    taskId: agentResult.task_id,
                    messageId: agentResult.message_id, // 🔧 新增：传递message_id
                    estimatedTime: agentResult.estimated_time
                });

                // 🔧 关键修复：开始任务轮询时使用正确的message_id
                if (startTaskPolling) {
                    console.log('🔄 [ChatSubmit] 启动异步任务轮询:', {
                        taskId: agentResult.task_id,
                        messageId: agentResult.message_id,
                        processingMessageId: processingMessage.id
                    });
                    
                    // 验证ID一致性
                    if (agentResult.message_id !== processingMessage.id) {
                        console.error('❌ [ChatSubmit] ID不一致警告:', {
                            backendMessageId: agentResult.message_id,
                            frontendMessageId: processingMessage.id
                        });
                    }
                    
                    // 使用后端返回的message_id启动轮询
                    startTaskPolling(agentResult.task_id, agentResult.message_id, {
                        messageIndex: null
                    });
                }
            } else {
                // 同步结果处理
                const successMessage = messageUtils.createSuccessMessage(agentResult);
                onAddMessage?.(successMessage);

                // 进度回调
                onProgress?.({ 
                    stage: 'completed', 
                    message: 'Request completed successfully' 
                });
            }

            // 对于视频生成任务，延迟处理
            if (isVideoTask && hasVideoResult) {
                setTimeout(() => {
                    onRemoveLoadingMessages?.();
                }, 2000);
            }

            // 更新token余额
            if (agentResult.remaining_tokens !== undefined) {
                onUpdateBalance?.(agentResult.remaining_tokens);
            }

            return {
                success: true,
                result: agentResult,
                sessionId: sessionId
            };

        } catch (error) {
            console.error('❌ Submit failed:', error);

            // 移除加载状态消息
            onRemoveLoadingMessages?.();

            // 处理错误
            const errorResult = errorHandling.handleSubmitError(error);
            setSubmissionError(errorResult.message);

            // 认证错误
            if (errorResult.shouldLogout) {
                onAuthError?.();
                return null;
            }

            // Token不足错误
            if (errorResult.isTokenError) {
                onShowUpgradeModal?.();
            }

            // 添加错误消息
            const errorMessage = messageUtils.createErrorMessage(error, errorResult.isTokenError);
            onAddMessage?.(errorMessage);

            // 进度回调
            onProgress?.({ 
                stage: 'error', 
                message: errorResult.message,
                error: error
            });

            return {
                success: false,
                error: errorResult.message,
                shouldRetry: errorResult.shouldRetry
            };

        } finally {
            setSubmissionLoading(false);
        }
    }, [
        activeSessionId,
        submissionLoading,
        onCreateSession,
        onAddMessage,
        onAddMessages,
        onRemoveLoadingMessages,
        onUpdateBalance,
        onAuthError,
        onShowUpgradeModal,
        startTaskPolling
    ]);

    // 简化的提交方法（兼容原有API）
    const simpleSubmit = useCallback(async (prompt, currentFile, currentMode) => {
        return await submitMessage({
            prompt,
            currentFile,
            currentMode,
            selectedReferences: []
        });
    }, [submitMessage]);

    // 带引用的提交方法
    const submitWithReferences = useCallback(async (options) => {
        return await submitMessage(options);
    }, [submitMessage]);

    // 取消当前提交
    const cancelSubmission = useCallback(() => {
        if (submissionLoading) {
            console.log('🛑 Cancelling current submission');
            setSubmissionLoading(false);
            setSubmissionError('Submission cancelled');
        }
    }, [submissionLoading]);

    // 重试提交
    const retrySubmission = useCallback(async (lastSubmissionData) => {
        if (lastSubmissionData && !submissionLoading) {
            console.log('🔄 Retrying submission');
            return await submitMessage(lastSubmissionData);
        }
        return null;
    }, [submitMessage, submissionLoading]);

    // 清除错误
    const clearError = useCallback(() => {
        setSubmissionError(null);
    }, []);

    return {
        // 状态
        isSubmitting: submissionLoading,
        submissionError,
        
        // 主要方法
        submitMessage,
        simpleSubmit,
        submitWithReferences,
        
        // 控制方法
        cancelSubmission,
        retrySubmission,
        clearError
    };
};

export default useChatSubmit;