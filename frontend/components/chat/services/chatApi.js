import axios from 'axios';
import { getCsrfToken } from '../../../utils/csrf';

// 聊天API服务 - 从chat.js重构而来
export const chatApi = {
    // 加载指定会话的消息
    async loadChatMessages(sessionId, token) {
        try {
            const requestUrl = `/api/chat/sessions/${sessionId}/messages/`;
            const requestHeaders = {
                'Authorization': `Token ${token}`,
                'Content-Type': 'application/json'
            };

            console.log(`🔄 [chatApi] 发起请求:`, {
                url: requestUrl,
                sessionId,
                tokenPrefix: token ? token.substring(0, 10) + '...' : 'No token',
                timestamp: new Date().toISOString()
            });

            const response = await axios.get(requestUrl, {
                headers: requestHeaders,
                timeout: 10000,
                validateStatus: function (status) {
                    return status < 500; // 只有500+才算错误
                }
            });

            console.log('📨 [chatApi] 响应详情:', {
                status: response.status,
                statusText: response.statusText,
                url: requestUrl,
                headers: response.headers,
                dataType: typeof response.data,
                dataKeys: response.data ? Object.keys(response.data) : 'No data'
            });

            console.log('📨 [chatApi] 原始响应数据:', {
                data: response.data,
                hasMessages: !!(response.data && response.data.messages),
                messagesType: response.data && response.data.messages ? typeof response.data.messages : 'undefined',
                messagesLength: response.data && response.data.messages ? response.data.messages.length : 'N/A'
            });

            // 检查响应状态
            if (response.status === 404) {
                console.log('📭 [chatApi] Session not found or no messages');
                return {
                    success: true,
                    messages: [],
                    sessionMedia: []
                };
            }

            if (response.status === 401 || response.status === 403) {
                console.log('🔐 [chatApi] Authentication failed, redirecting to login');
                throw new Error('AUTHENTICATION_FAILED');
            }

            if (response.status !== 200) {
                console.log('📭 [chatApi] Non-200 response:', response.status);
                return {
                    success: true,
                    messages: [],
                    sessionMedia: []
                };
            }

            if (response.status === 200 && response.data && Array.isArray(response.data.messages) && response.data.messages.length > 0) {
                // 处理会话媒体数据
                const sessionMedia = [];
                let mediaCounter = 1;

                console.log('🎬 [chatApi] 开始处理sessionMedia，消息总数:', response.data.messages.length);

                const processedMessages = response.data.messages.map((msg, index) => {
                    // 调试每条消息的媒体内容
                    if (index < 5 || msg.image_url || msg.video_url) { // 只调试前5条消息和包含媒体的消息
                        console.log(`📋 [chatApi] 消息 ${index}:`, {
                            id: msg.id,
                            type: msg.type,
                            hasContent: !!msg.content,
                            image_url: msg.image_url || 'No image_url',
                            video_url: msg.video_url || 'No video_url',
                            contentPreview: msg.content ? msg.content.substring(0, 50) + '...' : 'No content'
                        });
                    }

                    // 收集媒体数据
                    if (msg.image_url) {
                        console.log(`🖼️ [chatApi] 发现图片 ${mediaCounter}:`, {
                            url: msg.image_url,
                            message_id: msg.id,
                            reference_id: `image_${mediaCounter}`
                        });

                        sessionMedia.push({
                            type: 'image',
                            url: msg.image_url,
                            message_id: msg.id,
                            reference_id: `image_${mediaCounter}`, // 添加 reference_id
                            display_name: `Image ${mediaCounter}`
                        });
                        mediaCounter++;
                    }
                    if (msg.video_url) {
                        console.log(`🎥 [chatApi] 发现视频 ${mediaCounter}:`, {
                            url: msg.video_url,
                            message_id: msg.id,
                            reference_id: `video_${mediaCounter}`
                        });

                        sessionMedia.push({
                            type: 'video',
                            url: msg.video_url,
                            message_id: msg.id,
                            reference_id: `video_${mediaCounter}`, // 添加 reference_id
                            display_name: `Video ${mediaCounter}`
                        });
                        mediaCounter++;
                    }
                    
                    // 标准化sender属性 - 确保用户消息显示在右侧
                    let normalizedSender = msg.sender || msg.type || 'bot';
                    
                    // 检查各种可能的用户标识格式
                    if (normalizedSender === 'human' || 
                        normalizedSender === 'user' || 
                        normalizedSender === 'client' ||
                        normalizedSender === 'participant') {
                        normalizedSender = 'user';
                    } 
                    // 检查各种可能的AI标识格式
                    else if (normalizedSender === 'bot' || 
                             normalizedSender === 'assistant' || 
                             normalizedSender === 'system' ||
                             normalizedSender === 'ai' ||
                             normalizedSender === 'agent') {
                        normalizedSender = 'bot';
                    }
                    // 默认处理
                    else {
                        normalizedSender = normalizedSender === 'user' ? 'user' : 'bot';
                    }
                    
                    // 🔧 修复：恢复正在处理任务的状态
                    const msgMetadata = msg.metadata || {};
                    const isProcessing = msgMetadata.status === 'processing' && !msg.image_url && !msg.video_url;
                    const hasTaskId = msgMetadata.task_id;
                    
                    // 如果消息有task_id但没有结果，可能是正在处理的任务
                    const shouldResumeTask = isProcessing && hasTaskId;
                    
                    if (shouldResumeTask) {
                        console.log('🔄 恢复正在处理的任务:', {
                            messageId: msg.id,
                            taskId: msgMetadata.task_id,
                            content: msg.content
                        });
                    }

                    // 🔧 关键修复：从metadata.references转换为referenced_media格式
                    let referencedMedia = [];
                    try {
                        if (msg.referenced_media && Array.isArray(msg.referenced_media)) {
                            // 直接使用后端返回的referenced_media字段（新格式）
                            referencedMedia = msg.referenced_media;
                        } else if (msgMetadata && msgMetadata.references && Array.isArray(msgMetadata.references)) {
                            // 从metadata.references转换（旧格式兼容）
                            referencedMedia = msgMetadata.references
                                .filter(ref => ref && typeof ref === 'object') // 过滤无效引用
                                .map(ref => ({
                                    reference_id: ref.reference_id || '',
                                    type: ref.type || 'unknown',
                                    url: ref.url || '',
                                    display_name: ref.display_name || `Reference ${ref.reference_id || 'Unknown'}`,
                                    message_id: ref.original_message_id || ref.message_id || ''
                                }));
                        }
                    } catch (error) {
                        console.warn('⚠️ [chatApi] 引用媒体转换失败:', error);
                        referencedMedia = [];
                    }

                    // 记录有媒体或引用的消息
                    if (msg.image_url || msg.video_url || referencedMedia.length > 0 || (msgMetadata.references && msgMetadata.references.length > 0)) {
                        console.log(`📨 Media Message ${index}:`, {
                            id: msg.id,
                            type: msg.type,
                            normalizedSender,
                            content: msg.content?.substring(0, 30) + '...',
                            image_url: msg.image_url,
                            video_url: msg.video_url,
                            // 引用媒体相关调试信息
                            raw_referenced_media: msg.referenced_media?.length || 0,
                            metadata_references: msgMetadata.references?.length || 0,
                            final_referenced_media: referencedMedia.length,
                            referenced_media_details: referencedMedia.map(ref => ({
                                reference_id: ref.reference_id,
                                type: ref.type,
                                hasUrl: !!ref.url,
                                url_preview: ref.url ? ref.url.substring(0, 50) + '...' : 'no_url'
                            }))
                        });
                    }

                    return {
                        id: msg.id || `msg_${index}_${Date.now()}`,
                        type: msg.type || normalizedSender,
                        sender: normalizedSender,
                        content: msg.content || '',
                        image_url: msg.image_url || null,
                        video_url: msg.video_url || null,
                        referenced_media: referencedMedia, // 🔧 关键修复：正确转换引用媒体数据
                        metadata: msgMetadata,
                        created_at: msg.created_at || new Date().toISOString(),
                        timestamp: msg.created_at || new Date().toISOString(),
                        // 🔧 关键修复：根据metadata恢复任务状态
                        isLoading: shouldResumeTask,
                        isTaskProcessing: shouldResumeTask,
                        taskId: hasTaskId ? msgMetadata.task_id : null,
                        // 添加恢复标记，供后续任务轮询使用
                        needsTaskResume: shouldResumeTask
                    };
                });

                console.log('📨 Messages loaded successfully:', {
                    messageCount: processedMessages.length,
                    mediaCount: sessionMedia.length,
                    senderBreakdown: processedMessages.reduce((acc, msg) => {
                        acc[msg.sender] = (acc[msg.sender] || 0) + 1;
                        return acc;
                    }, {})
                });

                console.log('🎬 [chatApi] sessionMedia最终结果:', {
                    总数: sessionMedia.length,
                    媒体列表: sessionMedia.map((m, index) => ({
                        序号: index + 1,
                        类型: m.type,
                        引用ID: m.reference_id,
                        显示名称: m.display_name,
                        URL预览: m.url ? m.url.substring(0, 50) + '...' : 'No URL',
                        消息ID: m.message_id
                    })),
                    总体统计: {
                        图片数量: sessionMedia.filter(m => m.type === 'image').length,
                        视频数量: sessionMedia.filter(m => m.type === 'video').length
                    }
                });

                return {
                    success: true,
                    messages: processedMessages,
                    sessionMedia: sessionMedia
                };
            } else {
                console.log('📭 [chatApi] Empty or invalid messages response:', {
                    hasData: !!response.data,
                    dataStructure: response.data ? {
                        hasMessages: 'messages' in response.data,
                        messagesValue: response.data.messages,
                        isArray: Array.isArray(response.data.messages),
                        length: response.data.messages ? response.data.messages.length : 'N/A',
                        keys: Object.keys(response.data)
                    } : 'No response.data',
                    responseStatus: response.status,
                    responseStatusText: response.statusText
                });

                return {
                    success: true,
                    messages: [],
                    sessionMedia: []
                };
            }
        } catch (error) {
            console.error('❌ [chatApi] Loading messages failed:', {
                error: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                responseData: error.response?.data,
                requestUrl: `/api/chat/sessions/${sessionId}/messages/`,
                tokenAvailable: !!token
            });
            
            if (error.message === 'AUTHENTICATION_FAILED') {
                throw error;
            }
            
            if (error.code === 'ECONNABORTED') {
                throw new Error('网络请求超时，请检查网络连接');
            }
            
            if (error.response?.status >= 500) {
                throw new Error('服务器内部错误，请稍后重试');
            }
            
            throw new Error('加载消息失败');
        }
    },

    // 提交生成请求 - 统一AI服务接口
    async submitGenerate({ 
        sessionId, 
        prompt, 
        mode, 
        currentFile, 
        selectedReferences = [], 
        token,
        csrfToken 
    }) {
        try {
            // 根据是否有文件上传选择请求格式
            let config;
            let requestData;

            if (currentFile) {
                // 有文件上传，使用FormData格式
                requestData = new FormData();
                requestData.append('prompt', prompt);
                requestData.append('mode', mode);
                requestData.append('session_id', sessionId);
                requestData.append('media', currentFile);

                // 添加引用信息
                if (selectedReferences.length > 0) {
                    requestData.append('references', JSON.stringify(selectedReferences));
                }

                config = {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        // 不设置Content-Type，让浏览器自动设置multipart/form-data
                    },
                    withCredentials: true,
                    timeout: 360000, // 6 minutes timeout
                };
            } else {
                // 无文件上传，使用JSON格式
                requestData = {
                    session_id: sessionId,
                    prompt: prompt,
                    mode: mode,
                    references: selectedReferences.length > 0 ? selectedReferences : [],
                    attachments: []
                };

                config = {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    withCredentials: true,
                    timeout: 360000, // 6 minutes timeout
                };
            }

            console.log('🚀 Sending request to unified AI service:', {
                endpoint: '/api/generate/',
                hasFile: !!currentFile,
                references: selectedReferences.length,
                mode: mode
            });

            const response = await axios.post("/api/generate/", requestData, config);

            console.log('✅ Generate API response:', response.data);

            if (response.data.success) {
                return {
                    success: true,
                    data: response.data
                };
            } else {
                throw new Error(response.data.error || '生成请求失败');
            }
        } catch (error) {
            console.error('❌ Generate request failed:', error);
            
            if (error.response?.status === 401 || error.response?.status === 403) {
                throw new Error('AUTHENTICATION_FAILED');
            }
            
            if (error.response?.status === 429) {
                throw new Error('请求过于频繁，请稍后重试');
            }
            
            if (error.response?.status === 413) {
                throw new Error('文件大小超过限制');
            }
            
            if (error.code === 'ECONNABORTED') {
                throw new Error('请求超时，请重试');
            }
            
            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            }
            
            throw new Error('生成请求失败，请重试');
        }
    },

    // API连接健康检查
    async testAPIConnection() {
        try {
            console.log('🔄 Testing API connection...');
            const response = await axios.get('/api/health/', {
                timeout: 5000,
                validateStatus: function (status) {
                    return status >= 200 && status < 300;
                }
            });

            console.log('✅ API health check passed:', response.data);
            return {
                success: true,
                healthy: true,
                data: response.data
            };
        } catch (error) {
            console.error('❌ API health check failed:', error);
            return {
                success: false,
                healthy: false,
                error: error.message
            };
        }
    },

    // 获取CSRF Token
    async fetchCsrfToken() {
        try {
            const token = await getCsrfToken();
            return {
                success: true,
                token: token
            };
        } catch (error) {
            console.error('❌ Failed to fetch CSRF token:', error);
            return {
                success: false,
                token: '',
                error: error.message
            };
        }
    }
};

export default chatApi;