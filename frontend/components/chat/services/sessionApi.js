import axios from 'axios';

// 会话API服务 - 从chat.js重构而来
export const sessionApi = {
    // 加载用户聊天会话
    async loadChatSessions(token) {
        try {
            console.log('🔄 Loading chat sessions...');
            const response = await axios.get('/api/chat/sessions/', {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000, // 10秒超时
                withCredentials: true, // 包含认证cookie
                validateStatus: function (status) {
                    return status < 500; // 只有500+才算错误
                }
            });

            console.log('📋 Sessions response status:', response.status);
            console.log('📋 Sessions response data:', response.data);

            // 处理认证错误
            if (response.status === 401 || response.status === 403) {
                console.log('🔐 Token已过期，清除本地存储');
                throw new Error('AUTHENTICATION_FAILED');
            }

            // 处理没有会话的情况
            if (response.status === 200 && response.data.sessions) {
                return {
                    success: true,
                    sessions: response.data.sessions,
                    total: response.data.total || response.data.sessions.length
                };
            } else {
                console.log('📭 No sessions found');
                return {
                    success: true,
                    sessions: [],
                    total: 0
                };
            }
        } catch (error) {
            console.error('❌ Loading sessions failed:', error);
            
            if (error.message === 'AUTHENTICATION_FAILED') {
                throw error;
            }
            
            if (error.code === 'ECONNABORTED') {
                throw new Error('网络请求超时，请检查网络连接');
            }
            
            if (error.response?.status >= 500) {
                throw new Error('服务器内部错误，请稍后重试');
            }
            
            throw new Error('加载会话失败');
        }
    },

    // 创建新会话
    async createSession(token, title = '新对话') {
        try {
            console.log('🔄 Creating new session...');
            const response = await axios.post('/api/chat/sessions/create/',
                { title },
                {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 10000,
                    withCredentials: true
                }
            );

            console.log('✅ Session created:', response.data);
            return {
                success: true,
                session: response.data.session
            };
        } catch (error) {
            console.error('❌ Creating session failed:', error);
            
            if (error.response?.status === 401 || error.response?.status === 403) {
                throw new Error('AUTHENTICATION_FAILED');
            }
            
            throw new Error('创建会话失败');
        }
    },

    // 删除会话
    async deleteSession(token, sessionId) {
        try {
            console.log(`🗑️ Deleting session: ${sessionId}`);
            const response = await axios.delete(`/api/sessions/${sessionId}/delete/`, {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000,
                withCredentials: true
            });

            console.log('✅ Session deleted successfully');
            return { success: true };
        } catch (error) {
            console.error('❌ Deleting session failed:', error);
            
            if (error.response?.status === 401 || error.response?.status === 403) {
                throw new Error('AUTHENTICATION_FAILED');
            }
            
            if (error.response?.status === 404) {
                console.log('Session already deleted or not found');
                return { success: true }; // 会话已经不存在，认为删除成功
            }
            
            throw new Error('删除会话失败');
        }
    },

    // 更新会话标题
    async updateSessionTitle(token, sessionId, newTitle) {
        try {
            console.log(`✏️ Updating session title: ${sessionId} -> ${newTitle}`);
            const response = await axios.patch(`/api/sessions/${sessionId}/`,
                { title: newTitle },
                {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 10000,
                    withCredentials: true
                }
            );

            console.log('✅ Session title updated successfully');
            return {
                success: true,
                session: response.data.session
            };
        } catch (error) {
            console.error('❌ Updating session title failed:', error);
            
            if (error.response?.status === 401 || error.response?.status === 403) {
                throw new Error('AUTHENTICATION_FAILED');
            }
            
            throw new Error('更新会话标题失败');
        }
    }
};

export default sessionApi;