import axios from 'axios';

// 任务API服务 - 从chat.js重构而来
export const taskApi = {
    // 轮询任务状态 - 核心异步任务处理
    async pollTaskStatus(taskId, messageId = null, options = {}) {
        const {
            maxPollingTime = 10 * 60 * 1000, // 10分钟最大轮询时间
            pollInterval = 8000, // 8秒轮询间隔
            onProgress = null // 进度回调
        } = options;

        const startTime = Date.now();
        console.log('🚀 pollTaskStatus started for task:', taskId, 'messageId:', messageId, 'maxPollingTime:', maxPollingTime);

        // 🔧 关键修复：验证message_id有效性
        const isValidMessageId = messageId && 
                                messageId.trim() && 
                                messageId.length > 10 && 
                                !messageId.startsWith('user_') &&
                                !messageId.startsWith('async_') &&
                                !messageId.startsWith('error_') &&
                                !messageId.startsWith('success_');
        
        if (!isValidMessageId) {
            console.warn('⚠️ [TaskAPI] 无效或临时message_id，跳过传递给后端:', messageId);
            console.warn('  这将避免backend数据库查询错误，任务轮询将仅使用task_id');
            messageId = null; // 清空无效的messageId
        } else {
            console.log('✅ [TaskAPI] 使用有效的backend message_id:', messageId);
        }

        let consecutiveErrors = 0; // 连续错误计数
        const maxConsecutiveErrors = 5; // 增加容错次数，应对重启等情况
        let hasLoggedRecovery = false; // 避免重复记录恢复日志

        // 🔧 修复：使用Promise包装来确保正确的异步处理
        return new Promise((resolve, reject) => {
            let poll = async () => {
                try {
                    const token = localStorage.getItem('token');
                    const config = {
                        headers: {
                            'Authorization': token ? `Token ${token}` : '',
                            'Content-Type': 'application/json',
                        },
                        timeout: 15000, // 15秒超时，应对数据库重连
                    };

                    const params = { task_id: taskId };
                    // 🔧 修复：只有在message_id有效时才传递
                    if (messageId) {
                        params.message_id = messageId;
                    }

                    const response = await axios.get('/api/task-status/', {
                        ...config,
                        params
                    });

                    // 🎯 成功请求后重置错误计数
                    if (consecutiveErrors > 0) {
                        if (!hasLoggedRecovery) {
                            console.log(`🔄 Task polling recovered after ${consecutiveErrors} errors`);
                            hasLoggedRecovery = true;
                        }
                        consecutiveErrors = 0;
                    }

                    const data = response.data;
                    console.log('📥 Task status response:', data);

                    // 🔧 修复：正确映射后端返回的数据格式
                    // 后端返回格式：{ task_id, status, success, result?, message? }
                    
                    // 处理完成状态 - Celery SUCCESS 且业务逻辑成功
                    if (data.status === 'SUCCESS' && data.success === true) {
                        console.log('✅ Task completed successfully:', data);
                        resolve({
                            success: true,
                            data: data.result || data, // 使用result字段中的数据
                            final: true
                        });
                        return;
                    }

                    // 处理失败状态 - Celery SUCCESS 但业务逻辑失败，或者 Celery FAILURE
                    if (data.status === 'FAILURE' || (data.status === 'SUCCESS' && data.success === false)) {
                        console.log('❌ Task failed:', data);
                        resolve({
                            success: false,
                            error: data.error || 'Task failed',
                            data: data,
                            final: true
                        });
                        return;
                    }

                    // 检查是否超时
                    if (Date.now() - startTime > maxPollingTime) {
                        console.log('⏰ Polling timeout reached');
                        resolve({
                            success: false,
                            error: 'Polling timeout',
                            timeout: true,
                            final: true
                        });
                        return;
                    }

                    // 继续轮询 - 任务还在进行中 (PENDING, RETRY, STARTED, etc.)
                    if (onProgress) {
                        onProgress({
                            status: data.status,
                            message: data.message,
                            elapsedTime: Date.now() - startTime
                        });
                    }

                    // 📈 递增延迟：数据库重启后适应性延迟
                    let nextInterval = pollInterval;
                    if (consecutiveErrors > 0) {
                        nextInterval = Math.min(pollInterval * (1.5 ** consecutiveErrors), 30000);
                    }

                    // 🔧 修复：确保继续轮询而不是返回undefined
                    setTimeout(poll, nextInterval);

                } catch (error) {
                    consecutiveErrors++;
                    
                    // 🛡️ 特殊处理：服务重启期间的临时错误
                    const isTemporaryError = 
                        error.code === 'ECONNABORTED' || // 超时
                        error.response?.status === 500 || // 服务器内部错误
                        error.response?.status === 502 || // 网关错误
                        error.response?.status === 503 || // 服务不可用
                        error.message?.includes('timeout') ||
                        error.message?.includes('Connection') ||
                        error.message?.includes('Network Error');

                    if (isTemporaryError && consecutiveErrors <= maxConsecutiveErrors) {
                        console.log(`🔄 Temporary error ${consecutiveErrors}/${maxConsecutiveErrors}, retrying...`, error.message);
                        
                        // 检查是否超时
                        if (Date.now() - startTime > maxPollingTime) {
                            console.log('⏰ Polling timeout during error recovery');
                            resolve({
                                success: false,
                                error: 'Polling timeout during recovery',
                                timeout: true,
                                final: true
                            });
                            return;
                        }

                        // 🔄 重启恢复期间的适应性延迟
                        const retryDelay = Math.min(pollInterval * (1.2 ** consecutiveErrors), 20000);
                        setTimeout(poll, retryDelay);
                        return;
                    }

                    // 处理认证错误
                    if (error.response?.status === 401) {
                        console.error('🔐 Authentication error during polling');
                        resolve({
                            success: false,
                            error: 'Authentication failed',
                            authError: true,
                            final: true
                        });
                        return;
                    }

                    // 处理余额不足
                    if (error.response?.status === 402) {
                        const errorData = error.response.data || {};
                        resolve({
                            success: false,
                            error: `余额不足：需要 ${errorData.tokens_required || 'N/A'} tokens，当前 ${errorData.current_balance || 0} tokens`,
                            insufficientBalance: true,
                            final: true
                        });
                        return;
                    }

                    // 致命错误
                    console.error('💥 Fatal polling error after', consecutiveErrors, 'consecutive errors:', error);
                    reject({
                        success: false,
                        error: `轮询失败：${error.response?.data?.error || error.message || '未知错误'}`,
                        final: true
                    });
                }
            };

            // 开始轮询
            poll();
        });
    },

    // 🔧 新增：降级策略 - 直接获取Celery任务结果
    async getFallbackTaskResult(taskId) {
        try {
            // 这里可以尝试直接查询Celery结果或其他备用API
            console.log('🔄 Attempting fallback result retrieval for task:', taskId);
            
            // 方案1：尝试使用不同的API端点
            const token = localStorage.getItem('token');
            const config = {
                headers: {
                    'Authorization': token ? `Token ${token}` : '',
                    'Accept': 'application/json',
                },
                withCredentials: true,
                timeout: 10000
            };

            // 尝试老的API端点
            try {
                const response = await axios.get(`/api/status/${taskId}/`, config);
                if (response.data && response.data.result) {
                    return {
                        success: true,
                        result: response.data.result
                    };
                }
            } catch (oldApiError) {
                console.log('🔄 Old API endpoint also failed, trying other methods...');
            }

            // 方案2：可以添加其他降级方法，如直接查询Redis等
            
            return {
                success: false,
                error: 'All fallback methods failed'
            };
            
        } catch (error) {
            console.error('❌ Fallback task result retrieval failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    // 单次获取任务状态
    async getTaskStatus(taskId, messageId = null) {
        try {
            const token = localStorage.getItem('token');
            const config = {
                headers: {
                    'Authorization': token ? `Token ${token}` : '',
                    'Accept': 'application/json',
                },
                withCredentials: true,
                timeout: 10000
            };

            const params = new URLSearchParams({ task_id: taskId });
            if (messageId) {
                params.append('message_id', messageId);
            }

            const response = await axios.get(`/api/task-status/?${params.toString()}`, config);
            
            console.log('📊 Task status retrieved:', {
                taskId: taskId,
                status: response.data.status,
                hasResult: !!response.data.result
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('❌ Failed to get task status:', taskId, error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    // Debug helper function for analyzing task issues
    async debugTaskStatus(taskId) {
        try {
            const token = localStorage.getItem('token');
            const config = {
                headers: {
                    'Authorization': token ? `Token ${token}` : '',
                    'Accept': 'application/json',
                },
                withCredentials: true
            };

            const response = await axios.get(`/api/task-status/?task_id=${taskId}`, config);
            
            const debugInfo = {
                taskId: taskId,
                timestamp: new Date().toISOString(),
                response: response.data,
                statusCode: response.status,
                headers: response.headers
            };

            console.log('🔍 [DEBUG] Task Status Analysis:', debugInfo);

            // Log detailed analysis
            const data = response.data;
            const detailedAnalysis = {
                hasResult: !!data.result,
                hasImageUrl: !!(data.result?.image_url),
                hasVideoUrl: !!(data.result?.video_url),
                hasChatResponse: !!(data.result?.chat_response),
                resultKeys: data.result ? Object.keys(data.result) : [],
                fullResult: data.result
            };

            console.log('🔍 [DEBUG] Detailed Analysis:', detailedAnalysis);

            return {
                success: true,
                debugInfo: debugInfo,
                analysis: detailedAnalysis
            };
        } catch (error) {
            console.error('🔍 [DEBUG] Task status debug failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    // 取消任务
    async cancelTask(taskId) {
        try {
            const token = localStorage.getItem('token');
            const config = {
                headers: {
                    'Authorization': token ? `Token ${token}` : '',
                    'Content-Type': 'application/json',
                },
                withCredentials: true,
                timeout: 10000
            };

            const response = await axios.post('/api/cancel-task/', {
                task_id: taskId
            }, config);

            console.log('🛑 Task cancelled successfully:', taskId);
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('❌ Failed to cancel task:', taskId, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
};

// 暴露调试函数到全局，方便手动调试
if (typeof window !== 'undefined') {
    window.debugTaskStatus = taskApi.debugTaskStatus;
}

export default taskApi;