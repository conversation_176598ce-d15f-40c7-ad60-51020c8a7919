import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useLanguage } from '../../contexts/LanguageContext';
import { useUserContext } from '../../contexts/UserContext';
import { useToast } from '../Toast';
import { useAtMention } from '../../hooks/useAtMention';

// 导入模块化组件
import ModernNavbar from '../ModernNavbar';
import UserAvatar from '../UserAvatar';
import Loading from '../Loading';
import UpgradeModal from '../UpgradeModal';
import ReferenceSelector from '../ReferenceSelector';
import ReferencePreview from '../ReferencePreview';
import ErrorBoundary from '../ErrorBoundary';

// 导入自定义hooks
import { useChatSessions } from './hooks/useChatSessions';
import { useChatMessages } from './hooks/useChatMessages';
import { useWebSocket } from './hooks/useWebSocket';
import { useChatSubmit } from './hooks/useChatSubmit';

// 导入子组件
import MessageBubble from './components/MessageBubble';
import ChatInput from './components/ChatInput';
import SessionItem from './components/SessionItem';
import WebSocketStatus, { WebSocketStatusModal } from '../WebSocketStatus';

// 导入工具和常量
import { messageUtils, TEXT_CHAT_MODE } from './utils/messageUtils';

// 重构后的ChatPage组件 - 集成所有模块
const ChatPage = () => {
    const router = useRouter();
    const { t, language } = useLanguage();
    const { user, loading: userLoading, error: userError, updateUser, updateBalance } = useUserContext();
    const toast = useToast();

    // 本地UI状态
    const [currentPrompt, setCurrentPrompt] = useState('');
    const [currentFile, setCurrentFile] = useState(null);
    const [currentMode, setMode] = useState(TEXT_CHAT_MODE);
    const [advancedMode, setAdvancedMode] = useState(false);
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const [authCheckComplete, setAuthCheckComplete] = useState(false);

    // 用户界面状态
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    const [showWebSocketModal, setShowWebSocketModal] = useState(false);
    const [membershipInfo, setMembershipInfo] = useState({
        current_plan: 'Free',
        current_balance: 0,
        is_expired: false
    });

    // @mention功能
    const {
        isReferenceDropdownOpen,
        referenceSearchQuery,
        dropdownPosition,
        selectedReferences,
        visualReferences,
        handleInputChange: handleAtMentionInputChange,
        handleKeyDown: handleAtMentionKeyDown,
        handleReferenceSelect,
        closeReferenceDropdown,
        removeReference,
        parseReferences,
        processMessageForSubmission,
        clearReferences,
        reset: resetAtMention,
        clearAtSymbol,
        forceCleanAtSymbol
    } = useAtMention(null, setCurrentPrompt);

    // 处理认证错误的回调
    const handleAuthError = () => {
        console.log('🔐 Authentication error, logging out...');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        updateUser(null);
        
        // 清除所有状态
        setCurrentPrompt('');
        setCurrentFile(null);
        setShowUserMenu(false);
        setShowUpgradeModal(false);
        
        toast.success(t('auth.logout.success') || 'Logged out successfully');
        router.push('/');
    };

    // 使用自定义hooks
    const sessionHook = useChatSessions(user, handleAuthError);
    
    // WebSocket hooks - 替换轮询系统
    const webSocketHook = useWebSocket();

    // 配置WebSocket回调函数
    useEffect(() => {
        webSocketHook.setCallbacks({
            onTaskComplete: (taskId, result) => {
                console.log('✅ [WebSocket] 任务完成:', taskId, result);
            messageHook.updateMessageByTaskId(taskId, result);
        },
            onTaskError: (taskId, error, errorType) => {
                console.error('❌ [WebSocket] 任务失败:', taskId, error);
            const errorMessage = messageUtils.createErrorMessage(new Error(error));
            messageHook.addMessage(errorMessage);
            },
            onTaskProgress: (taskId, progressData) => {
                console.log('📊 [WebSocket] 任务进度:', taskId, progressData);
                // 这里可以添加进度更新逻辑
            }
        });
    }, [webSocketHook, messageHook]);

    // 用户登录时建立WebSocket连接
    useEffect(() => {
        if (user && !webSocketHook.isConnected && !webSocketHook.isConnecting) {
            const token = localStorage.getItem('token');
            if (token) {
                console.log('🔗 [ChatPage] 建立WebSocket连接');
                webSocketHook.connect(token);
            }
        }
        
        // 用户登出时断开连接
        if (!user && webSocketHook.isConnected) {
            console.log('🔌 [ChatPage] 断开WebSocket连接');
            webSocketHook.disconnect();
        }
    }, [user, webSocketHook]);

    // 修复：useChatMessages不再需要轮询参数
    const messageHook = useChatMessages(sessionHook.activeSessionId, handleAuthError);

    // 聊天提交hook - 移除轮询相关逻辑
    const submitHook = useChatSubmit({
        activeSessionId: sessionHook.activeSessionId,
        onCreateSession: sessionHook.createSession,
        onAddMessage: messageHook.addMessage,
        onAddMessages: messageHook.addMessages,
        onRemoveLoadingMessages: messageHook.removeLoadingMessages,
        onUpdateBalance: updateBalance,
        onAuthError: handleAuthError,
        onShowUpgradeModal: () => setShowUpgradeModal(true)
    });

    // 处理用户登录状态检查和重定向
    useEffect(() => {
        const token = localStorage.getItem('token');

        if (!token) {
            setAuthCheckComplete(true);
            router.push('/');
            return;
        }

        const timer = setTimeout(() => {
            setAuthCheckComplete(true);
            if (!userLoading && !user) {
                console.log('用户认证失败，重定向到首页');
                router.push('/');
            }
        }, 2000);

        if (user) {
            clearTimeout(timer);
            setAuthCheckComplete(true);
        }

        return () => clearTimeout(timer);
    }, [user, userLoading, router]);

    // 同步用户token余额数据
    useEffect(() => {
        if (user && user.tokens !== undefined) {
            setMembershipInfo({
                current_plan: user?.current_plan || 'Free',
                current_balance: user.tokens,
                is_expired: user?.tokens_expires_at ? new Date(user.tokens_expires_at) < new Date() : false,
                tokens_expires_at: user?.tokens_expires_at,
                total_generations: user?.total_generations || 0
            });
        }
    }, [user]);

    // 处理输入变化
    const handleInputChange = (e) => {
        handleAtMentionInputChange(e, (event) => {
            setCurrentPrompt(event.target.value);
        });
    };

    // 处理键盘事件
    const handleKeyDown = (e) => {
        handleAtMentionKeyDown(e, (event) => {
            if (event.key === 'Enter' && !event.shiftKey && !isReferenceDropdownOpen) {
                event.preventDefault();
                handleSubmit(event);
            }
        });

        if (e.key === 'Enter' && !e.shiftKey && !isReferenceDropdownOpen) {
            e.preventDefault();
            handleSubmit(e);
        }
    };

    // 处理提交
    const handleSubmit = async (e) => {
        e.preventDefault();

        if ((!currentPrompt.trim() && !currentFile) || submitHook.isSubmitting) {
            return;
        }

        // 处理@mentions
        const { message: processedMessage, selectedReferences: messageSelectedReferences } = processMessageForSubmission(currentPrompt);

        // 提交消息
        const result = await submitHook.submitWithReferences({
            prompt: currentPrompt,
            currentFile,
            currentMode,
            selectedReferences: messageSelectedReferences,
            processedMessage
        });

        if (result?.success) {
            // 清空输入
            setCurrentPrompt('');
            setCurrentFile(null);
            clearReferences();
        }
    };

    // 文件处理
    const handleFileChange = (file) => {
        setCurrentFile(file);
        if (advancedMode) {
            setMode(file.type.startsWith('image/') ? messageUtils.IMG_TO_IMG_MODE : TEXT_CHAT_MODE);
        }
    };

    const handleFileRemove = () => {
        setCurrentFile(null);
        if (advancedMode) {
            setMode(TEXT_CHAT_MODE);
        }
    };

    // 模式切换
    const toggleAdvancedMode = () => {
        setAdvancedMode(!advancedMode);
        if (advancedMode) {
            setMode(TEXT_CHAT_MODE);
        }
    };

    // 显示加载状态
    if (userLoading || !authCheckComplete) {
        return (
            <div className="min-h-screen bg-[#0f0a1d] flex items-center justify-center">
                <Loading type="spinner" size="lg" color="purple" />
            </div>
        );
    }

    return (
        <ErrorBoundary>
            <div className="h-screen flex bg-[#0f0a1d] overflow-hidden">
                <Head>
                    <title>MirageMakers AI - {t('chat.title')}</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                </Head>

                {/* 左侧侧边栏 - 会话列表 */}
                <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-black/25 backdrop-blur-xl flex-shrink-0 relative z-0`}>
                    <div className="h-full flex flex-col">
                        {/* 侧边栏头部 */}
                        <div className="p-4 border-b border-white/10">
                            <div className="flex items-center justify-between">
                                <h2 className="text-white font-semibold">{t('chat.sessions')}</h2>
                                <button
                                    onClick={() => sessionHook.createSession()}
                                    className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 rounded-lg flex items-center justify-center text-white transition-colors"
                                    title={t('chat.new.session')}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* 会话列表 */}
                        <div className="flex-1 overflow-y-auto p-2 space-y-1">
                            {sessionHook.loading ? (
                                <div className="flex items-center justify-center py-8">
                                    <Loading type="spinner" size="sm" color="purple" />
                                </div>
                            ) : sessionHook.sessions.length === 0 ? (
                                <div className="text-center py-8">
                                    <p className="text-white/60 text-sm">{t('chat.no.sessions')}</p>
                                </div>
                            ) : (
                                sessionHook.sessions.map((session) => (
                                    <SessionItem
                                        key={session.id}
                                        session={session}
                                        isActive={session.id === sessionHook.activeSessionId}
                                        onClick={sessionHook.switchToSession}
                                        onDelete={sessionHook.deleteSession}
                                        onUpdateTitle={sessionHook.updateSessionTitle}
                                    />
                                ))
                            )}
                        </div>
                    </div>
                </div>

                {/* 主聊天区域 */}
                <div className="flex-1 flex flex-col">
                    {/* 顶部导航 */}
                    <div className="bg-black/20 backdrop-blur-xl border-b border-white/10 px-6 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={() => setSidebarOpen(!sidebarOpen)}
                                    className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center text-white/70 hover:text-white transition-colors"
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <line x1="3" y1="6" x2="21" y2="6"></line>
                                        <line x1="3" y1="12" x2="21" y2="12"></line>
                                        <line x1="3" y1="18" x2="21" y2="18"></line>
                                    </svg>
                                </button>
                                
                                <div>
                                    <h1 className="text-white font-semibold">
                                        {sessionHook.activeSession?.title || t('chat.new.session')}
                                    </h1>
                                    <p className="text-white/60 text-sm">
                                        {messageHook.messageStats.display} {t('chat.messages')}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-4">
                                <ModernNavbar />
                                
                                {/* WebSocket状态指示器 */}
                                <WebSocketStatus 
                                    connectionStatus={webSocketHook.connectionStatus}
                                    isVisible={true}
                                    showDetails={false}
                                />
                                
                                <UserAvatar
                                    user={user}
                                    membershipInfo={membershipInfo}
                                    showUserMenu={showUserMenu}
                                    setShowUserMenu={setShowUserMenu}
                                    onLogout={handleAuthError}
                                />
                            </div>
                        </div>
                    </div>

                    {/* 消息区域 */}
                    <div className="flex-1 overflow-y-auto">
                        {messageHook.displayMessages.length === 0 ? (
                            <div className="h-full flex flex-col items-center justify-center text-center px-4">
                                <h1 className="text-3xl font-medium text-white mb-8">What can I help with?</h1>
                            </div>
                        ) : (
                            <div className="max-w-3xl mx-auto px-4 py-8">
                                {messageHook.displayMessages.map((msg, idx) => (
                                    <MessageBubble 
                                        key={`${msg.id || idx}-${msg.timestamp || Date.now()}`} 
                                        message={msg} 
                                        isUser={msg.sender === 'user'} 
                                    />
                                ))}
                                <div ref={messageHook.messagesEndRef} />
                            </div>
                        )}
                    </div>

                    {/* 底部输入区域 */}
                    <ChatInput
                        currentPrompt={currentPrompt}
                        onPromptChange={setCurrentPrompt}
                        onSubmit={handleSubmit}
                        onKeyDown={handleKeyDown}
                        currentFile={currentFile}
                        onFileChange={handleFileChange}
                        onFileRemove={handleFileRemove}
                        advancedMode={advancedMode}
                        onAdvancedModeToggle={toggleAdvancedMode}
                        currentMode={currentMode}
                        onModeChange={setMode}
                        isLoading={submitHook.isSubmitting}
                        isReferenceDropdownOpen={isReferenceDropdownOpen}
                        onAtMentionInputChange={handleAtMentionInputChange}
                        onAtMentionKeyDown={handleAtMentionKeyDown}
                        ReferenceSelector={() => (
                            <ReferenceSelector
                                isOpen={isReferenceDropdownOpen}
                                searchQuery={referenceSearchQuery}
                                position={dropdownPosition}
                                sessionMedia={messageHook.sessionMedia}
                                onSelect={handleReferenceSelect}
                                onClose={closeReferenceDropdown}
                            />
                        )}
                        ReferencePreview={() => (
                            <ReferencePreview
                                references={selectedReferences}
                                onRemove={removeReference}
                            />
                        )}
                    />
                </div>

                {/* 升级弹窗 */}
                {showUpgradeModal && (
                    <UpgradeModal
                        isOpen={showUpgradeModal}
                        onClose={() => setShowUpgradeModal(false)}
                        membershipInfo={membershipInfo}
                    />
                )}

                {/* WebSocket状态详情模态框 */}
                <WebSocketStatusModal
                    isOpen={showWebSocketModal}
                    onClose={() => setShowWebSocketModal(false)}
                    connectionStatus={webSocketHook.connectionStatus}
                    lastMessage={webSocketHook.lastMessage}
                    activeTasks={webSocketHook.activeTasks}
                    reconnectAttempts={0}
                />
            </div>
        </ErrorBoundary>
    );
};

export default ChatPage;