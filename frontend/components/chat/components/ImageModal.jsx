import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';

function ImageModal({ src, alt, isOpen, onClose }) {
    const { t } = useLanguage();

    if (!isOpen) return null;

    const handleDownload = async () => {
        try {
            const proxyUrl = `/api/download-proxy/?url=${encodeURIComponent(src)}`;
            const response = await fetch(proxyUrl, {
                method: 'GET',
                headers: {
                    'Authorization': localStorage.getItem('token') ? `Token ${localStorage.getItem('token')}` : '',
                },
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`Download failed: ${response.status}`);
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `MirageMakers-image-${Date.now()}.jpg`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();

            setTimeout(() => {
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }, 100);

        } catch (error) {
            console.error('download failed:', error);
            window.open(src, '_blank');
        }
    };

    return (
        <div
            className="fixed inset-0 bg-black/80 z-40 flex items-center justify-center p-4"
            onClick={onClose}
        >
            <div className="relative max-w-5xl max-h-[90vh] w-full h-full flex items-center justify-center">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-colors z-10"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>

                <button
                    onClick={handleDownload}
                    className="absolute top-4 right-16 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white transition-colors z-10"
                    title={t('image.download')}
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                        <polyline points="7,10 12,15 17,10" />
                        <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                </button>

                <img
                    src={src}
                    alt={alt}
                    className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                    onClick={(e) => e.stopPropagation()}
                />
            </div>
        </div>
    );
}

export default ImageModal;