import React, { useRef, useEffect } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { messageUtils } from '../utils/messageUtils';

// 聊天输入组件 - 从chat.js重构而来
const ChatInput = ({
    currentPrompt,
    onPromptChange,
    onSubmit,
    onKeyDown,
    currentFile,
    onFileChange,
    onFileRemove,
    advancedMode,
    onAdvancedModeToggle,
    currentMode,
    onModeChange,
    isLoading,
    isReferenceDropdownOpen,
    // @mention 相关props
    onAtMentionInputChange,
    onAtMentionKeyDown,
    // 引用组件props
    ReferenceSelector,
    ReferencePreview,
    // 文件相关props
    FileUpload
}) => {
    const { t } = useLanguage();
    const textareaRef = useRef(null);
    const fileInputRef = useRef(null);

    const { TEXT_CHAT_MODE, TEXT_TO_IMG_MODE, IMG_TO_IMG_MODE, TEXT_TO_VIDEO_MODE, IMG_TO_VIDEO_MODE } = messageUtils;

    // 自动调整文本框高度
    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, [currentPrompt]);

    // 处理输入变化
    const handleInputChange = (e) => {
        if (onAtMentionInputChange) {
            // 集成@mention功能
            onAtMentionInputChange(e, onPromptChange);
        } else {
            onPromptChange(e);
        }
    };

    // 处理键盘事件
    const handleKeyDown = (e) => {
        if (onAtMentionKeyDown) {
            // 集成@mention键盘处理
            onAtMentionKeyDown(e, onKeyDown);
        } else {
            onKeyDown(e);
        }
    };

    // 处理文件选择
    const handleFileChange = (e) => {
        if (e.target.files && e.target.files.length > 0) {
            const files = Array.from(e.target.files);
            console.log('Files selected:', files.map(f => f.name));

            // 目前处理第一个文件以保持向后兼容
            onFileChange(files[0]);

            // 如果启用高级模式且是图片文件，自动切换到相应模式
            if (advancedMode) {
                const newMode = files[0].type.startsWith('image/') ? IMG_TO_IMG_MODE : TEXT_CHAT_MODE;
                onModeChange(newMode);
            }

            // 多文件选择提示
            if (files.length > 1) {
                console.log(`Note: ${files.length} files selected, processing only the first file: ${files[0].name}`);
            }
        }
    };

    // 处理加号点击
    const handlePlusClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    // 移除选中的文件
    const handleFileRemove = () => {
        onFileRemove();
        
        // 重置文件输入元素
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }

        // 如果在高级模式下，重置回文本聊天模式
        if (advancedMode) {
            onModeChange(TEXT_CHAT_MODE);
        }
    };

    // 根据当前选择的模式和文件类型，判断是否显示相应的按钮
    const shouldShowButton = (buttonMode) => {
        return messageUtils.shouldShowModeButton(buttonMode, advancedMode, currentFile);
    };

    return (
        <div className="p-6 bg-black/30 backdrop-blur-xl border-t border-white/10">
            {/* 引用预览组件 */}
            {ReferencePreview && <ReferencePreview />}
            
            {/* 高级模式按钮组 */}
            {advancedMode && (
                <div className="mb-4 flex flex-wrap gap-2">
                    {shouldShowButton(TEXT_CHAT_MODE) && (
                        <button
                            onClick={() => onModeChange(TEXT_CHAT_MODE)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                currentMode === TEXT_CHAT_MODE
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white'
                                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                            }`}
                        >
                            💬 {t('mode.text.chat')}
                        </button>
                    )}
                    
                    {shouldShowButton(TEXT_TO_IMG_MODE) && (
                        <button
                            onClick={() => onModeChange(TEXT_TO_IMG_MODE)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                currentMode === TEXT_TO_IMG_MODE
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white'
                                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                            }`}
                        >
                            🎨 {t('mode.text.to.image')}
                        </button>
                    )}
                    
                    {shouldShowButton(IMG_TO_IMG_MODE) && (
                        <button
                            onClick={() => onModeChange(IMG_TO_IMG_MODE)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                currentMode === IMG_TO_IMG_MODE
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white'
                                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                            }`}
                        >
                            🖼️ {t('mode.image.to.image')}
                        </button>
                    )}
                    
                    {shouldShowButton(TEXT_TO_VIDEO_MODE) && (
                        <button
                            onClick={() => onModeChange(TEXT_TO_VIDEO_MODE)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                currentMode === TEXT_TO_VIDEO_MODE
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white'
                                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                            }`}
                        >
                            🎬 {t('mode.text.to.video')}
                        </button>
                    )}
                    
                    {shouldShowButton(IMG_TO_VIDEO_MODE) && (
                        <button
                            onClick={() => onModeChange(IMG_TO_VIDEO_MODE)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                currentMode === IMG_TO_VIDEO_MODE
                                    ? 'bg-gradient-to-r from-purple-500 to-violet-600 text-white'
                                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                            }`}
                        >
                            🎥 {t('mode.image.to.video')}
                        </button>
                    )}
                </div>
            )}

            {/* 文件显示区域 */}
            {currentFile && (
                <div className="mb-4 p-3 bg-white/5 rounded-lg border border-white/10">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-blue-500 rounded-lg flex items-center justify-center">
                                {currentFile.type.startsWith('image/') ? '🖼️' : '📁'}
                            </div>
                            <div>
                                <p className="text-white font-medium text-sm">{currentFile.name}</p>
                                <p className="text-white/60 text-xs">
                                    {(currentFile.size / 1024 / 1024).toFixed(1)} MB
                                </p>
                            </div>
                        </div>
                        <button
                            onClick={handleFileRemove}
                            className="w-6 h-6 bg-red-500/20 hover:bg-red-500/30 rounded-full flex items-center justify-center text-red-400 transition-colors"
                        >
                            ×
                        </button>
                    </div>
                </div>
            )}

            {/* 主要输入区域 */}
            <div className="relative">
                <div className="flex items-end space-x-3">
                    {/* 文件上传按钮 */}
                    <button
                        onClick={handlePlusClick}
                        className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-xl flex items-center justify-center text-white/70 hover:text-white transition-colors flex-shrink-0"
                        disabled={isLoading}
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </button>

                    {/* 文本输入区域 */}
                    <div className="flex-1 relative">
                        <textarea
                            ref={textareaRef}
                            value={currentPrompt}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            placeholder={t('chat.input.placeholder')}
                            className="w-full bg-white/10 backdrop-blur-xl text-white placeholder-white/50 border border-white/20 rounded-xl px-4 py-3 resize-none min-h-[50px] max-h-32 focus:outline-none focus:border-purple-400/50 transition-colors"
                            disabled={isLoading}
                        />
                        
                        {/* 引用选择器 */}
                        {ReferenceSelector && isReferenceDropdownOpen && <ReferenceSelector />}
                    </div>

                    {/* 发送按钮 */}
                    <button
                        onClick={onSubmit}
                        disabled={isLoading || (!currentPrompt.trim() && !currentFile)}
                        className="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 disabled:from-gray-500 disabled:to-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-xl flex items-center justify-center text-white transition-all flex-shrink-0"
                    >
                        {isLoading ? (
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        ) : (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                            </svg>
                        )}
                    </button>

                    {/* 高级模式切换按钮 */}
                    <button
                        onClick={onAdvancedModeToggle}
                        className={`w-10 h-10 rounded-xl flex items-center justify-center transition-colors flex-shrink-0 ${
                            advancedMode
                                ? 'bg-gradient-to-br from-purple-500 to-violet-600 text-white'
                                : 'bg-white/10 hover:bg-white/20 text-white/70 hover:text-white'
                        }`}
                        title={t('chat.advanced.mode')}
                    >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                    </button>
                </div>

                {/* 隐藏的文件输入 */}
                <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileChange}
                    accept="image/*,video/*"
                    className="hidden"
                    multiple
                />
            </div>

            {/* 文件上传组件 */}
            {FileUpload && <FileUpload />}
        </div>
    );
};

export default ChatInput;