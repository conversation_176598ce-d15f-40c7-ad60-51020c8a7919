import React, { useState } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import LoadingDots from '../../LoadingDots';

// 重构后的MessageBubble组件 - 从chat.js中提取
function MessageBubble({ message, isUser }) {
    const [showImageModal, setShowImageModal] = useState(false);
    const [isTextExpanded, setIsTextExpanded] = useState(false);
    const { t } = useLanguage();

    // 文本长度阈值
    const TEXT_COLLAPSE_THRESHOLD = 150;

    // GPT风格的媒体加载占位符
    const MediaLoadingPlaceholder = ({ startTime, taskType = 'text_to_image' }) => {
        const [elapsedTime, setElapsedTime] = useState(0);
        
        React.useEffect(() => {
            const interval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - (startTime || Date.now())) / 1000);
                setElapsedTime(elapsed);
            }, 1000);
            return () => clearInterval(interval);
        }, [startTime]);

        const isVideo = taskType.includes('video') || taskType.includes('img2video');

        return (
            <div className="relative overflow-hidden rounded-2xl ring-1 ring-white/10" 
                 style={{ 
                     width: isVideo ? '400px' : '280px', 
                     height: isVideo ? '225px' : '280px' 
                 }}>
                {/* 渐变背景 */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-blue-500/20 to-indigo-500/20"></div>
                
                {/* 动画波纹效果 */}
                <div className="absolute inset-0">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                </div>
                
                {/* 中心内容 */}
                <div className="absolute inset-0 flex items-center justify-center">
                    {/* 旋转的加载图标 - 去掉文字，只保留视觉效果 */}
                    <div className="relative">
                        <div className="w-16 h-16 border-4 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
                        <div className="absolute inset-3 border-2 border-transparent border-t-purple-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                        <div className="absolute inset-6 border border-transparent border-t-blue-400 rounded-full animate-spin" style={{ animationDuration: '3s' }}></div>
                    </div>
                </div>
                
                {/* 底部进度条 */}
                <div className="absolute bottom-4 left-4 right-4">
                    <div className="h-1 bg-white/20 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-purple-400 to-blue-400 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>
        );
    };

    // 获取引用媒体数据 - 优先使用后端返回的数据
    const getReferencedMedia = () => {
        // 1. 优先使用后端返回的 referenced_media 数据
        if (message.referenced_media && message.referenced_media.length > 0) {
            return message.referenced_media;
        }

        // 2. 如果没有后端数据，尝试从 metadata 中获取
        if (message.metadata && message.metadata.referenced_media && message.metadata.referenced_media.length > 0) {
            return message.metadata.referenced_media;
        }

        // 3. 最后回退到解析文本内容中的@引用
        if (message.content) {
            const mentionRegex = /@(\d+|image_\d+|video_\d+)/g;
            const mentions = [];
            let match;

            while ((match = mentionRegex.exec(message.content)) !== null) {
                const referenceId = match[1];
                let type = 'unknown';

                // 判断引用类型
                if (referenceId.startsWith('image_')) {
                    type = 'image';
                } else if (referenceId.startsWith('video_')) {
                    type = 'video';
                } else if (/^\d+$/.test(referenceId)) {
                    // 简化格式的数字引用，类型未知，显示为占位符
                    type = 'media';
                }

                mentions.push({
                    reference_id: referenceId,
                    type: type,
                    url: null, // 没有URL，会显示占位符
                    display_name: `Reference ${referenceId}`
                });
            }

            return mentions;
        }

        return [];
    };

    // 获取引用媒体
    const referencedMedia = getReferencedMedia();

    // 调试：记录引用媒体处理结果
    React.useEffect(() => {
        if (isUser && (message.referenced_media?.length > 0 || message.content?.includes('@'))) {
            console.log('🔍 [MessageBubble] 用户消息引用媒体处理:', {
                messageId: message.id,
                hasReferencedMedia: !!(message.referenced_media?.length > 0),
                referencedMediaCount: message.referenced_media?.length || 0,
                referencedMediaData: message.referenced_media?.map(ref => ({
                    reference_id: ref.reference_id,
                    type: ref.type,
                    hasUrl: !!ref.url
                })) || [],
                contentHasAtSymbol: message.content?.includes('@'),
                finalReferencedMediaCount: referencedMedia.length,
                finalReferencedMedia: referencedMedia.map(ref => ({
                    reference_id: ref.reference_id,
                    type: ref.type,
                    hasUrl: !!ref.url
                }))
            });
        }
    }, [message.referenced_media, message.content, isUser, referencedMedia]);

    // 处理文本内容 - 移除折叠功能，完全显示
    const processTextContent = (content) => {
        if (!content) return { displayText: '', needsTruncation: false };
        
        // 直接返回完整内容，不再进行文本折叠
        return {
            displayText: content,
            needsTruncation: false
        };
    };

    const { displayText, needsTruncation } = processTextContent(message.content);

    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        });
    };

    return (
        <div className={`w-full flex mb-8 ${isUser ? 'justify-end' : 'justify-start'} group animate-slideUp`}>
            <div className={`flex flex-col max-w-[85%] ${isUser ? 'items-end' : 'items-start'}`}>
                {/* 时间戳 */}
                <div className={`text-xs text-white/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 mb-2 ${isUser ? 'text-right' : 'text-left'}`}>
                    {formatTime(message.created_at || message.timestamp)}
                </div>

                {/* 文本消息气泡 - 智能过滤处理中的消息 */}
                {(() => {
                    // 定义处理中的消息关键词
                    const processingKeywords = [
                        'Processing your request',
                        '正在生成',
                        'Video is being generated',
                        'Please be patient',
                        'This may take',
                        'generation is in progress',
                        'please wait',
                        '请耐心等待',
                        '生成中',
                        'Generating',
                        'minutes'
                    ];
                    
                    // 检查是否是处理消息
                    const isProcessingMessage = message.content && 
                        processingKeywords.some(keyword => 
                            message.content.toLowerCase().includes(keyword.toLowerCase())
                        );
                    
                    // 对于历史消息（非加载状态）：如果有媒体内容，直接隐藏文字气泡，不考虑处理关键词
                    if (!message.isLoading && (message.image_url || message.video_url)) {
                        return false;
                    }
                    
                    // 对于加载中的消息：如果有媒体内容且包含处理关键词，不显示文字气泡
                    if (message.isLoading && (message.image_url || message.video_url) && isProcessingMessage) {
                        return false;
                    }
                    
                    // 其他情况：用户加载状态、错误消息、或正常内容
                    return (
                        (isUser && message.isLoading) || 
                        message.error || 
                        message.errorMessage ||
                        (message.content && !isProcessingMessage)
                    );
                })() && (
                    <div className={`relative ${isUser ? 
                        'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25' : 
                        message.error || message.errorMessage ? 'bg-red-500/20 backdrop-blur-xl text-red-200 border border-red-500/30 shadow-lg shadow-red-500/20' :
                        'bg-white/10 backdrop-blur-xl text-white border border-white/10 shadow-lg shadow-black/20'
                    } p-5 rounded-2xl ${isUser ? 'rounded-br-md' : 'rounded-bl-md'} max-w-md transition-all duration-200 hover:shadow-xl mb-3`}>
                        {isUser && message.isLoading ? (
                            <LoadingDots />
                        ) : (
                            <div className="relative">
                                <div className="text-base leading-relaxed whitespace-pre-wrap">
                                    {displayText}
                                </div>
                            </div>
                        )}
                        
                        {/* 气泡尾巴 */}
                        <div className={`absolute top-4 ${isUser ? 
                            'right-0 translate-x-1 border-l-8 border-l-purple-600 border-t-4 border-t-transparent border-b-4 border-b-transparent' :
                            message.error || message.errorMessage ? 
                                'left-0 -translate-x-1 border-r-8 border-r-red-500/20 border-t-4 border-t-transparent border-b-4 border-b-transparent' :
                                'left-0 -translate-x-1 border-r-8 border-r-white/10 border-t-4 border-t-transparent border-b-4 border-b-transparent'
                        }`}></div>
                    </div>
                )}

                {/* 用户消息的引用媒体显示 - 只在用户消息中显示 */}
                {isUser && referencedMedia.length > 0 && (
                    <div className={`${(message.content || message.isLoading) ? 'mt-3' : ''} flex flex-col items-end w-full`}>
                        {/* 引用媒体网格 */}
                        <div className="flex flex-wrap gap-2 justify-end mb-2">
                            {referencedMedia.map((media, index) => (
                                <div key={index} className="relative group">
                                    <div
                                        className="relative cursor-pointer overflow-hidden rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 ring-1 ring-white/20"
                                        style={{ width: '80px', height: '80px' }}
                                        onClick={() => {
                                            if (media.type === 'image' && media.url) {
                                                setShowImageModal(media.url);
                                            }
                                        }}
                                    >
                                        {media.url ? (
                                            // 有URL的情况 - 显示实际媒体
                                            media.type === 'image' ? (
                                                <img
                                                    src={media.url}
                                                    alt={`Referenced image ${media.reference_id}`}
                                                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                                                />
                                            ) : (
                                                <video
                                                    src={media.url}
                                                    className="w-full h-full object-cover rounded-xl"
                                                    preload="metadata"
                                                >
                                                    Your browser does not support the video tag.
                                                </video>
                                            )
                                        ) : (
                                            // 没有URL的情况 - 显示占位符
                                            <div className="w-full h-full flex flex-col items-center justify-center bg-purple-900/30 text-white text-center border border-purple-500/30 rounded-xl backdrop-blur-sm">
                                                <div className="text-xl mb-1">
                                                    {media.type === 'image' ? '🖼️' :
                                                        media.type === 'video' ? '🎥' :
                                                            '📎'}
                                                </div>
                                                <div className="text-xs opacity-75 font-medium">
                                                    @{media.reference_id}
                                                </div>
                                            </div>
                                        )}

                                        {/* 媒体遮罩层 */}
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* References 标签 */}
                        <div className="self-end">
                            <div className="bg-purple-900/30 text-purple-300 text-xs px-3 py-1 rounded-full border border-purple-500/20 backdrop-blur-sm font-medium">
                                {referencedMedia.length} reference{referencedMedia.length > 1 ? 's' : ''}
                            </div>
                        </div>
                    </div>
                )}

                {/* 媒体生成中的占位符 - 只在Bot侧加载且无错误时显示 */}
                {message.sender === 'bot' && message.isLoading && !message.error && !message.errorMessage && (
                    <MediaLoadingPlaceholder 
                        startTime={message.startTime || Date.now()}
                        taskType={message.taskType || 'text_to_image'}
                    />
                )}

                {/* 图片显示区域 - 现代化设计 */}
                {message.image_url && (
                    <div className="relative group">
                        <div
                            className="relative cursor-pointer overflow-hidden rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/30 flex-shrink-0 ring-1 ring-white/10"
                            onClick={() => setShowImageModal(message.image_url)}
                            style={{ 
                                width: '280px', 
                                height: '280px'
                            }}
                        >
                            <img
                                src={message.image_url}
                                alt="Generated content"
                                className="w-full h-full object-cover rounded-2xl hover:scale-105 transition-transform duration-500"
                            />
                            
                            {/* 图片遮罩层 */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                            
                            {/* 操作按钮组 */}
                            <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                {/* 下载按钮 */}
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const link = document.createElement('a');
                                        link.href = message.image_url;
                                        link.download = `image_${Date.now()}.png`;
                                        link.target = '_blank';
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                    }}
                                    className="bg-black/50 backdrop-blur-sm rounded-full p-2 hover:bg-black/70 transition-all duration-200"
                                    title="下载图片"
                                >
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </button>

                                {/* 放大图标 */}
                                <div className="bg-black/50 backdrop-blur-sm rounded-full p-2">
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* 视频显示区域 - 现代化设计 */}
                {message.video_url && (
                    <div className="relative group">
                        <div
                            className="relative overflow-hidden rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/30 ring-1 ring-white/10"
                            style={{ maxWidth: '400px' }}
                        >
                            <video
                                src={message.video_url}
                                controls
                                className="w-full h-auto rounded-2xl hover:scale-105 transition-transform duration-500"
                                style={{ maxHeight: '300px' }}
                                preload="metadata"
                            >
                                Your browser does not support the video tag.
                            </video>

                            {/* 视频遮罩层 */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none"></div>

                            {/* 下载按钮 */}
                            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const link = document.createElement('a');
                                        link.href = message.video_url;
                                        link.download = `video_${Date.now()}.mp4`;
                                        link.target = '_blank';
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                    }}
                                    className="bg-black/50 backdrop-blur-sm rounded-full p-2 hover:bg-black/70 transition-all duration-200"
                                    title="下载视频"
                                >
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* 现代化图片模态框 */}
            {showImageModal && (
                <div
                    className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fadeIn"
                    onClick={() => setShowImageModal(false)}
                >
                    <div className="relative max-w-6xl max-h-[95vh] w-full h-full flex items-center justify-center">
                        <button
                            onClick={() => setShowImageModal(false)}
                            className="absolute top-6 right-6 w-12 h-12 bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center text-white transition-all duration-200 z-10 hover:scale-110"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                        <img
                            src={showImageModal}
                            alt="Generated image"
                            className="max-w-full max-h-full object-contain rounded-2xl shadow-2xl transition-transform duration-300 hover:scale-105"
                            onClick={(e) => e.stopPropagation()}
                        />
                    </div>
                </div>
            )}
        </div>
    );
}

export default MessageBubble;