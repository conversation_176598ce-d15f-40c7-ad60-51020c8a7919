import React, { useState } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';

// 会话项组件 - 从chat.js重构而来
const SessionItem = ({
    session,
    isActive,
    onClick,
    onDelete,
    onUpdateTitle
}) => {
    const { t } = useLanguage();
    const [isEditing, setIsEditing] = useState(false);
    const [editTitle, setEditTitle] = useState(session.title);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

    // 处理点击会话
    const handleClick = () => {
        if (!isEditing) {
            onClick(session.id);
        }
    };

    // 开始编辑标题
    const startEditing = (e) => {
        e.stopPropagation();
        setIsEditing(true);
        setEditTitle(session.title);
    };

    // 保存标题
    const saveTitle = async () => {
        if (editTitle.trim() && editTitle !== session.title) {
            const success = await onUpdateTitle(session.id, editTitle.trim());
            if (success) {
                setIsEditing(false);
            }
        } else {
            setIsEditing(false);
            setEditTitle(session.title);
        }
    };

    // 取消编辑
    const cancelEdit = () => {
        setIsEditing(false);
        setEditTitle(session.title);
    };

    // 处理键盘事件
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            saveTitle();
        } else if (e.key === 'Escape') {
            cancelEdit();
        }
    };

    // 处理删除确认
    const handleDelete = (e) => {
        e.stopPropagation();
        setShowDeleteConfirm(true);
    };

    // 确认删除
    const confirmDelete = async (e) => {
        e.stopPropagation();
        const success = await onDelete(session.id);
        if (success) {
            setShowDeleteConfirm(false);
        }
    };

    // 取消删除
    const cancelDelete = (e) => {
        e.stopPropagation();
        setShowDeleteConfirm(false);
    };

    // 格式化时间
    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffHours = diffMs / (1000 * 60 * 60);
        const diffDays = diffMs / (1000 * 60 * 60 * 24);

        if (diffHours < 1) {
            return t('time.minutes.ago', { minutes: Math.max(1, Math.floor(diffMs / (1000 * 60))) });
        } else if (diffHours < 24) {
            return t('time.hours.ago', { hours: Math.floor(diffHours) });
        } else if (diffDays < 7) {
            return t('time.days.ago', { days: Math.floor(diffDays) });
        } else {
            return date.toLocaleDateString();
        }
    };

    return (
        <div
            onClick={handleClick}
            className={`group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                isActive
                    ? 'bg-gradient-to-r from-purple-500/20 to-violet-500/20 border border-purple-400/30'
                    : 'hover:bg-white/5 border border-transparent'
            }`}
        >
            {/* 删除确认覆盖层 */}
            {showDeleteConfirm && (
                <div className="absolute inset-0 bg-red-500/20 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                    <div className="text-center">
                        <p className="text-white text-sm mb-2">{t('session.delete.confirm')}</p>
                        <div className="flex space-x-2">
                            <button
                                onClick={confirmDelete}
                                className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded transition-colors"
                            >
                                {t('common.delete')}
                            </button>
                            <button
                                onClick={cancelDelete}
                                className="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white text-xs rounded transition-colors"
                            >
                                {t('common.cancel')}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* 会话内容 */}
            <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                    {isEditing ? (
                        <input
                            type="text"
                            value={editTitle}
                            onChange={(e) => setEditTitle(e.target.value)}
                            onBlur={saveTitle}
                            onKeyDown={handleKeyDown}
                            className="w-full bg-white/10 text-white text-sm border border-white/20 rounded px-2 py-1 focus:outline-none focus:border-purple-400/50"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                        />
                    ) : (
                        <h3 className={`font-medium text-sm truncate ${
                            isActive ? 'text-white' : 'text-white/90'
                        }`}>
                            {session.title}
                        </h3>
                    )}
                    
                    {/* 会话信息 */}
                    <div className="flex items-center space-x-2 mt-1">
                        <span className={`text-xs ${
                            isActive ? 'text-white/70' : 'text-white/50'
                        }`}>
                            {formatTime(session.updated_at || session.created_at)}
                        </span>
                        
                        {session.message_count && (
                            <>
                                <span className="text-white/30">•</span>
                                <span className={`text-xs ${
                                    isActive ? 'text-white/70' : 'text-white/50'
                                }`}>
                                    {t('session.messages.count', { count: session.message_count })}
                                </span>
                            </>
                        )}
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {!isEditing && (
                        <>
                            <button
                                onClick={startEditing}
                                className="w-6 h-6 flex items-center justify-center text-white/50 hover:text-white/80 hover:bg-white/10 rounded transition-colors"
                                title={t('session.edit.title')}
                            >
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                            </button>
                            
                            <button
                                onClick={handleDelete}
                                className="w-6 h-6 flex items-center justify-center text-white/50 hover:text-red-400 hover:bg-red-500/10 rounded transition-colors"
                                title={t('session.delete')}
                            >
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <polyline points="3,6 5,6 21,6"></polyline>
                                    <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                            </button>
                        </>
                    )}
                </div>
            </div>

            {/* 活动指示器 */}
            {isActive && (
                <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-purple-400 to-violet-500 rounded-r"></div>
            )}
        </div>
    );
};

export default SessionItem;