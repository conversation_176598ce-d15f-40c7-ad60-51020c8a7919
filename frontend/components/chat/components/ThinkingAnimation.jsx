import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { messageUtils } from '../utils/messageUtils';

// 思考动画组件 - 从chat.js重构而来
const ThinkingAnimation = ({ 
    startTime, 
    estimatedTime = 30, 
    taskType = 'text_to_image' 
}) => {
    const { language } = useLanguage();
    const [elapsedTime, setElapsedTime] = useState(0);
    const [currentStage, setCurrentStage] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            const now = Date.now();
            const elapsed = Math.floor((now - (startTime || now)) / 1000);
            setElapsedTime(elapsed);

            // 使用messageUtils获取任务进度阶段
            const stage = messageUtils.getTaskProgressStage(elapsed, taskType);
            setCurrentStage(stage);
        }, 1000);

        return () => clearInterval(interval);
    }, [startTime, taskType]);

    // 获取阶段描述
    const getStageDescription = () => {
        const isVideoTask = messageUtils.isVideoTask(taskType);
        
        if (isVideoTask) {
            // 视频生成阶段描述
            switch (currentStage) {
                case 0: return language === 'zh' ? '初始分析中...' : 'Initial analysis...';
                case 1: return language === 'zh' ? '图像分析中...' : 'Analyzing images...';
                case 2: return language === 'zh' ? '开始视频生成...' : 'Starting video generation...';
                case 3: return language === 'zh' ? '视频渲染中...' : 'Rendering video...';
                case 4: return language === 'zh' ? '视频合成中...' : 'Compositing video...';
                case 5: return language === 'zh' ? '视频渲染最终阶段...' : 'Final video rendering...';
                default: return language === 'zh' ? '处理中...' : 'Processing...';
            }
        } else {
            // 普通生成任务阶段描述
            switch (currentStage) {
                case 0: return language === 'zh' ? '初始思考中...' : 'Initial thinking...';
                case 1: return language === 'zh' ? '深度分析中...' : 'Deep analysis...';
                case 2: return language === 'zh' ? '复杂处理中...' : 'Complex processing...';
                case 3: return language === 'zh' ? '最终生成中...' : 'Final generation...';
                default: return language === 'zh' ? '处理中...' : 'Processing...';
            }
        }
    };

    // 获取进度百分比
    const getProgressPercentage = () => {
        const isVideoTask = messageUtils.isVideoTask(taskType);
        const maxTime = isVideoTask ? 480 : 60; // 视频任务8分钟，普通任务1分钟
        return Math.min((elapsedTime / maxTime) * 100, 95); // 最多显示95%
    };

    // 格式化时间显示
    const formatTime = (seconds) => {
        if (seconds < 60) {
            return `${seconds}s`;
        } else {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
    };

    return (
        <div className="flex items-center space-x-3 p-4">
            {/* 旋转光环动画 */}
            <div className="relative">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-400 via-violet-500 to-blue-500 rounded-lg flex items-center justify-center shadow-lg">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" className="text-white">
                        <path d="M12 2L2 7V17L12 22L22 17V7L12 2Z" stroke="currentColor" strokeWidth="2" />
                    </svg>
                </div>
                {/* 外层旋转环 */}
                <div 
                    className="absolute -inset-1 rounded-lg border-2 border-purple-400/40 animate-spin" 
                    style={{ animationDuration: '2s' }}
                ></div>
                {/* 内层脉冲环 */}
                <div 
                    className="absolute -inset-0.5 rounded-lg border border-violet-300/30 animate-pulse"
                    style={{ animationDuration: '1.5s' }}
                ></div>
            </div>

            {/* 状态信息 */}
            <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                    <div className="text-white/90 text-sm font-medium">
                        {getStageDescription()}
                    </div>
                    <div className="text-white/60 text-xs">
                        {formatTime(elapsedTime)}
                    </div>
                </div>

                {/* 进度条 */}
                <div className="w-full bg-white/10 rounded-full h-1.5 overflow-hidden">
                    <div 
                        className="h-full bg-gradient-to-r from-purple-400 to-violet-500 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${getProgressPercentage()}%` }}
                    ></div>
                </div>

                {/* 额外信息 */}
                <div className="flex items-center justify-between mt-1">
                    <div className="text-white/50 text-xs">
                        {language === 'zh' ? '正在生成中' : 'Generating'}
                        {elapsedTime > 10 && (
                            <span className="ml-1">
                                {language === 'zh' ? '• 请耐心等待' : '• Please wait'}
                            </span>
                        )}
                    </div>
                    {estimatedTime && elapsedTime < estimatedTime && (
                        <div className="text-white/40 text-xs">
                            {language === 'zh' ? '预计' : 'ETA'} ~{estimatedTime}s
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ThinkingAnimation;