'use client'

import React from 'react';
import { useRouter } from 'next/router';
import ModernNavbar from './ModernNavbar';
import { useUser } from '../contexts/UserContext';
import { useToast } from '../components/Toast';

const Layout = ({
    children,
    showNavbar = true,
    navbarVariant = 'default',
    className = '',
    containerClassName = ''
}) => {
    const router = useRouter();
    const { user } = useUser();
    const toast = useToast();

    const handleUpgrade = () => {
        if (user) {
            router.push('/profile');
        } else {
            toast.info('Please log in first to upgrade membership', {
                action: {
                    label: 'Go to Login',
                    onClick: () => router.push('/auth/login')
                }
            });
        }
    };

    const noNavbarPages = ['/auth/login', '/auth/register'];
    const shouldShowNavbar = showNavbar && !noNavbarPages.includes(router.pathname);

    return (
        <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
            {shouldShowNavbar && (
                <ModernNavbar
                    variant={navbarVariant}
                    onUpgrade={handleUpgrade}
                />
            )}

            <main className={`${shouldShowNavbar ? 'pt-0' : ''} ${containerClassName}`}>
                {children}
            </main>

            {shouldShowNavbar && <Footer />}
        </div>
    );
};

const Footer = () => {
    return (
        <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div className="md:col-span-2">
                        <div className="flex items-center space-x-3 mb-4">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-lg flex items-center justify-center">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="text-white">
                                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" fillOpacity="0.8" />
                                    <circle cx="12" cy="12" r="3" fill="currentColor" fillOpacity="0.6" />
                                    <path d="M12 1V5M12 19V23M23 12H19M5 12H1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" opacity="0.4" />
                                </svg>
                            </div>
                            <span className="text-xl font-bold bg-gradient-to-r from-gray-900 via-blue-600 to-purple-600 dark:from-white dark:via-blue-400 dark:to-purple-400 text-transparent bg-clip-text">
                                MirageMakers AI
                            </span>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed max-w-md">
                            AI-driven multimodal creative platform that enables everyone to easily create stunning visual content.
                        </p>
                    </div>

                    <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Products</h3>
                        <ul className="space-y-2">
                            <li>
                                <a href="/creative" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-sm transition-colors">
                                    AI Studio
                                </a>
                            </li>
                            <li>
                                <a href="/gallery" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-sm transition-colors">
                                    Gallery
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Support</h3>
                        <ul className="space-y-2">
                            <li>
                                <a href="/help" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-sm transition-colors">
                                    Help Center
                                </a>
                            </li>
                            <li>
                                <a href="/contact" className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-sm transition-colors">
                                    Contact Us
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex flex-col sm:flex-row justify-between items-center">
                        <p className="text-gray-500 dark:text-gray-400 text-sm">
                            © 2025 MirageMakers AI. All rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Layout; 