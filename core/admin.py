from django.contrib import admin
from .models import User, ChatSession, ChatMessage, EmailVerification, PasswordReset

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ("email", "name", "is_email_verified", "tokens", "current_plan", "created_at")
    search_fields = ("email", "name")
    list_filter = ("is_email_verified", "current_plan", "created_at")
    readonly_fields = ("created_at", "updated_at", "last_login_at")

@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "title", "created_at")
    search_fields = ("title", "user__email")
    list_filter = ("created_at",)
    readonly_fields = ("created_at", "updated_at")

@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ("id", "session", "type", "content_preview", "created_at")
    list_filter = ("type", "created_at")
    search_fields = ("content",)
    readonly_fields = ("created_at",)
    
    def content_preview(self, obj):
        return obj.content[:50] + "..." if len(obj.content) > 50 else obj.content
    content_preview.short_description = "Content Preview"

@admin.register(EmailVerification)
class EmailVerificationAdmin(admin.ModelAdmin):
    list_display = ("user", "code", "is_used", "created_at", "expires_at")
    list_filter = ("is_used", "created_at")
    search_fields = ("user__email", "code")
    readonly_fields = ("created_at",)

@admin.register(PasswordReset)
class PasswordResetAdmin(admin.ModelAdmin):
    list_display = ("user", "code", "is_used", "created_at", "expires_at")
    list_filter = ("is_used", "created_at")
    search_fields = ("user__email", "code")
    readonly_fields = ("created_at",)