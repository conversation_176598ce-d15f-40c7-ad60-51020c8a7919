from django.db import transaction
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
import logging

from core.models import User, TokenConsumption, PaymentOrder, MembershipPlan

logger = logging.getLogger(__name__)

class TokenService:
    """Token management service"""
    
    # Token costs for different services
    TOKEN_COSTS = {
        'img_gen': 400,           # Text to Image (img_gen tool): 400 tokens (同义词)
        'img_edit': 400,          # Image editing: 400 tokens
        'video_gen': 800,         # Text to Video: 800 tokens
        'text_to_video': 800,     # Text to Video (text_to_video tool): 800 tokens (同义词)
        'img2video': 3000,        # Image to Video: 3000 tokens
        'multi_img_video': 3000,  # Multi-image to Video composition: 3000 tokens
        'video_keyframe': 0,      # Video keyframe extraction: free
        'chat_api': 2,            # Chat API: 2 tokens per conversation
    }
    
    @classmethod
    def get_token_cost(cls, service_type, **kwargs):
        """
        Calculate token cost for service
        Args:
            service_type: Service type (image_gen, img_edit, video_gen, img2video, chat_api)
            **kwargs: Additional parameters (for future extension)
        Returns:
            int: Token cost
        """
        return cls.TOKEN_COSTS.get(service_type, 0)
    
    @classmethod
    def calculate_plan_cost(cls, plan):
        """
        Calculate total token cost for a JobPlan or ExecutionPlan
        Args:
            plan: JobPlan object with nodes or ExecutionPlan with tool_calls
        Returns:
            int: Total token cost
        """
        total_cost = 0
        
        # Handle ExecutionPlan from Graph (has tool_calls)
        if hasattr(plan, 'tool_calls') and plan.tool_calls:
            for tool_call in plan.tool_calls:
                service_type = tool_call.tool_name
                cost = cls.get_token_cost(service_type)
                total_cost += cost
        # Handle legacy JobPlan (has nodes)
        elif hasattr(plan, 'nodes'):
            for node in plan.nodes:
                service_type = node.tool
                cost = cls.get_token_cost(service_type)
                total_cost += cost
        
        return total_cost
    
    @classmethod
    def check_balance(cls, user, service_type, **kwargs):
        """
        Check if user has sufficient token balance
        Args:
            user: User object
            service_type: Service type
            **kwargs: Additional parameters
        Returns:
            tuple: (is_sufficient: bool, required_tokens: int, error_message: str)
        """
        if not user or not user.is_authenticated:
            return False, 0, "User not authenticated"
            
        # Check if tokens are expired
        if user.is_tokens_expired:
            user.tokens = 0
            user.save()
        
        required_tokens = cls.get_token_cost(service_type, **kwargs)
        
        # Only video keyframe extraction is truly free
        if required_tokens == 0:
            return True, 0, ""
            
        if user.tokens < required_tokens:
            return False, required_tokens, f"Insufficient token balance. Required {required_tokens} tokens, current balance {user.tokens} tokens"
        
        return True, required_tokens, ""
    
    @classmethod
    def check_plan_balance(cls, user, plan):
        """
        Check if user has sufficient token balance for entire plan
        Args:
            user: User object
            plan: JobPlan object
        Returns:
            tuple: (is_sufficient: bool, required_tokens: int, error_message: str)
        """
        if not user or not user.is_authenticated:
            return False, 0, "User not authenticated"
            
        # Check if tokens are expired
        if user.is_tokens_expired:
            user.tokens = 0
            user.save()
        
        total_cost = cls.calculate_plan_cost(plan)
        
        # Free services
        if total_cost == 0:
            return True, 0, ""
            
        if user.tokens < total_cost:
            return False, total_cost, f"Insufficient token balance. Required {total_cost} tokens, current balance {user.tokens} tokens"
        
        return True, total_cost, ""
    
    @classmethod
    @transaction.atomic
    def consume_tokens_after_execution(cls, user, plan, success_results=None, failed_nodes=None, session=None):
        """
        Consume tokens after successful plan execution (simplified approach)
        Args:
            user: User object
            plan: JobPlan object  
            success_results: List of successful execution results
            failed_nodes: List of failed node information (unused in this approach)
            session: ChatSession object (optional)
        Returns:
            tuple: (success: bool, total_consumed: int, error_message: str, consumption_records: list)
        """
        try:
            if not user or not user.is_authenticated:
                return False, 0, "User not authenticated", []
            
            # Check if tokens are expired
            if user.is_tokens_expired:
                user.tokens = 0
                user.save()
            
            total_consumed = 0
            consumption_records = []
            
            # Only consume tokens for successful results
            if success_results:
                # 检测是否为多图合成视频任务
                is_multi_img_video = cls._detect_multi_img_video_task(success_results, session)
                
                if is_multi_img_video:
                    # 多图合成视频：统一按740代币计费
                    service_type = 'multi_img_video'
                    node_cost = cls.get_token_cost(service_type)
                    
                    # Check if user has enough tokens
                    if user.tokens < node_cost:
                        logger.warning(f"Insufficient tokens for {service_type}: required {node_cost}, available {user.tokens}")
                        return False, 0, f"Insufficient tokens for multi-image video generation: required {node_cost}, available {user.tokens}", []
                    
                    # Consume tokens for multi-image video generation
                    user.tokens -= node_cost
                    total_consumed += node_cost
                    
                    # Create single consumption record for multi-image video
                    prompt = cls._extract_prompt_from_results(success_results)
                    result_url = cls._extract_final_result_url(success_results)
                    
                    consumption = TokenConsumption.objects.create(
                        user=user,
                        session=session,
                        service_type=service_type,
                        tokens_used=node_cost,
                        prompt=prompt[:500],
                        result_url=result_url,
                        metadata={
                            'status': 'COMPLETED',
                            'execution_results': success_results,
                            'consumed_at': timezone.now().isoformat(),
                            'multi_img_video_composition': True,
                            'component_costs': {
                                'img_edit': 40,
                                'img2video': 700,
                                'total': node_cost
                            }
                        }
                    )
                    consumption_records.append(consumption)
                    
                    logger.info(f"Multi-image video generation: consumed {node_cost} tokens for user {user.email}")
                    
                else:
                    # 普通任务：按照原有逻辑分别计费
                    for result in success_results:
                        node_type = result.get('node_type', 'unknown')
                        node_cost = cls.get_token_cost(node_type)
                        
                        # Create consumption record for all services (including free ones)
                        consumption = TokenConsumption.objects.create(
                            user=user,
                            session=session,
                            service_type=node_type,
                            tokens_used=node_cost,
                            prompt=result.get('prompt', '')[:500],
                            result_url=result.get('result_url', ''),
                            metadata={
                                'status': 'COMPLETED',
                                'execution_result': result,
                                'consumed_at': timezone.now().isoformat(),
                                'direct_consumption': True,
                                'is_free_service': node_cost == 0,
                            }
                        )
                        consumption_records.append(consumption)
                        
                        # Only deduct tokens for non-free services
                        if node_cost > 0:
                            # Check if user has enough tokens for this specific service
                            if user.tokens < node_cost:
                                logger.warning(f"Insufficient tokens for {node_type}: required {node_cost}, available {user.tokens}")
                                # Update the consumption record to reflect insufficient tokens
                                consumption.metadata.update({
                                    'status': 'INSUFFICIENT_TOKENS',
                                    'error': f"Insufficient tokens: required {node_cost}, available {user.tokens}"
                                })
                                consumption.save()
                                continue  # Skip this service if insufficient tokens
                            
                            # Consume tokens
                            user.tokens -= node_cost
                            total_consumed += node_cost
                        else:
                            logger.info(f"Free service used: {node_type} for user {user.email}")
                
                # Save user changes
                if total_consumed > 0:
                    user.total_generations += len([r for r in consumption_records if r.tokens_used > 0])
                    user.save()
                    
                    logger.info(f"User {user.email} consumed {total_consumed} tokens for {len(consumption_records)} services")
                elif consumption_records:
                    # Even if no tokens consumed, update generation count for free services
                    user.total_generations += len(consumption_records)
                    user.save()
                    logger.info(f"User {user.email} used {len(consumption_records)} free services")
            
            return True, total_consumed, "", consumption_records
            
        except Exception as e:
            error_msg = f"Token consumption after execution failed: {str(e)}"
            logger.error(f"{error_msg} - User: {user.email}")
            return False, 0, error_msg, []
    
    @classmethod
    def _detect_multi_img_video_task(cls, success_results, session=None):
        """
        检测是否为多图合成视频任务
        判断逻辑：
        1. 包含img_edit和img2video两种工具
        2. 或者session中有多个图片附件且最终生成视频
        """
        if not success_results:
            return False
        
        # 检查工具类型组合
        tool_types = set()
        has_video_result = False
        
        for result in success_results:
            node_type = result.get('node_type', '')
            tool_types.add(node_type)
            
            # 检查是否有视频结果
            result_url = result.get('result_url', '') or ''
            if result_url and any(ext in result_url.lower() for ext in ['.mp4', '.mov', '.avi', 'video']):
                has_video_result = True
        
        # 方式1：包含图片编辑和图生视频工具
        if 'img_edit' in tool_types and 'img2video' in tool_types:
            return True
        
        # 方式2：检查session中的附件数量（如果有session信息）
        if session and has_video_result:
            try:
                # 获取最近的用户消息
                latest_user_message = session.messages.filter(type='user').order_by('-created_at').first()
                if latest_user_message:
                    attachments = latest_user_message.metadata.get('attachments', [])
                    # 如果有多个图片附件且最终生成视频，判断为多图合成
                    if len(attachments) > 1 and has_video_result:
                        return True
            except Exception as e:
                logger.warning(f"Error checking session attachments: {e}")
        
        return False
    
    @classmethod
    def _extract_prompt_from_results(cls, success_results):
        """从结果中提取用户提示"""
        for result in success_results:
            prompt = result.get('prompt', '')
            if prompt:
                return prompt
        return "Multi-image video generation"
    
    @classmethod
    def _extract_final_result_url(cls, success_results):
        """提取最终结果URL（优先视频）"""
        video_url = None
        image_url = None
        
        for result in success_results:
            result_url = result.get('result_url', '') or ''
            if result_url and any(ext in result_url.lower() for ext in ['.mp4', '.mov', '.avi', 'video']):
                video_url = result_url
            elif result_url and any(ext in result_url.lower() for ext in ['.jpg', '.png', '.jpeg', 'image']):
                image_url = result_url
        
        return video_url or image_url or ""
    
    @classmethod
    @transaction.atomic
    def consume_tokens(cls, user, service_type, session=None, prompt="", result_url="", metadata=None, **kwargs):
        """
        Consume tokens and record usage
        Args:
            user: User object
            service_type: Service type
            session: ChatSession object (optional)
            prompt: User prompt
            result_url: Generated result URL
            metadata: Additional metadata
            **kwargs: Additional parameters
        Returns:
            tuple: (success: bool, consumption_record: TokenConsumption or None, error_message: str)
        """
        try:
            # Check balance for non-free services
            required_tokens = cls.get_token_cost(service_type, **kwargs)
            
            # For non-free services, check if user has sufficient balance
            if required_tokens > 0:
                is_sufficient, _, error_msg = cls.check_balance(user, service_type, **kwargs)
            if not is_sufficient:
                logger.warning(f"User {user.email} insufficient tokens: {error_msg}")
                return False, None, error_msg
            
            # Prepare metadata
            consumption_metadata = metadata or {}
            consumption_metadata.update({
                'service_cost': required_tokens,
                'balance_before': user.tokens,
                'consumed_at': timezone.now().isoformat(),
                'is_free_service': required_tokens == 0,
            })
            
            # Create consumption record for all services (including free ones)
            consumption = TokenConsumption.objects.create(
                user=user,
                session=session,
                service_type=service_type,
                tokens_used=required_tokens,
                prompt=prompt[:500],  # Limit length to prevent database overflow
                result_url=result_url or "",
                metadata=consumption_metadata
            )
            
            # Deduct tokens and update generation count only for non-free services
            if required_tokens > 0:
                user.tokens -= required_tokens
                user.total_generations += 1
                user.save()
                
                logger.info(f"User {user.email} consumed {required_tokens} tokens for {service_type}, balance: {user.tokens}")
            else:
                # For free services, still update generation count
                user.total_generations += 1
                user.save()
                
                logger.info(f"User {user.email} used free service {service_type}, balance unchanged: {user.tokens}")
            
            return True, consumption, ""
            
        except Exception as e:
            error_msg = f"Token consumption failed: {str(e)}"
            logger.error(f"{error_msg} - User: {user.email}, Service: {service_type}")
            return False, None, error_msg
    
    @classmethod
    @transaction.atomic
    def add_tokens_from_payment(cls, user, order):
        """
        Add tokens to user account from payment order with proper expiry management
        Args:
            user: User object
            order: PaymentOrder object
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            from django.utils import timezone
            from datetime import timedelta
            
            # Use tokens_to_add from order
            tokens_to_add = order.tokens_to_add
            now = timezone.now()
            
            # Clear existing tokens first (new purchase replaces old tokens)
            user.tokens = 0
            
            # Set plan information
            user.current_plan = order.plan_type
            user.plan_purchased_at = now
            user.tokens_purchased_at = now
            
            if order.plan_type == 'ANNUAL':
                # Annual plan: Special handling
                # 1. Set tokens expiry to 30 days from purchase
                user.tokens_expires_at = now + timedelta(days=30)
                # 2. Set next renewal to 1 month from purchase (for monthly token distribution)
                user.next_renewal_at = now + timedelta(days=30)
                # 3. Give first month's tokens (240,000 / 12 = 20,000)
                monthly_tokens = 20000
                user.tokens = monthly_tokens
                
                logger.info(f"Annual plan: User {user.email} received {monthly_tokens} tokens for first month, renews monthly")
                message = f"Successfully activated Annual plan: {monthly_tokens} tokens for first month (renews monthly)"
                
            else:
                # TRIAL, BASIC, PREMIUM: Give all tokens, expire in 30 days from purchase
                user.tokens = tokens_to_add
                user.tokens_expires_at = now + timedelta(days=30)
                user.next_renewal_at = None  # No auto-renewal for non-annual plans
                
                logger.info(f"Standard plan: User {user.email} received {tokens_to_add} tokens, expires in 30 days")
                message = f"Successfully added {tokens_to_add} tokens (expires: {user.tokens_expires_at.strftime('%Y-%m-%d')})"
            
            user.save()
            return True, message
                
        except Exception as e:
            error_msg = f"Token addition failed: {user.email}, Order: {order.id}, Error: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    @classmethod
    @transaction.atomic
    def add_tokens_from_dashboard(cls, user, tokens_to_add, reason="Dashboard addition"):
        """
        Add tokens to user account from dashboard (admin function)
        Args:
            user: User object
            tokens_to_add: Number of tokens to add
            reason: Reason for token addition
        """
        try:
            # Update user tokens
            user.tokens += tokens_to_add
            user.save()
            
            logger.info(f"Admin added {tokens_to_add} tokens to user {user.email}, reason: {reason}, new balance: {user.tokens}")
            
        except Exception as e:
            logger.error(f"Dashboard token addition failed: {user.email}, Tokens: {tokens_to_add}, Error: {e}")
            raise
    
    @classmethod
    def get_user_usage_stats(cls, user, days=30):
        """
        Get user usage statistics
        Args:
            user: User object  
            days: Number of days to analyze
        Returns:
            dict: Usage statistics data
        """
        from django.db.models import Sum
        from django.utils import timezone
        
        # Calculate time range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Query consumption records
        consumptions = TokenConsumption.objects.filter(
            user=user,
            created_at__gte=start_date
        )
        
        # Statistics
        total_used = consumptions.aggregate(Sum('tokens_used'))['tokens_used__sum'] or 0
        total_requests = consumptions.count()
        
        # Statistics by service type
        by_service = {}
        for service_type, service_name in TokenConsumption.SERVICE_CHOICES:
            service_consumptions = consumptions.filter(service_type=service_type)
            by_service[service_type] = {
                'name': service_name,
                'tokens_used': service_consumptions.aggregate(Sum('tokens_used'))['tokens_used__sum'] or 0,
                'count': service_consumptions.count()
            }
        
        # Daily statistics (last 7 days)
        daily_usage = []
        for i in range(7):
            day_start = (end_date - timedelta(days=i)).replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            day_consumptions = consumptions.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            )
            
            day_tokens = day_consumptions.aggregate(Sum('tokens_used'))['tokens_used__sum'] or 0
            day_requests = day_consumptions.count()
            
            daily_usage.append({
                'date': day_start.strftime('%m-%d'),
                'tokens_used': day_tokens,
                'requests_count': day_requests
            })
        
        # Recent usage records
        recent_usage = consumptions.order_by('-created_at')[:10]
        recent_records = []
        for record in recent_usage:
            recent_records.append({
                'service_type': record.service_type,
                'service_name': record.get_service_type_display(),
                'tokens_used': record.tokens_used,
                'created_at': record.created_at.strftime('%Y-%m-%d %H:%M'),
                'prompt': record.prompt[:100] + '...' if len(record.prompt) > 100 else record.prompt,
                'result_url': record.result_url
            })
        
        return {
            'period_days': days,
            'total_used_30days': total_used,
            'total_requests': total_requests,
            'by_service': by_service,
            'daily_usage': daily_usage[::-1],  # Reverse to chronological order
            'recent_records': recent_records,
            'current_balance': user.tokens,
            'low_balance_warning': user.tokens < 100,  # Warning when below 100 tokens
        } 