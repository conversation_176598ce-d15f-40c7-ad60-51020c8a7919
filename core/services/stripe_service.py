import stripe
import logging
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
from datetime import datetime
from typing import Dict, Any, Optional

from core.models import User, PaymentOrder, StripeSubscription, MembershipPlan
from .token_service import TokenService
from .email_service import EmailService

logger = logging.getLogger(__name__)

class StripeService:
    """Stripe payment service - optimized for token purchases"""
    
    def __init__(self):
        # Initialize Stripe
        stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', '')
        self.publishable_key = getattr(settings, 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', '')
        self.webhook_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')
        self.success_url = getattr(settings, 'STRIPE_SUCCESS_URL', 'https://miragemakers.ai/payment/success')
        self.cancel_url = getattr(settings, 'STRIPE_CANCEL_URL', 'https://miragemakers.ai/payment/cancel')
        
        # Plan price mapping (requires corresponding Prices in Stripe Dashboard)
        self.price_mapping = {
            'TRIAL': {
                'price_id': getattr(settings, 'STRIPE_TRIAL_PRICE_ID', ''),
                'amount': Decimal('6.99'),
                'tokens': 8000,
                'type': 'ONE_TIME'
            },
            'BASIC': {
                'price_id': getattr(settings, 'STRIPE_BASIC_PRICE_ID', ''),
                'amount': Decimal('19.99'),
                'tokens': 20000,
                'type': 'SUBSCRIPTION'
            },
            'PREMIUM': {
                'price_id': getattr(settings, 'STRIPE_PREMIUM_PRICE_ID', ''),
                'amount': Decimal('59.99'),
                'tokens': 66000,
                'type': 'SUBSCRIPTION'
            },
            'ANNUAL': {
                'price_id': getattr(settings, 'STRIPE_ANNUAL_PRICE_ID', ''),
                'amount': Decimal('199.99'),
                'tokens': 240000,
                'type': 'SUBSCRIPTION'
            }
        }
        
        if not stripe.api_key:
            logger.warning("Stripe configuration incomplete, please set STRIPE_SECRET_KEY in settings")
    
    def get_or_create_customer(self, user: User, email: str = None, name: str = None) -> str:
        """Get or create Stripe customer"""
        try:
            # Check if user already has a Stripe customer ID
            existing_order = PaymentOrder.objects.filter(
                user=user,
                stripe_customer_id__isnull=False
            ).first()
            
            if existing_order and existing_order.stripe_customer_id:
                try:
                    customer = stripe.Customer.retrieve(existing_order.stripe_customer_id)
                    return customer.id
                except stripe.error.InvalidRequestError:
                    pass
            
            # Create new customer
            customer_data = {
                'email': email or user.email,
                'name': name or user.name,
                'metadata': {
                    'user_id': str(user.id),
                    'user_email': user.email
                }
            }
            
            customer = stripe.Customer.create(**customer_data)
            logger.info(f"Created Stripe customer {customer.id} for user {user.email}")
            return customer.id
            
        except Exception as e:
            logger.error(f"Error creating Stripe customer for user {user.email}: {e}")
            raise
    
    def create_checkout_session(self, user: User, plan_type: str) -> Dict[str, Any]:
        """Create Stripe Checkout session"""
        try:
            if plan_type not in self.price_mapping:
                return {'success': False, 'error': f'Invalid plan type: {plan_type}'}
            
            plan_info = self.price_mapping[plan_type]
            price_id = plan_info['price_id']
            
            if not price_id:
                return {'success': False, 'error': f'Price ID not configured for {plan_type}'}
            
            # Get or create Stripe customer
            customer_id = self.get_or_create_customer(user)
            
            # Create local order record
            payment_order = PaymentOrder.objects.create(
                user=user,
                plan_type=plan_type,
                amount=plan_info['amount'],
                tokens_to_add=plan_info['tokens'],
                payment_type=plan_info['type'],
                stripe_customer_id=customer_id
            )
            
            # Prepare Checkout session parameters
            session_params = {
                'customer': customer_id,
                'line_items': [{
                    'price': price_id,
                    'quantity': 1,
                }],
                'success_url': f"{self.success_url}?session_id={{CHECKOUT_SESSION_ID}}",
                'cancel_url': self.cancel_url,
                'metadata': {
                    'user_id': str(user.id),
                    'plan_type': plan_type,
                    'order_id': str(payment_order.id)
                },
                'customer_update': {
                    'address': 'auto',
                    'name': 'auto'
                },
                'automatic_tax': {'enabled': True},
            }
            
            # Set mode based on payment type
            if plan_info['type'] == 'SUBSCRIPTION':
                session_params['mode'] = 'subscription'
                session_params['billing_address_collection'] = 'required'
                session_params['subscription_data'] = {
                    'trial_period_days': 7,  # 7-day trial
                    'metadata': {
                        'plan_type': plan_type,
                        'user_id': str(user.id)
                    }
                }
            else:
                session_params['mode'] = 'payment'
                session_params['payment_intent_data'] = {
                    'metadata': {
                        'plan_type': plan_type,
                        'user_id': str(user.id),
                        'order_id': str(payment_order.id)
                    }
                }
            
            # Create Checkout session
            session = stripe.checkout.Session.create(**session_params)
            
            # Update order record
            payment_order.stripe_session_id = session.id
            payment_order.save()
            
            logger.info(f"Created Stripe checkout session {session.id} for user {user.email}, plan {plan_type}")
            
            return {
                'success': True,
                'session_id': session.id,
                'url': session.url,
                'order_id': payment_order.id
            }
            
        except Exception as e:
            logger.error(f"Error creating Stripe checkout session: {e}")
            return {'success': False, 'error': str(e)}
    
    def handle_successful_payment(self, session_id: str) -> Dict[str, Any]:
        """Handle successful payment from Stripe"""
        try:
            session = stripe.checkout.Session.retrieve(session_id)
            
            if session.payment_status != 'paid':
                return {'success': False, 'error': 'Payment not completed'}
            
            user_id = session.metadata.get('user_id')
            plan_type = session.metadata.get('plan_type')
            order_id = session.metadata.get('order_id')
            
            if not user_id or not plan_type:
                return {'success': False, 'error': 'Missing metadata'}
            
            user = User.objects.get(id=user_id)
            
            # Get or create payment order
            if order_id:
                try:
                    payment_order = PaymentOrder.objects.get(id=order_id)
                except PaymentOrder.DoesNotExist:
                    payment_order = None
            else:
                payment_order = None
            
            if session.mode == 'subscription':
                return self._handle_subscription_payment(session, user, plan_type, payment_order)
            else:
                return self._handle_one_time_payment(session, user, plan_type, payment_order)
                
        except Exception as e:
            logger.error(f"Error handling successful payment: {e}")
            return {'success': False, 'error': str(e)}
    
    def _handle_subscription_payment(self, session, user: User, plan_type: str, payment_order: Optional[PaymentOrder]) -> Dict[str, Any]:
        """Handle subscription payment"""
        try:
            subscription = stripe.Subscription.retrieve(session.subscription)
            
            # Create or update StripeSubscription record
            stripe_sub, created = StripeSubscription.objects.get_or_create(
                stripe_subscription_id=subscription.id,
                defaults={
                    'user': user,
                    'stripe_customer_id': subscription.customer,
                    'plan_type': plan_type,
                    'status': subscription.status,
                    'current_period_start': datetime.fromtimestamp(subscription.current_period_start, tz=timezone.utc),
                    'current_period_end': datetime.fromtimestamp(subscription.current_period_end, tz=timezone.utc),
                    'amount': Decimal(str(subscription.items.data[0].price.unit_amount / 100)),
                    'currency': subscription.currency.upper()
                }
            )
            
            if not created:
                # Update existing subscription
                stripe_sub.status = subscription.status
                stripe_sub.current_period_start = datetime.fromtimestamp(subscription.current_period_start, tz=timezone.utc)
                stripe_sub.current_period_end = datetime.fromtimestamp(subscription.current_period_end, tz=timezone.utc)
                stripe_sub.save()
            
            # Add tokens for subscription
            plan_info = self.price_mapping.get(plan_type, {})
            tokens_to_add = plan_info.get('tokens', 0)
            
            if tokens_to_add > 0:
                # Create a temporary payment order for token addition if none exists
                if not payment_order:
                    payment_order = PaymentOrder(
                        user=user,
                        plan_type=plan_type,
                        amount=plan_info.get('amount', Decimal('0')),
                        tokens_to_add=tokens_to_add,
                        payment_type='SUBSCRIPTION',
                        stripe_customer_id=subscription.customer,
                        stripe_subscription_id=subscription.id,
                        status='COMPLETED'
                    )
                    payment_order.save()
                
                success, message = TokenService.add_tokens_from_payment(user, payment_order)
                if not success:
                    logger.error(f"Failed to add tokens for subscription: {message}")
            
            # Update payment order if exists
            if payment_order:
                payment_order.status = 'COMPLETED'
                payment_order.stripe_subscription_id = subscription.id
                payment_order.completed_at = timezone.now()
                payment_order.save()
            
            # Send success email
            try:
                from .email_service import send_payment_success_notification
                send_payment_success_notification(user, plan_type, tokens_to_add)
            except Exception as e:
                logger.error(f"Failed to send payment success email: {e}")
            
            logger.info(f"Successfully processed subscription payment for user {user.email}")
            return {'success': True, 'subscription_id': subscription.id}
            
        except Exception as e:
            logger.error(f"Error handling subscription payment: {e}")
            return {'success': False, 'error': str(e)}
    
    def _handle_one_time_payment(self, session, user: User, plan_type: str, payment_order: Optional[PaymentOrder]) -> Dict[str, Any]:
        """Handle one-time payment"""
        try:
            payment_intent = stripe.PaymentIntent.retrieve(session.payment_intent)
            
            # Create payment order if not exists
            if not payment_order:
                plan_info = self.price_mapping.get(plan_type, {})
                payment_order = PaymentOrder.objects.create(
                    user=user,
                    plan_type=plan_type,
                    amount=plan_info.get('amount', Decimal('0')),
                    tokens_to_add=plan_info.get('tokens', 0),
                    payment_type='ONE_TIME',
                    stripe_customer_id=session.customer,
                    stripe_session_id=session.id
                )
            
            # Update payment order
            payment_order.stripe_payment_intent_id = payment_intent.id
            payment_order.status = 'COMPLETED'
            payment_order.completed_at = timezone.now()
            payment_order.save()
            
            # Add tokens
            success, message = TokenService.add_tokens_from_payment(user, payment_order)
            if not success:
                logger.error(f"Failed to add tokens for one-time payment: {message}")
                return {'success': False, 'error': message}
            
            # Send success email
            try:
                EmailService.send_payment_success_notification(user, plan_type, payment_order.tokens_to_add)
            except Exception as e:
                logger.error(f"Failed to send payment success email: {e}")
            
            logger.info(f"Successfully processed one-time payment for user {user.email}")
            return {'success': True, 'payment_intent_id': payment_intent.id}
            
        except Exception as e:
            logger.error(f"Error handling one-time payment: {e}")
            return {'success': False, 'error': str(e)}
    
    def cancel_subscription(self, user: User, subscription_id: str) -> Dict[str, Any]:
        """Cancel subscription"""
        try:
            subscription = stripe.Subscription.modify(
                subscription_id,
                cancel_at_period_end=True
            )
            
            # Update local record
            try:
                stripe_sub = StripeSubscription.objects.get(stripe_subscription_id=subscription_id, user=user)
                stripe_sub.canceled_at = timezone.now()
                stripe_sub.status = subscription.status
                stripe_sub.save()
            except StripeSubscription.DoesNotExist:
                logger.warning(f"StripeSubscription not found for {subscription_id}")
            
            logger.info(f"Canceled subscription {subscription_id} for user {user.email}")
            return {'success': True, 'subscription': subscription}
            
        except Exception as e:
            logger.error(f"Error canceling subscription: {e}")
            return {'success': False, 'error': str(e)}
    
    def create_billing_portal_session(self, user: User) -> Dict[str, Any]:
        """Create billing portal session"""
        try:
            # Get customer ID
            customer_id = self.get_or_create_customer(user)
            
            session = stripe.billing_portal.Session.create(
                customer=customer_id,
                return_url=self.success_url,
            )
            
            return {'success': True, 'url': session.url}
            
        except Exception as e:
            logger.error(f"Error creating billing portal session: {e}")
            return {'success': False, 'error': str(e)}
    
    def create_payment_intent(self, user: User, plan_type: str) -> Dict[str, Any]:
        """Create Stripe Payment Intent for local payment form"""
        try:
            if plan_type not in self.price_mapping:
                return {'success': False, 'error': f'Invalid plan type: {plan_type}'}
            
            plan_info = self.price_mapping[plan_type]
            amount_cents = int(plan_info['amount'] * 100)  # Convert to cents
            
            # Get or create Stripe customer
            customer_id = self.get_or_create_customer(user)
            
            # Create local order record
            payment_order = PaymentOrder.objects.create(
                user=user,
                plan_type=plan_type,
                amount=plan_info['amount'],
                tokens_to_add=plan_info['tokens'],
                payment_type='ONE_TIME',  # Payment Intent is for one-time payments
                stripe_customer_id=customer_id
            )
            
            # Create Payment Intent
            payment_intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                customer=customer_id,
                metadata={
                    'user_id': str(user.id),
                    'plan_type': plan_type,
                    'order_id': str(payment_order.id)
                },
                automatic_payment_methods={
                    'enabled': True,
                },
                description=f"MirageMakers AI - {plan_info['tokens']} tokens ({plan_type} Plan)"
            )
            
            # Update order record
            payment_order.stripe_payment_intent_id = payment_intent.id
            payment_order.save()
            
            logger.info(f"Created Payment Intent {payment_intent.id} for user {user.email}, plan {plan_type}")
            
            return {
                'success': True,
                'client_secret': payment_intent.client_secret,
                'payment_intent_id': payment_intent.id,
                'order_id': payment_order.id,
                'amount': float(plan_info['amount']),
                'tokens': plan_info['tokens']
            }
            
        except Exception as e:
            logger.error(f"Error creating Payment Intent: {e}")
            return {'success': False, 'error': str(e)}
    
    def confirm_payment_intent(self, user: User, payment_intent_id: str) -> Dict[str, Any]:
        """Confirm payment intent and add tokens to user account"""
        try:
            # Retrieve Payment Intent
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            if payment_intent.status != 'succeeded':
                return {'success': False, 'error': f'Payment not completed. Status: {payment_intent.status}'}
            
            user_id = payment_intent.metadata.get('user_id')
            plan_type = payment_intent.metadata.get('plan_type')
            order_id = payment_intent.metadata.get('order_id')
            
            if not user_id or not plan_type:
                return {'success': False, 'error': 'Missing metadata in payment intent'}
            
            if str(user.id) != user_id:
                return {'success': False, 'error': 'Payment intent does not belong to this user'}
            
            # Get payment order
            try:
                payment_order = PaymentOrder.objects.get(id=order_id, user=user)
            except PaymentOrder.DoesNotExist:
                return {'success': False, 'error': 'Payment order not found'}
            
            # Check if already processed
            if payment_order.status == 'COMPLETED':
                return {
                    'success': True,
                    'message': 'Payment already processed',
                    'tokens_added': payment_order.tokens_to_add,
                    'plan_type': plan_type
                }
            
            # Update payment order
            payment_order.status = 'COMPLETED'
            payment_order.completed_at = timezone.now()
            payment_order.save()
            
            # Add tokens to user account
            success, message = TokenService.add_tokens_from_payment(user, payment_order)
            if not success:
                logger.error(f"Failed to add tokens for payment intent {payment_intent_id}: {message}")
                return {'success': False, 'error': message}
            
            # Send success email
            try:
                EmailService.send_payment_success_notification(user, plan_type, payment_order.tokens_to_add)
            except Exception as e:
                logger.error(f"Failed to send payment success email: {e}")
            
            logger.info(f"Successfully processed Payment Intent {payment_intent_id} for user {user.email}")
            
            return {
                'success': True,
                'tokens_added': payment_order.tokens_to_add,
                'plan_type': plan_type,
                'payment_intent_id': payment_intent_id,
                'message': f'Payment successful! {payment_order.tokens_to_add} tokens have been added to your account.'
            }
            
        except Exception as e:
            logger.error(f"Error confirming payment intent: {e}")
            return {'success': False, 'error': str(e)}