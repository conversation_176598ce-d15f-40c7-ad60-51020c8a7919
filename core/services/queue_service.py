"""
队列路由服务 - 单队列架构
简化架构，所有任务统一使用default队列，提高并发数来保证处理效率
"""

import logging
import redis
import psutil
from typing import Dict, Any, Optional
from celery import Celery
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

class QueueFullError(Exception):
    """队列已满异常"""
    pass

class SystemOverloadError(Exception):
    """系统过载异常"""
    pass

class QueueService:
    """
    单队列服务 - 简化的任务路由和队列管理
    所有任务统一使用default队列，通过增加并发数来保证效率
    """
    
    # 统一队列配置
    DEFAULT_QUEUE = 'default'
    MAX_QUEUE_LENGTH = 200  # 单队列最大长度
    
    @classmethod
    def send_task_to_queue(cls, task_name: str, task_type: str, *args, **kwargs) -> Any:
        """
        发送任务到统一队列
        
        Args:
            task_name: 任务名称
            task_type: 任务类型（保留用于日志记录）
            *args: 任务参数
            **kwargs: 任务关键字参数
            
        Returns:
            Celery AsyncResult对象
        """
        
        # 检查系统资源
        system_status = cls.check_system_resources()
        if system_status.get('memory_critical', False):
            logger.error("系统内存严重不足，拒绝新任务")
            raise SystemOverloadError("系统资源不足，请稍后重试")
        
        # 检查队列长度
        try:
            r = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=0
            )
            
            queue_length = r.llen(cls.DEFAULT_QUEUE)
            if queue_length >= cls.MAX_QUEUE_LENGTH:
                logger.error(f"队列已满: {queue_length}/{cls.MAX_QUEUE_LENGTH}")
                raise QueueFullError("系统繁忙，请稍后重试")
            
        except redis.ConnectionError:
            logger.warning("无法连接Redis检查队列长度，继续提交任务")
        
        try:
            # 从celery_app导入Celery实例
            from app.celery_app import app
            
            # 发送任务到统一队列
            result = app.send_task(
                task_name,
                args=args,
                kwargs=kwargs,
                queue=cls.DEFAULT_QUEUE,
                # 在任务元数据中保留任务类型信息，方便调试
                headers={'task_type': task_type}
            )
            
            logger.info(f"✅ 任务 {task_name} ({task_type}) 已发送到队列 {cls.DEFAULT_QUEUE} (ID: {result.id})")
            return result
            
        except Exception as e:
            logger.error(f"发送任务到队列失败: {e}")
            # Redis连接异常时的fallback处理
            if 'redis' in str(e).lower() or 'connection' in str(e).lower():
                logger.error("Redis连接异常，任务发送失败")
                raise SystemOverloadError("系统暂时不可用，请稍后重试")
            raise
    
    @classmethod
    def get_queue_status(cls) -> Dict[str, Any]:
        """获取队列的状态信息"""
        try:
            from app.celery_app import app
            inspect = app.control.inspect()
            
            # 获取活跃队列
            active_queues = inspect.active_queues()
            
            # 获取队列长度
            queue_length = 0
            try:
                r = redis.Redis(
                    host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                    port=getattr(settings, 'REDIS_PORT', 6379),
                    db=0
                )
                queue_length = r.llen(cls.DEFAULT_QUEUE)
            except Exception as e:
                logger.warning(f"无法获取队列长度: {e}")
            
            return {
                'active_queues': active_queues,
                'queue_name': cls.DEFAULT_QUEUE,
                'queue_length': queue_length,
                'max_length': cls.MAX_QUEUE_LENGTH,
                'usage_percent': (queue_length / cls.MAX_QUEUE_LENGTH) * 100 if cls.MAX_QUEUE_LENGTH > 0 else 0
            }
        except Exception as e:
            logger.error(f"获取队列状态失败: {e}")
            return {'error': str(e)}
    
    @classmethod
    def estimate_wait_time(cls, task_type: str = None, queue_position: int = 0) -> int:
        """
        估算任务等待时间（秒）
        
        Args:
            task_type: 任务类型（可选，用于更精确的估算）
            queue_position: 队列中的位置
            
        Returns:
            预估等待时间（秒）
        """
        # 基于任务类型的平均处理时间
        avg_processing_times = {
            'chat': 5,           # LLM对话: 5秒
            'text_to_image': 45,  # 文生图: 45秒
            'image_edit': 45,     # 图片编辑: 45秒
            'text_to_video': 200, # 文生视频: 200秒
            'image_to_video': 600, # 图生视频: 600秒
        }
        
        # 默认处理时间
        avg_time = avg_processing_times.get(task_type, 60)  # 默认60秒
        
        # 获取当前队列长度
        try:
            r = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=0
            )
            current_queue_length = r.llen(cls.DEFAULT_QUEUE)
        except:
            current_queue_length = queue_position
        
        # 基于当前队列长度和平均处理时间估算
        # 假设并发数为6（docker配置中会提高到6）
        concurrency = 6
        wait_time = (current_queue_length // concurrency) * avg_time
        
        return max(wait_time, 0)
    
    @classmethod
    def check_system_resources(cls) -> Dict[str, Any]:
        """检查系统资源状态"""
        try:
            # 检查内存使用率
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存告警阈值
            memory_warning = memory.percent > 80  # 80%告警
            memory_critical = memory.percent > 90  # 90%拒绝新任务
            
            # CPU告警阈值
            cpu_warning = cpu_percent > 80
            cpu_critical = cpu_percent > 95
            
            return {
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'memory_warning': memory_warning,
                'memory_critical': memory_critical,
                'cpu_percent': cpu_percent,
                'cpu_warning': cpu_warning,
                'cpu_critical': cpu_critical,
                'status': 'critical' if (memory_critical or cpu_critical) else 'warning' if (memory_warning or cpu_warning) else 'healthy'
            }
        except Exception as e:
            logger.error(f"检查系统资源失败: {e}")
            return {'error': str(e), 'status': 'unknown'}
    
    @classmethod
    def check_queue_status(cls) -> Dict[str, Any]:
        """检查队列状态和告警"""
        try:
            r = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=0
            )
            
            queue_length = r.llen(cls.DEFAULT_QUEUE)
            usage_percent = (queue_length / cls.MAX_QUEUE_LENGTH) * 100
            
            warnings = []
            status = 'healthy'
            
            if queue_length >= cls.MAX_QUEUE_LENGTH:
                warnings.append(f"🚨 队列已满 ({queue_length}/{cls.MAX_QUEUE_LENGTH})")
                status = 'critical'
            elif usage_percent > 80:
                warnings.append(f"⚠️ 队列接近满载 ({queue_length}/{cls.MAX_QUEUE_LENGTH})")
                status = 'warning'
            
            return {
                'queue_name': cls.DEFAULT_QUEUE,
                'length': queue_length,
                'max_length': cls.MAX_QUEUE_LENGTH,
                'usage_percent': usage_percent,
                'status': status,
                'warnings': warnings
            }
            
        except Exception as e:
            logger.error(f"检查队列状态失败: {e}")
            return {'error': str(e), 'status': 'unknown'} 