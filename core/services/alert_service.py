import logging
import json
from datetime import datetime, timedelta
from django.core.mail import send_mail
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from typing import Dict, Any

logger = logging.getLogger(__name__)

class AlertService:
    """API告警服务 - 监控API限制和故障，发送邮件通知"""
    
    ALERT_COOLDOWN = 300  # 5分钟内同一类型告警只发送一次
    
    @staticmethod
    def check_api_error(error_message: str, api_name: str = "DashScope") -> bool:
        """
        检查是否为API限制错误
        返回True表示需要发送告警
        """
        error_keywords = [
            "RateQuota", "Arrearage", "throttling", "rate limit",
            "quota exceeded", "too many requests", "限流", "配额"
        ]
        
        error_lower = error_message.lower()
        return any(keyword.lower() in error_lower for keyword in error_keywords)
    
    @staticmethod
    def send_api_alert(error_message: str, api_name: str = "DashScope", context: Dict[str, Any] = None):
        """
        发送API告警邮件
        """
        try:
            # 检查告警冷却期
            cache_key = f"api_alert_{api_name}_{hash(error_message) % 1000}"
            if cache.get(cache_key):
                logger.info(f"告警冷却期内，跳过发送: {api_name}")
                return
            
            # 设置冷却期
            cache.set(cache_key, True, AlertService.ALERT_COOLDOWN)
            
            # 获取告警邮箱
            alert_email = getattr(settings, 'ALERT_EMAIL', '<EMAIL>')
            
            # 构建邮件内容
            subject = f"🚨 {api_name} API 告警通知"
            
            message = f"""
尊敬的管理员：

系统检测到 {api_name} API 服务异常：

错误信息：{error_message}

时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
            
            if context:
                message += f"""
详细信息：
- 用户：{context.get('user', '匿名用户')}
- 会话ID：{context.get('session_id', 'N/A')}
- 请求模式：{context.get('mode', 'N/A')}
- 提示词：{context.get('prompt', 'N/A')[:100]}...

"""
            
            message += f"""
可能原因：
- API配额已用完
- 请求频率过高
- 账户欠费
- 服务临时不可用

建议处理：
1. 检查API账户余额和配额
2. 查看请求频率是否超限
3. 联系API服务商确认服务状态
4. 考虑启用备用API服务

系统将尝试使用备用服务继续提供功能。

---
MirageMakers AI 监控系统
{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # 发送邮件
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[alert_email],
                fail_silently=False,
            )
            
            logger.info(f"API告警邮件已发送到: {alert_email}")
            
            # 记录告警历史
            AlertService._log_alert(api_name, error_message, context)
            
        except Exception as e:
            logger.error(f"发送API告警邮件失败: {e}")
    
    @staticmethod
    def _log_alert(api_name: str, error_message: str, context: Dict[str, Any] = None):
        """记录告警历史到缓存中"""
        try:
            alert_history_key = "api_alert_history"
            history = cache.get(alert_history_key, [])
            
            alert_record = {
                'timestamp': timezone.now().isoformat(),
                'api_name': api_name,
                'error_message': error_message,
                'context': context or {}
            }
            
            history.append(alert_record)
            
            # 只保留最近100条记录
            if len(history) > 100:
                history = history[-100:]
            
            # 缓存7天
            cache.set(alert_history_key, history, 7 * 24 * 3600)
            
        except Exception as e:
            logger.error(f"记录告警历史失败: {e}")
    
    @staticmethod
    def get_alert_history(limit: int = 50):
        """获取告警历史"""
        try:
            history = cache.get("api_alert_history", [])
            return history[-limit:] if history else []
        except Exception as e:
            logger.error(f"获取告警历史失败: {e}")
            return [] 