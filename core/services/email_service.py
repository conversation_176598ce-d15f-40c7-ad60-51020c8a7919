import logging
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from django.utils import timezone
import stripe
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def send_video_completion_email(user_email, video_url, prompt, session_id):
    """
    发送视频生成完成通知邮件
    
    Args:
        user_email: 用户邮箱
        video_url: 生成的视频URL
        prompt: 用户的原始提示词
        session_id: 会话ID
    """
    try:
        subject = 'MirageMakers AI - Your Video is Ready! 🎬'
        
        # HTML邮件内容 - 黑紫色主题
        html_message = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #fff; margin: 0; padding: 0; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
            <div style="max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
                <!-- Header -->
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 40px 20px; text-align: center;">
                    <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 700; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">MirageMakers AI</h1>
                    <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px; font-weight: 300;">Your AI Creative Assistant</p>
                </div>
                
                <!-- Main Content -->
                <div style="padding: 50px 30px; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <div style="font-size: 60px; margin-bottom: 20px; text-shadow: 0 0 20px rgba(122, 95, 250, 0.5);">🎬</div>
                        <h2 style="color: #fff; margin: 0; font-size: 28px; font-weight: 600; background: linear-gradient(135deg, #00d2ff, #7a5ffa); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Your Video is Ready!</h2>
                    </div>
                    
                    <p style="font-size: 18px; color: #a0a9c0; margin-bottom: 40px; text-align: center; line-height: 1.6;">
                        Fantastic! Your AI-generated video has been completed successfully. 
                        <br>We're excited to share your creation with you!
                    </p>
                    
                    <!-- Video Download Section -->
                    <div style="background: linear-gradient(135deg, rgba(0, 210, 255, 0.1) 0%, rgba(122, 95, 250, 0.1) 100%); 
                                border: 1px solid rgba(122, 95, 250, 0.3); 
                                padding: 40px; border-radius: 16px; margin: 40px 0; text-align: center;
                                box-shadow: 0 8px 32px rgba(122, 95, 250, 0.1);">
                        <div style="font-size: 40px; margin-bottom: 20px; filter: drop-shadow(0 0 10px rgba(0, 210, 255, 0.5));">🎯</div>
                        <p style="margin: 0 0 25px 0; color: #00d2ff; font-weight: 600; font-size: 18px;">Download Your Video</p>
                        
                        <a href="{video_url}" 
                           style="display: inline-block; 
                                  background: linear-gradient(135deg, #00d2ff, #7a5ffa); 
                                  color: white; padding: 18px 36px; text-decoration: none; 
                                  border-radius: 12px; font-weight: 600; font-size: 16px;
                                  box-shadow: 0 8px 25px rgba(0, 210, 255, 0.4);
                                  transition: all 0.3s ease;
                                  text-transform: uppercase;
                                  letter-spacing: 1px;">
                            🎥 Download Video
                        </a>
                    </div>
                    
                    <!-- Success Message -->
                    <div style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%); 
                                border: 1px solid rgba(16, 185, 129, 0.3); 
                                border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                            <div style="font-size: 24px; margin-right: 10px;">✨</div>
                            <p style="margin: 0; color: #10b981; font-weight: 600; font-size: 16px;">Creation Complete!</p>
                        </div>
                        <p style="margin: 0; color: #6ee7b7; font-size: 14px; line-height: 1.5;">
                            Your video has been generated with cutting-edge AI technology. Enjoy your creation!
                    </p>
                </div>
                </div>
                
                <!-- Footer -->
                <div style="background: rgba(0, 0, 0, 0.3); padding: 30px; text-align: center; border-top: 1px solid rgba(122, 95, 250, 0.2);">
                    <p style="margin: 0; color: #6b7280; font-size: 12px; line-height: 1.6;">
                        This email was sent automatically by MirageMakers AI<br>
                        © 2024 MirageMakers AI. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # 纯文本版本
        plain_message = f"""
        MirageMakers AI - Your Video is Ready!
        
        Fantastic! Your AI-generated video has been completed successfully.
        
        Download your video: {video_url}
        
        Your video has been generated with cutting-edge AI technology. Enjoy your creation!
        
        This email was sent automatically by MirageMakers AI.
        © 2024 MirageMakers AI. All rights reserved.
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Video completion notification sent to: {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send video completion notification: {e}")
        return False


def send_video_processing_notification(user_email, prompt, session_id):
    """
    发送视频正在处理的通知邮件
    
    Args:
        user_email: 用户邮箱
        prompt: 用户的原始提示词
        session_id: 会话ID
    """
    try:
        subject = 'MirageMakers AI - Video Generation Started ⚡'
        
        # HTML邮件内容 - 黑紫色主题
        html_message = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #fff; margin: 0; padding: 0; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
            <div style="max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
                <!-- Header -->
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 40px 20px; text-align: center;">
                    <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 700; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">MirageMakers AI</h1>
                    <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px; font-weight: 300;">Your AI Creative Assistant</p>
                </div>
                
                <!-- Main Content -->
                <div style="padding: 50px 30px; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <div style="font-size: 60px; margin-bottom: 20px; text-shadow: 0 0 20px rgba(122, 95, 250, 0.5);">⚡</div>
                        <h2 style="color: #fff; margin: 0; font-size: 28px; font-weight: 600; background: linear-gradient(135deg, #00d2ff, #7a5ffa); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Video Generation Started!</h2>
                    </div>
                    
                    <p style="font-size: 18px; color: #a0a9c0; margin-bottom: 40px; text-align: center; line-height: 1.6;">
                        Your video generation request has been received successfully. 
                        <br>Our AI is now crafting your video with advanced technology.
                    </p>
                    
                    <!-- Processing Status Section -->
                    <div style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%); 
                                border: 1px solid rgba(255, 193, 7, 0.3); 
                                padding: 40px; border-radius: 16px; margin: 40px 0; text-align: center;
                                box-shadow: 0 8px 32px rgba(255, 193, 7, 0.1);">
                        <div style="font-size: 40px; margin-bottom: 20px; filter: drop-shadow(0 0 10px rgba(255, 193, 7, 0.5));">🎬</div>
                        <p style="margin: 0 0 25px 0; color: #ffc107; font-weight: 600; font-size: 18px;">Generation in Progress</p>
                        
                        <div style="background: rgba(255, 193, 7, 0.1); 
                                    border-radius: 8px; padding: 20px; margin: 20px 0;
                                    border: 1px solid rgba(255, 193, 7, 0.2);">
                            <p style="margin: 0; color: #fff; font-size: 14px; line-height: 1.6;">
                                <strong>⏱️ Estimated Time:</strong> 2-5 minutes<br>
                                <strong>📧 Notification:</strong> You'll receive an email when complete<br>
                                <strong>💫 Status:</strong> Even if the chat page doesn't update immediately, we'll send you the result!
                            </p>
                        </div>
                    </div>
                    
                    <!-- Info Message -->
                    <div style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%); 
                                border: 1px solid rgba(59, 130, 246, 0.3); 
                                border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                            <div style="font-size: 24px; margin-right: 10px;">🚀</div>
                            <p style="margin: 0; color: #3b82f6; font-weight: 600; font-size: 16px;">AI Processing Active</p>
                        </div>
                        <p style="margin: 0; color: #93c5fd; font-size: 14px; line-height: 1.5;">
                            Your video is being generated using cutting-edge AI technology. Please be patient!
                        </p>
                    </div>
                </div>
                
                <!-- Footer -->
                <div style="background: rgba(0, 0, 0, 0.3); padding: 30px; text-align: center; border-top: 1px solid rgba(122, 95, 250, 0.2);">
                    <p style="margin: 0; color: #6b7280; font-size: 12px; line-height: 1.6;">
                        This email was sent automatically by MirageMakers AI<br>
                        © 2024 MirageMakers AI. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        plain_message = f"""
        MirageMakers AI - Video Generation Started
        
        Your video generation request has been received successfully.
        
        Estimated Time: 2-5 minutes
        Notification: You'll receive an email when complete
        Status: Even if the chat page doesn't update immediately, we'll send you the result!
        
        Your video is being generated using cutting-edge AI technology. Please be patient!
        
        This email was sent automatically by MirageMakers AI.
        © 2024 MirageMakers AI. All rights reserved.
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Video processing notification sent to: {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send video processing notification: {e}")
        return False


def send_image_completion_email(user_email, image_url, prompt, session_id):
    """
    发送图片生成完成通知邮件
    
    Args:
        user_email: 用户邮箱
        image_url: 生成的图片URL
        prompt: 用户的原始提示词
        session_id: 会话ID
    """
    try:
        subject = 'MirageMakers AI - Your Image is Ready!'
        
        # HTML邮件内容
        html_message = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #00d2ff; margin: 0;">MirageMakers AI</h1>
                    <p style="color: #666; margin: 5px 0;">Your AI Creative Assistant</p>
                </div>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h2 style="color: #333; margin-top: 0;">🎨 Your Image is Ready!</h2>
                    <p>Hello! Your image generation request has been completed successfully.</p>
                    
                    <div style="background: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <strong>Your prompt:</strong><br>
                        <em style="color: #666;">"{prompt}"</em>
                    </div>
                    
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{image_url}" 
                           style="display: inline-block; background: linear-gradient(135deg, #00d2ff, #7a5ffa); 
                                  color: white; padding: 12px 24px; text-decoration: none; 
                                  border-radius: 6px; font-weight: bold;">
                            🖼️ View Your Image
                        </a>
                    </div>
                    
                    <p style="font-size: 14px; color: #666;">
                        You can also access the image directly at:<br>
                        <a href="{image_url}" style="color: #00d2ff; word-break: break-all;">{image_url}</a>
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="https://miragemakers.ai/chat?session={session_id}" 
                       style="color: #00d2ff; text-decoration: none;">
                        Return to Chat
                    </a>
                </div>
                
                <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; 
                           text-align: center; font-size: 12px; color: #999;">
                    <p>This email was sent automatically by MirageMakers AI. Please do not reply.</p>
                    <p>If you have any questions, please contact our support team.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # 纯文本版本
        plain_message = f"""
        MirageMakers AI - Your Image is Ready!
        
        Hello! Your image generation request has been completed successfully.
        
        Your prompt: "{prompt}"
        
        Image link: {image_url}
        
        Return to chat: https://miragemakers.ai/chat?session={session_id}
        
        This email was sent automatically by MirageMakers AI. Please do not reply.
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"Image completion notification sent to: {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send image completion notification: {e}")
        return False


def send_generation_processing_notification(user_email, prompt, session_id, content_type="image"):
    """
    发送生成任务正在处理的通知邮件
    
    Args:
        user_email: 用户邮箱
        prompt: 用户的原始提示词
        session_id: 会话ID
        content_type: 内容类型 ("image" 或 "video")
    """
    try:
        if content_type == "video":
            subject = 'MirageMakers AI - Your Video is Being Generated ✨'
            content_title = '🎬 Video Generation in Progress...'
            content_desc = 'We are carefully crafting your video with our advanced AI technology.'
            time_estimate = 'Usually takes 2-5 minutes.'
            emoji = '🎬'
        else:
            subject = 'MirageMakers AI - Your Image is Being Generated ✨'
            content_title = '🎨 Image Generation in Progress...'
            content_desc = 'We are carefully crafting your image with our advanced AI technology.'
            time_estimate = 'Usually takes 1-3 minutes.'
            emoji = '🎨'
        
        html_message = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f7fa;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                <!-- Header -->
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 30px 20px; text-align: center; border-radius: 12px 12px 0 0;">
                    <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">MirageMakers AI</h1>
                    <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 16px;">Your AI Creative Assistant</p>
                </div>
                
                <!-- Main Content -->
                <div style="padding: 40px 30px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="font-size: 48px; margin-bottom: 15px;">{emoji}</div>
                        <h2 style="color: #2d3748; margin: 0; font-size: 24px; font-weight: 600;">{content_title}</h2>
                    </div>
                    
                    <p style="font-size: 16px; color: #4a5568; margin-bottom: 25px; text-align: center;">
                        Hello! Your generation request has been received successfully. {content_desc}
                    </p>
                    
                    <!-- Prompt Display -->
                    <div style="background: #f7fafc; border-left: 4px solid #00d2ff; padding: 20px; border-radius: 8px; margin: 25px 0;">
                        <p style="margin: 0; color: #2d3748;">
                            <strong style="color: #00d2ff;">Your Prompt:</strong><br>
                            <em style="color: #4a5568; font-size: 15px; line-height: 1.5;">"{prompt}"</em>
                        </p>
                    </div>
                    
                    <!-- Notification Info -->
                    <div style="background: linear-gradient(135deg, #e6f7ff 0%, #f0f5ff 100%); padding: 25px; border-radius: 12px; border: 1px solid #b3e0ff; margin: 25px 0;">
                        <div style="text-align: center;">
                            <div style="font-size: 32px; margin-bottom: 10px;">📧</div>
                            <p style="margin: 0; color: #1e3a8a; font-weight: 600; font-size: 16px;">Email Notification</p>
                            <p style="margin: 8px 0 0 0; color: #1e40af; font-size: 14px; line-height: 1.5;">
                                Since generation takes some time, we'll notify you via email once it's complete.<br>
                                Please be patient - {time_estimate}<br>
                                <strong>Even if the chat page doesn't show results immediately, we'll send you the final result via email!</strong>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Return to Chat Button -->
                    <div style="text-align: center; margin: 35px 0;">
                        <a href="https://miragemakers.ai/chat?session={session_id}" 
                           style="display: inline-block; background: linear-gradient(135deg, #00d2ff, #7a5ffa); 
                                  color: white; padding: 14px 28px; text-decoration: none; 
                                  border-radius: 8px; font-weight: 600; font-size: 16px;
                                  box-shadow: 0 4px 12px rgba(0, 210, 255, 0.3);
                                  transition: all 0.3s ease;">
                            Return to Chat
                        </a>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <p style="color: #718096; font-size: 13px; margin: 0;">
                            Need help? Contact our support team at 
                            <a href="mailto:<EMAIL>" style="color: #00d2ff; text-decoration: none;"><EMAIL></a>
                        </p>
                    </div>
                </div>
                
                <!-- Footer -->
                <div style="background: #f8fafc; padding: 25px 30px; text-align: center; border-top: 1px solid #e2e8f0; border-radius: 0 0 12px 12px;">
                    <p style="margin: 0; color: #a0aec0; font-size: 12px; line-height: 1.4;">
                        This email was sent automatically by MirageMakers AI. Please do not reply.<br>
                        © 2024 MirageMakers AI. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        plain_message = f"""
        MirageMakers AI - {content_type.title()} Generation in Progress
        
        Hello! Your {content_type} generation request has been received successfully.
        
        Your prompt: "{prompt}"
        
        We are carefully crafting your {content_type} with our advanced AI technology.
        
        Email Notification:
        Since generation takes some time, we'll notify you via email once it's complete.
        Please be patient - {time_estimate}
        Even if the chat page doesn't show results immediately, we'll send you the final result via email!
        
        Return to chat: https://miragemakers.ai/chat?session={session_id}
        
        Need help? Contact our support <NAME_EMAIL>
        
        This email was sent automatically by MirageMakers AI. Please do not reply.
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"{content_type.title()} processing notification sent to: {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send {content_type} processing notification: {e}")
        return False


def send_generation_failure_notification(user_email, prompt, session_id, content_type="image"):
    """
    发送生成任务失败的通知邮件
    
    Args:
        user_email: 用户邮箱
        prompt: 用户的原始提示词
        session_id: 会话ID
        content_type: 内容类型 ("image" 或 "video")
    """
    try:
        if content_type == "video":
            subject = 'MirageMakers AI - Video Generation Failed ⚠️'
            content_title = '🚫 Video Generation Failed'
            content_desc = 'Unfortunately, we encountered an issue while generating your video.'
            emoji = '🚫'
        else:
            subject = 'MirageMakers AI - Image Generation Failed ⚠️'
            content_title = '🚫 Image Generation Failed'
            content_desc = 'Unfortunately, we encountered an issue while generating your image.'
            emoji = '🚫'
        
        html_message = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #fff; margin: 0; padding: 0; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
            <div style="max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
                <!-- Header -->
                <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); padding: 40px 20px; text-align: center;">
                    <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 700; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">MirageMakers AI</h1>
                    <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px; font-weight: 300;">Your AI Creative Assistant</p>
                </div>
                
                <!-- Main Content -->
                <div style="padding: 50px 30px; background: linear-gradient(135deg, #0f0a1d 0%, #1a0f2e 100%);">
                    <div style="text-align: center; margin-bottom: 40px;">
                        <div style="font-size: 60px; margin-bottom: 20px; text-shadow: 0 0 20px rgba(220, 38, 38, 0.5);">{emoji}</div>
                        <h2 style="color: #fff; margin: 0; font-size: 28px; font-weight: 600; color: #dc2626;">{content_title}</h2>
                    </div>
                    
                    <p style="font-size: 18px; color: #a0a9c0; margin-bottom: 40px; text-align: center; line-height: 1.6;">
                        We apologize for the inconvenience. {content_desc}
                        <br>Your tokens have been automatically refunded to your account.
                    </p>
                    
                    <!-- Prompt Display -->
                    <div style="background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(185, 28, 28, 0.1) 100%); 
                                border: 1px solid rgba(220, 38, 38, 0.3); 
                                padding: 25px; border-radius: 12px; margin: 30px 0;">
                        <p style="margin: 0 0 15px 0; color: #dc2626; font-weight: 600; font-size: 16px;">Your Prompt:</p>
                        <p style="margin: 0; color: #fff; font-size: 14px; line-height: 1.6; font-style: italic;">
                            "{prompt}"
                        </p>
                    </div>
                    
                    <!-- Refund Notice -->
                    <div style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%); 
                                border: 1px solid rgba(16, 185, 129, 0.3); 
                                border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                            <div style="font-size: 24px; margin-right: 10px;">💰</div>
                            <p style="margin: 0; color: #10b981; font-weight: 600; font-size: 16px;">Tokens Refunded</p>
                        </div>
                        <p style="margin: 0; color: #6ee7b7; font-size: 14px; line-height: 1.5;">
                            Don't worry! The tokens used for this generation have been automatically returned to your account.
                            You can try generating again or use them for other creations.
                        </p>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div style="text-align: center; margin: 40px 0;">
                        <a href="https://miragemakers.ai/chat?session={session_id}
                           style="display: inline-block; 
                                  background: linear-gradient(135deg, #00d2ff, #7a5ffa); 
                                  color: white; padding: 18px 36px; text-decoration: none; 
                                  border-radius: 12px; font-weight: 600; font-size: 16px;
                                  box-shadow: 0 8px 25px rgba(0, 210, 255, 0.4);
                                  margin: 0 10px;">
                            Try Again
                        </a>
                        <a href="mailto:<EMAIL>"
                           style="display: inline-block; 
                                  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)); 
                                  color: #fff; padding: 18px 36px; text-decoration: none; 
                                  border-radius: 12px; font-weight: 600; font-size: 16px;
                                  border: 1px solid rgba(255,255,255,0.2);
                                  margin: 0 10px;">
                            Contact Support
                        </a>
                    </div>
                </div>
                
                <!-- Footer -->
                <div style="background: rgba(0, 0, 0, 0.3); padding: 30px; text-align: center; border-top: 1px solid rgba(220, 38, 38, 0.2);">
                    <p style="margin: 0; color: #6b7280; font-size: 12px; line-height: 1.6;">
                        This email was sent automatically by MirageMakers AI<br>
                        © 2024 MirageMakers AI. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        plain_message = f"""
        MirageMakers AI - {content_type.title()} Generation Failed
        
        We apologize for the inconvenience. Unfortunately, we encountered an issue while generating your {content_type}.
        
        Your prompt: "{prompt}"
        
        Don't worry! The tokens used for this generation have been automatically returned to your account.
        You can try generating again or use them for other creations.
        
        Try again: https://miragemakers.ai/chat?session={session_id}
        Contact support: <EMAIL>
        
        This email was sent automatically by MirageMakers AI.
        © 2024 MirageMakers AI. All rights reserved.
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"{content_type.title()} failure notification sent to: {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send {content_type} failure notification: {e}")
        return False


def send_payment_success_notification(user, plan_type, tokens_added):
    """
    发送支付成功通知邮件
    """
    try:
        # 使用EmailService类发送邮件
        email_service = EmailService()
        # 由于我们需要PaymentOrder对象，这里创建一个临时对象用于显示
        class TempOrder:
            def __init__(self, plan_type, tokens_added):
                self.plan_type = plan_type
                self.tokens_to_add = tokens_added
                self.amount = {
                    'TRIAL': 6.99,
                    'BASIC': 19.99,
                    'PREMIUM': 59.99,
                    'ANNUAL': 199.99
                }.get(plan_type, 0)
                self.completed_at = timezone.now()
                self.id = f"temp_{int(timezone.now().timestamp())}"
                
            def get_payment_gateway_display(self):
                return "Stripe"
        
        temp_order = TempOrder(plan_type, tokens_added)
        return email_service.send_payment_success_email(user, temp_order, tokens_added)
        
    except Exception as e:
        logger.error(f"Failed to send payment success notification: {e}")
        return False


class EmailService:
    """Email service for sending notifications"""
    
    def __init__(self):
        self.smtp_server = getattr(settings, 'EMAIL_HOST', 'smtp.gmail.com')
        self.smtp_port = getattr(settings, 'EMAIL_PORT', 587)
        self.smtp_username = getattr(settings, 'EMAIL_HOST_USER', '')
        self.smtp_password = getattr(settings, 'EMAIL_HOST_PASSWORD', '')
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        
    def send_payment_success_email(self, user, payment_order, tokens_added: int) -> bool:
        """发送支付成功邮件"""
        try:
            subject = "Payment Successful - MirageMakers AI"
            
            # 准备邮件数据
            context = {
                'user_name': user.username or user.email.split('@')[0],
                'user_email': user.email,
                'plan_name': self._get_plan_display_name(payment_order.plan_type),
                'amount': f"${payment_order.amount}",
                'tokens_added': tokens_added,
                'current_balance': user.tokens,
                'payment_date': payment_order.completed_at or timezone.now(),
                'order_id': payment_order.id,
                'payment_gateway': payment_order.get_payment_gateway_display(),
            }
            
            # 生成HTML内容
            html_content = self._generate_payment_success_html(context)
            
            # 发送邮件
            return self._send_email(
                to_email=user.email,
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send payment success email to {user.email}: {e}")
            return False
    
    def send_invoice_email(self, user, payment_order) -> bool:
        """发送发票邮件"""
        try:
            subject = f"Invoice #{payment_order.id} - MirageMakers AI"
            
            # 获取Stripe发票信息（如果有）
            invoice_data = None
            if payment_order.stripe_payment_intent_id:
                invoice_data = self._get_stripe_invoice_data(payment_order)
            
            # 准备邮件数据
            context = {
                'user_name': user.username or user.email.split('@')[0],
                'user_email': user.email,
                'invoice_number': f"INV-{payment_order.id}",
                'invoice_date': payment_order.completed_at or timezone.now(),
                'plan_name': self._get_plan_display_name(payment_order.plan_type),
                'amount': f"${payment_order.amount}",
                'tokens': payment_order.tokens_to_add,
                'payment_method': payment_order.get_payment_gateway_display(),
                'order_id': payment_order.id,
                'company_info': {
                    'name': 'MirageMakers AI Studio',
                    'email': '<EMAIL>',
                    'website': 'https://miragemakers.ai'
                },
                'invoice_data': invoice_data
            }
            
            # 生成HTML内容
            html_content = self._generate_invoice_html(context)
            
            # 发送邮件
            return self._send_email(
                to_email=user.email,
                subject=subject,
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send invoice email to {user.email}: {e}")
            return False
    
    def _get_stripe_invoice_data(self, payment_order) -> Optional[Dict]:
        """获取Stripe发票数据"""
        try:
            if not payment_order.stripe_payment_intent_id:
                return None
                
            # 获取Payment Intent
            intent = stripe.PaymentIntent.retrieve(payment_order.stripe_payment_intent_id)
            
            # 如果有订阅，获取相关发票
            if payment_order.stripe_subscription_id:
                subscription = stripe.Subscription.retrieve(payment_order.stripe_subscription_id)
                if subscription.latest_invoice:
                    invoice = stripe.Invoice.retrieve(subscription.latest_invoice)
                    return {
                        'stripe_invoice_id': invoice.id,
                        'invoice_url': invoice.hosted_invoice_url,
                        'invoice_pdf': invoice.invoice_pdf
                    }
            
            return {
                'payment_intent_id': intent.id,
                'receipt_url': intent.charges.data[0].receipt_url if intent.charges.data else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get Stripe invoice data: {e}")
            return None
    
    def _get_plan_display_name(self, plan_type: str) -> str:
        """获取套餐显示名称"""
        plan_names = {
            'TRIAL': 'Trial Plan',
            'BASIC': 'Basic Plan', 
            'PREMIUM': 'Premium Plan',
            'ANNUAL': 'Annual Plan'
        }
        return plan_names.get(plan_type, plan_type)
    
    def _generate_payment_success_html(self, context: Dict) -> str:
        """生成支付成功邮件HTML"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Payment Successful</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                .success-icon {{ font-size: 48px; margin-bottom: 20px; }}
                .amount {{ font-size: 32px; font-weight: bold; color: #4CAF50; margin: 20px 0; }}
                .details {{ background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; }}
                .btn {{ display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="success-icon">✅</div>
                    <h1>Payment Successful!</h1>
                    <p>Thank you for your purchase, {context['user_name']}!</p>
                </div>
                
                <div class="content">
                    <div class="amount">{context['amount']}</div>
                    
                    <div class="details">
                        <h3>Order Details</h3>
                        <p><strong>Plan:</strong> {context['plan_name']}</p>
                        <p><strong>Tokens Added:</strong> {context['tokens_added']:,}</p>
                        <p><strong>Current Balance:</strong> {context['current_balance']:,} tokens</p>
                        <p><strong>Order ID:</strong> #{context['order_id']}</p>
                        <p><strong>Payment Method:</strong> {context['payment_gateway']}</p>
                        <p><strong>Date:</strong> {context['payment_date'].strftime('%B %d, %Y at %I:%M %p')}</p>
                    </div>
                    
                    <div style="text-align: center;">
                        <a href="https://miragemakers.ai/chat" class="btn">Start Creating</a>
                        <a href="https://miragemakers.ai/profile" class="btn">View Account</a>
                    </div>
                    
                    <p>Your tokens have been automatically added to your account and are ready to use!</p>
                </div>
                
                <div class="footer">
                    <p>Thank you for choosing MirageMakers AI</p>
                    <p>If you have any questions, contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _generate_invoice_html(self, context: Dict) -> str:
        """生成发票邮件HTML"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Invoice {context['invoice_number']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #2c3e50; color: white; padding: 30px; text-align: center; }}
                .invoice-details {{ background: #f8f9fa; padding: 30px; }}
                .company-info {{ text-align: right; margin-bottom: 30px; }}
                .customer-info {{ margin-bottom: 30px; }}
                .invoice-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .invoice-table th, .invoice-table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                .invoice-table th {{ background: #f1f1f1; }}
                .total {{ font-size: 24px; font-weight: bold; color: #2c3e50; text-align: right; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>INVOICE</h1>
                    <h2>{context['invoice_number']}</h2>
                </div>
                
                <div class="invoice-details">
                    <div class="company-info">
                        <strong>{context['company_info']['name']}</strong><br>
                        Email: {context['company_info']['email']}<br>
                        Website: {context['company_info']['website']}
                    </div>
                    
                    <div class="customer-info">
                        <strong>Bill To:</strong><br>
                        {context['user_name']}<br>
                        {context['user_email']}
                    </div>
                    
                    <p><strong>Invoice Date:</strong> {context['invoice_date'].strftime('%B %d, %Y')}</p>
                    <p><strong>Order ID:</strong> #{context['order_id']}</p>
                    
                    <table class="invoice-table">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>Tokens</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{context['plan_name']}</td>
                                <td>{context['tokens']:,}</td>
                                <td>{context['amount']}</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="total">
                        Total: {context['amount']}
                    </div>
                    
                    <p><strong>Payment Method:</strong> {context['payment_method']}</p>
                    <p><strong>Status:</strong> Paid</p>
                </div>
                
                <div class="footer">
                    <p>Thank you for your business!</p>
                    <p>For questions about this invoice, contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _send_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """发送邮件"""
        try:
            if not all([self.smtp_username, self.smtp_password]):
                logger.warning("Email configuration missing, skipping email send")
                return False
                
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = to_email
            
            # 添加HTML内容
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # 发送邮件
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False 