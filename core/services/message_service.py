from core.models import ChatSession, ChatMessage
from typing import Any, Dict, List

def get_or_create_session(user_id: str, title: str) -> ChatSession:
    """
    根据 user_id 获取或新建一个 Session。
    兼容main分支的接口，但使用我们的ChatSession模型
    """
    # 注意：这里的user_id是字符串，需要根据实际情况处理
    # 如果user_id是"guest"，则user设为None（匿名用户）
    if user_id == "guest":
        user_obj = None
    else:
        # 这里需要根据实际的用户ID获取用户对象
        # 暂时使用None，实际使用时需要修改
        user_obj = None
    
    session, _ = ChatSession.objects.get_or_create(
        user=user_obj,
        defaults={"title": title}
    )
    return session

def save_message(
    session: ChatSession, 
    role: str, 
    content: str,
    image_url: str = None,
    video_url: str = None,
    metadata: Dict[str, Any] = None
) -> ChatMessage:
    """
    为指定会话新增一条消息记录。
    支持保存 image_url / video_url / metadata。
    """
    return ChatMessage.objects.create(
        session    = session,
        type       = role,
        content    = content,
        image_url  = image_url or None,
        video_url  = video_url or None,
        metadata   = metadata or {},
    )
    
def load_session_history(session: ChatSession) -> List[Dict[str, Any]]:
    """
    加载一个会话的历史消息，并把 image_url / video_url 带上。
    返回格式：
      [
         {"role": "...", "content": "...", "image_url": "...", "video_url": "..."},
         ...
      ]
    """
    qs = ChatMessage.objects.filter(session=session).order_by("created_at")
    history: List[Dict[str, Any]] = []
    for msg in qs:
        entry: Dict[str, Any] = {
            "role":      msg.type,
            "content":   msg.content or "",
            "image_url": msg.image_url or "",
            "video_url": msg.video_url or "",
        }
        history.append(entry)
    return history