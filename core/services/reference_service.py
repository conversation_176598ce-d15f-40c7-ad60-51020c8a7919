"""
消息引用服务
处理@功能相关的逻辑，包括引用解析、媒体处理等
"""
import re
import json
import uuid
import requests
from typing import List, Dict, Any, Optional, Tuple
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db.models import Q
from core.models import ChatMessage, ChatSession
import logging

logger = logging.getLogger(__name__)

class MessageReferenceService:
    """消息引用服务"""
    
    # 引用模式：@[消息ID]或@image_1, @video_2等
    REFERENCE_PATTERN = r'@(msg_[\w-]+|image_\d+|video_\d+|latest_image|latest_video)'
    
    @classmethod
    def extract_references(cls, content: str) -> List[str]:
        """从内容中提取引用标识"""
        return re.findall(cls.REFERENCE_PATTERN, content)
    
    @classmethod
    def get_session_media_messages(cls, session: ChatSession, limit: int = 20) -> List[Dict[str, Any]]:
        """获取会话中包含媒体的消息"""
        media_messages = session.messages.filter(
            Q(image_url__isnull=False) | Q(video_url__isnull=False)
        ).order_by('-created_at')[:limit]
        
        result = []
        image_count = 0
        video_count = 0
        
        for msg in reversed(media_messages):  # 按时间顺序编号
            media_info = {
                'id': str(msg.id),
                'content': msg.content[:100] + '...' if len(msg.content) > 100 else msg.content,
                'created_at': msg.created_at.isoformat(),
                'type': msg.get_media_type(),
                'url': msg.get_media_url(),
                'msg_type': msg.type
            }
            
            if msg.image_url:
                image_count += 1
                media_info['reference_id'] = f"image_{image_count}"
                media_info['display_name'] = f"Image {image_count}"
            elif msg.video_url:
                video_count += 1
                media_info['reference_id'] = f"video_{video_count}"
                media_info['display_name'] = f"Video {video_count}"
            
            result.append(media_info)
        
        return result
    
    @classmethod
    def resolve_references(cls, content: str, session: ChatSession) -> Tuple[str, List[ChatMessage], Dict[str, Any]]:
        """
        解析内容中的引用，返回处理后的内容和引用的消息
        返回：(处理后的内容, 引用的消息列表, 引用上下文)
        """
        references = cls.extract_references(content)
        if not references:
            return content, [], {}
        
        # 获取会话中的媒体消息
        media_messages = cls.get_session_media_messages(session, limit=50)
        referenced_messages = []
        reference_context = {
            'references': [],
            'media_urls': [],
            'reference_map': {}
        }
        
        # 创建引用映射
        reference_map = {}
        for media_msg in media_messages:
            reference_map[media_msg['reference_id']] = media_msg
            reference_map[f"msg_{media_msg['id']}"] = media_msg
        
        # 处理特殊引用
        if media_messages:
            latest_images = [m for m in media_messages if m['type'] == 'image']
            latest_videos = [m for m in media_messages if m['type'] == 'video']
            
            if latest_images:
                reference_map['latest_image'] = latest_images[0]
            if latest_videos:
                reference_map['latest_video'] = latest_videos[0]
        
        # 解析引用
        processed_content = content
        for ref in references:
            if ref in reference_map:
                media_info = reference_map[ref]
                
                # 获取实际的消息对象
                try:
                    message_obj = ChatMessage.objects.get(id=media_info['id'])
                    referenced_messages.append(message_obj)
                    
                    # 记录引用信息
                    ref_info = {
                        'reference_id': ref,
                        'message_id': media_info['id'],
                        'media_type': media_info['type'],
                        'media_url': media_info['url'],
                        'display_name': media_info['display_name']
                    }
                    reference_context['references'].append(ref_info)
                    reference_context['media_urls'].append(media_info['url'])
                    reference_context['reference_map'][ref] = ref_info
                    
                    # 替换引用为更友好的显示文本
                    display_text = f"[{media_info['display_name']}]"
                    processed_content = processed_content.replace(f"@{ref}", display_text)
                    
                except ChatMessage.DoesNotExist:
                    logger.warning(f"Referenced message {media_info['id']} not found")
                    continue
            else:
                logger.warning(f"Reference {ref} not found in session")
        
        return processed_content, referenced_messages, reference_context
    
    @classmethod
    def download_media_file(cls, url: str) -> Optional[str]:
        """下载媒体文件到本地存储"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # 生成文件名
            file_extension = url.split('.')[-1].split('?')[0]
            filename = f"referenced_{uuid.uuid4()}.{file_extension}"
            
            # 保存文件
            file_content = ContentFile(response.content)
            file_path = default_storage.save(f"media/references/{filename}", file_content)
            
            return default_storage.url(file_path)
            
        except Exception as e:
            logger.error(f"Failed to download media file {url}: {e}")
            return None
    
    @classmethod
    def prepare_multimodal_context(cls, referenced_messages: List[ChatMessage]) -> Dict[str, Any]:
        """准备多模态上下文信息，用于AI处理"""
        context = {
            'images': [],
            'videos': [],
            'text_context': [],
            'total_media_count': 0
        }
        
        for msg in referenced_messages:
            media_context = {
                'message_id': str(msg.id),
                'content': msg.content,
                'created_at': msg.created_at.isoformat(),
                'url': msg.get_media_url(),
                'type': msg.get_media_type()
            }
            
            if msg.image_url:
                context['images'].append({
                    **media_context,
                    'image_url': msg.image_url
                })
            elif msg.video_url:
                context['videos'].append({
                    **media_context,
                    'video_url': msg.video_url
                })
            
            # 添加文本上下文
            if msg.content:
                context['text_context'].append({
                    'message_id': str(msg.id),
                    'content': msg.content,
                    'type': msg.type
                })
        
        context['total_media_count'] = len(context['images']) + len(context['videos'])
        return context

class MultiModalProcessor:
    """多模态处理器"""
    
    @classmethod
    def process_with_references(cls, prompt: str, referenced_context: Dict[str, Any], mode: str = 'image_gen') -> Dict[str, Any]:
        """
        使用引用上下文处理多模态请求
        支持的模式：
        - image_gen: 图像生成
        - image_edit: 图像编辑
        - video_gen: 视频生成
        - image_to_video: 图像转视频
        - multi_image_compose: 多图合成
        """
        
        if mode == 'multi_image_compose' and len(referenced_context.get('images', [])) > 1:
            return cls._process_multi_image_composition(prompt, referenced_context)
        elif mode == 'image_edit' and referenced_context.get('images'):
            return cls._process_image_editing(prompt, referenced_context)
        elif mode == 'image_to_video' and referenced_context.get('images'):
            return cls._process_image_to_video(prompt, referenced_context)
        else:
            return cls._process_standard_generation(prompt, referenced_context, mode)
    
    @classmethod
    def _process_multi_image_composition(cls, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理多图合成"""
        images = context.get('images', [])
        if len(images) < 2:
            return {'error': '多图合成需要至少2张图片'}
        
        # 构建合成提示
        composition_prompt = f"""
        基于以下图片进行合成创作：
        {prompt}
        
        引用的图片信息：
        """
        
        for i, img in enumerate(images, 1):
            composition_prompt += f"\n图片{i}: {img.get('content', '无描述')}"
        
        return {
            'mode': 'multi_image_compose',
            'prompt': composition_prompt,
            'source_images': [img['image_url'] for img in images],
            'context': context
        }
    
    @classmethod
    def _process_image_editing(cls, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理图像编辑"""
        images = context.get('images', [])
        if not images:
            return {'error': '图像编辑需要至少1张图片'}
        
        source_image = images[0]  # 使用第一张图片作为源图
        
        edit_prompt = f"""
        对以下图片进行编辑：
        原图描述: {source_image.get('content', '无描述')}
        编辑要求: {prompt}
        """
        
        return {
            'mode': 'image_edit',
            'prompt': edit_prompt,
            'source_image': source_image['image_url'],
            'context': context
        }
    
    @classmethod
    def _process_image_to_video(cls, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理图像转视频"""
        images = context.get('images', [])
        if not images:
            return {'error': '图像转视频需要至少1张图片'}
        
        source_image = images[0]
        
        video_prompt = f"""
        基于以下图片生成视频：
        源图描述: {source_image.get('content', '无描述')}
        视频要求: {prompt}
        """
        
        return {
            'mode': 'img2video',
            'prompt': video_prompt,
            'source_image': source_image['image_url'],
            'context': context
        }
    
    @classmethod
    def _process_standard_generation(cls, prompt: str, context: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """处理标准生成"""
        enhanced_prompt = prompt
        
        # 如果有引用上下文，增强提示
        if context.get('text_context'):
            context_info = "\n相关上下文:\n"
            for text_ctx in context['text_context']:
                context_info += f"- {text_ctx['content']}\n"
            enhanced_prompt = enhanced_prompt + context_info
        
        return {
            'mode': mode,
            'prompt': enhanced_prompt,
            'context': context
        } 