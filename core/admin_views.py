import json
import logging
from datetime import datetime, timedelta
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.admin.views.decorators import staff_member_required
from django.core.cache import cache
from django.utils import timezone
from django.db.models import Count, Q, Avg, Sum
from django.db.models.functions import <PERSON>runcDate, TruncWeek, TruncMonth, TruncYear
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.conf import settings
from rest_framework.authtoken.models import Token
from functools import wraps
from django.db import connection

from core.models import User, ChatSession, ChatMessage, PaymentOrder, BetaApplication, EmailVerification, PasswordReset, TokenConsumption
from core.services.alert_service import AlertService
from django.contrib.auth.hashers import make_password

logger = logging.getLogger(__name__)

def api_token_required(view_func):
    """检查API token或用户是否为staff的装饰器"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # 检查URL参数中的token（用于dashboard访问）
        token_param = request.GET.get('token') or request.POST.get('token')
        if token_param:
            try:
                token = Token.objects.get(key=token_param)
                if token.user.is_staff and token.user.is_active:
                    request.user = token.user
                    # 设置session以保持登录状态
                    request.session['user_id'] = str(token.user.id)
                    request.session['is_staff'] = True
                    return view_func(request, *args, **kwargs)
            except (Token.DoesNotExist, AttributeError):
                pass
        
        # 检查Authorization header中的token
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer ') or auth_header.startswith('Token '):
            token_key = auth_header.split(' ')[1]
            try:
                token = Token.objects.get(key=token_key)
                if token.user.is_staff:
                    request.user = token.user
                    return view_func(request, *args, **kwargs)
            except Token.DoesNotExist:
                pass
        
        # 检查是否已登录且为staff用户
        if request.user.is_authenticated and request.user.is_staff:
            return view_func(request, *args, **kwargs)
        
        # 检查session中的用户
        if hasattr(request, 'session') and request.session.get('user_id'):
            try:
                user = User.objects.get(id=request.session['user_id'])
                if user.is_staff:
                    request.user = user
                    return view_func(request, *args, **kwargs)
            except User.DoesNotExist:
                pass
        
        # 对于AJAX请求返回JSON错误
        if request.headers.get('Accept', '').startswith('application/json'):
            return JsonResponse({'error': '需要管理员权限'}, status=403)
        
        # 对于普通请求返回简单的HTML页面
        return render(request, 'admin/login_required.html', {
            'message': '访问Dashboard需要管理员权限，请先登录管理后台',
            'admin_url': '/admin/'
        }, status=403)
    
    return wrapper

def dashboard_csp(view_func):
    """为dashboard设置宽松的CSP头 - 现在由Nginx处理"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        response = view_func(request, *args, **kwargs)
        
        # 删除任何Django设置的CSP头，让Nginx的CSP设置生效
        if 'Content-Security-Policy' in response:
            del response['Content-Security-Policy']
        if 'content-security-policy' in response:
            del response['content-security-policy']
            
        return response
    return wrapper

@api_token_required
@dashboard_csp
def admin_dashboard(request):
    """管理员仪表板主页"""
    return render(request, 'admin/dashboard.html')

@api_token_required
def admin_api(request):
    """管理员API数据接口"""
    try:
        # 获取时间范围参数
        period = request.GET.get('period', 'day')  # day, week, month, year
        days = int(request.GET.get('days', 30))
        
        # 计算时间范围
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # 用户统计
        user_stats = get_user_statistics(start_date, end_date, period)
        
        # 聊天统计
        chat_stats = get_chat_statistics(start_date, end_date, period)
        
        # API状态统计
        api_stats = get_api_statistics()
        
        # 系统状态
        system_stats = get_system_statistics()
        
        # 告警历史
        alert_history = AlertService.get_alert_history(20)
        
        return JsonResponse({
            'user_stats': user_stats,
            'chat_stats': chat_stats,
            'api_stats': api_stats,
            'system_stats': system_stats,
            'alert_history': alert_history,
            'period': period,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"获取管理员数据失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@api_token_required
@csrf_exempt
@require_http_methods(["GET", "POST", "DELETE"])
def user_management(request):
    """用户账号管理"""
    if request.method == 'GET':
        try:
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            search = request.GET.get('search', '')
            status_filter = request.GET.get('status', '')  # active, inactive, all
            
            # 构建查询
            users_query = User.objects.all()
            
            if search:
                # 优化搜索逻辑：优先精确匹配，然后部分匹配
                search_term = search.strip()
                
                # 首先尝试精确匹配邮箱和用户名
                exact_match = users_query.filter(
                    Q(email__iexact=search_term) |
                    Q(username__iexact=search_term)
                )
                
                if exact_match.exists():
                    # 如果有精确匹配，只返回精确匹配的结果
                    users_query = exact_match
                else:
                    # 否则进行部分匹配
                    users_query = users_query.filter(
                        Q(name__icontains=search_term) |
                        Q(email__icontains=search_term) |
                        Q(username__icontains=search_term)
                    )
            
            if status_filter == 'active':
                users_query = users_query.filter(is_active=True)
            elif status_filter == 'inactive':
                users_query = users_query.filter(is_active=False)
            
            # 分页
            total_count = users_query.count()
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            users = users_query.order_by('-created_at')[start_index:end_index]
            
            # 构建用户数据
            users_data = []
            for user in users:
                # 获取用户的聊天统计
                session_count = ChatSession.objects.filter(user=user).count()
                message_count = ChatMessage.objects.filter(session__user=user).count()
                last_activity = ChatSession.objects.filter(user=user).order_by('-updated_at').first()
                
                users_data.append({
                    'id': str(user.id),
                    'name': user.name,
                    'email': user.email,
                    'username': user.username,
                    'tokens': user.tokens,
                    'current_plan': user.current_plan or 'Free Plan',
                    'tokens_expires_at': user.tokens_expires_at.isoformat() if user.tokens_expires_at else None,
                    'is_active': user.is_active,
                    'is_email_verified': user.is_email_verified,
                    'invitation_type': getattr(user, 'invitation_type', 'NORMAL'),
                    'invitation_type_display': dict(user.INVITATION_TYPE_CHOICES).get(getattr(user, 'invitation_type', 'NORMAL'), 'Normal Registration'),
                    'created_at': user.created_at.isoformat(),
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    'session_count': session_count,
                    'message_count': message_count,
                    'last_activity': last_activity.updated_at.isoformat() if last_activity else None,
                })
            
            return JsonResponse({
                'users': users_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size
                }
            })
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')
            
            if action == 'create_user':
                # 创建新用户
                name = data.get('name')
                email = data.get('email')
                password = data.get('password')
                tokens = int(data.get('tokens', 0))
                
                if not all([name, email, password]):
                    return JsonResponse({'error': '请填写所有必填字段'}, status=400)
                
                # 检查邮箱是否已存在
                if User.objects.filter(email=email).exists():
                    return JsonResponse({'error': '该邮箱已被注册'}, status=400)
                
                # 创建用户
                user = User.objects.create(
                    username=email,
                    email=email,
                    name=name,
                    password=make_password(password),
                    tokens=tokens,
                    is_email_verified=True,  # 管理员创建的用户默认已验证
                    is_active=True
                )
                
                return JsonResponse({
                    'message': '用户创建成功',
                    'user': {
                        'id': str(user.id),
                        'name': user.name,
                        'email': user.email,
                        'tokens': user.tokens
                    }
                })
            
            elif action == 'update_tokens':
                # 更新用户tokens
                user_id = data.get('user_id')
                tokens = int(data.get('tokens', 0))
                
                try:
                    user = User.objects.get(id=user_id)
                    user.tokens = tokens
                    user.save()
                    
                    return JsonResponse({
                        'message': f'用户 {user.name} 的tokens已更新为 {tokens}',
                        'user': {
                            'id': str(user.id),
                            'name': user.name,
                            'email': user.email,
                            'tokens': user.tokens
                        }
                    })
                except User.DoesNotExist:
                    return JsonResponse({'error': '用户不存在'}, status=404)
            
            elif action == 'update_status':
                # 更新用户状态
                user_id = data.get('user_id')
                is_active = data.get('is_active')
                
                try:
                    user = User.objects.get(id=user_id)
                    user.is_active = is_active
                    user.save()
                    
                    status_text = '激活' if is_active else '禁用'
                    return JsonResponse({
                        'message': f'用户 {user.name} 已{status_text}',
                        'user': {
                            'id': str(user.id),
                            'name': user.name,
                            'email': user.email,
                            'is_active': user.is_active
                        }
                    })
                except User.DoesNotExist:
                    return JsonResponse({'error': '用户不存在'}, status=404)
            
            elif action == 'delete_user':
                # 删除用户
                user_id = data.get('user_id')
                confirm = data.get('confirm', False)
                
                if not confirm:
                    return JsonResponse({'error': '请确认删除操作'}, status=400)
                
                try:
                    user = User.objects.get(id=user_id)
                    user_name = user.name
                    user_email = user.email
                    
                    # 删除用户相关的所有数据
                    # 1. 删除Token消费记录
                    TokenConsumption.objects.filter(user=user).delete()
                    
                    # 2. 删除聊天会话和消息（级联删除）
                    ChatSession.objects.filter(user=user).delete()
                    
                    # 3. 删除邮箱验证记录
                    EmailVerification.objects.filter(user=user).delete()
                    
                    # 4. 删除密码重置记录
                    PasswordReset.objects.filter(user=user).delete()
                    
                    # 5. 使用安全删除函数（避免Django的表名问题）
                    if not safe_delete_user(user):
                        logger.error(f"用户 {user.email} 删除失败")
                        return JsonResponse({
                            'error': f'用户 {user_name} ({user_email}) 删除失败，请检查外键约束或数据完整性'
                        }, status=500)
                    
                    logger.info(f"管理员删除用户: {user_name} ({user_email})")
                    
                    return JsonResponse({
                        'message': f'用户 {user_name} ({user_email}) 及其所有数据已删除',
                        'deleted_user': {
                            'name': user_name,
                            'email': user_email
                        }
                    })
                except User.DoesNotExist:
                    return JsonResponse({'error': '用户不存在'}, status=404)
            
            else:
                return JsonResponse({'error': '未知操作'}, status=400)
                
        except Exception as e:
            logger.error(f"用户管理操作失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)
    
    elif request.method == 'DELETE':
        try:
            # 从URL参数或请求体获取用户ID
            user_id = request.GET.get('user_id') or request.GET.get('id')
            
            # 如果GET参数中没有，尝试从请求体中获取
            if not user_id and request.body:
                try:
                    data = json.loads(request.body)
                    user_id = data.get('user_id') or data.get('id')
                except:
                    pass
            
            if not user_id:
                return JsonResponse({'error': '缺少用户ID参数'}, status=400)
                
            try:
                user = User.objects.get(id=user_id)
                user_name = user.name
                user_email = user.email
                
                # 删除用户相关的所有数据
                # 1. 删除Token消费记录
                TokenConsumption.objects.filter(user=user).delete()
                
                # 2. 删除聊天会话和消息（级联删除）
                ChatSession.objects.filter(user=user).delete()
                
                # 3. 删除邮箱验证记录
                EmailVerification.objects.filter(user=user).delete()
                
                # 4. 删除密码重置记录
                PasswordReset.objects.filter(user=user).delete()
                
                # 5. 使用安全删除函数（避免Django的表名问题）
                if not safe_delete_user(user):
                    logger.error(f"用户 {user.email} 删除失败")
                    return JsonResponse({
                        'error': f'用户 {user_name} ({user_email}) 删除失败，请检查外键约束或数据完整性'
                    }, status=500)
                
                logger.info(f"管理员删除用户: {user_name} ({user_email})")
                
                return JsonResponse({
                    'message': f'用户 {user_name} ({user_email}) 及其所有数据已删除',
                    'deleted_user': {
                        'name': user_name,
                        'email': user_email
                    }
                })
            except User.DoesNotExist:
                return JsonResponse({'error': '用户不存在'}, status=404)
                
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)


@api_token_required
@csrf_exempt
@require_http_methods(["POST"])
def export_messages(request):
    """导出消息数据为JSON格式"""
    try:
        data = json.loads(request.body)
        message_ids = data.get('message_ids', [])
        
        if not message_ids:
            return JsonResponse({'error': '请选择要导出的消息'}, status=400)
        
        # 获取选中的消息
        messages = ChatMessage.objects.filter(
            id__in=message_ids
        ).select_related('session__user').order_by('created_at')
        
        export_data = []
        for message in messages:
            session = message.session
            user = session.user if session else None
            
            # 获取对应的用户提示（如果这是assistant消息）
            user_prompt = None
            if message.type == 'assistant':
                prev_user_msg = ChatMessage.objects.filter(
                    session=session,
                    type='user',
                    created_at__lt=message.created_at
                ).order_by('-created_at').first()
                
                if prev_user_msg:
                    user_prompt = prev_user_msg.content
            
            export_data.append({
                'message_id': str(message.id),
                'session_id': str(session.id),
                'session_title': session.title,
                'user': {
                    'id': str(user.id) if user else None,
                    'name': user.name if user else 'Anonymous',
                    'email': user.email if user else None,
                },
                'message_type': message.type,
                'content': message.content,
                'user_prompt': user_prompt,
                'image_url': message.image_url,
                'video_url': message.video_url,
                'has_media': bool(message.image_url or message.video_url),
                'created_at': message.created_at.isoformat(),
                'updated_at': message.updated_at.isoformat() if hasattr(message, 'updated_at') else None,
            })
        
        # 生成导出文件名
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        filename = f'messages_export_{timestamp}.json'
        
        response = JsonResponse({
            'filename': filename,
            'data': export_data,
            'total_count': len(export_data),
            'export_time': timezone.now().isoformat()
        })
        
        # 设置下载头
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        logger.error(f"导出消息失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@api_token_required
@require_http_methods(["GET"])
def filter_messages(request):
    """过滤消息数据"""
    try:
        # 获取过滤参数
        keyword = request.GET.get('keyword', '')
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')
        message_type = request.GET.get('type', '')
        has_media = request.GET.get('has_media', '')
        user_email = request.GET.get('user_email', '')
        session_id = request.GET.get('session_id', '')  # 新增：session_id过滤
        limit = int(request.GET.get('limit', 100))
        
        # 构建查询
        messages_query = ChatMessage.objects.select_related('session__user').order_by('-created_at')
        
        # 关键字过滤
        if keyword:
            messages_query = messages_query.filter(content__icontains=keyword)
        
        # 日期范围过滤
        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                messages_query = messages_query.filter(created_at__gte=start_dt)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                messages_query = messages_query.filter(created_at__lte=end_dt)
            except ValueError:
                pass
        
        # 消息类型过滤
        if message_type:
            messages_query = messages_query.filter(type=message_type)
        
        # 媒体过滤
        if has_media == 'true':
            messages_query = messages_query.filter(
                Q(image_url__isnull=False) | Q(video_url__isnull=False)
            )
        elif has_media == 'false':
            messages_query = messages_query.filter(
                image_url__isnull=True, video_url__isnull=True
            )
        
        # 用户邮箱过滤
        if user_email:
            messages_query = messages_query.filter(session__user__email__icontains=user_email)
        
        # 新增：session_id过滤
        if session_id:
            messages_query = messages_query.filter(session__id=session_id)
        
        # 限制结果数量
        messages = messages_query[:limit]
        
        messages_data = []
        for message in messages:
            session = message.session
            user = session.user if session else None
            
            # 获取对应的用户提示（如果这是assistant消息）
            user_prompt = None
            if message.type == 'assistant':
                prev_user_msg = ChatMessage.objects.filter(
                    session=session,
                    type='user',
                    created_at__lt=message.created_at
                ).order_by('-created_at').first()
                
                if prev_user_msg:
                    user_prompt = prev_user_msg.content[:200] + '...' if len(prev_user_msg.content) > 200 else prev_user_msg.content
            
            # 处理显示内容
            display_content = message.content
            if len(display_content) > 150:
                display_content = display_content[:150] + '...'
            
            messages_data.append({
                'id': str(message.id),
                'type': message.type,
                'content': display_content,
                'content_full': message.content,
                'user_prompt': user_prompt,
                'image_url': message.image_url,
                'video_url': message.video_url,
                'has_media': bool(message.image_url or message.video_url),
                'media_type': 'image' if message.image_url else ('video' if message.video_url else None),
                'created_at': message.created_at.isoformat(),
                'session': {
                    'id': str(session.id),
                    'title': session.title
                },
                'session_id': str(session.id),  # 新增：专门的session_id字段
                'user': {
                    'id': str(user.id) if user else None,
                    'username': user.username if user else 'Anonymous',
                    'email': user.email if user else None,
                    'name': getattr(user, 'name', user.username) if user else 'Anonymous',
                    'avatar': getattr(user, 'avatar', None) if user else None
                }
            })
        
        return JsonResponse({
            'messages': messages_data,
            'total_count': len(messages_data),
            'filters': {
                'keyword': keyword,
                'start_date': start_date,
                'end_date': end_date,
                'type': message_type,
                'has_media': has_media,
                'user_email': user_email,
                'session_id': session_id,  # 新增：返回session_id过滤条件
                'limit': limit
            }
        })
        
    except Exception as e:
        logger.error(f"过滤消息失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)

def get_user_statistics(start_date, end_date, period):
    """获取用户统计数据"""
    try:
        # 选择时间截断函数
        trunc_func = {
            'day': TruncDate,
            'week': TruncWeek,
            'month': TruncMonth,
            'year': TruncYear
        }.get(period, TruncDate)
        
        # 总注册用户数 - 使用User表
        total_users = User.objects.count()
        
        # 付费用户数 - 暂时设为0，后续可以基于其他标准统计
        # 可以基于 tokens > 0 或其他标准
        paid_users = User.objects.filter(tokens__gt=0).count()
        
        # 当前在线用户数（最近15分钟有活动的用户）
        online_threshold = timezone.now() - timedelta(minutes=15)
        
        # 基于ChatSession表查询最近活跃用户
        online_users_by_sessions = User.objects.filter(
            chatsession__updated_at__gte=online_threshold
        ).distinct().count()
        
        # 如果TokenConsumption表存在，也加入统计
        online_users_by_consumption = 0
        try:
            online_users_by_consumption = User.objects.filter(
                tokenconsumption__created_at__gte=online_threshold
            ).distinct().count()
        except:
            pass
        
        # 取两者的最大值作为在线用户数
        online_users = max(online_users_by_consumption, online_users_by_sessions)
        
        # 如果还是0，检查最近1小时内有活动的用户
        if online_users == 0:
            recent_threshold = timezone.now() - timedelta(hours=1)
            online_users = User.objects.filter(
                Q(chatsession__updated_at__gte=recent_threshold) |
                Q(last_login__gte=recent_threshold)
            ).distinct().count()
        
        # 新注册用户（按时间段）- 使用created_at字段
        new_users = User.objects.filter(
            created_at__range=[start_date, end_date]
        ).annotate(
            period=trunc_func('created_at')
        ).values('period').annotate(
            count=Count('id')
        ).order_by('period')
        
        # 活跃用户（有聊天会话的用户）
        active_users = User.objects.filter(
            chatsession__updated_at__range=[start_date, end_date]
        ).distinct().count()
        
        # 如果TokenConsumption表存在，基于它统计活跃用户
        try:
            token_active_users = User.objects.filter(
                tokenconsumption__created_at__range=[start_date, end_date]
            ).distinct().count()
            active_users = max(active_users, token_active_users)
        except:
            pass
        
        # 聊天最多的用户（Top 5）- 基于ChatSession表
        top_active_users = User.objects.filter(
            chatsession__updated_at__range=[start_date, end_date]
        ).annotate(
            session_count=Count('chatsession'),
            message_count=Count('chatsession__messages')
        ).order_by('-message_count')[:5]
        
        top_users_data = []
        for user in top_active_users:
            top_users_data.append({
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'message_count': user.message_count or 0,
                'session_count': user.session_count or 0,
                'last_active': user.last_login.isoformat() if user.last_login else None
            })
        
        # 每日用户在线时段分布（24小时）- 基于ChatMessage表
        hourly_distribution = []
        for hour in range(24):
            # 查询过去7天该小时的活跃用户数
            active_count = 0
            try:
                # 使用ChatMessage表统计
                active_count = ChatMessage.objects.filter(
                    created_at__gte=timezone.now() - timedelta(days=7),
                    created_at__time__hour=hour
                ).values('session__user').distinct().count()
            except:
                pass
            
            hourly_distribution.append({
                'hour': hour,
                'active_users': active_count
            })
        
        # 用户增长趋势
        growth_data = []
        for item in new_users:
            growth_data.append({
                'date': item['period'].isoformat() if item['period'] else None,
                'new_users': item['count'],
                'period': period
            })
        
        return {
            'total_users': total_users,
            'paid_users': paid_users,
            'online_users': online_users,
            'new_users_count': sum(item['count'] for item in new_users),
            'active_users': active_users,
            'conversion_rate': round((paid_users / total_users * 100) if total_users > 0 else 0, 2),
            'growth_data': growth_data,
            'top_active_users': top_users_data,
            'hourly_distribution': hourly_distribution
        }
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        return {
            'total_users': 0,
            'paid_users': 0,
            'online_users': 0,
            'new_users_count': 0,
            'active_users': 0,
            'conversion_rate': 0,
            'growth_data': [],
            'top_active_users': [],
            'hourly_distribution': []
        }

def get_chat_statistics(start_date, end_date, period):
    """获取聊天统计数据 - 基于ChatMessage表"""
    try:
        # 选择时间截断函数
        trunc_func = {
            'day': TruncDate,
            'week': TruncWeek,
            'month': TruncMonth,
            'year': TruncYear
        }.get(period, TruncDate)
        
        # 总会话数 - 使用ChatSession表
        total_sessions = ChatSession.objects.count()
        
        # 总消息数 - 使用ChatMessage表
        total_messages = ChatMessage.objects.count()
        
        # 时间范围内的会话
        period_sessions = ChatSession.objects.filter(
            created_at__range=[start_date, end_date]
        ).annotate(
            period=trunc_func('created_at')
        ).values('period').annotate(
            count=Count('id')
        ).order_by('period')
        
        # 时间范围内的消息
        period_messages = ChatMessage.objects.filter(
            created_at__range=[start_date, end_date]
        ).annotate(
            period=trunc_func('created_at')
        ).values('period').annotate(
            count=Count('id')
        ).order_by('period')
        
        # 按内容类型统计生成数量 - 基于ChatMessage表
        image_generations = ChatMessage.objects.filter(
            created_at__range=[start_date, end_date],
            image_url__isnull=False
        ).count()
        
        video_generations = ChatMessage.objects.filter(
            created_at__range=[start_date, end_date],
            video_url__isnull=False
        ).count()
        
        # 如果TokenConsumption表存在，使用它来统计
        try:
            token_image_generations = TokenConsumption.objects.filter(
                created_at__range=[start_date, end_date],
                service_type__in=['image_gen', 'img_edit']
            ).count()
            
            token_video_generations = TokenConsumption.objects.filter(
                created_at__range=[start_date, end_date],
                service_type__in=['video_gen', 'img2video']
            ).count()
            
            # 使用较大值
            image_generations = max(image_generations, token_image_generations)
            video_generations = max(video_generations, token_video_generations)
        except:
            pass
        
        # 活跃度趋势 - 基于ChatMessage表
        activity_data = []
        message_by_period = ChatMessage.objects.filter(
            created_at__range=[start_date, end_date]
        ).annotate(
            period=trunc_func('created_at')
        ).values('period').annotate(
            count=Count('id')
        ).order_by('period')
        
        for item in message_by_period:
            activity_data.append({
                'date': item['period'].isoformat() if item['period'] else None,
                'messages': item['count'],
                'period': period
            })
        
        return {
            'total_sessions': total_sessions,
            'total_messages': total_messages,
            'period_sessions': sum(item['count'] for item in period_sessions),
            'period_messages': sum(item['count'] for item in period_messages),
            'image_generations': image_generations,
            'video_generations': video_generations,
            'activity_data': activity_data,
            'avg_messages_per_session': round(
                total_messages / total_sessions if total_sessions > 0 else 0, 2
            )
        }
        
    except Exception as e:
        logger.error(f"获取聊天统计失败: {e}")
        return {
            'total_sessions': 0,
            'total_messages': 0,
            'period_sessions': 0,
            'period_messages': 0,
            'image_generations': 0,
            'video_generations': 0,
            'activity_data': [],
            'avg_messages_per_session': 0
        }

def get_api_statistics():
    """获取API状态统计 - 基于TokenConsumption表"""
    try:
        # 从缓存获取API状态信息
        api_status = cache.get('api_status', {})
        
        # API使用量统计 - 基于TokenConsumption表的service_type
        total_consumptions = TokenConsumption.objects.count()
        
        # 按服务类型统计API调用
        service_stats = TokenConsumption.objects.values('service_type').annotate(
            calls=Count('id'),
            tokens_used=Sum('tokens_used')
        )
        
        # 映射服务类型到API类型
        api_usage_stats = {
            'text': {'calls': 0, 'percentage': 0},
            'image': {'calls': 0, 'percentage': 0},
            'video': {'calls': 0, 'percentage': 0}
        }
        
        for stat in service_stats:
            service_type = stat['service_type']
            calls = stat['calls']
            
            if service_type in ['chat_api']:
                api_usage_stats['text']['calls'] += calls
            elif service_type in ['image_gen', 'img_edit']:
                api_usage_stats['image']['calls'] += calls
            elif service_type in ['video_gen', 'img2video', 'video_keyframe']:
                api_usage_stats['video']['calls'] += calls
        
        # 计算百分比
        total_api_calls = sum(api['calls'] for api in api_usage_stats.values())
        for api_type in api_usage_stats:
            if total_api_calls > 0:
                api_usage_stats[api_type]['percentage'] = round(
                    (api_usage_stats[api_type]['calls'] / total_api_calls * 100), 1
                )
        
        # 获取告警历史
        alert_history = AlertService.get_alert_history(20)
        api_alerts = {}
        for alert in alert_history:
            api_name = alert.get('api_name', 'unknown')
            if api_name not in api_alerts:
                api_alerts[api_name] = 0
            api_alerts[api_name] += 1
        
        return {
            'api_status': api_status,
            'api_usage_stats': api_usage_stats,
            'api_alerts': api_alerts,
            'total_apis': len(api_status),
            'healthy_apis': len([s for s in api_status.values() if s.get('status') == 'healthy']),
            'warning_apis': len([s for s in api_status.values() if s.get('status') == 'warning']),
            'error_apis': len([s for s in api_status.values() if s.get('status') == 'error']),
            'avg_response_time': round(
                sum(s.get('response_time', 0) for s in api_status.values()) / len(api_status)
                if api_status else 0, 2
            ),
            'total_api_calls': total_api_calls
        }
        
    except Exception as e:
        logger.error(f"获取API统计失败: {e}")
        return {
            'api_status': {},
            'api_usage_stats': {'text': {'calls': 0, 'percentage': 0}, 'image': {'calls': 0, 'percentage': 0}, 'video': {'calls': 0, 'percentage': 0}},
            'api_alerts': {},
            'total_apis': 0,
            'healthy_apis': 0,
            'warning_apis': 0,
            'error_apis': 0,
            'avg_response_time': 0,
            'total_api_calls': 0
        }

def get_system_statistics():
    """获取系统统计信息"""
    try:
        import psutil
        import os
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used = round(memory.used / (1024**3), 2)  # GB
        memory_total = round(memory.total / (1024**3), 2)  # GB
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_percent = round((disk.used / disk.total) * 100, 2)
        disk_used = round(disk.used / (1024**3), 2)  # GB
        disk_total = round(disk.total / (1024**3), 2)  # GB
        
        # 网络统计
        network = psutil.net_io_counters()
        
        # 进程信息
        process_count = len(psutil.pids())
        
        return {
            'cpu_percent': cpu_percent,
            'memory': {
                'percent': memory_percent,
                'used': memory_used,
                'total': memory_total
            },
            'disk': {
                'percent': disk_percent,
                'used': disk_used,
                'total': disk_total
            },
            'network': {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            },
            'process_count': process_count,
            'uptime': round((timezone.now().timestamp() - psutil.boot_time()) / 3600, 2)  # hours
        }
        
    except ImportError:
        # 如果psutil不可用，返回模拟数据
        return {
            'cpu_percent': 25.5,
            'memory': {'percent': 68.2, 'used': 5.4, 'total': 8.0},
            'disk': {'percent': 45.8, 'used': 91.6, 'total': 200.0},
            'network': {'bytes_sent': 1024000, 'bytes_recv': 2048000, 'packets_sent': 1500, 'packets_recv': 2800},
            'process_count': 156,
            'uptime': 72.5
        }
    except Exception as e:
        logger.error(f"获取系统统计失败: {e}")
        return {
            'cpu_percent': 0,
            'memory': {'percent': 0, 'used': 0, 'total': 0},
            'disk': {'percent': 0, 'used': 0, 'total': 0},
            'network': {'bytes_sent': 0, 'bytes_recv': 0, 'packets_sent': 0, 'packets_recv': 0},
            'process_count': 0,
            'uptime': 0
        }

@api_token_required
@require_http_methods(["GET"])
def user_details(request):
    """用户详情列表"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')
        
        # 构建查询
        users_query = User.objects.all()
        
        if search:
            users_query = users_query.filter(
                Q(username__icontains=search) |
                Q(email__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search)
            )
        
        # 分页
        total_count = users_query.count()
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        users = users_query[start_index:end_index]
        
        # 构建用户数据
        users_data = []
        for user in users:
            # 获取用户的聊天统计
            session_count = ChatSession.objects.filter(user=user).count()
            message_count = ChatMessage.objects.filter(session__user=user).count()
            last_activity = ChatSession.objects.filter(user=user).order_by('-updated_at').first()
            
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'session_count': session_count,
                'message_count': message_count,
                'last_activity': last_activity.updated_at.isoformat() if last_activity else None,
                'is_paid': False  # 暂时模拟，等支付功能完成后更新
            })
        
        return JsonResponse({
            'users': users_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@api_token_required
@require_http_methods(["GET"])
def chat_details(request):
    """聊天详情列表"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        user_id = request.GET.get('user_id')
        
        # 构建查询
        sessions_query = ChatSession.objects.select_related('user').prefetch_related('messages')
        
        if user_id:
            sessions_query = sessions_query.filter(user_id=user_id)
        
        # 分页
        total_count = sessions_query.count()
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        sessions = sessions_query.order_by('-created_at')[start_index:end_index]
        
        # 构建会话数据
        sessions_data = []
        for session in sessions:
            messages = session.messages.all()
            last_message = messages.last()
            
            sessions_data.append({
                'id': str(session.id),
                'title': session.title,
                'user': {
                    'id': session.user.id if session.user else None,
                    'username': session.user.username if session.user else 'Anonymous',
                    'email': session.user.email if session.user else None
                },
                'message_count': messages.count(),
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'last_message': {
                    'content': last_message.content[:100] + '...' if last_message and len(last_message.content) > 100 else (last_message.content if last_message else ''),
                    'type': last_message.type if last_message else None,
                    'created_at': last_message.created_at.isoformat() if last_message else None
                } if last_message else None,
                'has_media': any(msg.image_url or msg.video_url for msg in messages)
            })
        
        return JsonResponse({
            'sessions': sessions_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取聊天详情失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@api_token_required
@require_http_methods(["GET"])
def message_details(request):
    """获取会话的详细消息列表"""
    try:
        session = ChatSession.objects.select_related('user').get(id=session_id)
        messages = ChatMessage.objects.filter(session=session).order_by('created_at')
        
        messages_data = []
        for message in messages:
            messages_data.append({
                'id': str(message.id),
                'type': message.type,
                'content': message.content,
                'image_url': message.image_url,
                'video_url': message.video_url,
                'metadata': message.metadata,
                'created_at': message.created_at.isoformat(),
                'session': {
                    'id': str(session.id),
                    'title': session.title,
                    'user': {
                        'id': str(session.user.id) if session.user else None,
                        'username': session.user.username if session.user else 'Anonymous',
                        'email': session.user.email if session.user else None,
                        'name': getattr(session.user, 'name', session.user.username) if session.user else 'Anonymous'
                    }
                }
            })
        
        return JsonResponse({
            'session': {
                'id': str(session.id),
                'title': session.title,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'user': {
                    'id': str(session.user.id) if session.user else None,
                    'username': session.user.username if session.user else 'Anonymous',
                    'email': session.user.email if session.user else None,
                    'name': getattr(session.user, 'name', session.user.username) if session.user else 'Anonymous'
                }
            },
            'messages': messages_data,
            'message_count': len(messages_data)
        })
        
    except ChatSession.DoesNotExist:
        return JsonResponse({'error': '会话不存在'}, status=404)
    except Exception as e:
        logger.error(f"获取消息详情失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@api_token_required
@require_http_methods(["GET"])
def message_history(request):
    """获取最近的消息历史，用于dashboard展示（成对显示用户提示和AI回复）"""
    try:
        limit = int(request.GET.get('limit', 50))
        message_type = request.GET.get('type', '')  # user, assistant, system
        has_media = request.GET.get('has_media', '')  # true/false
        session_id = request.GET.get('session_id', '')  # 新增：按session_id过滤
        
        # 构建查询 - 优先获取assistant消息（包含生成结果）
        base_query = ChatMessage.objects.select_related('session__user').order_by('-created_at')
        
        if message_type == 'user':
            messages_query = base_query.filter(type='user')
        elif message_type == 'assistant':
            messages_query = base_query.filter(type='assistant')
        elif message_type == 'system':
            messages_query = base_query.filter(type='system')
        else:
            # 默认只显示assistant消息（生成结果）
            messages_query = base_query.filter(type='assistant')
            
        if has_media == 'true':
            messages_query = messages_query.filter(
                Q(image_url__isnull=False) | Q(video_url__isnull=False)
            )
        elif has_media == 'false':
            messages_query = messages_query.filter(
                image_url__isnull=True, video_url__isnull=True
            )
        
        # 新增：session_id过滤
        if session_id:
            messages_query = messages_query.filter(session__id=session_id)
        
        messages = messages_query[:limit]
        
        messages_data = []
        for message in messages:
            session = message.session
            user = session.user if session else None
            
            # 查找对应的用户提示（前一条用户消息）
            user_prompt = None
            if message.type == 'assistant':
                user_message = ChatMessage.objects.filter(
                    session=session,
                    type='user',
                    created_at__lt=message.created_at
                ).order_by('-created_at').first()
                if user_message:
                    user_prompt = user_message.content
            
            # 组合显示内容
            if message.type == 'assistant' and user_prompt:
                # 显示：用户提示 → AI回复
                display_content = f"👤 {user_prompt[:100]}{'...' if len(user_prompt) > 100 else ''}\n🤖 {message.content[:100]}{'...' if len(message.content) > 100 else ''}"
            else:
                # 单独显示
                display_content = message.content[:200] + '...' if len(message.content) > 200 else message.content
            
            messages_data.append({
                'id': str(message.id),
                'type': message.type,
                'content': display_content,
                'content_full': message.content,
                'user_prompt': user_prompt,  # 添加用户提示
                'image_url': message.image_url,
                'video_url': message.video_url,
                'has_media': bool(message.image_url or message.video_url),
                'media_type': 'image' if message.image_url else ('video' if message.video_url else None),
                'created_at': message.created_at.isoformat(),
                'session': {
                    'id': str(session.id),
                    'title': session.title
                },
                'session_id': str(session.id),  # 新增：专门的session_id字段
                'user': {
                    'id': str(user.id) if user else None,
                    'username': user.username if user else 'Anonymous',
                    'email': user.email if user else None,
                    'name': getattr(user, 'name', user.username) if user else 'Anonymous',
                    'avatar': getattr(user, 'avatar', None) if user else None
                }
            })
        
        return JsonResponse({
            'messages': messages_data,
            'total_count': len(messages_data),
            'filters': {
                'type': message_type,
                'has_media': has_media,
                'session_id': session_id,  # 新增：返回session_id过滤条件
                'limit': limit
            }
        })
        
    except Exception as e:
        logger.error(f"获取消息历史失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@api_token_required
@csrf_exempt
@require_http_methods(["GET", "POST", "DELETE"])
def beta_application_management(request):
    """内测申请管理"""
    if request.method == 'GET':
        try:
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            search = request.GET.get('search', '')
            status_filter = request.GET.get('status', '')  # PENDING, APPROVED, REJECTED, INVITED
            source_filter = request.GET.get('source', '')
            
            # 构建查询
            applications_query = BetaApplication.objects.all()
            
            if search:
                applications_query = applications_query.filter(
                    Q(email__icontains=search) |
                    Q(name__icontains=search) |
                    Q(company__icontains=search) |
                    Q(purpose__icontains=search)
                )
            
            if status_filter:
                applications_query = applications_query.filter(status=status_filter)
            
            if source_filter:
                applications_query = applications_query.filter(source=source_filter)
            
            # 分页
            total_count = applications_query.count()
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            applications = applications_query.order_by('-created_at')[start_index:end_index]
            
            # 构建申请数据
            applications_data = []
            for app in applications:
                applications_data.append({
                    'id': str(app.id),
                    'email': app.email,
                    'name': app.name or '',
                    'company': app.company or '',
                    'purpose': app.purpose,
                    'source': app.source,
                    'source_display': app.get_source_display(),
                    'source_detail': app.source_detail or '',
                    'status': app.status,
                    'status_display': app.get_status_display(),
                    'admin_notes': app.admin_notes or '',
                    'invitation_code': app.invitation_code or '',
                    'invitation_sent_at': app.invitation_sent_at.isoformat() if app.invitation_sent_at else None,
                    'invitation_used_at': app.invitation_used_at.isoformat() if app.invitation_used_at else None,
                    'created_at': app.created_at.isoformat(),
                    'reviewed_at': app.reviewed_at.isoformat() if app.reviewed_at else None,
                    'reviewed_by': app.reviewed_by.name if app.reviewed_by else None,
                })
            
            # 获取统计数据
            stats = {
                'total': BetaApplication.objects.count(),
                'pending': BetaApplication.objects.filter(status='PENDING').count(),
                'approved': BetaApplication.objects.filter(status='APPROVED').count(),
                'rejected': BetaApplication.objects.filter(status='REJECTED').count(),
                'invited': BetaApplication.objects.filter(status='INVITED').count(),
            }
            
            # 获取来源统计
            source_stats = {}
            for choice in BetaApplication.SOURCE_CHOICES:
                source_code = choice[0]
                source_name = choice[1]
                count = BetaApplication.objects.filter(source=source_code).count()
                if count > 0:
                    source_stats[source_name] = count
            
            return JsonResponse({
                'applications': applications_data,
                'total': total_count,
                'page': page,
                'page_size': page_size,
                'pages': (total_count + page_size - 1) // page_size,
                'stats': stats,
                'source_stats': source_stats,
                'status_choices': BetaApplication.STATUS_CHOICES,
                'source_choices': BetaApplication.SOURCE_CHOICES,
            })
        
        except Exception as e:
            logger.error(f"获取内测申请列表失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')
            application_id = data.get('application_id')
            
            if not application_id:
                return JsonResponse({'error': '申请ID不能为空'}, status=400)
            
            try:
                application = BetaApplication.objects.get(id=application_id)
            except BetaApplication.DoesNotExist:
                return JsonResponse({'error': '申请记录不存在'}, status=404)
            
            if action == 'approve':
                application.status = 'APPROVED'
                application.reviewed_by = request.user
                application.reviewed_at = timezone.now()
                application.admin_notes = data.get('admin_notes', '')
                application.save()
                
                return JsonResponse({
                    'success': True,
                    'message': f'已批准申请: {application.email}'
                })
            
            elif action == 'reject':
                application.status = 'REJECTED'
                application.reviewed_by = request.user
                application.reviewed_at = timezone.now()
                application.admin_notes = data.get('admin_notes', '')
                application.save()
                
                return JsonResponse({
                    'success': True,
                    'message': f'已拒绝申请: {application.email}'
                })
            
            elif action == 'send_invitation':
                logger.info(f"开始处理邀请发送请求: {application.email}, 状态: {application.status}")
                
                if application.status != 'APPROVED':
                    return JsonResponse({'error': '只能给已批准的申请发送邀请'}, status=400)
                
                # 检查用户是否已存在
                if User.objects.filter(email=application.email).exists():
                    return JsonResponse({'error': '该邮箱已注册用户账户'}, status=400)
                
                # 生成随机密码
                import secrets
                import string
                password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
                
                # 创建用户账户和发送邮件
                try:
                    # 创建用户账户
                    user = User.objects.create(
                        email=application.email,
                        username=application.email,  # 使用邮箱作为用户名
                        name=application.name or application.email.split('@')[0],
                        password=make_password(password),
                        is_email_verified=True,  # 管理员邀请默认验证邮箱
                        tokens=8000,  # 默认2300代币
                        tokens_expires_at=timezone.now() + timezone.timedelta(days=30),  # 30天有效期
                        current_plan='TRIAL',
                        invitation_type='BETA_APPLICATION',
                        is_active=True
                    )
                    
                    # 更新申请状态
                    application.status = 'INVITED'
                    application.invitation_sent_at = timezone.now()
                    application.save()
                
                    # 发送邮件
                    from django.core.mail import send_mail
                    from django.conf import settings
                    
                    subject = 'Welcome to MirageMakers AI Beta - Your Account Details'
                    
                    email_content = f"""Dear {application.name or 'User'},

Congratulations! Your beta application for MirageMakers AI has been approved and your account has been created.

Your login credentials:
Email: {application.email}
Password: {password}

You can now login directly at: https://miragemakers.ai/auth/login

Your account includes:
- 8000 trial tokens (valid for 30 days)
- Advanced AI generation capabilities
- Premium features access
- Direct support from our team
- Early access to new features

Please keep your login credentials secure. You can change your password after logging in.

If you have any questions, please don't hesitate to contact our support team.

Best regards,
The MirageMakers AI Team
"""
                    
                    # 尝试发送邮件
                    email_sent = False
                    email_error = None
                    
                    try:
                        send_mail(
                            subject,
                            email_content,
                            settings.DEFAULT_FROM_EMAIL,
                            [application.email],
                            fail_silently=False,
                        )
                        email_sent = True
                        logger.info(f"邮件发送成功: {application.email}")
                        
                    except Exception as e:
                        email_error = str(e)
                        logger.error(f"邮件发送失败: {application.email}, 错误: {email_error}")
                        # 不删除账户，只记录错误
                    
                    # 无论邮件是否发送成功，都返回成功响应（因为账户已创建）
                    response_data = {
                        'success': True,
                        'message': f'账户已创建: {application.email}',
                        'user_id': str(user.id),
                        'password': password,  # 用于管理员查看
                        'email_sent': email_sent
                    }
                    
                    if email_sent:
                        response_data['message'] += ' (邮件已发送)'
                    else:
                        response_data['message'] += f' (邮件发送失败: {email_error})'
                        response_data['email_error'] = email_error
                        response_data['manual_credentials'] = {
                            'email': application.email,
                            'password': password,
                            'login_url': 'https://miragemakers.ai/auth/login'
                        }
                    
                    return JsonResponse(response_data)
                    
                except Exception as error:
                    # 如果出现任何错误，回滚操作
                    logger.error(f"邀请发送失败: {str(error)}", exc_info=True)
                    
                    try:
                        if 'user' in locals():
                            user.delete()
                            logger.info("已删除创建的用户账户")
                    except Exception as del_error:
                        logger.error(f"删除用户账户失败: {del_error}")
                    
                    try:
                        application.status = 'APPROVED'
                        application.invitation_sent_at = None
                        application.save()
                        logger.info("已回滚申请状态")
                    except Exception as save_error:
                        logger.error(f"回滚申请状态失败: {save_error}")
                    
                    return JsonResponse({
                        'error': f'操作失败: {str(error)}，请稍后重试'
                    }, status=500)
            
            elif action == 'update_notes':
                application.admin_notes = data.get('admin_notes', '')
                application.save()
                
                return JsonResponse({
                    'success': True,
                    'message': '备注已更新'
                })
            
            elif action == 'delete_invitation':
                try:
                    # 如果已经创建了用户账户，也要删除
                    if application.status == 'INVITED':
                        user = User.objects.filter(email=application.email, invitation_type__in=['BETA_APPLICATION', 'ADMIN_INVITATION']).first()
                        if user:
                            # 删除用户相关的所有数据
                            # 1. 删除Token消费记录
                            TokenConsumption.objects.filter(user=user).delete()
                            
                            # 2. 删除聊天会话和消息（级联删除）
                            ChatSession.objects.filter(user=user).delete()
                            
                            # 3. 删除邮箱验证记录
                            EmailVerification.objects.filter(user=user).delete()
                            
                            # 4. 删除密码重置记录
                            PasswordReset.objects.filter(user=user).delete()
                            
                            # 5. 使用安全删除函数（避免Django的表名问题）
                            user_deletion_success = safe_delete_user(user)
                            if not user_deletion_success:
                                logger.error(f"用户 {user.email} 删除失败，但将继续删除申请记录")
                    
                    # 删除申请记录
                    application.delete()
                    
                    # 根据用户删除结果返回适当的消息
                    if 'user_deletion_success' in locals() and not user_deletion_success:
                        return JsonResponse({
                            'success': True,
                            'message': f'已删除邀请: {application.email}，但用户账户删除失败（可能由于外键约束）',
                            'warning': '用户账户可能仍然存在，请手动检查'
                        })
                    else:
                        return JsonResponse({
                            'success': True,
                            'message': f'已删除邀请: {application.email}'
                        })
                    
                except Exception as delete_error:
                    return JsonResponse({
                        'error': f'删除失败: {str(delete_error)}'
                    }, status=500)
            
            else:
                return JsonResponse({'error': '无效的操作'}, status=400)
        
        except Exception as e:
            logger.error(f"处理内测申请操作失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)
    
    elif request.method == 'DELETE':
        try:
            # 从URL参数或请求体获取申请ID
            application_id = request.GET.get('application_id') or request.GET.get('id')
            
            # 如果GET参数中没有，尝试从请求体中获取
            if not application_id and request.body:
                try:
                    data = json.loads(request.body)
                    application_id = data.get('application_id') or data.get('id')
                except:
                    pass
            
            if not application_id:
                return JsonResponse({'error': '缺少申请ID参数'}, status=400)
            
            try:
                application = BetaApplication.objects.get(id=application_id)
                application_email = application.email
                application_name = application.name or application_email
                
                # 如果已经创建了用户账户，也要删除
                if application.status == 'INVITED':
                    user = User.objects.filter(
                        email=application.email, 
                        invitation_type__in=['BETA_APPLICATION', 'ADMIN_INVITATION']
                    ).first()
                    if user:
                        # 删除用户相关的所有数据
                        ChatSession.objects.filter(user=user).delete()
                        TokenConsumption.objects.filter(user=user).delete()
                        EmailVerification.objects.filter(user=user).delete()
                        PasswordReset.objects.filter(user=user).delete()
                        user.delete()
                        logger.info(f"删除了相关的用户账户: {user.email}")
                
                # 删除申请记录
                application.delete()
                logger.info(f"管理员删除Beta申请: {application_name} ({application_email})")
                
                return JsonResponse({
                    'message': f'Beta申请 {application_name} ({application_email}) 已删除',
                    'deleted_application': {
                        'name': application_name,
                        'email': application_email
                    }
                })
                
            except BetaApplication.DoesNotExist:
                return JsonResponse({'error': 'Beta申请不存在'}, status=404)
                
        except Exception as e:
            logger.error(f"删除Beta申请失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)

@api_token_required
@csrf_exempt
@require_http_methods(["POST"])
def send_direct_invitation(request):
    """直接发送邀请"""
    try:
        data = json.loads(request.body)
        email = data.get('email')
        
        if not email:
            return JsonResponse({'error': 'Email is required'}, status=400)
        
        # 验证邮箱格式
        import re
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return JsonResponse({'error': 'Invalid email format'}, status=400)
        
        # 检查用户是否已存在
        if User.objects.filter(email=email).exists():
            return JsonResponse({'error': 'User with this email already exists'}, status=400)
        
        # 生成随机密码
        import secrets
        import string
        password_length = 12
        password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(password_chars) for i in range(password_length))
        
        # 创建用户账号
        
        user = User.objects.create(
            email=email,
            username=email,  # 使用邮箱作为用户名
            name=email.split('@')[0],  # 默认使用邮箱前缀作为用户名
            password=make_password(password),
            is_email_verified=True,  # 管理员邀请默认验证邮箱
            tokens=8000,  # 默认2300代币
            tokens_expires_at=timezone.now() + timezone.timedelta(days=30),  # 30天有效期
            current_plan='TRIAL',
            invitation_type='ADMIN_INVITATION',
            is_active=True
        )
        
        # 创建对应的Beta Application记录以便在管理界面中跟踪
        beta_application = BetaApplication.objects.create(
            email=email,
            name=user.name,
            purpose='Direct invitation from administrator',
            source='ADMIN_INVITATION',
            status='INVITED',
            reviewed_by=request.user,
            reviewed_at=timezone.now(),
            invitation_sent_at=timezone.now(),
            admin_notes='Direct invitation sent by administrator'
        )
        
        # 发送邀请邮件
        from django.core.mail import send_mail
        from django.conf import settings
        
        subject = 'Welcome to MirageMakers AI - Your Beta Access Account'
        
        # 邮件内容
        email_content = f"""Dear {user.name},

Welcome to MirageMakers AI! You have been personally invited to join our beta testing program.

Your account details:
Email: {email}
Password: {password}
Trial Tokens: 8000 (valid for 30 days)

You can now sign in at: https://miragemakers.ai/auth/login

Please keep your login credentials secure. You can change your password after logging in.

If you have any questions, please don't hesitate to contact our support team.

Best regards,
The MirageMakers AI Team
"""
        
        # 发送邮件，如果失败则删除已创建的账户
        try:
            send_mail(
                subject,
                email_content,
                settings.DEFAULT_FROM_EMAIL,
                [email],
                fail_silently=False,
            )
            logger.info(f"直接邀请邮件发送成功: {email}")
            
            return JsonResponse({
                'message': f'Invitation sent successfully to {email}',
                'user_id': str(user.id),
                'beta_application_id': str(beta_application.id),
                'password': password  # 用于管理员查看
            })
            
        except Exception as email_error:
            # 邮件发送失败，删除已创建的用户和Beta Application记录
            user.delete()
            beta_application.delete()
            logger.error(f"直接邀请邮件发送失败，已删除账户: {email}, 错误: {str(email_error)}")
            
            return JsonResponse({
                'error': f'Failed to send invitation email: {str(email_error)}'
            }, status=500)
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': f'Failed to send invitation: {str(e)}'}, status=500) 

def safe_delete_user(user):
    """安全删除用户，使用邮箱作为唯一标识避免UUID格式问题"""
    logger = logging.getLogger(__name__)
    
    try:
        with connection.cursor() as cursor:
            user_email = user.email
            
            logger.info(f"开始删除用户: {user_email}")
            
            # 首先获取用户的数据库ID（用于删除关联表）
            cursor.execute("SELECT id FROM core_user WHERE email = %s", [user_email])
            result = cursor.fetchone()
            
            if not result:
                logger.error(f"用户 {user_email} 不存在")
                return False
            
            user_db_id = result[0]
            logger.info(f"找到用户数据库ID: {user_db_id}")
            
            # 1. 删除AuthToken记录（关键：解决外键约束问题）
            cursor.execute("DELETE FROM authtoken_token WHERE user_id = %s", [user_db_id])
            tokens_deleted = cursor.rowcount
            logger.info(f"删除AuthToken记录: {tokens_deleted} 条")
            
            # 2. 删除用户组关系（使用数据库ID）
            cursor.execute("DELETE FROM auth_user_groups WHERE user_id = %s", [user_db_id])
            groups_deleted = cursor.rowcount
            logger.info(f"删除用户组关系: {groups_deleted} 条")
            
            # 3. 删除用户权限关系（使用数据库ID）
            cursor.execute("DELETE FROM auth_user_user_permissions WHERE user_id = %s", [user_db_id])
            permissions_deleted = cursor.rowcount
            logger.info(f"删除用户权限关系: {permissions_deleted} 条")
            
            # 4. 删除用户记录（使用邮箱）
            cursor.execute("DELETE FROM core_user WHERE email = %s", [user_email])
            user_deleted = cursor.rowcount
            logger.info(f"删除用户记录: {user_deleted} 条")
            
            if user_deleted > 0:
                logger.info(f"用户 {user_email} 删除成功")
                return True
            else:
                logger.error(f"用户 {user_email} 删除失败：未找到匹配记录")
                return False
            
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False 