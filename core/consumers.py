# core/consumers.py
"""
WebSocket消费者 - 处理AI任务的实时状态推送
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from django.contrib.auth.models import AnonymousUser

logger = logging.getLogger(__name__)


class AITaskConsumer(AsyncWebsocketConsumer):
    """
    AI任务WebSocket消费者
    处理用户连接、任务状态推送、断线重连等
    """
    
    async def connect(self):
        """建立WebSocket连接"""
        try:
            # 获取用户信息
            user = self.scope.get("user")
            
            # 检查用户认证
            if isinstance(user, AnonymousUser) or not user.is_authenticated:
                logger.warning("未认证用户尝试建立WebSocket连接")
                await self.close(code=4001)  # 自定义关闭代码：未认证
                return
            
            self.user = user
            self.user_id = str(user.id)
            
            # 创建用户专属的群组名称
            self.group_name = f"ai_task_user_{self.user_id}"
            
            # 加入群组
            await self.channel_layer.group_add(
                self.group_name,
                self.channel_name
            )
            
            # 接受连接
            await self.accept()
            
            logger.info(f"✅ [WebSocket] 用户 {self.user_id} 建立连接，加入群组: {self.group_name}")
            
            # 发送连接成功消息
            await self.send(text_data=json.dumps({
                "type": "connection_established",
                "message": "WebSocket连接已建立",
                "user_id": self.user_id,
                "timestamp": self._get_timestamp()
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 连接建立失败: {e}")
            await self.close(code=4000)  # 内部错误
    
    async def disconnect(self, close_code):
        """断开WebSocket连接"""
        try:
            if hasattr(self, 'group_name'):
                # 离开群组
                await self.channel_layer.group_discard(
                    self.group_name,
                    self.channel_name
                )
                
            logger.info(f"🔌 [WebSocket] 用户 {getattr(self, 'user_id', 'unknown')} 断开连接，代码: {close_code}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 断开连接时发生错误: {e}")
    
    async def receive(self, text_data):
        """接收客户端消息"""
        try:
            data = json.loads(text_data)
            message_type = data.get("type")
            
            logger.debug(f"📨 [WebSocket] 收到消息: {message_type} from user {self.user_id}")
            
            # 处理不同类型的消息
            if message_type == "ping":
                # 心跳检测
                await self.send(text_data=json.dumps({
                    "type": "pong",
                    "timestamp": self._get_timestamp()
                }))
                
            elif message_type == "subscribe_task":
                # 订阅特定任务状态
                task_id = data.get("task_id")
                if task_id:
                    await self._subscribe_task(task_id)
                    
            elif message_type == "unsubscribe_task":
                # 取消订阅任务
                task_id = data.get("task_id")
                if task_id:
                    await self._unsubscribe_task(task_id)
                    
            else:
                logger.warning(f"⚠️ [WebSocket] 未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("❌ [WebSocket] 无效的JSON消息")
        except Exception as e:
            logger.error(f"❌ [WebSocket] 处理消息时发生错误: {e}")
    
    async def ai_task_update(self, event):
        """
        处理AI任务状态更新
        这个方法会被channel_layer.group_send调用
        """
        try:
            # 发送任务更新到客户端
            await self.send(text_data=json.dumps({
                "type": "task_update",
                "data": event["data"],
                "timestamp": self._get_timestamp()
            }))
            
            logger.debug(f"📤 [WebSocket] 发送任务更新: {event['data'].get('task_id')}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务更新失败: {e}")
    
    async def ai_task_progress(self, event):
        """处理AI任务进度更新"""
        try:
            await self.send(text_data=json.dumps({
                "type": "task_progress",
                "data": event["data"],
                "timestamp": self._get_timestamp()
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务进度失败: {e}")
    
    async def ai_task_completed(self, event):
        """处理AI任务完成"""
        try:
            await self.send(text_data=json.dumps({
                "type": "task_completed",
                "data": event["data"],
                "timestamp": self._get_timestamp()
            }))
            
            logger.info(f"✅ [WebSocket] 任务完成推送: {event['data'].get('task_id')}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务完成失败: {e}")
    
    async def ai_task_failed(self, event):
        """处理AI任务失败"""
        try:
            await self.send(text_data=json.dumps({
                "type": "task_failed",
                "data": event["data"],
                "timestamp": self._get_timestamp()
            }))
            
            logger.warning(f"❌ [WebSocket] 任务失败推送: {event['data'].get('task_id')}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务失败失败: {e}")
    
    async def _subscribe_task(self, task_id: str):
        """订阅特定任务的状态更新"""
        try:
            # 可以在这里添加任务订阅逻辑
            # 例如：将用户添加到特定任务的群组中
            task_group = f"task_{task_id}"
            await self.channel_layer.group_add(task_group, self.channel_name)
            
            await self.send(text_data=json.dumps({
                "type": "subscribed",
                "task_id": task_id,
                "message": f"已订阅任务 {task_id} 的状态更新"
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 订阅任务失败: {e}")
    
    async def _unsubscribe_task(self, task_id: str):
        """取消订阅特定任务"""
        try:
            task_group = f"task_{task_id}"
            await self.channel_layer.group_discard(task_group, self.channel_name)
            
            await self.send(text_data=json.dumps({
                "type": "unsubscribed",
                "task_id": task_id,
                "message": f"已取消订阅任务 {task_id}"
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 取消订阅失败: {e}")
    
    def _get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


class AdminTaskConsumer(AsyncWebsocketConsumer):
    """
    管理员专用WebSocket消费者
    用于监控所有任务状态和系统指标
    """
    
    async def connect(self):
        """建立管理员WebSocket连接"""
        try:
            user = self.scope.get("user")
            
            # 检查管理员权限
            if (isinstance(user, AnonymousUser) or 
                not user.is_authenticated or 
                not user.is_staff):
                logger.warning("非管理员用户尝试建立管理员WebSocket连接")
                await self.close(code=4003)  # 权限不足
                return
            
            self.user = user
            self.admin_group = "admin_task_monitor"
            
            # 加入管理员群组
            await self.channel_layer.group_add(
                self.admin_group,
                self.channel_name
            )
            
            await self.accept()
            
            logger.info(f"👑 [WebSocket] 管理员 {user.id} 建立监控连接")
            
            # 发送连接成功消息
            await self.send(text_data=json.dumps({
                "type": "admin_connected",
                "message": "管理员监控连接已建立",
                "timestamp": self._get_timestamp()
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 管理员连接失败: {e}")
            await self.close(code=4000)
    
    async def disconnect(self, close_code):
        """断开管理员连接"""
        try:
            if hasattr(self, 'admin_group'):
                await self.channel_layer.group_discard(
                    self.admin_group,
                    self.channel_name
                )
                
            logger.info(f"👑 [WebSocket] 管理员断开连接，代码: {close_code}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 管理员断开连接错误: {e}")
    
    async def system_stats(self, event):
        """接收系统统计信息"""
        try:
            await self.send(text_data=json.dumps({
                "type": "system_stats",
                "data": event["data"],
                "timestamp": self._get_timestamp()
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送系统统计失败: {e}")
    
    async def all_task_update(self, event):
        """接收所有任务状态更新（管理员专用）"""
        try:
            await self.send(text_data=json.dumps({
                "type": "all_task_update",
                "data": event["data"],
                "timestamp": self._get_timestamp()
            }))
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送全局任务更新失败: {e}")
    
    def _get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat() 