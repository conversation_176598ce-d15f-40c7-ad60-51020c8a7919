# core/websocket_middleware.py
"""
WebSocket认证中间件
处理WebSocket连接的用户认证和授权
"""

import logging
from urllib.parse import parse_qs
from django.contrib.auth.models import AnonymousUser
from django.contrib.auth import get_user_model
from django.db import close_old_connections
from rest_framework.authtoken.models import Token
from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)
User = get_user_model()


class TokenAuthMiddleware(BaseMiddleware):
    """
    Token认证中间件
    从WebSocket连接参数或头部获取token并验证用户身份
    """
    
    async def __call__(self, scope, receive, send):
        # 关闭旧的数据库连接
        close_old_connections()
        
        # 获取用户信息
        scope['user'] = await self.get_user_from_scope(scope)
        
        return await super().__call__(scope, receive, send)
    
    @database_sync_to_async
    def get_user_from_scope(self, scope):
        """
        从WebSocket scope中获取用户信息
        支持多种认证方式：查询参数、headers
        """
        try:
            # 方法1：从查询参数获取token
            query_string = scope.get('query_string', b'')
            query_params = parse_qs(query_string.decode())
            token = query_params.get('token', [None])[0]
            
            logger.debug(f"🔍 [WebSocket Auth] 查询字符串: {query_string}")
            logger.debug(f"🔍 [WebSocket Auth] 解析的查询参数: {query_params}")
            logger.debug(f"🔍 [WebSocket Auth] 提取的token: {token[:8] if token else 'None'}...")
            
            # 方法2：从headers获取token
            if not token:
                headers = dict(scope.get('headers', []))
                auth_header = headers.get(b'authorization', b'').decode()
                
                if auth_header.startswith('Token '):
                    token = auth_header.split(' ')[1]
                elif auth_header.startswith('Bearer '):
                    token = auth_header.split(' ')[1]
            
            # 方法3：从cookie获取token（如果使用cookie认证）
            if not token:
                cookie_header = headers.get(b'cookie', b'').decode()
                if cookie_header:
                    # 解析cookie中的token
                    for cookie in cookie_header.split(';'):
                        if cookie.strip().startswith('auth_token='):
                            token = cookie.split('=')[1].strip()
                            break
            
            if not token:
                logger.debug("🔍 [WebSocket Auth] 未找到认证token")
                return AnonymousUser()
            
            # 验证token
            try:
                token_obj = Token.objects.select_related('user').get(key=token)
                user = token_obj.user
                
                if user.is_active:
                    logger.info(f"✅ [WebSocket Auth] 用户认证成功: {user.email} (ID: {user.id})")
                    return user
                else:
                    logger.warning(f"⚠️ [WebSocket Auth] 用户账户未激活: {user.email}")
                    return AnonymousUser()
                    
            except Token.DoesNotExist:
                logger.warning(f"❌ [WebSocket Auth] 无效的token: {token[:10]}...")
                return AnonymousUser()
                
        except Exception as e:
            logger.error(f"❌ [WebSocket Auth] 认证过程发生错误: {e}")
            return AnonymousUser()


class WebSocketAuthMiddlewareStack:
    """
    WebSocket认证中间件栈
    组合Token认证中间件和Django的AuthMiddleware
    """
    
    def __init__(self, inner):
        self.inner = inner
    
    def __call__(self, scope):
        return TokenAuthMiddleware(self.inner)(scope)


def get_websocket_auth_middleware_stack():
    """
    获取WebSocket认证中间件栈
    用于在routing中配置认证
    """
    # 直接返回Token认证中间件，避免继承AuthMiddlewareStack的兼容性问题
    return TokenAuthMiddleware


class IPWhitelistMiddleware(BaseMiddleware):
    """
    IP白名单中间件（可选）
    限制特定IP访问WebSocket
    """
    
    # 允许的IP地址列表（可以从设置中读取）
    ALLOWED_IPS = [
        '127.0.0.1',
        'localhost',
        '**************',  # 您的服务器IP
        '0.0.0.0',  # 开发环境
    ]
    
    async def __call__(self, scope, receive, send):
        # 获取客户端IP
        client_ip = self.get_client_ip(scope)
        
        # 开发环境跳过IP检查
        from django.conf import settings
        if getattr(settings, 'DEBUG', False):
            logger.debug(f"🔍 [WebSocket IP] 开发环境跳过IP检查: {client_ip}")
            return await super().__call__(scope, receive, send)
        
        # 检查IP白名单
        if client_ip not in self.ALLOWED_IPS:
            logger.warning(f"🚫 [WebSocket IP] IP地址不在白名单中: {client_ip}")
            # 直接关闭连接
            await send({
                'type': 'websocket.close',
                'code': 4003  # 自定义关闭代码：IP被禁止
            })
            return
        
        logger.info(f"✅ [WebSocket IP] IP地址验证通过: {client_ip}")
        return await super().__call__(scope, receive, send)
    
    def get_client_ip(self, scope):
        """获取客户端真实IP地址"""
        headers = dict(scope.get('headers', []))
        
        # 尝试从X-Forwarded-For获取（反向代理情况）
        forwarded_for = headers.get(b'x-forwarded-for', b'').decode()
        if forwarded_for:
            # 取第一个IP（客户端真实IP）
            return forwarded_for.split(',')[0].strip()
        
        # 尝试从X-Real-IP获取
        real_ip = headers.get(b'x-real-ip', b'').decode()
        if real_ip:
            return real_ip
        
        # 从scope获取客户端IP
        client = scope.get('client', ['unknown', 0])
        return client[0] if client else 'unknown'


class RateLimitMiddleware(BaseMiddleware):
    """
    WebSocket连接频率限制中间件
    防止恶意连接攻击
    """
    
    # 简单的内存存储（生产环境建议使用Redis）
    connection_counts = {}
    
    # 每个IP的最大连接数
    MAX_CONNECTIONS_PER_IP = 5
    
    async def __call__(self, scope, receive, send):
        # 获取客户端IP
        client_ip = self.get_client_ip(scope)
        
        # 检查连接数限制
        current_connections = self.connection_counts.get(client_ip, 0)
        
        if current_connections >= self.MAX_CONNECTIONS_PER_IP:
            logger.warning(f"🚫 [WebSocket Rate] IP连接数超限: {client_ip} ({current_connections})")
            await send({
                'type': 'websocket.close',
                'code': 4004  # 自定义关闭代码：连接数超限
            })
            return
        
        # 增加连接计数
        self.connection_counts[client_ip] = current_connections + 1
        logger.debug(f"📊 [WebSocket Rate] IP连接数: {client_ip} -> {self.connection_counts[client_ip]}")
        
        try:
            return await super().__call__(scope, receive, send)
        finally:
            # 连接结束时减少计数
            if client_ip in self.connection_counts:
                self.connection_counts[client_ip] -= 1
                if self.connection_counts[client_ip] <= 0:
                    del self.connection_counts[client_ip]
    
    def get_client_ip(self, scope):
        """获取客户端IP地址"""
        headers = dict(scope.get('headers', []))
        
        # 尝试从X-Forwarded-For获取
        forwarded_for = headers.get(b'x-forwarded-for', b'').decode()
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        # 从scope获取
        client = scope.get('client', ['unknown', 0])
        return client[0] if client else 'unknown' 