# core/websocket_utils.py
"""
WebSocket推送工具 - 用于Celery任务中发送实时消息
"""

import logging
from typing import Dict, Any
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from datetime import datetime

logger = logging.getLogger(__name__)


class WebSocketNotifier:
    """
    WebSocket消息推送器
    提供简单的API用于在Celery任务中发送实时消息
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
        if not self.channel_layer:
            logger.warning("⚠️ [WebSocket] Channel layer未配置，WebSocket消息将被忽略")
    
    def send_task_update(self, user_id: str, task_id: str, status: str, 
                        message: str = None, data: Dict[str, Any] = None):
        """
        发送任务状态更新
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            status: 任务状态 (PENDING, STARTED, PROGRESS, SUCCESS, FAILURE)
            message: 状态消息
            data: 附加数据
        """
        if not self.channel_layer:
            return
            
        try:
            group_name = f"ai_task_user_{user_id}"
            
            update_data = {
                "task_id": task_id,
                "status": status,
                "message": message or f"任务状态更新: {status}",
                "timestamp": datetime.now().isoformat(),
                **(data or {})
            }
            
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    "type": "ai_task_update",
                    "data": update_data
                }
            )
            
            logger.debug(f"📤 [WebSocket] 发送任务更新: {task_id} -> {status}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务更新失败: {e}")
    
    def send_task_progress(self, user_id: str, task_id: str, progress: int, 
                          stage: str = None, message: str = None):
        """
        发送任务进度更新
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            progress: 进度百分比 (0-100)
            stage: 当前阶段描述
            message: 进度消息
        """
        if not self.channel_layer:
            return
            
        try:
            group_name = f"ai_task_user_{user_id}"
            
            progress_data = {
                "task_id": task_id,
                "progress": progress,
                "stage": stage or "处理中",
                "message": message or f"任务进度: {progress}%",
                "timestamp": datetime.now().isoformat()
            }
            
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    "type": "ai_task_progress",
                    "data": progress_data
                }
            )
            
            logger.debug(f"📊 [WebSocket] 发送进度更新: {task_id} -> {progress}%")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送进度更新失败: {e}")
    
    def send_task_completed(self, user_id: str, task_id: str, result: Dict[str, Any]):
        """
        发送任务完成通知
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            result: 任务结果数据
        """
        if not self.channel_layer:
            return
            
        try:
            group_name = f"ai_task_user_{user_id}"
            
            completion_data = {
                "task_id": task_id,
                "status": "SUCCESS",
                "message": "任务完成",
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    "type": "ai_task_completed",
                    "data": completion_data
                }
            )
            
            logger.info(f"✅ [WebSocket] 发送任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务完成失败: {e}")
    
    def send_task_failed(self, user_id: str, task_id: str, error: str, 
                        error_type: str = None):
        """
        发送任务失败通知
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            error: 错误消息
            error_type: 错误类型
        """
        if not self.channel_layer:
            return
            
        try:
            group_name = f"ai_task_user_{user_id}"
            
            error_data = {
                "task_id": task_id,
                "status": "FAILURE",
                "error": error,
                "error_type": error_type or "unknown_error",
                "message": f"任务失败: {error}",
                "timestamp": datetime.now().isoformat()
            }
            
            async_to_sync(self.channel_layer.group_send)(
                group_name,
                {
                    "type": "ai_task_failed",
                    "data": error_data
                }
            )
            
            logger.warning(f"❌ [WebSocket] 发送任务失败: {task_id} - {error}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送任务失败通知失败: {e}")
    
    def send_admin_notification(self, event_type: str, data: Dict[str, Any]):
        """
        发送管理员通知
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if not self.channel_layer:
            return
            
        try:
            admin_group = "admin_task_monitor"
            
            notification_data = {
                "event_type": event_type,
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            
            async_to_sync(self.channel_layer.group_send)(
                admin_group,
                {
                    "type": "all_task_update",
                    "data": notification_data
                }
            )
            
            logger.debug(f"👑 [WebSocket] 发送管理员通知: {event_type}")
            
        except Exception as e:
            logger.error(f"❌ [WebSocket] 发送管理员通知失败: {e}")


# 全局实例
websocket_notifier = WebSocketNotifier()


# 便捷函数，供Celery任务使用
def notify_task_started(user_id: str, task_id: str, message: str = None):
    """任务开始通知"""
    websocket_notifier.send_task_update(
        user_id, task_id, "STARTED", 
        message or "任务已开始处理"
    )


def notify_task_progress(user_id: str, task_id: str, progress: int, 
                        stage: str = None, message: str = None):
    """任务进度通知"""
    websocket_notifier.send_task_progress(
        user_id, task_id, progress, stage, message
    )


def notify_task_completed(user_id: str, task_id: str, result: Dict[str, Any]):
    """任务完成通知"""
    websocket_notifier.send_task_completed(user_id, task_id, result)


def notify_task_failed(user_id: str, task_id: str, error: str, error_type: str = None):
    """任务失败通知"""
    websocket_notifier.send_task_failed(user_id, task_id, error, error_type)


def notify_admin(event_type: str, data: Dict[str, Any]):
    """管理员通知"""
    websocket_notifier.send_admin_notification(event_type, data) 