# Generated by Django 4.2.23 on 2025-07-04 05:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('core', '0001_user_next_renewal_at_user_plan_purchased_at_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, db_table='auth_user_groups', help_text='The groups this user belongs to.', related_name='core_user_set', related_query_name='core_user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AlterField(
            model_name='user',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, db_table='auth_user_user_permissions', help_text='Specific permissions for this user.', related_name='core_user_set', related_query_name='core_user', to='auth.permission', verbose_name='user permissions'),
        ),
    ]
