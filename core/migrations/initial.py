# Generated by Django 4.2.23 on 2025-06-21 13:00
# Complete initial migration for MirageMakers AI system

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        # Create User model
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('avatar', models.URLField(blank=True, null=True, verbose_name='Avatar')),
                ('is_email_verified', models.BooleanField(default=False, verbose_name='Email Verified')),
                ('tokens', models.IntegerField(default=0, verbose_name='Token Balance')),
                ('tokens_expires_at', models.DateTimeField(blank=True, null=True, verbose_name='Tokens Expiry Date')),
                ('current_plan', models.CharField(blank=True, max_length=20, verbose_name='Current Plan')),
                ('total_generations', models.IntegerField(default=0, verbose_name='Total Generations')),
                ('invitation_type', models.CharField(choices=[('NORMAL', 'Normal Registration'), ('BETA_APPLICATION', 'Beta Application'), ('ADMIN_INVITATION', 'Admin Invitation')], default='NORMAL', max_length=20, verbose_name='Invitation Type')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Registration Time')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated Time')),
                ('last_login_at', models.DateTimeField(blank=True, null=True, verbose_name='Last Login Time')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'core_user',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),

        # Create ChatSession model
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(default='New Conversation', max_length=200, verbose_name='Session Title')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Chat Session',
                'verbose_name_plural': 'Chat Sessions',
                'ordering': ['-updated_at'],
            },
        ),

        # Create ChatMessage model
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('user', 'User'), ('assistant', 'Assistant'), ('system', 'System')], max_length=10, verbose_name='Message Type')),
                ('content', models.TextField(verbose_name='Message Content')),
                ('image_url', models.TextField(blank=True, null=True, verbose_name='Image URL')),
                ('video_url', models.TextField(blank=True, null=True, verbose_name='Video URL')),
                ('reference_context', models.JSONField(blank=True, default=dict, help_text='存储引用的上下文信息，如引用类型、位置等', verbose_name='Reference Context')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='Metadata')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('referenced_messages', models.ManyToManyField(blank=True, related_name='referenced_by', to='core.chatmessage', verbose_name='Referenced Messages')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='core.chatsession', verbose_name='Session')),
            ],
            options={
                'verbose_name': 'Chat Message',
                'verbose_name_plural': 'Chat Messages',
                'ordering': ['created_at'],
            },
        ),

        # Create MembershipPlan model
        migrations.CreateModel(
            name='MembershipPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_type', models.CharField(choices=[('TRIAL', 'Trial'), ('BASIC', 'Basic'), ('PREMIUM', 'Premium'), ('ANNUAL', 'Annual')], max_length=20, unique=True, verbose_name='Plan Type')),
                ('name', models.CharField(max_length=100, verbose_name='Plan Name')),
                ('price_usd', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Price (USD)')),
                ('tokens', models.IntegerField(verbose_name='Tokens Included')),
                ('duration_days', models.IntegerField(verbose_name='Duration (Days)')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Membership Plan',
                'verbose_name_plural': 'Membership Plans',
            },
        ),

        # Create PaymentOrder model
        migrations.CreateModel(
            name='PaymentOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_type', models.CharField(max_length=20, verbose_name='Plan Type')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('tokens_to_add', models.IntegerField(verbose_name='Tokens to Add')),
                ('payment_type', models.CharField(choices=[('ONE_TIME', 'One Time'), ('SUBSCRIPTION', 'Subscription')], default='ONE_TIME', max_length=20, verbose_name='Payment Type')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled'), ('REFUNDED', 'Refunded')], default='PENDING', max_length=20, verbose_name='Status')),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Payment Intent ID')),
                ('stripe_session_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Session ID')),
                ('stripe_customer_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Customer ID')),
                ('stripe_subscription_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Subscription ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Payment Order',
                'verbose_name_plural': 'Payment Orders',
                'ordering': ['-created_at'],
            },
        ),

        # Create StripeSubscription model
        migrations.CreateModel(
            name='StripeSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_subscription_id', models.CharField(max_length=255, unique=True, verbose_name='Stripe Subscription ID')),
                ('stripe_customer_id', models.CharField(max_length=255, verbose_name='Stripe Customer ID')),
                ('plan_type', models.CharField(max_length=20, verbose_name='Plan Type')),
                ('status', models.CharField(max_length=50, verbose_name='Status')),
                ('current_period_start', models.DateTimeField(verbose_name='Current Period Start')),
                ('current_period_end', models.DateTimeField(verbose_name='Current Period End')),
                ('trial_start', models.DateTimeField(blank=True, null=True, verbose_name='Trial Start')),
                ('trial_end', models.DateTimeField(blank=True, null=True, verbose_name='Trial End')),
                ('canceled_at', models.DateTimeField(blank=True, null=True, verbose_name='Canceled At')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('currency', models.CharField(default='USD', max_length=3, verbose_name='Currency')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Stripe Subscription',
                'verbose_name_plural': 'Stripe Subscriptions',
                'ordering': ['-created_at'],
            },
        ),

        # Create TokenConsumption model - with correct result_url field as TextField
        migrations.CreateModel(
            name='TokenConsumption',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('service_type', models.CharField(choices=[('image_gen', 'Text to Image'), ('img_edit', 'Image Editing'), ('video_gen', 'Text to Video'), ('img2video', 'Image to Video'), ('multi_img_video', 'Multi-Image to Video'), ('video_keyframe', 'Video Keyframe Extraction'), ('chat_api', 'Chat API')], max_length=20, verbose_name='Service Type')),
                ('tokens_used', models.IntegerField(verbose_name='Tokens Used')),
                ('prompt', models.CharField(blank=True, max_length=500, verbose_name='User Prompt')),
                ('result_url', models.TextField(blank=True, null=True, verbose_name='Result URL')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='Metadata')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.chatsession', verbose_name='Chat Session')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Token Consumption',
                'verbose_name_plural': 'Token Consumptions',
                'ordering': ['-created_at'],
            },
        ),

        # Create EmailVerification model
        migrations.CreateModel(
            name='EmailVerification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=6, verbose_name='Verification Code')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('expires_at', models.DateTimeField(verbose_name='Expires At')),
                ('is_used', models.BooleanField(default=False, verbose_name='Is Used')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Email Verification',
                'verbose_name_plural': 'Email Verifications',
            },
        ),

        # Create PasswordReset model
        migrations.CreateModel(
            name='PasswordReset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=6, verbose_name='Reset Code')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('expires_at', models.DateTimeField(verbose_name='Expires At')),
                ('is_used', models.BooleanField(default=False, verbose_name='Is Used')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Password Reset',
                'verbose_name_plural': 'Password Resets',
            },
        ),

        # Create BetaApplication model
        migrations.CreateModel(
            name='BetaApplication',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(help_text='Used for sending beta invitation', max_length=254, verbose_name='Email')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='Name')),
                ('company', models.CharField(blank=True, max_length=200, verbose_name='Company/Organization')),
                ('purpose', models.TextField(help_text='Please describe how you plan to use our services', verbose_name='Usage Purpose')),
                ('source', models.CharField(choices=[('SOCIAL_MEDIA', 'Social Media'), ('SEARCH_ENGINE', 'Search Engine'), ('FRIEND_REFERRAL', 'Friend Referral'), ('TECH_BLOG', 'Tech Blog'), ('ONLINE_COMMUNITY', 'Online Community'), ('ADVERTISING', 'Advertising'), ('ADMIN_INVITATION', 'Admin Invitation'), ('OTHER', 'Other')], help_text='How did you come to know about our services?', max_length=20, verbose_name='Knowledge Channel')),
                ('source_detail', models.CharField(blank=True, help_text='Please describe the specific source in detail', max_length=500, verbose_name='Channel Details')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('INVITED', 'Invited')], default='PENDING', max_length=20, verbose_name='Application Status')),
                ('admin_notes', models.TextField(blank=True, verbose_name='Admin Notes')),
                ('invitation_code', models.CharField(blank=True, max_length=32, verbose_name='Invitation Code')),
                ('invitation_sent_at', models.DateTimeField(blank=True, null=True, verbose_name='Invitation Sent Time')),
                ('invitation_used_at', models.DateTimeField(blank=True, null=True, verbose_name='Invitation Used Time')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Application Time')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated Time')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True, verbose_name='Review Time')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_applications', to=settings.AUTH_USER_MODEL, verbose_name='Reviewer')),
            ],
            options={
                'verbose_name': 'Beta Application',
                'verbose_name_plural': 'Beta Applications',
                'ordering': ['-created_at'],
            },
        ),

        # Add database indexes for better performance
        migrations.AddIndex(
            model_name='betaapplication',
            index=models.Index(fields=['email'], name='core_betaap_email_e99a09_idx'),
        ),
        migrations.AddIndex(
            model_name='betaapplication',
            index=models.Index(fields=['status'], name='core_betaap_status_aea74d_idx'),
        ),
        migrations.AddIndex(
            model_name='betaapplication',
            index=models.Index(fields=['created_at'], name='core_betaap_created_0a96bc_idx'),
        ),
    ] 