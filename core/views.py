import asyncio
import json
import logging
import uuid
from datetime import datetime, timed<PERSON>ta
from urllib.parse import urlparse
import time
from uuid import uuid4

from django.shortcuts import get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.cache import cache
from django.utils import timezone
from asgiref.sync import async_to_sync, sync_to_async
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authtoken.models import Token
from celery.result import AsyncResult
from celery import states

from core.models import ChatSession, ChatMessage, TokenConsumption, User, Attachment
from core.services.token_service import TokenService
from core.services.reference_service import MessageReferenceService
from core.forms import GenerateForm
from app.adapters.oss_upload import upload_image_to_oss
import os

# Celery任务导入
from app.tasks import execute_langchain_workflow

logger = logging.getLogger(__name__)

@api_view(['GET', 'POST', 'OPTIONS'])
@authentication_classes([TokenAuthentication])
def index(request):
    """
    统一AI服务入口点 - 异步Celery架构
    支持GET(健康检查), POST(异步聊天), OPTIONS(CORS预检)
    兼容前端现有的调用方式，但使用新的异步后端
    """
    # GET请求和OPTIONS请求不需要强制认证
    if request.method == 'POST':
        from django.conf import settings as django_settings
        # 在生产环境中要求认证
        if not django_settings.DEBUG and not request.user.is_authenticated:
            return JsonResponse({'error': 'Authentication required'}, status=401)
    
    return async_to_sync(_async_index)(request)

async def _async_index(request):
    """
    异步index处理函数 - 使用Celery架构
    """
    logger.info("🚀 UNIFIED AI SERVICE - 异步Celery架构启动")
    
    # 处理预检请求
    if request.method == "OPTIONS":
        response = JsonResponse({
            "status": "ok",
            "message": "CORS预检请求成功"
        })
        response["Access-Control-Allow-Origin"] = request.META.get("HTTP_ORIGIN", "*")
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = (
            "Content-Type, X-CSRFToken, X-Requested-With, Authorization"
        )
        response["Access-Control-Allow-Credentials"] = "true"
        return response

    if request.method == "GET":
        # 检查是否是API请求
        if request.META.get('HTTP_ACCEPT', '').startswith('application/json'):
            # API模式 - 返回JSON响应
            response = JsonResponse({
                "status": "ok",
                "message": "异步API服务正常运行",
                "architecture": "Celery + Redis",
                "api_version": "max"
            })
            return response
        else:
            # 浏览器访问 - 返回调试页面
            from django.conf import settings as django_settings
            from django.shortcuts import render
            context = {
                'debug': django_settings.DEBUG,
                'allowed_hosts': django_settings.ALLOWED_HOSTS,
                'admin_url': '/admin/',
                'endpoints': {
                    'health': '/api/health/',
                    'async_chat': '/api/chat/async/',
                    'register': '/api/auth/register/',
                    'login': '/api/auth/login/',
                    'verify_email': '/api/auth/verify-email/',
                },
                'frontend_url': f"{os.getenv('PROTOCOL', 'https')}://{os.getenv('PRIMARY_DOMAIN', request.get_host().split(':')[0])}",
                'system_info': {
                    'architecture': 'Async Celery + Redis',
                    'api_version': 'max',
                    'django_version': '4.2.23',
                    'python_version': '3.11.12',
                    'containers': 'MirageMakers AI (6 services)',
                }
            }
            return render(request, 'debug.html', context)

    # POST请求处理 - 转发到异步聊天接口
    try:
        # 1. 解析请求数据 - 支持JSON和FormData两种格式
        references_data = []
        if request.content_type and 'application/json' in request.content_type:
            data = json.loads(request.body)
            prompt = data.get('prompt', '').strip()
            session_id = data.get('session_id')
            attachments_data = data.get('attachments', [])
            references_data = data.get('references', [])  # 解析引用信息
            
            logger.info(f"🔍 [Index] 检测到JSON请求，prompt: {prompt[:100]}..., references: {len(references_data)}")
                
        else:
            # FormData请求 - 支持文件上传
            form = GenerateForm(request.POST, request.FILES)
            if not form.is_valid():
                logger.error(f"❌ [Index] GenerateForm验证失败: {form.errors}")
                return JsonResponse({"error": "Invalid form data", "details": str(form.errors)}, status=400)
            prompt = form.cleaned_data["prompt"].strip()
            media_file = request.FILES.get("media")
            session_id = request.POST.get("session_id")
            
            # 解析FormData中的引用信息
            references_json = request.POST.get("references", "[]")
            try:
                references_data = json.loads(references_json) if references_json else []
            except json.JSONDecodeError:
                references_data = []
                
            logger.info(f"🔍 [Index] 检测到FormData请求，prompt: {prompt[:100]}..., references: {len(references_data)}")
            
            # 处理文件上传
            attachments_data = []
            if media_file:
                try:
                    # 上传文件到OSS
                    upload_func = sync_to_async(upload_image_to_oss)
                    media_url = await upload_func(media_file)
                    logger.info(f"✅ [Index] 媒体文件上传成功: {media_url}")
                    
                    media_type = "video" if media_file.content_type.startswith("video/") else "image"
                    attachment_data = {
                        'type': media_type,
                        'url': str(media_url)
                    }
                    attachments_data.append(attachment_data)
                    
                except Exception as upload_error:
                    logger.error(f"❌ [Index] OSS上传失败: {str(upload_error)}")
                    return JsonResponse({
                        "error": "File upload failed", 
                        "details": str(upload_error)
                    }, status=500)

        if not prompt:
            return JsonResponse({"error": "Prompt is required"}, status=400)

        # 2. 获取用户信息
        user = get_user_from_request(request)
        if not user:
            return JsonResponse({
                "error": "Authentication required"
            }, status=401)

        logger.info(f"🎯 [Index] 处理用户请求: {user.email if hasattr(user, 'email') else user.username}")

        # 3. 异步处理会话和消息保存（复用chat_async的逻辑）
        async def prepare_task_data():
            # 获取或创建会话
            session = await get_or_create_session_async(user, session_id, "New Chat")
            
            # 保存用户消息，包含引用信息
            user_image_url = None
            user_video_url = None
            if attachments_data:
                for att in attachments_data:
                    if isinstance(att, dict):
                        if att.get('type') == "image" and att.get('url'):
                            user_image_url = att['url']
                        elif att.get('type') == "video" and att.get('url'):
                            user_video_url = att['url']
            
            await save_user_message_async(session, prompt, user_image_url, user_video_url, references_data)
            
            # 创建assistant消息占位符
            assistant_message = await save_assistant_message_async(session, "Processing your request...")
            
            # 构建对话历史（包含引用信息）
            conversation = await build_conversation_from_session_async(session, prompt, attachments_data, references_data)
            
            # 记录媒体统计信息（调试用）
            if references_data:
                logger.info(f"🔗 [Index] 任务包含 {len(references_data)} 个引用媒体")
            if attachments_data:
                logger.info(f"📎 [Index] 任务包含 {len(attachments_data)} 个上传附件")
            
            return {
                "user_id": str(user.id),
                "session_id": str(session.id),
                "message_id": str(assistant_message.id),
                "conversation": conversation,
                "api_version": "max",
                "media_context": {  # 新增：媒体上下文信息
                    "references_count": len(references_data) if references_data else 0,
                    "attachments_count": len(attachments_data) if attachments_data else 0,
                    "has_media": bool(references_data or attachments_data)
                }
            }
        
        # 运行异步任务准备
        task_data = await prepare_task_data()
        
        # 创建Celery任务
        task_id = str(uuid4())
        task_data["task_id"] = task_id
        
        logger.info(f"🚀 [Index] 创建Celery任务: {task_id}")
        
        # 存储任务元数据到Redis
        store_task_metadata_in_redis(
            task_id=task_id,
            user_id=user.id,
            session_id=task_data["session_id"],
            prompt=prompt,
            api_version="max",
            message_id=task_data["message_id"]
        )
        
        # 发送任务到Celery
        celery_result = execute_langchain_workflow.apply_async(
            args=[task_data],
            task_id=task_id
        )
        
        logger.info(f"✅ [Index] Celery任务已发送: {task_id}")
        
        # 立即返回task_id
        return JsonResponse({
            "success": True,
            "async_task": True,  # 标识为异步任务
            "task_id": task_id,
            "message_id": task_data["message_id"],  # 前端需要的message_id
            "session_id": task_data["session_id"],
            "estimated_duration": 120,  # 预估2分钟完成
            "chat_response": "Processing your request... This may take several minutes.",  # 改为更友好的消息
            "estimated_time": "3-5 minutes"  # 预估时间
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            "error": "Invalid JSON format"
        }, status=400)
    except Exception as e:
        logger.error(f"❌ [Index] 处理请求失败: {str(e)}", exc_info=True)
        return JsonResponse({
            "error": f"Failed to process request: {str(e)}"
        }, status=500)
        
def get_user_from_request(request):
    """从请求中获取用户信息"""
    if hasattr(request, 'user') and request.user.is_authenticated:
        return request.user
    
    # 如果没有认证用户，尝试从Token获取
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    if auth_header.startswith('Token '):
        token = auth_header.split(' ')[1]
        try:
            token_obj = Token.objects.get(key=token)
            return token_obj.user
        except Token.DoesNotExist:
            pass
    
    return None

async def build_conversation_from_session_async(chat_session, current_prompt, attachments, references=None):
    """
    从ChatSession构建Graph所需的conversation格式（异步版本）
    参考eval/run_agent.py的数据格式和views_legacy.py的逻辑
    增强版本：支持前端引用媒体数据的完整处理
    """
    conversation = []
    
    logger.info(f"🔍 [Conversation] 开始构建对话历史，session_id: {chat_session.id if chat_session else 'None'}, references: {len(references) if references else 0}")
    
    # 添加历史消息 - 使用异步数据库查询
    if chat_session:
        def get_recent_messages():
            messages = list(chat_session.messages.order_by('-created_at')[:10])
            logger.info(f"🔍 [Conversation] 查询到 {len(messages)} 条历史消息")
            return messages
        
        recent_messages = await sync_to_async(get_recent_messages)()
        
        for i, msg in enumerate(reversed(recent_messages)):
            logger.info(f"🔍 [Conversation] 历史消息{i}: type={msg.type}, content='{msg.content[:50]}...', image_url='{msg.image_url}', video_url='{msg.video_url}'")
            
            # 构造历史消息，包含媒体信息
            history_msg = {
                "role": msg.type,  # "user" or "assistant"
                "content": msg.content,
                "image_url": msg.image_url or "",
                "video_url": msg.video_url or "",
                "timestamp": msg.created_at.isoformat() if msg.created_at else "",
                "message_id": str(msg.id)
            }
            
            # 如果历史消息有媒体，添加到媒体字段
            if msg.image_url or msg.video_url:
                history_msg["media"] = []
                if msg.image_url:
                    history_msg["media"].append({
                        "type": "image",
                        "url": msg.image_url,
                        "source": "history",
                        "message_id": str(msg.id)
                    })
                if msg.video_url:
                    history_msg["media"].append({
                        "type": "video", 
                        "url": msg.video_url,
                        "source": "history",
                        "message_id": str(msg.id)
                    })
            
            conversation.append(history_msg)
    
    # 构造当前用户消息，包含完整的媒体信息
    current_msg = {
        "role": "user",
        "content": current_prompt,
        "image_url": "",
        "video_url": "",
        "media": [],  # 新增媒体数组字段
        "timestamp": datetime.now().isoformat()
    }
    
    # 处理直接上传的附件
    if attachments:
        for att in attachments:
            logger.debug(f"🔍 [Conversation] 处理上传附件: {att}")
            if isinstance(att, dict):
                if att.get('type') == "image" and att.get('url'):
                    current_msg["image_url"] = att['url']  # 向后兼容
                    current_msg["media"].append({
                        "type": "image",
                        "url": att['url'], 
                        "source": "upload",
                        "description": "User uploaded image"
                    })
                elif att.get('type') == "video" and att.get('url'):
                    current_msg["video_url"] = att['url']  # 向后兼容
                    current_msg["media"].append({
                        "type": "video",
                        "url": att['url'],
                        "source": "upload", 
                        "description": "User uploaded video"
                    })
            elif hasattr(att, 'type') and hasattr(att, 'url'):
                if att.type == "image":
                    current_msg["image_url"] = att.url
                    current_msg["media"].append({
                        "type": "image",
                        "url": att.url,
                        "source": "upload",
                        "description": "User uploaded image"
                    })
                elif att.type == "video":
                    current_msg["video_url"] = att.url
                    current_msg["media"].append({
                        "type": "video", 
                        "url": att.url,
                        "source": "upload",
                        "description": "User uploaded video"
                    })
    
    # 处理前端@引用的媒体 - 增强版处理
    if references and len(references) > 0:
        logger.info(f"🔗 [Conversation] 开始处理 {len(references)} 个引用媒体")
        
        # 统计引用媒体类型
        image_refs = []
        video_refs = []
        
        for i, ref in enumerate(references):
            if not isinstance(ref, dict):
                logger.warning(f"❌ [Conversation] 引用数据格式错误: {ref}")
                continue
                
            ref_type = ref.get('type', 'unknown')
            ref_url = ref.get('url', '')
            ref_id = ref.get('reference_id', str(i + 1))
            ref_message_id = ref.get('message_id', '')
            ref_display_name = ref.get('display_name', f'{ref_type.title()} {ref_id}')
            
            if not ref_url:
                logger.warning(f"⚠️ [Conversation] 引用媒体缺少URL: {ref}")
                continue
            
            # 构造引用媒体数据
            media_data = {
                "type": ref_type,
                "url": ref_url,
                "source": "reference",
                "reference_id": ref_id,
                "display_name": ref_display_name,
                "original_message_id": ref_message_id,
                "description": f"Referenced {ref_type} from conversation history"
            }
            
            current_msg["media"].append(media_data)
            
            # 分类统计
            if ref_type == "image":
                image_refs.append(media_data)
            elif ref_type == "video":
                video_refs.append(media_data)
                
            logger.debug(f"🔗 [Conversation] 添加引用媒体 {i+1}: {ref_type} - {ref_url[:50]}...")
        
        # 增强用户提示，让AI了解引用的媒体内容
        reference_context_parts = []
        
        if image_refs:
            image_context = f"Referenced Images ({len(image_refs)}): "
            image_descriptions = [f"@{ref['reference_id']} ({ref['display_name']})" for ref in image_refs]
            image_context += ", ".join(image_descriptions)
            reference_context_parts.append(image_context)
        
        if video_refs:
            video_context = f"Referenced Videos ({len(video_refs)}): "
            video_descriptions = [f"@{ref['reference_id']} ({ref['display_name']})" for ref in video_refs]
            video_context += ", ".join(video_descriptions)
            reference_context_parts.append(video_context)
        
        if reference_context_parts:
            reference_prompt = f"\n\n[Media References]\n{'; '.join(reference_context_parts)}\nPlease use these referenced media as context for understanding my request and generating appropriate content."
            current_msg["content"] += reference_prompt
        
        # 向后兼容：如果没有直接上传的媒体，将第一个引用媒体设为主媒体
        if not current_msg["image_url"] and image_refs:
            current_msg["image_url"] = image_refs[0]["url"]
        if not current_msg["video_url"] and video_refs:
            current_msg["video_url"] = video_refs[0]["url"]
        
        logger.info(f"✅ [Conversation] 成功处理引用媒体: {len(image_refs)} 图片, {len(video_refs)} 视频")
    
    # 如果当前消息有媒体内容，在content中添加媒体描述
    if current_msg["media"]:
        media_summary = []
        total_images = len([m for m in current_msg["media"] if m["type"] == "image"])
        total_videos = len([m for m in current_msg["media"] if m["type"] == "video"])
        
        if total_images > 0:
            media_summary.append(f"{total_images} image{'s' if total_images > 1 else ''}")
        if total_videos > 0:
            media_summary.append(f"{total_videos} video{'s' if total_videos > 1 else ''}")
            
        if media_summary:
            if not current_msg["content"].endswith(".") and not current_msg["content"].endswith("?") and not current_msg["content"].endswith("!"):
                current_msg["content"] += "."
            current_msg["content"] += f"\n\n[Media Context: {', '.join(media_summary)} included for reference]"
    
    conversation.append(current_msg)
    
    logger.info(f"✅ [Conversation] 构建完成，总计 {len(conversation)} 条消息，当前消息包含 {len(current_msg.get('media', []))} 个媒体项")
    return conversation

@sync_to_async
def get_or_create_session_async(user, session_id=None, title="New Chat"):
    """异步获取或创建聊天会话"""
    if session_id:
        try:
            session = ChatSession.objects.get(id=session_id, user=user)
            return session
        except ChatSession.DoesNotExist:
            logger.warning(f"Session {session_id} not found for user {user.id}, creating new one")
    
    # 创建新会话
    session = ChatSession.objects.create(
        user=user,
        title=title
    )
    logger.info(f"Created new session {session.id} for user {user.id}")
    return session

@sync_to_async
def save_user_message_async(session, content, image_url=None, video_url=None, references_data=None):
    """异步保存用户消息到数据库，支持引用媒体数据"""
    
    # 准备metadata，包含引用信息
    metadata = {}
    if references_data and len(references_data) > 0:
        # 保存引用媒体的详细信息
        metadata['references'] = []
        for ref in references_data:
            if isinstance(ref, dict):
                metadata['references'].append({
                    'reference_id': ref.get('reference_id', ''),
                    'type': ref.get('type', 'unknown'),
                    'url': ref.get('url', ''),
                    'display_name': ref.get('display_name', ''),
                    'original_message_id': ref.get('message_id', ''),
                    'source': 'reference'
                })
        metadata['references_count'] = len(metadata['references'])
        
        logger.info(f"💾 [SaveMessage] 保存用户消息包含 {len(metadata['references'])} 个引用媒体")
    
    message = ChatMessage.objects.create(
        session=session,
        type='user',
        content=content,
        image_url=image_url or "",
        video_url=video_url or "",
        metadata=metadata
    )
    
    # 创建引用关系记录（如果有引用）
    if references_data and len(references_data) > 0:
        try:
            from core.services.reference_service import MessageReferenceService
            
            # 为每个引用创建关系记录
            for ref in references_data:
                if isinstance(ref, dict) and ref.get('message_id'):
                    try:
                        # 查找被引用的消息
                        referenced_message = ChatMessage.objects.get(id=ref['message_id'])
                        
                        # 使用ReferenceService创建引用关系
                        MessageReferenceService.create_reference(
                            referencing_message=message,
                            referenced_message=referenced_message,
                            reference_type=ref.get('type', 'media'),
                            metadata={
                                'reference_id': ref.get('reference_id', ''),
                                'display_name': ref.get('display_name', ''),
                                'url': ref.get('url', '')
                            }
                        )
                        
                        logger.debug(f"🔗 [SaveMessage] 创建引用关系: {message.id} -> {referenced_message.id}")
                        
                    except ImportError:
                        logger.warning("⚠️ [SaveMessage] MessageReferenceService不可用，跳过引用关系创建")
        except Exception as e:
            logger.error(f"❌ [SaveMessage] 引用关系处理失败: {e}")
    
    logger.debug(f"💾 [SaveMessage] 用户消息已保存 {message.id} 到会话 {session.id}")
    return message

@sync_to_async
def save_assistant_message_async(session, content, image_url=None, video_url=None):
    """异步保存assistant消息到数据库"""
    message = ChatMessage.objects.create(   
        session=session,
        type='assistant',
        content=content,
        image_url=image_url or "",
        video_url=video_url or "",
        metadata={'status': 'processing'}
    )
    logger.debug(f"Saved assistant message {message.id} to session {session.id}")
    return message

def store_task_metadata_in_redis(task_id, user_id, session_id, prompt, api_version="max", message_id=None):
    """在Redis中存储任务元数据"""
    task_meta = {
        "task_id": task_id,
        "user_id": str(user_id),
        "session_id": str(session_id),
        "prompt": prompt,
        "api_version": api_version,
        "status": "PENDING",
        "created_at": datetime.now().isoformat(),
        "progress": 0.0
    }
    
    if message_id:
        task_meta["message_id"] = str(message_id)
    
    # 存储到Redis，过期时间1小时
    cache.set(f"task_meta:{task_id}", task_meta, timeout=3600)
    cache.set(f"task_status:{task_id}", "PENDING", timeout=3600)
    
    logger.info(f"Stored task metadata for task_id: {task_id}")
    return task_meta

def get_task_metadata_from_redis(task_id):
    """从Redis获取任务元数据"""
    return cache.get(f"task_meta:{task_id}")

@require_http_methods(["GET"])
def task_status(request, task_id: str):
    """任务状态查询接口（RESTful格式）"""
    try:
        user = get_user_from_request(request)
        
        # 详细的认证日志
        logger.info(f"🔍 [TaskStatus] 查询任务状态: {task_id}")
        logger.info(f"   用户认证: {user.email if user else 'Anonymous'}")
        
        if not user:
            logger.warning(f"   ❌ 认证失败: 无用户信息")
            return JsonResponse({
                "error": "Authentication required"
            }, status=401)
        
        # 获取任务元数据
        task_meta = get_task_metadata_from_redis(task_id)
        logger.info(f"   Redis任务元数据: {bool(task_meta)}")
        
        if task_meta:
            task_user_id = task_meta.get('user_id')
            current_user_id = str(user.id)
            logger.info(f"   任务用户ID: {task_user_id}")
            logger.info(f"   当前用户ID: {current_user_id}")
            
            # 验证任务权限 - 宽松模式：允许合理的用户切换场景
            if task_user_id and task_user_id != current_user_id:
                logger.warning(f"   ⚠️ 用户ID不匹配: 任务属于用户 {task_user_id}, 当前用户 {current_user_id}")
                logger.warning(f"   🔧 采用宽松模式: 允许访问任务状态（可能是用户切换或session过期）")
        else:
            # 如果Redis中没有任务元数据，记录警告但继续处理
            logger.warning(f"   ⚠️ Redis中未找到任务元数据，可能已过期，继续处理...")
        
        # 获取Celery任务状态
        celery_result = AsyncResult(task_id)
        logger.info(f"   Celery任务状态: {celery_result.status}")
        
        response_data = {
            "task_id": task_id,
            "status": celery_result.status,
            "timestamp": datetime.now().isoformat()
        }
        
        if celery_result.status == states.SUCCESS:
            result = celery_result.result
            logger.info(f"   ✅ Celery任务完成，检查业务逻辑结果")
            
            # 🔍 关键修复：检查业务逻辑是否成功
            business_success = result.get("success", False)
            logger.info(f"   📊 业务逻辑成功: {business_success}")
            
            if business_success:
                # 业务逻辑成功 - 正常的生成任务完成
                logger.info(f"   🎉 任务和业务逻辑都成功")
                response_data.update({
                    "success": True,
                    "result": {
                        "chat_response": result.get("chat_response", "Generation completed successfully!"),
                        "message": result.get("chat_response", "Generation completed successfully!"),
                        "image_url": result.get("image_url", ""),
                        "video_url": result.get("video_url", ""),
                        "tokens_consumed": result.get("tokens_consumed", 0)
                    }
                })
            else:
                # 业务逻辑失败 - 如token不足、安全检查失败等
                error_message = result.get("error", "Task execution failed")
                logger.warning(f"   💰 业务逻辑失败: {error_message}")
                
                # 检查是否是token相关错误，设置适当的HTTP状态码
                is_token_error = "token" in error_message.lower() or "balance" in error_message.lower()
                status_code = 402 if is_token_error else 400  # 402 Payment Required for token issues
                
                response_data.update({
                    "success": False,
                    "error": error_message,
                    "error_type": "insufficient_balance" if is_token_error else "task_failed"
                })
                
                # 添加token相关详细信息
                if result.get("plan_failure_details"):
                    response_data.update({
                        "tokens_required": result["plan_failure_details"].get("required_tokens", 0),
                        "current_balance": result["plan_failure_details"].get("current_balance", 0),
                        "deficit": result["plan_failure_details"].get("deficit", 0)
                    })
                
                # 对于token错误，返回402状态码让前端正确处理
                if is_token_error:
                    logger.info(f"   💳 返回402状态码，触发前端token错误处理")
                    return JsonResponse(response_data, status=status_code)
                
        elif celery_result.status == states.FAILURE:
            error_info = str(celery_result.info) if celery_result.info else "Unknown error"
            logger.warning(f"   ❌ Celery任务执行失败: {error_info}")
            response_data.update({
                "success": False,
                "error": error_info,
                "error_type": "celery_failure"
            })
        else:
            logger.info(f"   ⏳ 任务仍在处理中: {celery_result.status}")
            response_data.update({
                "success": None,
                "message": "Task is still processing"
            })
        
        return JsonResponse(response_data)
        
    except Exception as e:
        logger.error(f"❌ [TaskStatus] 查询任务状态失败: {str(e)}", exc_info=True)
        return JsonResponse({
            "task_id": task_id,
            "success": False,
            "error": f"Failed to get task status: {str(e)}",
            "error_type": "server_error"
        }, status=500)

@api_view(['GET'])
@authentication_classes([TokenAuthentication])
def user_usage(request):
    """用户使用记录查询接口"""
    try:
        user = get_user_from_request(request)
        if not user:
            return JsonResponse({
                "success": False,
                "error": "Authentication required"
            }, status=401)
        
        # 获取用户的token使用记录
        from core.models import TokenConsumption
        from datetime import datetime, timedelta
        from django.db.models import Sum, Count
        
        # 获取最近30天的记录
        thirty_days_ago = datetime.now() - timedelta(days=30)
        
        # 查询使用记录
        usage_records = TokenConsumption.objects.filter(
            user=user,
            created_at__gte=thirty_days_ago
        ).order_by('-created_at')
        
        # 转换为前端需要的格式
        formatted_records = []
        for record in usage_records:
            # 获取服务名称
            service_display = dict(TokenConsumption.SERVICE_TYPE_CHOICES).get(
                record.service_type, record.service_type
            )
            
            formatted_records.append({
                "date": record.created_at.strftime("%m/%d/%Y, %I:%M:%S %p"),
                "service": service_display,
                "tokens_used": record.tokens_used,
                "status": "Completed"
            })
        
        # 统计数据
        total_tokens_used = usage_records.aggregate(
            total=Sum('tokens_used')
        )['total'] or 0
        
        total_requests = usage_records.count()
        
        # 按服务类型统计
        usage_by_service = {}
        for choice in TokenConsumption.SERVICE_TYPE_CHOICES:
            service_type, service_name = choice
            service_total = usage_records.filter(
                service_type=service_type
            ).aggregate(total=Sum('tokens_used'))['total'] or 0
            if service_total > 0:
                usage_by_service[service_name] = service_total
        
        # 每日使用统计（最近7天）
        daily_usage = []
        for i in range(7):
            day = datetime.now() - timedelta(days=i)
            day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            day_total = usage_records.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).aggregate(total=Sum('tokens_used'))['total'] or 0
            
            daily_usage.append({
                "date": day.strftime("%m/%d"),
                "tokens_used": day_total
            })
        
        # 反转列表，让最早的日期在前
        daily_usage.reverse()
        
        return JsonResponse({
            "success": True,
            "usage_records": formatted_records,
            "total_tokens_used": total_tokens_used,
            "total_requests": total_requests,
            "usage_by_service": usage_by_service,
            "daily_usage": daily_usage
        })
        
    except Exception as e:
        logger.error(f"❌ [UserUsage] 查询用户使用记录失败: {str(e)}")
        return JsonResponse({
            "success": False,
            "error": f"Failed to get usage data: {str(e)}"
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def health_check(request):
    """健康检查端点，用于 Docker 健康检查"""
    return JsonResponse({"status": "healthy", "service": "django"})
