from django.urls import path, include
from . import views, auth_views, social_auth_views, admin_views

urlpatterns = [
    # 健康检查端点
    path("health/", views.health_check, name="health_check"),
    
    # 健康检查端点 - 已移除，使用 api/urls.py 中的 health_check
    
    # 队列监控端点 - 已移除
    
    # 主要AI服务端点
    path("", views.index, name="index"),
    path("api/", views.index, name="api_index"),
    path("api/generate/", views.index, name="api_generate"),
    path("api/generate", views.index, name="api_generate_no_slash"),
    # 下载代理端点 - 已移除
    
    # 任务状态检查 - 已移除，统一使用 task_status
    
    # 会话管理 - 已移除，核心逻辑在 _async_index 中
    
    # 代币管理API - 已移除，核心功能在 graph.py 中
    
    # 管理员仪表板路由（避免与Django admin冲突）
    path("dashboard/", admin_views.admin_dashboard, name="admin_dashboard"),
    path("dashboard", admin_views.admin_dashboard, name="admin_dashboard_no_slash"),
    path("dashboard/api/", admin_views.admin_api, name="admin_api"),
    path("dashboard/users/", admin_views.user_details, name="admin_user_details"),
    path("dashboard/chats/", admin_views.chat_details, name="admin_chat_details"),
    path("dashboard/messages/", admin_views.message_history, name="admin_message_history"),
    path("dashboard/users/management/", admin_views.user_management, name="admin_user_management"),
    path("dashboard/messages/filter/", admin_views.filter_messages, name="admin_filter_messages"),
    path("dashboard/messages/export/", admin_views.export_messages, name="admin_export_messages"),
    path("dashboard/beta-applications/", admin_views.beta_application_management, name="admin_beta_applications"),
    path("dashboard/send-invitation/", admin_views.send_direct_invitation, name="admin_send_invitation"),
    path("dashboard/sessions/<uuid:session_id>/messages/", admin_views.message_details, name="admin_message_details"),
    
    # 认证相关路由
    path('api/auth/register/', auth_views.register, name='register'),
    path('api/auth/register', auth_views.register, name='register_no_slash'),
    path('api/auth/beta-apply/', auth_views.beta_apply, name='beta_apply'),
    path('api/auth/beta-apply', auth_views.beta_apply, name='beta_apply_no_slash'),
    path('api/auth/verify-email/', auth_views.verify_email,
         name='verify_email'),
    path('api/auth/verify-email', auth_views.verify_email,
         name='verify_email_no_slash'),
    path('api/auth/resend-verification/', auth_views.resend_verification,
         name='resend_verification'),
    path('api/auth/resend-verification', auth_views.resend_verification,
         name='resend_verification_no_slash'),
    path('api/auth/login/', auth_views.login, name='login'),
    path('api/auth/login', auth_views.login, name='login_no_slash'),
    path('api/auth/logout/', auth_views.logout, name='logout'),
    path('api/auth/logout', auth_views.logout, name='logout_no_slash'),
    path('api/auth/profile/', auth_views.profile, name='profile'),
    path('api/auth/profile', auth_views.profile, name='profile_no_slash'),
    
    # 社交登录相关路由
    path('api/auth/social-success/', social_auth_views.social_login_success,
         name='social_login_success'),
    path('api/auth/social-success', social_auth_views.social_login_success,
         name='social_login_success_no_slash'),
    path('api/auth/social-success/providers/', 
         social_auth_views.get_social_providers,
         name='get_social_providers'),
    path('api/auth/social-success/providers', 
         social_auth_views.get_social_providers,
         name='get_social_providers_no_slash'),
    
    # 密码重置相关路由
    path('api/auth/forgot-password/', auth_views.forgot_password,
         name='forgot_password'),
    path('api/auth/forgot-password', auth_views.forgot_password,
         name='forgot_password_no_slash'),
    path('api/auth/reset-password/', auth_views.reset_password,
         name='reset_password'),
    path('api/auth/reset-password', auth_views.reset_password,
         name='reset_password_no_slash'),
    
    # 修改密码相关路由
    path('api/auth/change-password/', auth_views.change_password,
         name='change_password'),
    path('api/auth/change-password', auth_views.change_password,
         name='change_password_no_slash'),
    
    # CSRF token路由
    path('api/auth/csrf-token/', auth_views.csrf_token,
         name='csrf_token'),
    path('api/auth/csrf-token', auth_views.csrf_token,
         name='csrf_token_no_slash'),
    
    # 聊天历史相关路由
    path('api/chat/sessions/', auth_views.chat_sessions, name='chat_sessions'),
    path('api/chat/sessions', auth_views.chat_sessions, name='chat_sessions_no_slash'),
    path('api/chat/sessions/<uuid:session_id>/messages/',
         auth_views.chat_messages, name='chat_messages'),
    path('api/chat/sessions/<uuid:session_id>/messages',
         auth_views.chat_messages, name='chat_messages_no_slash'),
    path('api/chat/messages/save/', auth_views.save_chat_message, name='save_chat_message'),
    path('api/chat/messages/save', auth_views.save_chat_message, name='save_chat_message_no_slash'),
    path('api/chat/sessions/create/', auth_views.create_chat_session,
         name='create_chat_session'),
    path('api/chat/sessions/create', auth_views.create_chat_session,
         name='create_chat_session_no_slash'),
    path('api/chat/sessions/<uuid:session_id>/delete/', 
         auth_views.delete_chat_session, name='delete_chat_session'),
    path('api/chat/sessions/<uuid:session_id>/delete', 
         auth_views.delete_chat_session, name='delete_chat_session_no_slash'),
    path('api/chat/sessions/<uuid:session_id>/update/', 
         auth_views.update_chat_session, name='update_chat_session'),
    path('api/chat/sessions/<uuid:session_id>/update', 
         auth_views.update_chat_session, name='update_chat_session_no_slash'),
    
    # 任务状态查询API
    path('api/status/<str:task_id>/', views.task_status, name='task_status'),
    path('api/status/<str:task_id>', views.task_status, name='task_status_no_slash'),
    
    # 会话消息API - 已移除
    
    # 任务消息API - 已移除
    
    # @mention 引用功能API
    path('api/sessions/<uuid:session_id>/media/', auth_views.get_session_media_messages, name='get_session_media_messages'),
    path('api/sessions/<uuid:session_id>/media', auth_views.get_session_media_messages, name='get_session_media_messages_no_slash'),
    
    # 前端需要的sessions messages API
    path('api/sessions/<uuid:session_id>/messages/', auth_views.chat_messages, name='get_session_messages_frontend'),
    path('api/sessions/<uuid:session_id>/messages', auth_views.chat_messages, name='get_session_messages_frontend_no_slash'),
    
    path('api/messages/process-references/', auth_views.process_message_with_references, name='process_message_with_references'),
    path('api/messages/process-references', auth_views.process_message_with_references, name='process_message_with_references_no_slash'),
    
    # PayPal 代理端点 - 新的CSP兼容方案  
    # api.urls 的路由现在由项目主urls.py处理，避免重复包含
    
    # ========== 异步聊天API - 基于Celery+Redis ==========
    path('api/chat/async/', views.index, name='chat_async'),
    
    # 任务状态和结果查询API - 已移除，统一使用 task_status
    
    # 会话管理API - 已移除
]
