# core/routing.py
"""
WebSocket路由配置
定义WebSocket URL模式和对应的消费者
"""

from django.urls import re_path
from . import consumers

# WebSocket URL模式
websocket_urlpatterns = [
    # AI任务实时状态推送 - 用户专用
    re_path(r'ws/ai-tasks/user/(?P<user_id>\w+)/$', consumers.AITaskConsumer.as_asgi()),
    
    # 简化的AI任务推送路径（自动获取当前用户）
    re_path(r'ws/ai-tasks/$', consumers.AITaskConsumer.as_asgi()),
    
    # 管理员任务监控 - 管理员专用
    re_path(r'ws/admin/tasks/$', consumers.AdminTaskConsumer.as_asgi()),
] 