from django.shortcuts import redirect
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
import json


@api_view(['GET'])
@permission_classes([AllowAny])
def social_login_success(request):
    """Social login success callback"""
    try:
        if request.user.is_authenticated:
            # Get or create token
            token, created = Token.objects.get_or_create(user=request.user)
            
            # Ensure user email is verified (social login defaults to verified)
            if not request.user.is_email_verified:
                request.user.is_email_verified = True
                request.user.save()
            
            # Redirect to frontend with token and user info
            frontend_url = f"http://localhost:3000/auth/social-success?token={token.key}&user_id={request.user.id}"
            return redirect(frontend_url)
        else:
            # Login failed, redirect to error page
            return redirect("http://localhost:3000/auth/login?error=social_login_failed")
            
    except Exception as e:
        return redirect(f"http://localhost:3000/auth/login?error=social_error&message={str(e)}")


@api_view(['GET'])
@permission_classes([AllowAny])
def social_login_error(request):
    """Social login failure callback"""
    error = request.GET.get('error', 'unknown_error')
    return redirect(f"http://localhost:3000/auth/login?error={error}")


@api_view(['GET'])
@permission_classes([AllowAny])
def get_social_providers(request):
    """Get available social login providers"""
    from django.conf import settings
    
    providers = []
    social_providers = getattr(settings, 'SOCIALACCOUNT_PROVIDERS', {})
    
    # Default social login providers (shown even without config for dev/testing)
    default_providers = [
        {
            'name': 'google',
            'display_name': 'Google',
            'login_url': '/accounts/google/login/',
        },
        {
            'name': 'facebook', 
            'display_name': 'Facebook',
            'login_url': '/accounts/facebook/login/',
        },
        {
            'name': 'discord',
            'display_name': 'Discord', 
            'login_url': '/accounts/discord/login/',
        },
        {
            'name': 'twitter',
            'display_name': 'X (Twitter)',
            'login_url': '/accounts/twitter/login/',
        }
    ]
    
    # If there are configured providers, use them; otherwise use defaults
    if social_providers:
        for provider_name, config in social_providers.items():
            app_config = config.get('APP', {})
            client_id = app_config.get('client_id', '')
            # Only return providers with configured client_id
            if client_id and client_id.strip() and client_id != '':
                display_name = provider_name.title()
                if provider_name == 'twitter':
                    display_name = 'X (Twitter)'
                providers.append({
                    'name': provider_name,
                    'display_name': display_name,
                    'login_url': f'/accounts/{provider_name}/login/',
                })
    
    # If no configured providers, return defaults (for development)
    if not providers:
        providers = default_providers
    
    return Response({
        'providers': providers
    }, status=status.HTTP_200_OK)


def get_provider_icon(provider_name):
    """Get social login provider icons"""
    icons = {
        'google': '🟦',  # Or use actual icon URL
        'facebook': '🔵',
        'discord': '🟣',
        'twitter': '🐦',
    }
    return icons.get(provider_name, '🔗')


@api_view(['POST'])
@permission_classes([AllowAny])
def connect_social_account(request):
    """Connect social account to existing account"""
    try:
        data = json.loads(request.body)
        provider = data.get('provider')
        access_token = data.get('access_token')
        
        if not all([provider, access_token]):
            return Response({
                'message': 'Missing required parameters'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Social account connection logic can be added here
        # Due to complex OAuth flow, frontend redirect approach is recommended
        
        return Response({
            'message': 'Please use page redirect for social login'
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except json.JSONDecodeError:
        return Response({
            'message': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'message': f'Connection failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 