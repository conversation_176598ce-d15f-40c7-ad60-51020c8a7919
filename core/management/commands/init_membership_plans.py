from django.core.management.base import BaseCommand
from core.models import MembershipPlan

class Command(BaseCommand):
    help = '初始化会员套餐数据'

    def handle(self, *args, **options):
        plans = [
            {
                'plan_type': 'TRIAL',
                'name': 'Trial Plan',
                'price_usd': 6.99,
                'tokens': 8000,
                'duration_days': 30,
            },
            {
                'plan_type': 'BASIC',
                'name': 'Basic Plan',
                'price_usd': 19.99,
                'tokens': 20000,
                'duration_days': 30,
            },
            {
                'plan_type': 'PREMIUM',
                'name': 'Premium Plan',
                'price_usd': 59.99,
                'tokens': 66000,
                'duration_days': 30,
            },
            {
                'plan_type': 'ANNUAL',
                'name': 'Annual Plan',
                'price_usd': 199.99,
                'tokens': 240000,
                'duration_days': 365,
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for plan_data in plans:
            plan, created = MembershipPlan.objects.get_or_create(
                plan_type=plan_data['plan_type'],
                defaults=plan_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ 创建套餐: {plan.name} - ${plan.price_usd}')
                )
            else:
                # 更新现有套餐
                for key, value in plan_data.items():
                    if key != 'plan_type':
                        setattr(plan, key, value)
                plan.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'🔄 更新套餐: {plan.name} - ${plan.price_usd}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 套餐初始化完成: 创建 {created_count} 个，更新 {updated_count} 个'
            )
        ) 