# Default Django app config
default_app_config = 'core.apps.CoreConfig'

import re
from django.conf import settings
from django.middleware.csrf import CsrfViewMiddleware
from django.utils.deprecation import MiddlewareMixin


class CustomCSRFMiddleware(CsrfViewMiddleware):
    """
    Custom CSRF middleware that allows specific URLs to be exempt from CSRF checks
    """
    def process_request(self, request):
        # Check if in exemption list
        if hasattr(settings, 'CSRF_EXEMPT_URLS'):
            for pattern in settings.CSRF_EXEMPT_URLS:
                if re.match(pattern, request.path_info):
                    # Skip CSRF check for exempt URLs
                    setattr(request, '_dont_enforce_csrf_checks', True)
                    return None
        
        # For non-exempt URLs, continue with normal CSRF check
        return super().process_request(request)
