from django import forms

class GenerateForm(forms.Form):
    prompt    = forms.CharField(widget=forms.Textarea, label='Prompt')
    media_url = forms.URLField(required=False, label='Image/Video URL')
    media = forms.FileField(required=False, label='Media File Upload')
    
    # 添加所有前端可能发送的字段，避免validation失败
    mode = forms.CharField(required=False, initial='auto', label='Generation Mode')
    session_id = forms.CharField(required=False, label='Session ID')
    has_references = forms.CharField(required=False, label='Has References')
    referenced_message_ids = forms.CharField(required=False, label='Referenced Message IDs')
    references = forms.CharField(required=False, label='References JSON')

class PaymentForm(forms.Form):
    """Payment form"""
    order_id = forms.UUIDField(required=True, label='Order ID')
    email = forms.EmailField(required=False, label='Email')
    service_type = forms.Char<PERSON>ield(required=True, label='Service Type')
    session_id = forms.CharField(required=True, label='Session ID')
