from django.core.mail import send_mail
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

# 邮件模板定义
EMAIL_TEMPLATES = {
    'verification': {
        'en': {
            'subject': 'Verify Your MirageMakers AI Account',
            'html_template': """
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333;">
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 20px; text-align: center;">
                    <h1 style="color: white; margin: 0;">MirageMakers AI</h1>
                </div>
                
                <div style="padding: 30px; background: #f8f9fa;">
                    <h2 style="color: #333; margin-bottom: 20px;">Verify Your Email Address</h2>
                    
                    <p>Hello {user_name},</p>
                    
                    <p>Thank you for registering with MirageMakers AI! Please use the following verification code to verify your email address:</p>
                    
                    <div style="background: white; border: 2px solid #00d2ff; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                        <h1 style="color: #00d2ff; font-size: 32px; margin: 0; letter-spacing: 8px;">{code}</h1>
                    </div>
                    
                    <p style="color: #666; font-size: 14px;">
                        This verification code will expire in 10 minutes. If you did not register for a MirageMakers AI account, please ignore this email.
                    </p>
                    
                    <div style="margin-top: 30px; text-align: center;">
                        <p style="color: #999; font-size: 12px;">
                            MirageMakers AI - Powerful AI Generation Platform<br>
                            This email was sent automatically, please do not reply.
                        </p>
                    </div>
                </div>
            </div>
            """,
            'text_template': """
            MirageMakers AI Email Verification
            
            Hello {user_name},
            
            Thank you for registering with MirageMakers AI!
            
            Your verification code is: {code}
            
            This verification code will expire in 10 minutes.
            
            If you did not register for a MirageMakers AI account, please ignore this email.
            
            MirageMakers AI Team
            """
        },
        'zh': {
            'subject': '验证您的MirageMakers AI账户',
            'html_template': """
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333;">
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 20px; text-align: center;">
                    <h1 style="color: white; margin: 0;">MirageMakers AI</h1>
                </div>
                
                <div style="padding: 30px; background: #f8f9fa;">
                    <h2 style="color: #333; margin-bottom: 20px;">验证您的邮箱地址</h2>
                    
                    <p>您好 {user_name}，</p>
                    
                    <p>感谢您注册MirageMakers AI！请使用以下验证码验证您的邮箱地址：</p>
                    
                    <div style="background: white; border: 2px solid #00d2ff; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                        <h1 style="color: #00d2ff; font-size: 32px; margin: 0; letter-spacing: 8px;">{code}</h1>
                    </div>
                    
                    <p style="color: #666; font-size: 14px;">
                        此验证码将在10分钟后过期。如果您没有注册MirageMakers AI账户，请忽略此邮件。
                    </p>
                    
                    <div style="margin-top: 30px; text-align: center;">
                        <p style="color: #999; font-size: 12px;">
                            MirageMakers AI - 强大的AI生成平台<br>
                            此邮件由系统自动发送，请勿回复。
                        </p>
                    </div>
                </div>
            </div>
            """,
            'text_template': """
            MirageMakers AI 邮箱验证
            
            您好 {user_name}，
            
            感谢您注册MirageMakers AI！
            
            您的验证码是：{code}
            
            此验证码将在10分钟后过期。
            
            如果您没有注册MirageMakers AI账户，请忽略此邮件。
            
            MirageMakers AI团队
            """
        }
    },
    'password_reset': {
        'en': {
            'subject': 'Reset Your MirageMakers AI Password',
            'html_template': """
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333;">
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 20px; text-align: center;">
                    <h1 style="color: white; margin: 0;">MirageMakers AI</h1>
                </div>
                
                <div style="padding: 30px; background: #f8f9fa;">
                    <h2 style="color: #333; margin-bottom: 20px;">Reset Your Password</h2>
                    
                    <p>Hello {user_name},</p>
                    
                    <p>We received a request to reset your password. Please use the following verification code to reset your password:</p>
                    
                    <div style="background: white; border: 2px solid #ff6b6b; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                        <h1 style="color: #ff6b6b; font-size: 32px; margin: 0; letter-spacing: 8px;">{code}</h1>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{reset_url}" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block; font-weight: bold;">
                            Reset Password Now
                        </a>
                    </div>

                    <p style="color: #666; font-size: 14px;">
                        This reset code will expire in 10 minutes. If you did not request a password reset, please ignore this email and your password will remain unchanged.
                    </p>

                    <p style="color: #999; font-size: 12px;">
                        If the button doesn't work, you can also visit: {reset_url}
                    </p>
                    
                    <div style="margin-top: 30px; text-align: center;">
                        <p style="color: #999; font-size: 12px;">
                            MirageMakers AI - Powerful AI Generation Platform<br>
                            This email was sent automatically, please do not reply.
                        </p>
                    </div>
                </div>
            </div>
            """,
            'text_template': """
            MirageMakers AI Password Reset
            
            Hello {user_name},
            
            We received a request to reset your password.
            
            Your reset verification code is: {code}
            
            This reset code will expire in 10 minutes.
            
            If you did not request a password reset, please ignore this email.
            
            MirageMakers AI Team
            """
        },
        'zh': {
            'subject': '重置您的MirageMakers AI密码',
            'html_template': """
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333;">
                <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 20px; text-align: center;">
                    <h1 style="color: white; margin: 0;">MirageMakers AI</h1>
                </div>
                
                <div style="padding: 30px; background: #f8f9fa;">
                    <h2 style="color: #333; margin-bottom: 20px;">重置您的密码</h2>
                    
                    <p>您好 {user_name}，</p>
                    
                    <p>我们收到了您重置密码的请求。请使用以下验证码重置您的密码：</p>
                    
                    <div style="background: white; border: 2px solid #ff6b6b; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                        <h1 style="color: #ff6b6b; font-size: 32px; margin: 0; letter-spacing: 8px;">{code}</h1>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{reset_url}" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block; font-weight: bold;">
                            立即重置密码
                        </a>
                    </div>

                    <p style="color: #666; font-size: 14px;">
                        此重置码将在10分钟后过期。如果您没有请求重置密码，请忽略此邮件，您的密码将保持不变。
                    </p>

                    <p style="color: #999; font-size: 12px;">
                        如果按钮无法使用，您也可以访问：{reset_url}
                    </p>
                    
                    <div style="margin-top: 30px; text-align: center;">
                        <p style="color: #999; font-size: 12px;">
                            MirageMakers AI - 强大的AI生成平台<br>
                            此邮件由系统自动发送，请勿回复。
                        </p>
                    </div>
                </div>
            </div>
            """,
            'text_template': """
            MirageMakers AI 密码重置
            
            您好 {user_name}，
            
            我们收到了您重置密码的请求。
            
            您的重置验证码是：{code}
            
            此重置码将在10分钟后过期。
            
            如果您没有请求重置密码，请忽略此邮件。
            
            MirageMakers AI团队
            """
        }
    }
}


def send_verification_email(user, verification_code, language='en'):
    """发送邮箱验证邮件"""
    try:
        # 获取语言模板，默认使用英文
        lang_templates = EMAIL_TEMPLATES['verification'].get(language, EMAIL_TEMPLATES['verification']['en'])
        
        subject = lang_templates['subject']
        
        # 邮件内容
        html_content = lang_templates['html_template'].format(
            user_name=user.name,
            code=verification_code
        )
        
        # 纯文本版本
        text_content = lang_templates['text_template'].format(
            user_name=user.name,
            code=verification_code
        )
        
        # 记录发送前的配置信息
        logger.info(f"Attempting to send verification email to {user.email}")
        logger.info(f"Email backend: {settings.EMAIL_BACKEND}")
        logger.info(f"SMTP settings - Host: {settings.EMAIL_HOST}, Port: {settings.EMAIL_PORT}")
        logger.info(f"SMTP TLS settings - USE_TLS: {settings.EMAIL_USE_TLS}")
        logger.info(f"From email: {settings.DEFAULT_FROM_EMAIL}")
        
        # 发送邮件 - 不再静默失败
        send_mail(
            subject=subject,
            message=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False,  # 改为False，以便捕获和记录错误
        )
        
        logger.info(f"Verification email sent successfully to {user.email} (language: {language})")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send verification email to {user.email}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error details: {str(e)}")
        logger.error(f"Email settings: HOST={settings.EMAIL_HOST}, PORT={settings.EMAIL_PORT}, TLS={settings.EMAIL_USE_TLS}")
        # 在开发环境下，即使出错也不阻止注册流程
        if settings.DEBUG:
            logger.warning("Debug mode: Proceeding despite email error")
            return True
        return False


def send_password_reset_email(user, reset_code, language='en'):
    """发送密码重置邮件"""
    try:
        # 获取语言模板，默认使用英文
        lang_templates = EMAIL_TEMPLATES['password_reset'].get(language, EMAIL_TEMPLATES['password_reset']['en'])
        
        subject = lang_templates['subject']
        
        # 生成重置页面URL
        frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
        reset_url = f"{frontend_url}/auth/reset-password?email={user.email}"
        
        html_content = lang_templates['html_template'].format(
            user_name=user.name,
            code=reset_code,
            reset_url=reset_url
        )
        
        text_content = lang_templates['text_template'].format(
            user_name=user.name,
            code=reset_code,
            reset_url=reset_url
        )
        
        # 记录发送前的配置信息
        logger.info(f"Attempting to send password reset email to {user.email}")
        logger.info(f"Email backend: {settings.EMAIL_BACKEND}")
        logger.info(f"SMTP settings - Host: {settings.EMAIL_HOST}, Port: {settings.EMAIL_PORT}")
        logger.info(f"SMTP TLS settings - USE_TLS: {settings.EMAIL_USE_TLS}")
        logger.info(f"From email: {settings.DEFAULT_FROM_EMAIL}")
        
        send_mail(
            subject=subject,
            message=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False,  # 改为False，以便捕获和记录错误
        )
        
        logger.info(f"Password reset email sent successfully to {user.email} (language: {language})")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send password reset email to {user.email}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error details: {str(e)}")
        logger.error(f"Email settings: HOST={settings.EMAIL_HOST}, PORT={settings.EMAIL_PORT}, TLS={settings.EMAIL_USE_TLS}")
        # 在开发环境下，即使出错也不阻止流程
        if settings.DEBUG:
            logger.warning("Debug mode: Proceeding despite email error")
            return True
        return False


def send_welcome_email(user):
    """发送欢迎邮件"""
    try:
        subject = '欢迎加入MirageMakers AI！'
        
        html_content = f"""
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333;">
            <div style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); padding: 20px; text-align: center;">
                <h1 style="color: white; margin: 0;">MirageMakers AI</h1>
            </div>
            
            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-bottom: 20px;">欢迎加入MirageMakers AI！</h2>
                
                <p>您好 {user.name}，</p>
                
                <p>恭喜您成功验证邮箱并加入MirageMakers AI！</p>
                
                <div style="background: white; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #00d2ff; margin-top: 0;">您的账户信息：</h3>
                    <ul style="color: #666;">
                        <li>邮箱：{user.email}</li>
                        <li>初始额度：{user.tokens} 次</li>
                        <li>注册时间：{user.created_at.strftime('%Y年%m月%d日')}</li>
                    </ul>
                </div>
                
                <p>现在您可以开始体验强大的AI生成功能了！</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')}" 
                       style="background: linear-gradient(135deg, #00d2ff 0%, #7a5ffa 100%); 
                              color: white; text-decoration: none; padding: 12px 24px; 
                              border-radius: 6px; display: inline-block;">
                        开始使用 MirageMakers AI
                    </a>
                </div>
                
                <div style="margin-top: 30px; text-align: center;">
                    <p style="color: #999; font-size: 12px;">
                        MirageMakers AI - 强大的AI生成平台<br>
                        此邮件由系统自动发送，请勿回复。
                    </p>
                </div>
            </div>
        </div>
        """
        
        text_content = f"""
        欢迎加入MirageMakers AI！
        
        您好 {user.name}，
        
        恭喜您成功验证邮箱并加入MirageMakers AI！
        
        您的账户信息：
        - 邮箱：{user.email}
        - 初始额度：{user.tokens} 次
        - 注册时间：{user.created_at.strftime('%Y年%m月%d日')}
        
        现在您可以开始体验强大的AI生成功能了！
        
        访问：{getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')}
        
        MirageMakers AI团队
        """
        
        send_mail(
            subject=subject,
            message=text_content,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False,
        )
        
        logger.info(f"Welcome email sent to {user.email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")
        return False 