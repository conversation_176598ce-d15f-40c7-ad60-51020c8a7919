from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password, check_password
from rest_framework.authtoken.models import Token
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
import json
import logging
from rest_framework.authentication import TokenAuthentication

from .models import User, EmailVerification, PasswordReset, ChatSession, ChatMessage, BetaApplication
from .email_utils import (send_verification_email, send_welcome_email,
                          send_password_reset_email)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def register(request):
    """用户注册"""
    try:
        # 使用DRF的request.data而不是json.loads(request.body)
        data = request.data
        name = data.get('name')
        email = data.get('email')
        password = data.get('password')
        language = data.get('language', 'en')  # 获取语言参数，默认英文
        
        # 验证必填字段
        if not all([name, email, password]):
            return Response({
                'message': '请填写所有必填字段'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查邮箱是否已存在
        if User.objects.filter(email=email).exists():
            return Response({
                'message': '该邮箱已被注册'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建用户（未验证状态）
        user = User.objects.create(
            username=email,
            email=email,
            name=name,
            password=make_password(password),
            is_email_verified=False
        )
        
        # 创建邮箱验证记录
        verification = EmailVerification.objects.create(user=user)
        
        # 发送验证邮件（带语言参数）
        email_sent = send_verification_email(user, verification.code, language)
        
        if not email_sent:
            return Response({
                'message': '邮件发送失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'message': '注册成功，请查收邮件并验证您的邮箱',
            'email': user.email,
            'requires_verification': True
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        return Response({
            'message': f'注册失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def verify_email(request):
    """验证邮箱"""
    try:
        data = request.data
        email = data.get('email')
        code = data.get('code')
        
        if not all([email, code]):
            return Response({
                'message': '请提供邮箱和验证码'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 查找用户
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 查找验证记录
        verification = EmailVerification.objects.filter(
            user=user,
            code=code,
            is_used=False
        ).first()
        
        if not verification:
            return Response({
                'message': '验证码无效'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not verification.is_valid():
            return Response({
                'message': '验证码已过期'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 标记验证码已使用和用户邮箱已验证
        verification.is_used = True
        verification.save()
        
        user.is_email_verified = True
        user.save()
        
        # 创建token
        token, created = Token.objects.get_or_create(user=user)
        
        # 发送欢迎邮件
        send_welcome_email(user)
        
        return Response({
            'message': '邮箱验证成功',
            'token': token.key,
            'user': {
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'tokens': user.tokens,
                'is_email_verified': user.is_email_verified
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'验证失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def resend_verification(request):
    """重新发送验证邮件"""
    try:
        data = request.data
        email = data.get('email')
        language = data.get('language', 'en')  # 获取语言参数，默认英文
        
        if not email:
            return Response({
                'message': '请提供邮箱地址'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        if user.is_email_verified:
            return Response({
                'message': '邮箱已验证，无需重复验证'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建新的验证记录
        verification = EmailVerification.objects.create(user=user)
        
        # 发送验证邮件（带语言参数）
        email_sent = send_verification_email(user, verification.code, language)
        
        if not email_sent:
            return Response({
                'message': '邮件发送失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'message': '验证邮件已重新发送'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'发送失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def login(request):
    """用户登录"""
    try:
        data = request.data
        email = data.get('email')
        password = data.get('password')
        
        # 验证必填字段
        if not all([email, password]):
            return Response({
                'message': '请填写邮箱和密码'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证用户
        user = authenticate(username=email, password=password)
        if not user:
            return Response({
                'message': '邮箱或密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 检查邮箱是否已验证
        if not user.is_email_verified:
            return Response({
                'message': '请先验证您的邮箱',
                'requires_verification': True,
                'email': user.email
            }, status=status.HTTP_403_FORBIDDEN)
        
        # 获取或创建token
        token, created = Token.objects.get_or_create(user=user)
        
        # 更新最后登录时间
        user.last_login_at = timezone.now()
        user.save()
        
        return Response({
            'message': '登录成功',
            'token': token.key,
            'user': {
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'tokens': user.tokens,
                'current_plan': user.current_plan or 'Free Plan',
                'tokens_expires_at': user.tokens_expires_at.isoformat() if user.tokens_expires_at else None,
                'total_generations': user.total_generations,
                'is_email_verified': user.is_email_verified,
                'created_at': user.created_at.isoformat(),
                'last_login_at': user.last_login_at.isoformat() if user.last_login_at else None
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'登录失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@authentication_classes([TokenAuthentication])
def logout(request):
    """用户登出"""
    try:
        # 检查用户是否已认证
        if not request.user.is_authenticated:
            return Response({
                'message': '用户未登录，无需注销'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查用户是否有token
        if hasattr(request.user, 'auth_token'):
            request.user.auth_token.delete()
            return Response({
                'message': '登出成功'
            }, status=status.HTTP_200_OK)
        else:
            # 用户已认证但没有token（可能已经删除了）
            return Response({
                'message': '登出成功'
            }, status=status.HTTP_200_OK)
            
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f'用户登出失败: {str(e)}', exc_info=True)
        return Response({
            'message': f'登出失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def profile(request):
    """获取用户信息"""
    try:
        user = request.user
        logger = logging.getLogger(__name__)
        
        # 检查用户是否已认证
        if not user.is_authenticated:
            return Response({
                'message': '需要登录才能访问个人信息'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        logger.info(f"Profile request for user: {user.email}, tokens: {user.tokens}")
        
        user_data = {
            'id': str(user.id),
            'name': user.name,
            'email': user.email,
            'username': user.username,  # 添加username字段
            'tokens': user.tokens,
            'current_plan': user.current_plan or 'Free Plan',
            'tokens_expires_at': user.tokens_expires_at.isoformat() if user.tokens_expires_at else None,
            'total_generations': user.total_generations,
            'is_email_verified': user.is_email_verified,
            'created_at': user.created_at.isoformat(),
            'last_login_at': user.last_login_at.isoformat() if user.last_login_at else None
        }
        
        return Response({
            'user': user_data
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f'获取用户信息失败: {str(e)}', exc_info=True)
        return Response({
            'message': f'获取用户信息失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 聊天历史相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def chat_sessions(request):
    """获取用户聊天会话列表"""
    try:
        sessions = ChatSession.objects.filter(user=request.user).order_by('-updated_at')
        sessions_data = []
        
        for session in sessions:
            # 获取最后一条消息作为预览
            last_message = session.messages.last()
            preview = last_message.content[:50] + "..." if last_message and len(last_message.content) > 50 else (last_message.content if last_message else "")
            
            sessions_data.append({
                'id': str(session.id),
                'title': session.title,
                'preview': preview,
                'message_count': session.messages.count(),
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat()
            })
        
        return Response({
            'sessions': sessions_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'获取会话列表失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def chat_messages(request, session_id):
    """获取指定会话的消息"""
    try:
        session = ChatSession.objects.get(id=session_id, user=request.user)
        messages = session.messages.all()
        
        messages_data = []
        for message in messages:
            messages_data.append({
                'id': str(message.id),
                'type': message.type,
                'content': message.content,
                'image_url': message.image_url,
                'video_url': message.video_url,
                'metadata': message.metadata,
                'created_at': message.created_at.isoformat()
            })
        
        return Response({
            'session': {
                'id': str(session.id),
                'title': session.title,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat()
            },
            'messages': messages_data
        }, status=status.HTTP_200_OK)
        
    except ChatSession.DoesNotExist:
        return Response({
            'message': '会话不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'message': f'获取消息失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def create_chat_session(request):
    """创建新的聊天会话"""
    try:
        data = json.loads(request.body)
        title = data.get('title', '新对话')
        
        session = ChatSession.objects.create(
            user=request.user,
            title=title
        )
        
        return Response({
            'session': {
                'id': str(session.id),
                'title': session.title,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat()
            }
        }, status=status.HTTP_201_CREATED)
        
    except json.JSONDecodeError:
        return Response({
            'message': '无效的JSON数据'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'message': f'创建会话失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE', 'POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def delete_chat_session(request, session_id):
    """删除聊天会话"""
    try:
        session = ChatSession.objects.get(id=session_id, user=request.user)
        session.delete()
        
        return Response({
            'message': '会话删除成功'
        }, status=status.HTTP_200_OK)
        
    except ChatSession.DoesNotExist:
        return Response({
            'message': '会话不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'message': f'删除会话失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def update_chat_session(request, session_id):
    """更新聊天会话（重命名）"""
    try:
        session = ChatSession.objects.get(id=session_id, user=request.user)
        data = json.loads(request.body)
        
        if 'title' in data:
            session.title = data['title']
            session.save()
        
        return Response({
            'session': {
                'id': str(session.id),
                'title': session.title,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat()
            }
        }, status=status.HTTP_200_OK)
        
    except ChatSession.DoesNotExist:
        return Response({
            'message': '会话不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except json.JSONDecodeError:
        return Response({
            'message': '无效的JSON数据'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'message': f'更新会话失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def check_email(request):
    """检查邮箱是否存在"""
    try:
        data = request.data
        email = data.get('email')
        
        if not email:
            return Response({
                'message': '请提供邮箱地址',
                'exists': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查邮箱是否存在
        exists = User.objects.filter(email=email.strip()).exists()
        
        return Response({
            'exists': exists,
            'email': email.strip()
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'检查失败: {str(e)}',
            'exists': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def forgot_password(request):
    """发送密码重置邮件"""
    try:
        data = request.data
        email = data.get('email')
        language = data.get('language', 'en')  # 获取语言参数，默认英文
        
        if not email:
            return Response({
                'message': '请提供邮箱地址'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            # 为了安全考虑，即使用户不存在也返回成功消息
            return Response({
                'message': '如果该邮箱存在，我们已发送重置链接'
            }, status=status.HTTP_200_OK)
        
        # 创建密码重置记录
        reset = PasswordReset.objects.create(user=user)
        
        # 发送重置邮件（带语言参数）
        email_sent = send_password_reset_email(user, reset.code, language)
        
        if not email_sent:
            return Response({
                'message': '邮件发送失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'message': '如果该邮箱存在，我们已发送重置链接'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'发送失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([TokenAuthentication])
def reset_password(request):
    """重置密码"""
    try:
        data = request.data
        email = data.get('email')
        code = data.get('code')
        new_password = data.get('password')
        
        if not all([email, code, new_password]):
            return Response({
                'message': '请提供所有必要信息'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if len(new_password) < 6:
            return Response({
                'message': '密码至少需要6个字符'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({
                'message': '重置码无效'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 查找重置记录
        reset = PasswordReset.objects.filter(
            user=user,
            code=code,
            is_used=False
        ).first()
        
        if not reset:
            return Response({
                'message': '重置码无效'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not reset.is_valid():
            return Response({
                'message': '重置码已过期'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 更新密码
        user.password = make_password(new_password)
        # 重置密码成功时，同时验证邮箱
        # （因为用户已通过邮箱验证码验证）
        user.is_email_verified = True
        user.save()
        
        # 标记重置码已使用
        reset.is_used = True
        reset.save()
        
        return Response({
            'message': '密码重置成功，请使用新密码登录'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'message': f'重置失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@authentication_classes([TokenAuthentication])
def save_chat_message(request):
    """保存聊天消息"""
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')
        message_type = data.get('type')
        content = data.get('content', '')
        image_url = data.get('image_url')
        video_url = data.get('video_url')
        metadata = data.get('metadata', {})
        
        if not session_id:
            return Response({
                'message': '会话ID必填'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 查找会话
        try:
            session = ChatSession.objects.get(id=session_id, user=request.user)
        except ChatSession.DoesNotExist:
            return Response({
                'message': '会话不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 创建消息
        message = ChatMessage.objects.create(
            session=session,
            type=message_type,
            content=content,
            image_url=image_url,
            video_url=video_url,
            metadata=metadata
        )
        
        return Response({
            'message': '消息保存成功',
            'message_id': str(message.id)
        }, status=status.HTTP_201_CREATED)
        
    except json.JSONDecodeError:
        return Response({
            'message': '无效的JSON数据'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'message': f'保存消息失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
def beta_apply(request):
    """内测申请提交"""
    try:
        data = request.data
        email = data.get('email')
        name = data.get('name', '')
        company = data.get('company', '')
        purpose = data.get('purpose')
        source = data.get('source')
        source_detail = data.get('source_detail', '')
        
        # 验证必填字段
        if not all([email, purpose, source]):
            return Response({
                'message': 'Please fill in all required fields'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证邮箱格式
        import re
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return Response({
                'message': 'Please enter a valid email address'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查是否已经申请过
        if BetaApplication.objects.filter(email=email).exists():
            return Response({
                'message': 'This email has already submitted an application. We will process it as soon as possible.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建申请记录
        application = BetaApplication.objects.create(
            email=email,
            name=name,
            company=company,
            purpose=purpose,
            source=source,
            source_detail=source_detail
        )
        
        return Response({
            'message': 'Application submitted successfully! We will review your application and contact you via email soon.',
            'application_id': str(application.id)
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        logging.error(f"Beta application error: {str(e)}")
        return Response({
            'message': f'申请提交失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
@authentication_classes([TokenAuthentication])
def change_password(request):
    """修改密码"""
    try:
        # 检查用户是否登录
        if not request.user.is_authenticated:
            return Response({
                'message': '请先登录'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        data = request.data
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        
        # 验证必填字段
        if not all([current_password, new_password]):
            return Response({
                'message': '请提供当前密码和新密码'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证新密码长度
        if len(new_password) < 6:
            return Response({
                'message': '新密码至少需要6个字符'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证当前密码是否正确
        if not check_password(current_password, request.user.password):
            return Response({
                'message': '当前密码不正确'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查新密码是否与当前密码相同
        if check_password(new_password, request.user.password):
            return Response({
                'message': '新密码不能与当前密码相同'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 更新密码
        request.user.password = make_password(new_password)
        request.user.save()
        
        return Response({
            'message': '密码修改成功'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logging.error(f"Change password error: {str(e)}")
        return Response({
            'message': f'密码修改失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['GET'])
@permission_classes([AllowAny])
def csrf_token(request):
    """获取CSRF token"""
    from django.middleware.csrf import get_token
    try:
        token = get_token(request)
        return Response({
            'csrfToken': token
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'message': f'获取CSRF token失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 


@api_view(['GET'])
@authentication_classes([TokenAuthentication])
def get_session_media_messages(request, session_id):
    """获取会话中的媒体消息，用于@引用功能"""
    try:
        # 验证会话所有权
        if request.user.is_authenticated:
            session = ChatSession.objects.get(id=session_id, user=request.user)
        else:
            # 匿名用户也可以访问自己的会话
            session = ChatSession.objects.get(id=session_id)
        
        # 使用引用服务获取媒体消息
        from core.services.reference_service import MessageReferenceService
        media_messages = MessageReferenceService.get_session_media_messages(session, limit=30)
        
        return Response({
            'session_id': str(session.id),
            'media_messages': media_messages,
            'total_count': len(media_messages)
        }, status=status.HTTP_200_OK)
        
    except ChatSession.DoesNotExist:
        return Response({
            'message': '会话不存在或无权限访问'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'message': f'获取媒体消息失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@authentication_classes([TokenAuthentication])
def process_message_with_references(request):
    """处理包含引用的消息"""
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')
        content = data.get('content', '')
        mode = data.get('mode', 'text')
        
        if not session_id:
            return Response({
                'message': '会话ID必填'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取会话
        if request.user.is_authenticated:
            session = ChatSession.objects.get(id=session_id, user=request.user)
        else:
            session = ChatSession.objects.get(id=session_id)
        
        # 处理引用
        from core.services.reference_service import MessageReferenceService, MultiModalProcessor
        
        processed_content, referenced_messages, reference_context = MessageReferenceService.resolve_references(
            content, session
        )
        
        # 准备多模态上下文
        multimodal_context = MessageReferenceService.prepare_multimodal_context(referenced_messages)
        
        # 处理多模态请求
        if referenced_messages:
            processing_result = MultiModalProcessor.process_with_references(
                processed_content, multimodal_context, mode
            )
            
            # 如果有错误，直接返回
            if 'error' in processing_result:
                return Response({
                    'message': processing_result['error']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'success': True,
                'processed_content': processed_content,
                'reference_context': reference_context,
                'multimodal_context': multimodal_context,
                'processing_result': processing_result,
                'referenced_message_ids': [str(msg.id) for msg in referenced_messages]
            }, status=status.HTTP_200_OK)
        else:
            # 没有引用，返回原内容
            return Response({
                'success': True,
                'processed_content': processed_content,
                'has_references': False
            }, status=status.HTTP_200_OK)
        
    except ChatSession.DoesNotExist:
        return Response({
            'message': '会话不存在或无权限访问'
        }, status=status.HTTP_404_NOT_FOUND)
    except json.JSONDecodeError:
        return Response({
            'message': '无效的JSON数据'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({
            'message': f'处理引用消息失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 