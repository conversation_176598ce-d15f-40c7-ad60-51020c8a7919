from pydantic import BaseModel, HttpUrl
from typing import List
from django.db import models
from django.contrib.auth.models import AbstractUser
import uuid
import random
import string
from datetime import timedelta
from django.utils import timezone
from decimal import Decimal

# Pydantic models - for API requests/responses
class Attachment(BaseModel):
    """Attachment model (input image or video)"""
    type: str  # "image" or "video"
    url: HttpUrl  # URL of the attachment

class RouteRequest(BaseModel):
    """Router request containing user request and any attachments"""
    session_id: str
    user_id: str = "guest"
    utterance: str
    attachments: List[Attachment] = []

# Django models - user system
class User(AbstractUser):
    """User model"""
    INVITATION_TYPE_CHOICES = [
        ('NORMAL', 'Normal Registration'),
        ('BETA_APPLICATION', 'Beta Application'),
        ('ADMIN_INVITATION', 'Admin Invitation'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True, verbose_name='Email')
    name = models.CharField(max_length=100, verbose_name='Name')
    avatar = models.URLField(blank=True, null=True, verbose_name='Avatar')
    
    # Email verification
    is_email_verified = models.BooleanField(default=False, verbose_name='Email Verified')
    
    # Token system
    tokens = models.IntegerField(default=0, verbose_name='Token Balance')
    tokens_expires_at = models.DateTimeField(null=True, blank=True, verbose_name='Tokens Expiry Date')
    tokens_purchased_at = models.DateTimeField(null=True, blank=True, verbose_name='Tokens Purchase Date')
    
    # Membership
    current_plan = models.CharField(max_length=20, blank=True, verbose_name='Current Plan')
    plan_purchased_at = models.DateTimeField(null=True, blank=True, verbose_name='Plan Purchase Date')
    next_renewal_at = models.DateTimeField(null=True, blank=True, verbose_name='Next Renewal Date')
    total_generations = models.IntegerField(default=0, verbose_name='Total Generations')
    
    # Invitation type
    invitation_type = models.CharField(
        max_length=20, 
        choices=INVITATION_TYPE_CHOICES, 
        default='NORMAL', 
        verbose_name='Invitation Type'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Registration Time')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated Time')
    last_login_at = models.DateTimeField(null=True, blank=True, verbose_name='Last Login Time')

    # 重新定义groups和user_permissions以使用正确的表名
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name="core_user_set",
        related_query_name="core_user",
        db_table='auth_user_groups'
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name="core_user_set",
        related_query_name="core_user", 
        db_table='auth_user_user_permissions'
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['name']
    
    class Meta:
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        db_table = 'core_user'
    
    def __str__(self):
        return f"{self.name} ({self.email})"
    
    @property 
    def is_tokens_expired(self):
        """Check if user's tokens are expired"""
        if not self.tokens_expires_at:
            return False
        return timezone.now() > self.tokens_expires_at
    
class MembershipPlan(models.Model):
    """Membership plan model"""
    PLAN_TYPES = [
        ('TRIAL', 'Trial'),
        ('BASIC', 'Basic'),
        ('PREMIUM', 'Premium'),
        ('ANNUAL', 'Annual'),
    ]
    
    plan_type = models.CharField(max_length=20, choices=PLAN_TYPES, unique=True, verbose_name='Plan Type')
    name = models.CharField(max_length=100, verbose_name='Plan Name')
    price_usd = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='Price (USD)')
    tokens = models.IntegerField(verbose_name='Tokens Included')
    duration_days = models.IntegerField(verbose_name='Duration (Days)')
    description = models.TextField(blank=True, verbose_name='Description')
    is_active = models.BooleanField(default=True, verbose_name='Is Active')
    
    class Meta:
        verbose_name = 'Membership Plan'
        verbose_name_plural = 'Membership Plans'

    def __str__(self):
        return f"{self.name} - ${self.price_usd}"

class PaymentOrder(models.Model):
    """Payment order model - Stripe only"""
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('REFUNDED', 'Refunded'),
    ]
    
    PAYMENT_TYPE_CHOICES = [
        ('ONE_TIME', 'One Time'),
        ('SUBSCRIPTION', 'Subscription'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='User')
    plan_type = models.CharField(max_length=20, verbose_name='Plan Type')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='Amount')
    tokens_to_add = models.IntegerField(verbose_name='Tokens to Add')
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES, default='ONE_TIME', verbose_name='Payment Type')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', verbose_name='Status')
    
    # Stripe fields only
    stripe_payment_intent_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='Stripe Payment Intent ID')
    stripe_session_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='Stripe Session ID')
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='Stripe Customer ID')
    stripe_subscription_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='Stripe Subscription ID')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='Completed At')
    
    class Meta:
        verbose_name = 'Payment Order'
        verbose_name_plural = 'Payment Orders'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Order {self.id} - {self.user.email} - {self.plan_type} - ${self.amount}"
    
class StripeSubscription(models.Model):
    """Stripe subscription model"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='User')
    stripe_subscription_id = models.CharField(max_length=255, unique=True, verbose_name='Stripe Subscription ID')
    stripe_customer_id = models.CharField(max_length=255, verbose_name='Stripe Customer ID')
    plan_type = models.CharField(max_length=20, verbose_name='Plan Type')
    status = models.CharField(max_length=50, verbose_name='Status')
    
    # Subscription periods
    current_period_start = models.DateTimeField(verbose_name='Current Period Start')
    current_period_end = models.DateTimeField(verbose_name='Current Period End')
    trial_start = models.DateTimeField(null=True, blank=True, verbose_name='Trial Start')
    trial_end = models.DateTimeField(null=True, blank=True, verbose_name='Trial End')
    canceled_at = models.DateTimeField(null=True, blank=True, verbose_name='Canceled At')
    
    # Pricing
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='Amount')
    currency = models.CharField(max_length=3, default='USD', verbose_name='Currency')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    
    class Meta:
        verbose_name = 'Stripe Subscription'
        verbose_name_plural = 'Stripe Subscriptions'
        ordering = ['-created_at']

    def __str__(self):
        return f"Subscription {self.stripe_subscription_id} - {self.user.email}"

class TokenConsumption(models.Model):
    """Token consumption record"""
    SERVICE_TYPE_CHOICES = [
        ('image_gen', 'Text to Image'),
        ('img_edit', 'Image Editing'),
        ('video_gen', 'Text to Video'),
        ('img2video', 'Image to Video'),
        ('multi_img_video', 'Multi-Image to Video'),
        ('video_keyframe', 'Video Keyframe Extraction'),
        ('chat_api', 'Chat API'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='User')
    session = models.ForeignKey('ChatSession', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='Chat Session')
    service_type = models.CharField(max_length=20, choices=SERVICE_TYPE_CHOICES, verbose_name='Service Type')
    tokens_used = models.IntegerField(verbose_name='Tokens Used')
    prompt = models.CharField(max_length=500, blank=True, verbose_name='User Prompt')
    result_url = models.TextField(blank=True, null=True, verbose_name='Result URL')
    metadata = models.JSONField(default=dict, blank=True, verbose_name='Metadata')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    
    class Meta:
        verbose_name = 'Token Consumption'
        verbose_name_plural = 'Token Consumptions'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.email} - {self.service_type} - {self.tokens_used} tokens"

class ChatSession(models.Model):
    """Chat session model"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='User')
    title = models.CharField(max_length=200, default='New Conversation', verbose_name='Session Title')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated At')
    
    class Meta:
        verbose_name = 'Chat Session'
        verbose_name_plural = 'Chat Sessions'
        ordering = ['-updated_at']
    
    def __str__(self):
        user_display = self.user.email if self.user else 'Anonymous'
        return f"Session {self.id} - {user_display} - {self.title}"

class ChatMessage(models.Model):
    """Chat Message Model"""
    ROLE_CHOICES = [
        ("user", "User"),
        ("assistant", "Assistant"),
        ("system", "System"),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(ChatSession, related_name="messages", on_delete=models.CASCADE, verbose_name='Session')
    type = models.CharField(max_length=10, choices=ROLE_CHOICES, verbose_name='Message Type')
    content = models.TextField(verbose_name='Message Content')
    
    # Media content
    image_url = models.TextField(blank=True, null=True, verbose_name='Image URL')
    video_url = models.TextField(blank=True, null=True, verbose_name='Video URL')
    
    # 新增：消息引用功能
    referenced_messages = models.ManyToManyField(
        'self', 
        blank=True, 
        symmetrical=False,
        related_name='referenced_by',
        verbose_name='Referenced Messages'
    )
    reference_context = models.JSONField(
        default=dict, 
        blank=True, 
        verbose_name='Reference Context',
        help_text='存储引用的上下文信息，如引用类型、位置等'
    )
    
    # Metadata
    metadata = models.JSONField(default=dict, blank=True, verbose_name='Metadata')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    
    class Meta:
        verbose_name = 'Chat Message'
        verbose_name_plural = 'Chat Messages'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.type}: {self.content[:50]}..."
        
    def get_media_url(self):
        """获取媒体URL"""
        return self.image_url or self.video_url
    
    def get_media_type(self):
        """获取媒体类型"""
        if self.image_url:
            return 'image'
        elif self.video_url:
            return 'video'
        return None
    
    def has_media(self):
        """检查是否包含媒体内容"""
        return bool(self.image_url or self.video_url)

class EmailVerification(models.Model):
    """Email verification model"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='User')
    code = models.CharField(max_length=6, verbose_name='Verification Code')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    expires_at = models.DateTimeField(verbose_name='Expires At')
    is_used = models.BooleanField(default=False, verbose_name='Is Used')
    
    class Meta:
        verbose_name = 'Email Verification'
        verbose_name_plural = 'Email Verifications'
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(minutes=10)
        if not self.code:
            self.code = ''.join(random.choices(string.digits, k=6))
        super().save(*args, **kwargs)
    
    def is_valid(self):
        """检查验证码是否有效（未使用且未过期）"""
        return not self.is_used and timezone.now() <= self.expires_at
    
    def __str__(self):
        return f"{self.user.email} - {self.code}"

class PasswordReset(models.Model):
    """Password reset model"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='User')
    code = models.CharField(max_length=6, verbose_name='Reset Code')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Created At')
    expires_at = models.DateTimeField(verbose_name='Expires At')
    is_used = models.BooleanField(default=False, verbose_name='Is Used')
    
    class Meta:
        verbose_name = 'Password Reset'
        verbose_name_plural = 'Password Resets'
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(minutes=10)
        if not self.code:
            self.code = ''.join(random.choices(string.digits, k=6))
        super().save(*args, **kwargs)
    
    def is_valid(self):
        """检查重置码是否有效（未使用且未过期）"""
        return not self.is_used and timezone.now() <= self.expires_at
    
    def __str__(self):
        return f"{self.user.email} - {self.code}"

# For compatibility with main branch, create alias
Session = ChatSession
Message = ChatMessage
class BetaApplication(models.Model):
    """Beta Application Model"""
    STATUS_CHOICES = [
        ('PENDING', 'Pending Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('INVITED', 'Invited'),
    ]
    
    SOURCE_CHOICES = [
        ('SOCIAL_MEDIA', 'Social Media'),
        ('SEARCH_ENGINE', 'Search Engine'),
        ('FRIEND_REFERRAL', 'Friend Referral'),
        ('TECH_BLOG', 'Tech Blog'),
        ('ONLINE_COMMUNITY', 'Online Community'),
        ('ADVERTISING', 'Advertising'),
        ('ADMIN_INVITATION', 'Admin Invitation'),
        ('OTHER', 'Other'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(verbose_name='Email', help_text='Used for sending beta invitation')
    name = models.CharField(max_length=100, blank=True, verbose_name='Name')
    company = models.CharField(max_length=200, blank=True, verbose_name='Company/Organization')
    
    # Application information
    purpose = models.TextField(verbose_name='Usage Purpose', help_text='Please describe how you plan to use our services')
    source = models.CharField(
        max_length=20, 
        choices=SOURCE_CHOICES, 
        verbose_name='Knowledge Channel',
        help_text='How did you come to know about our services?'
    )
    source_detail = models.CharField(max_length=500, blank=True, verbose_name='Channel Details', help_text='Please describe the specific source in detail')
    
    # Status management
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING', verbose_name='Application Status')
    admin_notes = models.TextField(blank=True, verbose_name='Admin Notes')
    
    # Invitation related
    invitation_code = models.CharField(max_length=32, blank=True, verbose_name='Invitation Code')
    invitation_sent_at = models.DateTimeField(null=True, blank=True, verbose_name='Invitation Sent Time')
    invitation_used_at = models.DateTimeField(null=True, blank=True, verbose_name='Invitation Used Time')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Application Time')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Updated Time')
    reviewed_at = models.DateTimeField(null=True, blank=True, verbose_name='Review Time')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, 
                                   related_name='reviewed_applications', verbose_name='Reviewer')
    
    def generate_invitation_code(self):
        """Generate invitation code"""
        if not self.invitation_code:
            self.invitation_code = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
        return self.invitation_code
    
    def __str__(self):
        return f"{self.email} - {self.get_status_display()}"
    
    class Meta:
        verbose_name = 'Beta Application'
        verbose_name_plural = 'Beta Applications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

