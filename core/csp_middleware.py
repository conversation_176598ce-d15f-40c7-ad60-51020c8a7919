from django.conf import settings
from django.utils.deprecation import MiddlewareMixin


class CSPMiddleware(MiddlewareMixin):
    """
    Content Security Policy 中间件
    为支持外部资源设置适当的CSP头部
    """
    
    def process_response(self, request, response):
        # 只为HTML响应添加CSP头部
        if response.get('Content-Type', '').startswith('text/html'):
            csp_policy = self.build_csp_policy()
            response['Content-Security-Policy'] = csp_policy
        
        return response
    
    def build_csp_policy(self):
        """构建CSP策略字符串"""
        policy_parts = []
        
        # 获取配置或使用默认值
        default_src = getattr(settings, 'CSP_DEFAULT_SRC', ["'self'"])
        script_src = getattr(settings, 'CSP_SCRIPT_SRC', ["'self'"])
        style_src = getattr(settings, 'CSP_STYLE_SRC', ["'self'"])
        font_src = getattr(settings, 'CSP_FONT_SRC', ["'self'"])
        img_src = getattr(settings, 'CSP_IMG_SRC', ["'self'"])
        connect_src = getattr(settings, 'CSP_CONNECT_SRC', ["'self'"])
        frame_src = getattr(settings, 'CSP_FRAME_SRC', ["'self'"])
        object_src = getattr(settings, 'CSP_OBJECT_SRC', ["'none'"])
        base_uri = getattr(settings, 'CSP_BASE_URI', ["'self'"])
        
        # 构建策略
        policy_parts.append(f"default-src {' '.join(default_src)}")
        policy_parts.append(f"script-src {' '.join(script_src)}")
        policy_parts.append(f"style-src {' '.join(style_src)}")
        policy_parts.append(f"font-src {' '.join(font_src)}")
        policy_parts.append(f"img-src {' '.join(img_src)}")
        policy_parts.append(f"connect-src {' '.join(connect_src)}")
        policy_parts.append(f"frame-src {' '.join(frame_src)}")
        policy_parts.append(f"object-src {' '.join(object_src)}")
        policy_parts.append(f"base-uri {' '.join(base_uri)}")
        
        return '; '.join(policy_parts) + ';' 