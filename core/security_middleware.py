from django.conf import settings
from django.utils.deprecation import MiddlewareMixin


class SecurityMiddleware(MiddlewareMixin):
    """
    增强的安全中间件 - 处理CSP和Permissions Policy
    为支付服务和外部资源设置适当的安全头部
    """
    
    def process_response(self, request, response):
        # 只为HTML响应添加安全头部
        if response.get('Content-Type', '').startswith('text/html'):
            # 添加CSP头部
            csp_policy = self.build_csp_policy()
            response['Content-Security-Policy'] = csp_policy
            
            # 添加Permissions Policy头部
            permissions_policy = self.build_permissions_policy()
            if permissions_policy:
                response['Permissions-Policy'] = permissions_policy
        
        return response
    
    def build_csp_policy(self):
        """构建CSP策略字符串"""
        policy_parts = []
        
        # 获取配置或使用默认值
        default_src = getattr(settings, 'CSP_DEFAULT_SRC', ["'self'"])
        script_src = getattr(settings, 'CSP_SCRIPT_SRC', ["'self'"])
        style_src = getattr(settings, 'CSP_STYLE_SRC', ["'self'"])
        font_src = getattr(settings, 'CSP_FONT_SRC', ["'self'"])
        img_src = getattr(settings, 'CSP_IMG_SRC', ["'self'"])
        connect_src = getattr(settings, 'CSP_CONNECT_SRC', ["'self'"])
        frame_src = getattr(settings, 'CSP_FRAME_SRC', ["'self'"])
        object_src = getattr(settings, 'CSP_OBJECT_SRC', ["'none'"])
        base_uri = getattr(settings, 'CSP_BASE_URI', ["'self'"])
        
        # 构建策略
        policy_parts.append(f"default-src {' '.join(default_src)}")
        policy_parts.append(f"script-src {' '.join(script_src)}")
        policy_parts.append(f"style-src {' '.join(style_src)}")
        policy_parts.append(f"font-src {' '.join(font_src)}")
        policy_parts.append(f"img-src {' '.join(img_src)}")
        policy_parts.append(f"connect-src {' '.join(connect_src)}")
        policy_parts.append(f"frame-src {' '.join(frame_src)}")
        policy_parts.append(f"object-src {' '.join(object_src)}")
        policy_parts.append(f"base-uri {' '.join(base_uri)}")
        
        return '; '.join(policy_parts) + ';'
    
    def build_permissions_policy(self):
        """构建Permissions Policy字符串"""
        permissions_config = getattr(settings, 'PERMISSIONS_POLICY', {})
        
        if not permissions_config:
            return ""
        
        policy_parts = []
        
        for directive, origins in permissions_config.items():
            if not origins:
                # 空列表表示禁用该功能
                policy_parts.append(f"{directive}=()")
            else:
                # 将origins转换为适当的格式
                formatted_origins = []
                for origin in origins:
                    if origin == 'self':
                        formatted_origins.append('self')
                    else:
                        formatted_origins.append(f'"{origin}"')
                
                policy_parts.append(f"{directive}=({' '.join(formatted_origins)})")
        
        return ', '.join(policy_parts) 