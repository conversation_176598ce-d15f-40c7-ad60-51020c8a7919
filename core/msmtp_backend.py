import subprocess
import logging
from django.core.mail.backends.base import BaseEmailBackend
from django.core.mail.message import sanitize_address
from django.conf import settings

logger = logging.getLogger(__name__)


class MsmtpEmailBackend(BaseEmailBackend):
    """
    Django邮件后端，使用msmtp发送邮件
    """
    
    def __init__(self, msmtp_path=None, account=None, **kwargs):
        super().__init__(**kwargs)
        
        # 从Django设置读取配置，优先使用传入的参数
        self.msmtp_path = (msmtp_path or 
                          getattr(settings, 'EMAIL_MSMTP_PATH', 
                                 '/opt/homebrew/bin/msmtp'))
        self.account = (account or 
                       getattr(settings, 'EMAIL_MSMTP_ACCOUNT', 'default'))
        
    def send_messages(self, email_messages):
        """
        发送邮件消息列表
        """
        if not email_messages:
            return 0
            
        sent_count = 0
        
        for message in email_messages:
            try:
                if self._send_message(message):
                    sent_count += 1
                    logger.info(
                        f"Email sent successfully via msmtp to {message.to}")
                else:
                    logger.error(
                        f"Failed to send email via msmtp to {message.to}")
            except Exception as e:
                logger.error(
                    f"Error sending email via msmtp to {message.to}: {str(e)}")
                
        return sent_count
    
    def _send_message(self, message):
        """
        发送单个邮件消息
        """
        try:
            # 构建msmtp命令
            recipients = message.to + message.cc + message.bcc
            cmd = [self.msmtp_path, '--account', self.account] + recipients
            
            # 构建完整的邮件内容
            email_content = self._build_email_content(message)
            
            # 执行msmtp命令
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(input=email_content)
            
            if process.returncode == 0:
                logger.info(f"msmtp sent email successfully: {stdout}")
                return True
            else:
                logger.error(
                    f"msmtp failed with return code "
                    f"{process.returncode}: {stderr}")
                return False
                
        except FileNotFoundError:
            logger.error(f"msmtp not found at {self.msmtp_path}")
            return False
        except Exception as e:
            logger.error(f"Error executing msmtp: {str(e)}")
            return False
    
    def _build_email_content(self, message):
        """
        构建完整的邮件内容，包括头部和正文
        """
        # 构建邮件头部
        email_lines = []
        
        # From header
        if message.from_email:
            from_addr = sanitize_address(message.from_email, message.encoding)
            email_lines.append(f"From: {from_addr}")
            
        # To header
        if message.to:
            to_addresses = [
                sanitize_address(addr, message.encoding) 
                for addr in message.to]
            email_lines.append(f"To: {', '.join(to_addresses)}")
            
        # CC header
        if message.cc:
            cc_addresses = [
                sanitize_address(addr, message.encoding) 
                for addr in message.cc]
            email_lines.append(f"Cc: {', '.join(cc_addresses)}")
            
        # Subject header
        email_lines.append(f"Subject: {message.subject}")
        
        # Content-Type header
        if (hasattr(message, 'content_subtype') and 
            message.content_subtype == 'html'):
            email_lines.append("Content-Type: text/html; charset=utf-8")
        else:
            email_lines.append("Content-Type: text/plain; charset=utf-8")
            
        email_lines.append("Content-Transfer-Encoding: 8bit")
        
        # MIME-Version header
        email_lines.append("MIME-Version: 1.0")
        
        # 空行分隔头部和正文
        email_lines.append("")
        
        # 邮件正文
        if hasattr(message, 'alternatives') and message.alternatives:
            # 如果有HTML版本，使用multipart
            return self._build_multipart_content(message, email_lines)
        else:
            # 纯文本邮件
            email_lines.append(message.body)
            
        return "\n".join(email_lines)
    
    def _build_multipart_content(self, message, header_lines):
        """
        构建multipart邮件内容（包含HTML和文本版本）
        """
        import uuid
        boundary = f"----=_Part_{uuid.uuid4().hex}"
        
        # 更新Content-Type头部为multipart
        for i, line in enumerate(header_lines):
            if line.startswith("Content-Type:"):
                header_lines[i] = (
                    f"Content-Type: multipart/alternative; "
                    f"boundary=\"{boundary}\"")
                break
        
        # 构建邮件内容
        content_lines = header_lines[:]
        
        # 开始multipart内容
        content_lines.append(f"--{boundary}")
        content_lines.append("Content-Type: text/plain; charset=utf-8")
        content_lines.append("Content-Transfer-Encoding: 8bit")
        content_lines.append("")
        content_lines.append(message.body)
        content_lines.append("")
        
        # HTML部分
        for content, mimetype in message.alternatives:
            if mimetype == 'text/html':
                content_lines.append(f"--{boundary}")
                content_lines.append("Content-Type: text/html; charset=utf-8")
                content_lines.append("Content-Transfer-Encoding: 8bit")
                content_lines.append("")
                content_lines.append(content)
                content_lines.append("")
                break
        
        # 结束multipart
        content_lines.append(f"--{boundary}--")
        
        return "\n".join(content_lines) 