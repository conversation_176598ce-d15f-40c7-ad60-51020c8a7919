from django.apps import AppConfig
from django.conf import settings
import os


class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    
    def ready(self):
        """Django应用启动时执行"""
        # 修复邮件SSL/TLS配置冲突
        self.fix_email_settings()
    
    def fix_email_settings(self):
        """修复邮件设置中的SSL/TLS冲突"""
        try:
            # 检查是否在Docker环境中且使用真实邮件配置
            if getattr(settings, 'USE_DOCKER', False):
                email_port = getattr(settings, 'EMAIL_PORT', 587)
                
                if email_port == 465:
                    # 465端口必须使用SSL，不能使用TLS
                    settings.EMAIL_USE_SSL = True
                    settings.EMAIL_USE_TLS = False
                elif email_port == 587:
                    # 587端口必须使用TLS，不能使用SSL
                    settings.EMAIL_USE_SSL = False
                    settings.EMAIL_USE_TLS = True
                    
        except Exception as e:
            print(f"[CoreConfig] 邮件设置修复失败: {e}") 