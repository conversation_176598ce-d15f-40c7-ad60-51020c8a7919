"""
Result Analysis Agent - 结果分析智能体
使用LLM分析生成结果质量并决定下一步行动
"""

import json
import logging
from typing import Dict, Any
from app.core.state import (
    AgentState, QualityAnalysis, NextAction, log_agent_action
)
from app.core.llm_client import get_llm_client

logger = logging.getLogger(__name__)

class ResultAnalysisAgent:
    """Result Analysis Agent - 基于LLM的智能结果分析"""
    
    def __init__(self, model: str = None):
        self.llm_client = get_llm_client(model)
        # 行动映射
        self.action_mapping = {
            "COMPLETE": NextAction.COMPLETE,
            "REPLAN": NextAction.REPLAN,
            "REFINE": NextAction.REFINE,
            "REGENERATE": NextAction.REGENERATE
        }

    def process(self, state: AgentState) -> AgentState:
        """分析生成结果质量并决定下一步行动"""
        log_agent_action(state, "ResultAnalysisAgent", "开始分析生成结果")
        
        # 检查是否有生成结果
        if not state.get("last_generation_result"):
            log_agent_action(state, "ResultAnalysisAgent", "无生成结果，跳过分析")
            state["next_action"] = NextAction.REPLAN
            return state
        
        try:
            # 使用LLM分析生成结果
            analysis_result = self._analyze_with_llm(state)
            
            if analysis_result["success"]:
                # 提取分析结果
                analysis_data = analysis_result["analysis"]
                
                # 创建质量分析对象
                quality_analysis = self._create_quality_analysis(analysis_data)
                state["quality_analysis"] = quality_analysis
                
                # 确定下一步行动
                next_action = self._determine_next_action(analysis_data)
                state["next_action"] = next_action
                
                # 打印分析结果
                self._print_analysis_results(analysis_data, next_action)
                
                log_agent_action(state, "ResultAnalysisAgent", 
                               f"分析完成，下一步行动: {next_action.value}")
            else:
                # LLM分析失败，使用备用逻辑
                log_agent_action(state, "ResultAnalysisAgent", 
                               f"LLM分析失败: {analysis_result['error']}")
                quality_analysis, next_action = self._fallback_analysis(state)
                state["quality_analysis"] = quality_analysis
                state["next_action"] = next_action
            
        except Exception as e:
            log_agent_action(state, "ResultAnalysisAgent", f"分析失败: {str(e)}")
            # 默认需要重新规划
            state["next_action"] = NextAction.REPLAN
            state["last_error"] = str(e)
        
        return state

    def _analyze_with_llm(self, state: AgentState) -> Dict[str, Any]:
        """使用LLM分析生成结果"""
        user_intent = state.get("user_intent", "")
        user_requirements = state.get("user_requirements", {})
        execution_plan = state.get("execution_plan")
        generation_result = state.get("last_generation_result", "")
        generation_history = state.get("generation_history", [])
        
        # 转换execution_plan为字典格式
        plan_dict = {}
        if execution_plan:
            plan_dict = {
                "task_type": execution_plan.task_type.value if hasattr(execution_plan.task_type, 'value') else str(execution_plan.task_type),
                "description": execution_plan.description,
                "estimated_time": execution_plan.estimated_time,
                "tool_calls": [
                    {
                        "tool_name": call.tool_name,
                        "parameters": call.parameters,
                        "description": call.description,
                        "order": call.order
                    }
                    for call in execution_plan.tool_calls
                ]
            }
        
        # 格式化生成历史
        formatted_history = []
        for record in generation_history:
            formatted_history.append({
                "round_number": record.round_number,
                "user_input": record.user_input,
                "description": record.description,
                "result_path": record.result_path,
                "task_type": record.task_type.value,
                "success": record.success
            })
        
        # 调用LLM分析结果
        return self.llm_client.analyze_generation_result(
            user_intent=user_intent,
            user_requirements=user_requirements,
            execution_plan=plan_dict,
            generation_result=generation_result,
            generation_history=formatted_history
        )

    def _create_quality_analysis(self, analysis_data: Dict[str, Any]) -> QualityAnalysis:
        """根据LLM分析结果创建质量分析对象"""
        quality_analysis = analysis_data.get("quality_analysis", {})
        problem_analysis = analysis_data.get("problem_analysis", {})
        
        return QualityAnalysis(
            overall_score=quality_analysis.get("overall_score", 7.0),
            content_accuracy=quality_analysis.get("content_accuracy", 7.0),
            visual_quality=quality_analysis.get("visual_quality", 7.0),
            style_consistency=quality_analysis.get("style_consistency", 7.0),
            specific_issues=problem_analysis.get("moderate_issues", []) + problem_analysis.get("minor_issues", []),
            improvement_suggestions=analysis_data.get("decision", {}).get("suggestions", [])
        )

    def _determine_next_action(self, analysis_data: Dict[str, Any]) -> NextAction:
        """根据LLM分析结果确定下一步行动"""
        decision = analysis_data.get("decision", {})
        next_action_str = decision.get("next_action", "COMPLETE").upper()
        
        return self.action_mapping.get(next_action_str, NextAction.COMPLETE)

    def _fallback_analysis(self, state: AgentState) -> tuple[QualityAnalysis, NextAction]:
        """备用分析逻辑（当LLM失败时）"""
        log_agent_action(state, "ResultAnalysisAgent", "使用备用分析逻辑")
        
        # 简单的备用分析
        quality_analysis = QualityAnalysis(
            overall_score=6.0,
            content_accuracy=6.0,
            visual_quality=6.0,
            style_consistency=6.0,
            specific_issues=["LLM分析不可用，使用默认评估"],
            improvement_suggestions=["建议检查LLM服务状态"]
        )
        
        # 默认完成任务
        next_action = NextAction.COMPLETE
        
        return quality_analysis, next_action

    def _print_analysis_results(self, analysis_data: Dict[str, Any], next_action: NextAction) -> None:
        """打印分析结果详情"""
        quality_analysis = analysis_data.get("quality_analysis", {})
        problem_analysis = analysis_data.get("problem_analysis", {})
        decision = analysis_data.get("decision", {})
        user_feedback = analysis_data.get("user_feedback_prediction", {})
        
        print(f"\n📊 生成结果质量分析:")
        print(f"  总体评分: {quality_analysis.get('overall_score', 0)}/10")
        print(f"  内容准确性: {quality_analysis.get('content_accuracy', 0)}/10")
        print(f"  视觉质量: {quality_analysis.get('visual_quality', 0)}/10")
        print(f"  风格一致性: {quality_analysis.get('style_consistency', 0)}/10")
        print(f"  技术执行: {quality_analysis.get('technical_execution', 0)}/10")
        
        print(f"\n🔍 问题分析:")
        critical_issues = problem_analysis.get("critical_issues", [])
        moderate_issues = problem_analysis.get("moderate_issues", [])
        minor_issues = problem_analysis.get("minor_issues", [])
        
        if critical_issues:
            print(f"  严重问题: {critical_issues}")
        if moderate_issues:
            print(f"  中等问题: {moderate_issues}")
        if minor_issues:
            print(f"  轻微问题: {minor_issues}")
        
        if not any([critical_issues, moderate_issues, minor_issues]):
            print(f"  ✅ 未发现明显问题")
        
        print(f"\n🎯 决策分析:")
        print(f"  下一步行动: {next_action.value}")
        print(f"  置信度: {decision.get('confidence', 0)}")
        print(f"  推理过程: {decision.get('reasoning', '')}")
        
        suggestions = decision.get("suggestions", [])
        if suggestions:
            print(f"  改进建议:")
            for suggestion in suggestions:
                print(f"    - {suggestion}")
        
        print(f"\n👤 用户反馈预测:")
        print(f"  满意度概率: {user_feedback.get('satisfaction_probability', 0)}")
        print(f"  可能反馈: {user_feedback.get('likely_feedback', '')}")
        
        potential_concerns = user_feedback.get("potential_concerns", [])
        if potential_concerns:
            print(f"  潜在关注点: {potential_concerns}")
        
        overall_assessment = problem_analysis.get("overall_assessment", "")
        if overall_assessment:
            print(f"\n💡 整体评估: {overall_assessment}")
        
        print() 