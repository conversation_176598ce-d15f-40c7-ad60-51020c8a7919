"""
Safety Agent - 内容安全审查智能体
基于LLM进行智能内容安全审查，确保生成内容符合法规要求
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
from app.core.state import AgentState, add_to_chat_history, log_agent_action
from app.core.llm_client import get_llm_client
from app.config.safety_config import SafetyConfig

logger = logging.getLogger(__name__)

class SafetyAgent:
    """Safety Agent - 内容安全审查智能体"""
    
    def __init__(self, model: str = None):
        self.llm_client = get_llm_client(model)
        self.safety_system_prompt = self._load_safety_system_prompt()
        self.chinese_political_keywords = self._load_chinese_political_keywords()

    def process(self, state: AgentState) -> AgentState:
        """处理安全审查"""
        log_agent_action(state, "SafetyAgent", "开始内容安全审查")
        
        user_input = state["user_input"]
        if not user_input:
            # 没有用户输入，直接通过
            state["safety_status"] = "safe"
            state["safety_message"] = ""
            state["original_input"] = user_input
            state["processed_input"] = user_input
            return state
        
        # 首先进行关键词检查
        keyword_result = self._check_chinese_political_keywords(user_input)
        if keyword_result["blocked"]:
            # 直接拒绝政治敏感内容
            state["safety_status"] = "forbidden"
            state["safety_message"] = keyword_result["message"]
            state["original_input"] = user_input
            state["processed_input"] = ""
            
            add_to_chat_history(state, "assistant", keyword_result["message"])
            print(f"❌ 关键词拦截: {keyword_result['message']}")
            log_agent_action(state, "SafetyAgent", f"政治敏感词拦截: {keyword_result['keyword']}")
            return state
        
        # 调用LLM进行安全审查
        safety_result = self._perform_safety_check(user_input, state)
        
        # 更新状态
        state["safety_status"] = safety_result["status"]
        state["safety_message"] = safety_result["message"]
        state["original_input"] = user_input
        state["processed_input"] = safety_result["processed_content"]
        
        # 如果内容被改写，通知用户
        if safety_result["status"] == "rewritten":
            notification = f"为了确保内容安全，我对您的请求进行了优化调整：'{safety_result['processed_content']}'"
            add_to_chat_history(state, "assistant", notification)
            print(f"🛡️ 安全提示: {notification}")
            log_agent_action(state, "SafetyAgent", "内容已改写并通知用户")
            
        # 如果内容违规，直接拒绝
        elif safety_result["status"] == "forbidden":
            error_message = safety_result["message"]
            add_to_chat_history(state, "assistant", error_message)
            print(f"❌ 安全拒绝: {error_message}")
            log_agent_action(state, "SafetyAgent", "内容违规，已拒绝处理")
            
        # 如果内容安全，正常通过
        else:
            log_agent_action(state, "SafetyAgent", "内容安全审查通过")
        
        return state

    def _check_chinese_political_keywords(self, user_input: str) -> Dict[str, Any]:
        """检查中国政治敏感关键词 - 使用完全匹配策略，最大程度避免误判"""
        if not self.chinese_political_keywords:
            return {"blocked": False, "keyword": None, "message": ""}
        
        # 将输入转换为小写以便匹配
        input_lower = user_input.lower().strip()
        
        # 检查所有类别的关键词
        for category, keywords in self.chinese_political_keywords.items():
            for keyword in keywords:
                keyword_lower = keyword.lower().strip()
                
                # 使用完全匹配策略 - 必须与关键词完全一致才算匹配
                if self._is_exact_keyword_match(input_lower, keyword_lower):
                    print(f"🔍 [SafetyAgent Debug] 检测到政治敏感词: '{keyword}' (类别: {category})")
                    return {
                        "blocked": True,
                        "keyword": keyword,
                        "category": category,
                        "message": SafetyConfig.get_safety_message("political")
                    }
        
        return {"blocked": False, "keyword": None, "message": ""}
    
    def _is_exact_keyword_match(self, user_input: str, keyword: str) -> bool:
        """
        检查是否为精确关键词匹配
        使用配置化的匹配策略，避免硬编码
        """
        import re
        
        # 获取当前匹配策略配置
        strategy = SafetyConfig.get_current_strategy()
        
        # 情况1：完全相等
        if user_input == keyword:
            return True
        
        # 如果不要求词边界，直接进行包含匹配
        if not strategy["require_word_boundary"]:
            return keyword in user_input
        
        # 情况2：作为独立词汇出现（使用配置化的分隔符）
        separator_pattern = SafetyConfig.get_separator_pattern()
        
        # 构建匹配模式：关键词前后必须是分隔符或字符串开始/结束
        pattern = f'(?:^|{separator_pattern}){re.escape(keyword)}(?:$|{separator_pattern})'
        
        return bool(re.search(pattern, user_input))
    
    def _normalize_text(self, text: str) -> str:
        """规范化文本，移除多余空格和特殊字符"""
        return re.sub(r'\s+', ' ', text.strip())

    def _perform_safety_check(self, user_input: str, state: AgentState) -> Dict[str, Any]:
        """执行安全检查 - 纯LLM驱动，并添加了详细的调试日志"""
        chat_history = state.get("chat_history", [])
        
        print("\n🕵️  [SafetyAgent Debug] 准备调用LLM进行安全审查...")
        sanitized_prompt = self.safety_system_prompt[:100].replace('\n', ' ')
        print(f"   - System Prompt (前100字符): {sanitized_prompt}...")
        print(f"   - User Input: {user_input}")

        try:
            # 调用LLM进行安全审查
            llm_result = self.llm_client.safety_check(
                user_input=user_input,
                chat_history=chat_history,
                system_prompt=self.safety_system_prompt
            )

            print(f"   - LLM 调用成功: {llm_result.get('success', False)}")
            
            if llm_result['success']:
                result = llm_result['result']
                print(f"   - LLM 返回原始结果: {json.dumps(result, ensure_ascii=False)}")
                
                # 检查LLM是否生成了专业回复内容
                raw_response = llm_result.get('raw_response', '')
                if raw_response and len(raw_response) > 200 and '###' in raw_response:
                    # LLM生成了详细的专业回复（如教程），保存到状态中
                    print(f"   🎓 [SafetyAgent Debug] 检测到LLM生成的专业回复，长度: {len(raw_response)} 字符")
                    state["llm_generated_response"] = raw_response
                    print(f"   📝 [SafetyAgent Debug] 已保存专业回复到state['llm_generated_response']")
                
                return {
                    "status": result.get("safety_status", "safe"), # 确保使用 'safety_status'
                    "message": result.get("safety_message", ""),
                    "processed_content": result.get("processed_input", user_input),
                }
            else:
                # LLM调用失败，采用保守策略：允许通过但记录错误
                error_msg = llm_result.get('error', 'Unknown error')
                logger.error(f"安全审查LLM调用失败: {error_msg}")
                print(f"   ⚠️ 安全审查LLM调用失败，采用保守策略允许通过: {error_msg}")
                
                # 保守策略：允许通过但标记为未知风险
                return {
                    "status": "safe",
                    "message": "",
                    "processed_content": user_input,
                    "risk_level": "unknown",
                    "violation_type": "llm_error"
                }
                
        except Exception as e:
            logger.error(f"安全审查过程出错: {str(e)}")
            print(f"   ⚠️ 安全审查系统错误，采用保守策略允许通过: {str(e)}")
            
            # 系统错误时采用保守策略
            return {
                "status": "safe",
                "message": "",
                "processed_content": user_input,
                "risk_level": "unknown", 
                "violation_type": "system_error"
            }

    def _load_safety_system_prompt(self) -> str:
        """从文件加载安全审查的系统提示词"""
        try:
            config_dir = Path(__file__).parent.parent / "config" / "prompts"
            prompt_file = config_dir / "safety_agent_system.md"
            
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                logger.error(f"安全审查系统提示词文件未找到: {prompt_file}")
                raise FileNotFoundError(f"安全审查系统提示词文件未找到: {prompt_file}")
        except Exception as e:
            logger.error(f"加载安全审查系统提示词失败: {e}")
            raise e

    def _load_chinese_political_keywords(self) -> Dict[str, List[str]]:
        """从文件加载中国政治敏感关键词"""
        try:
            config_dir = Path(__file__).parent.parent / "config" / "prompts"
            keywords_file = config_dir / "chinese_political_keywords.json"
            
            if keywords_file.exists():
                with open(keywords_file, 'r', encoding='utf-8') as f:
                    keywords_data = json.load(f)
                    print(f"🔍 [SafetyAgent Debug] 已加载政治敏感词库，共 {sum(len(v) for v in keywords_data.values())} 个关键词")
                    return keywords_data
            else:
                logger.warning(f"中国政治关键词文件未找到: {keywords_file}，将跳过关键词检查")
                return {}
        except Exception as e:
            logger.error(f"加载中国政治关键词失败: {e}")
            return {}

def safety_check_agent(state: AgentState) -> AgentState:
    """安全审查智能体入口函数"""
    agent = SafetyAgent()
    return agent.process(state) 