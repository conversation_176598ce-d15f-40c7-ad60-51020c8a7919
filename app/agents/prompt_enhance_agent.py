# app/agents/prompt_enhance_agent.py

import json
import os
import re
from typing import Dict, Any, Optional, List
from pathlib import Path

from app.core.llm_client import LLMClient
from app.core.state import AgentState
from app.config.settings import settings

class PromptEnhanceAgent:
    """提示词增强代理 - 负责场景识别和模板应用"""
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.templates_dir = Path("app/config/prompt_templates")
        self.registry_path = self.templates_dir / "template_registry.json"
        self.templates_cache = {}
        self._load_templates()
    
    def _load_templates(self):
        """加载所有模板到缓存"""
        try:
            if not self.registry_path.exists():
                print(f"⚠️ 模板注册表不存在: {self.registry_path}")
                return
            
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            for template_name, config in registry["templates"].items():
                if config.get("enabled", True):
                    template_file = self.templates_dir / config["file"]
                    if template_file.exists():
                        with open(template_file, 'r', encoding='utf-8') as f:
                            template_data = json.load(f)
                            self.templates_cache[template_name] = {
                                **config,
                                **template_data
                            }
                        print(f"✅ 已加载模板: {template_name}")
                    else:
                        print(f"⚠️ 模板文件不存在: {template_file}")
            
            print(f"🔧 [PromptEnhanceAgent] 已加载 {len(self.templates_cache)} 个模板")
            
        except Exception as e:
            print(f"❌ 加载模板失败: {e}")
            self.templates_cache = {}
    
    async def identify_scenario(self, user_input: str, user_intent: str = "") -> Dict[str, Any]:
        """使用LLM识别用户输入对应的场景模板"""
        
        if not self.templates_cache:
            return {"scenario": None, "confidence": 0.0, "variables": {}}
        
        # 构建可用模板的描述
        available_templates = []
        for name, template in self.templates_cache.items():
            available_templates.append({
                "name": name,
                "description": template["description"],
                "keywords": template["keywords"],
                "variables": template["variables"]
            })
        
        system_prompt = """你是一个专业的场景识别助手。你的任务是分析用户输入，判断是否匹配已有的提示词模板。

分析规则:
1. 仔细分析用户的意图和需求
2. 检查是否与任何已有模板高度匹配
3. 如果匹配，提取模板所需的变量
4. 如果不匹配或不确定，返回null

返回格式必须是有效的JSON:
{
  "scenario": "模板名称或null",
  "confidence": 0.95,
  "variables": {"VAR1": "值1", "VAR2": "值2"},
  "reasoning": "选择理由"
}"""
        
        user_prompt = f"""
用户输入: {user_input}
用户意图: {user_intent}

可用模板:
{json.dumps(available_templates, ensure_ascii=False, indent=2)}

请分析用户输入是否匹配任何模板，如果匹配请提取相应变量。
"""
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self.llm_client.chat_completion(
                messages=messages,
                model=settings.llm_model
            )
            
            # 检查LLM调用是否成功
            if not response['success']:
                raise Exception(response.get('error', 'LLM call failed'))
            
            # 解析LLM返回的JSON
            result = json.loads(response['content'].strip())
            
            # 验证结果格式
            if not isinstance(result, dict):
                raise ValueError("Invalid response format")
            
            scenario = result.get("scenario")
            confidence = float(result.get("confidence", 0.0))
            variables = result.get("variables", {})
            reasoning = result.get("reasoning", "")
            
            print(f"🔍 [PromptEnhanceAgent] 场景识别结果:")
            print(f"   - 场景: {scenario}")
            print(f"   - 置信度: {confidence}")
            print(f"   - 变量: {variables}")
            print(f"   - 理由: {reasoning}")
            
            return {
                "scenario": scenario,
                "confidence": confidence,
                "variables": variables,
                "reasoning": reasoning
            }
            
        except json.JSONDecodeError as e:
            print(f"❌ LLM返回格式错误: {e}")
            print(f"原始返回: {response.get('content', 'No content')}")
            return {"scenario": None, "confidence": 0.0, "variables": {}}
        except Exception as e:
            print(f"❌ 场景识别失败: {e}")
            return {"scenario": None, "confidence": 0.0, "variables": {}}
    
    def apply_template(self, template_name: str, variables: Dict[str, str], user_input: str) -> Dict[str, str]:
        """应用模板并替换变量"""
        
        if template_name not in self.templates_cache:
            print(f"❌ 模板不存在: {template_name}")
            return {"zh": user_input, "en": user_input}
        
        template = self.templates_cache[template_name]
        template_zh = template.get("template_zh", "")
        template_en = template.get("template_en", "")
        
        # 替换变量
        enhanced_zh = template_zh
        enhanced_en = template_en
        
        for var_name, var_value in variables.items():
            placeholder = f"[{var_name}]"
            enhanced_zh = enhanced_zh.replace(placeholder, var_value)
            enhanced_en = enhanced_en.replace(placeholder, var_value)
        
        print(f"🔧 [PromptEnhanceAgent] 应用模板: {template_name}")
        print(f"   - 变量替换: {variables}")
        
        return {
            "zh": enhanced_zh,
            "en": enhanced_en
        }
    
    async def process(self, state: AgentState) -> AgentState:
        """处理提示词增强逻辑"""
        
        user_input = state.get("user_input", "")
        user_intent = state.get("user_intent", "")
        
        print(f"🔧 [PromptEnhanceAgent] 开始处理提示词增强")
        print(f"   - 用户输入: {user_input}")
        print(f"   - 用户意图: {user_intent}")
        
        # 1. 场景识别
        scenario_result = await self.identify_scenario(user_input, user_intent)
        
        scenario = scenario_result["scenario"]
        confidence = scenario_result["confidence"]
        variables = scenario_result["variables"]
        
        # 2. 判断是否应用模板
        if scenario is None or confidence < 0.7:
            # 没有匹配的模板或置信度太低，跳过改写
            print(f"⏭️ [PromptEnhanceAgent] 跳过改写 - 场景: {scenario}, 置信度: {confidence}")
            state["enhancement_status"] = "skipped"
            state["enhanced_prompt"] = user_input
            state["template_used"] = ""
            state["template_confidence"] = confidence
            return state
        
        # 3. 应用模板
        enhanced_prompts = self.apply_template(scenario, variables, user_input)
        
        # 4. 更新状态
        state["enhancement_status"] = "enhanced"
        state["enhanced_prompt"] = enhanced_prompts["zh"]  # 默认使用中文版本
        state["enhanced_prompt_en"] = enhanced_prompts["en"]  # 保存英文版本
        state["template_used"] = scenario
        state["template_confidence"] = confidence
        state["template_variables"] = variables
        
        print(f"✅ [PromptEnhanceAgent] 提示词增强完成")
        print(f"   - 使用模板: {scenario}")
        print(f"   - 置信度: {confidence}")
        print(f"   - 增强后提示词(中文): {enhanced_prompts['zh'][:100]}...")
        print(f"   - 增强后提示词(英文): {enhanced_prompts['en'][:100]}...")
        
        return state


# 全局实例
prompt_enhance_agent = PromptEnhanceAgent()

async def prompt_enhance_agent_node(state: AgentState) -> AgentState:
    """提示词增强节点入口函数"""
    return await prompt_enhance_agent.process(state) 