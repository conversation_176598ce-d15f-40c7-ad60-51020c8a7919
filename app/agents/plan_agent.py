"""
Plan Agent - 计划制定智能体
使用LLM将明确的用户意图转换为可执行的工具调用计划
"""

import logging
import asyncio
from typing import Dict, List, Any
from app.core.state import (
    AgentState, ExecutionPlan, ToolCall, GenerationTaskType, 
    PlanStatus, log_agent_action
)
from app.core.tools import get_available_tools
from app.core.llm_client import get_llm_client
from app.utils.data_validator import validate_image_url, validate_video_url
from app.utils.debug_formatter import format_json_debug, format_state_keys, format_attachments, format_plan_debug, format_tool_parameters, format_url_mapping
import re

logger = logging.getLogger(__name__)

class PlanAgent:
    """Plan Agent - 基于LLM的智能计划制定"""
    
    def __init__(self, model: str = None):
        self.llm_client = get_llm_client(model)
        self.available_tools = get_available_tools()

    def process(self, state: AgentState) -> AgentState:
        """制定执行计划"""
        log_agent_action(state, "PlanAgent", "开始制定执行计划")
        
        if not state["user_intent"] or not state["user_requirements"]:
            log_agent_action(state, "PlanAgent", "用户意图不明确，无法制定计划")
            state["plan_status"] = PlanStatus.NEEDS_RECHAT
            state["plan_failure_reason"] = "用户意图不明确，需要更多信息"
            state["plan_failure_details"] = {
                "error_type": "unclear_intent",
                "missing_info": "user_intent" if not state["user_intent"] else "user_requirements",
                "suggested_action": "clarify_requirements",
                "user_friendly_message": "Please provide more information."
            }
            return state
        
        try:
            # 使用LLM分析需求并制定计划
            plan_result = self._generate_plan_with_llm(state)
            
            if plan_result["success"]:
                print(format_plan_debug(plan_result['plan']))
                
                # 检查是否为聊天对话
                task_analysis = plan_result["plan"].get("task_analysis", {})
                if task_analysis.get("task_type") == "chat":
                    log_agent_action(state, "PlanAgent", "检测到聊天对话，转交给ChatAgent处理")
                    state["plan_status"] = PlanStatus.NEEDS_RECHAT
                    state["current_agent"] = "chat"
                    state["plan_failure_reason"] = "聊天对话，非视觉生成任务"
                    state["plan_failure_details"] = {
                        "error_type": "non_visual_task",
                        "task_type": "chat",
                        "original_intent": state["user_intent"],
                        "user_friendly_message": "I'm specialized in visual content generation and editing."
                    }
                    return state
                
                # 将LLM返回的计划转换为ExecutionPlan对象
                execution_plan = self._convert_llm_plan_to_execution_plan(plan_result["plan"], state)
                
                print("🔍 [PlanAgent Debug] 转换后的执行计划:")
                print(f"   任务类型: {execution_plan.task_type}")
                print(f"   工具调用数量: {len(execution_plan.tool_calls)}")
                for i, tc in enumerate(execution_plan.tool_calls):
                    print(f"   工具{i+1}: {tc.tool_name}")
                    print(format_tool_parameters(tc.parameters))
                
                # 验证计划有效性（包括URL验证）
                try:
                    # 尝试异步验证，如果在事件循环中运行，使用线程池
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 在运行的事件循环中，使用线程池执行
                            import concurrent.futures
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(asyncio.run, self._validate_plan_async(execution_plan, state))
                                validation_result = future.result()
                        else:
                            validation_result = asyncio.run(self._validate_plan_async(execution_plan, state))
                    except RuntimeError as e:
                        if "cannot be called from a running event loop" in str(e):
                            # 使用同步验证作为fallback
                            validation_result = self._validate_plan(execution_plan)
                        else:
                            raise
                except Exception as e:
                    # 如果验证失败，使用简化验证
                    log_agent_action(state, "PlanAgent", f"计划验证异常，使用简化验证: {str(e)}")
                    validation_result = {"valid": True, "error": None, "warnings": []}
                
                if validation_result["valid"]:
                    state["execution_plan"] = execution_plan
                    state["plan_status"] = PlanStatus.PENDING
                    log_agent_action(state, "PlanAgent", f"计划制定完成，包含{len(execution_plan.tool_calls)}个工具调用")
                    
                    # 打印计划详情
                    self._print_plan_details(execution_plan)
                else:
                    # 传递详细的失败原因给ChatAgent
                    state["plan_status"] = PlanStatus.NEEDS_RECHAT
                    state["current_agent"] = "chat"
                    
                    # 设置失败原因和详细信息
                    state["plan_failure_reason"] = validation_result["error"]
                    state["plan_failure_details"] = {
                        "error_type": self._classify_error_type(validation_result["error"]),
                        "original_intent": state["user_intent"],
                        "failed_requirements": state["user_requirements"],
                        "suggested_action": self._get_suggested_action(validation_result["error"]),
                        "user_friendly_message": self._create_user_friendly_message(validation_result["error"])
                    }
                    
                    log_agent_action(state, "PlanAgent", f"计划验证失败，转交ChatAgent处理: {validation_result['error']}")
            else:
                # LLM计划生成失败，转交给ChatAgent处理
                log_agent_action(state, "PlanAgent", f"LLM计划生成失败: {plan_result['error']}")
                state["plan_status"] = PlanStatus.NEEDS_RECHAT
                state["plan_failure_reason"] = f"计划生成失败: {plan_result['error']}"
                state["plan_failure_details"] = {
                    "error_type": "llm_generation_failure",
                    "original_intent": state["user_intent"],
                    "failed_requirements": state["user_requirements"],
                    "suggested_action": "clarify_requirements",
                    "user_friendly_message": "系统无法理解您的需求，请您提供更详细的描述。"
                }
            
        except Exception as e:
            state["plan_status"] = PlanStatus.NEEDS_REPLAN
            state["last_error"] = str(e)
            log_agent_action(state, "PlanAgent", f"计划制定失败: {str(e)}")
        
        return state

    def _generate_plan_with_llm(self, state: AgentState) -> Dict[str, Any]:
        """使用LLM生成执行计划"""
        # 优先使用增强后的提示词
        if state.get("enhancement_status") == "enhanced":
            user_prompt = state.get("enhanced_prompt")
            print(f"🔧 使用增强后的提示词: {user_prompt[:100]}...")
        else:
            user_prompt = state.get("user_input")
            print(f"📝 使用原始提示词: {user_prompt[:100]}...")
        
        user_intent = state["user_intent"]
        user_requirements = state["user_requirements"].copy()  # 复制避免修改原始数据
        
        # 如果有增强提示词，更新requirements
        if state.get("enhancement_status") == "enhanced":
            user_requirements["prompt"] = user_prompt
            user_requirements["template_used"] = state.get("template_used", "")
            user_requirements["template_confidence"] = state.get("template_confidence", 0.0)
        
        generation_history = state.get("generation_history", [])
        
        # 🎯 使用图文交织的enhanced_chat_history，而不是分离的attachments
        enhanced_chat_history = state.get("enhanced_chat_history", [])  # 图文交织的对话历史
        attachments = state.get("attachments", [])  # 备用：分离的附件信息
        chat_history = state.get("chat_history", [])  # 原始对话历史
        
        print("\n🔍 [PlanAgent Debug] 使用图文交织格式:")
        print(f"🔍 [PlanAgent Debug] enhanced_chat_history长度: {len(enhanced_chat_history)}")
        print(f"🔍 [PlanAgent Debug] 备用attachments: {len(attachments)}")
        print(format_state_keys(list(state.keys())))
        
        # 🎯 调试图文交织内容
        if enhanced_chat_history:
            print(format_json_debug(enhanced_chat_history, "Enhanced Chat History内容"))
        
        # 🎯 新增：URL替换逻辑 - 将长URL替换为placeholder
        processed_history, url_mapping = self._replace_urls_with_placeholders(enhanced_chat_history)
        
        print(format_url_mapping(url_mapping))
        
        # 格式化生成历史
        formatted_history = []
        for record in generation_history:
            formatted_history.append({
                "round_number": record.round_number,
                "user_input": record.user_input,
                "description": record.description,
                "result_path": record.result_path,
                "task_type": record.task_type.value,
                "success": record.success
            })
        
        # 🎯 调用LLM生成计划，使用处理后的enhanced_chat_history
        llm_result = self.llm_client.generate_execution_plan(
            user_intent=user_intent,
            user_requirements=user_requirements,
            generation_history=formatted_history,
            available_tools=self.available_tools,
            enhanced_chat_history=processed_history,  # 🎯 使用处理后的对话历史
            attachments=attachments,  # 备用：分离的附件信息（向后兼容）
            chat_history=chat_history  # 备用：原始对话历史（向后兼容）
        )
        
        # 🎯 如果LLM调用成功，保存URL映射表到state中
        if llm_result.get("success") and url_mapping:
            state["url_mapping"] = url_mapping
            print("🔍 [PlanAgent Debug] URL映射表已保存到state")
        
        return llm_result

    def _replace_urls_with_placeholders(self, enhanced_chat_history: List[Dict]) -> tuple:
        """将长URL替换为placeholder"""
        url_mapping = {}
        placeholder_counter = 1
        processed_history = []
        
        print(f"🔄 [URL替换] 开始处理 {len(enhanced_chat_history)} 条消息")
        
        for msg_idx, msg in enumerate(enhanced_chat_history):
            processed_msg = msg.copy()
            
            if msg.get("media"):
                processed_media = []
                for media_idx, media in enumerate(msg["media"]):
                    processed_media_item = media.copy()
                    
                    if media.get("url"):
                        original_url = media["url"]
                        placeholder = f"{{{{placeholder_{placeholder_counter}}}}}"
                        
                        # 保存映射关系
                        url_mapping[placeholder] = original_url
                        processed_media_item["url"] = placeholder
                        
                        print(f"   📎 消息{msg_idx+1}媒体{media_idx+1}: {placeholder} <- {original_url[:50]}...")
                        placeholder_counter += 1
                    
                    processed_media.append(processed_media_item)
                
                processed_msg["media"] = processed_media
            
            processed_history.append(processed_msg)
        
        print(f"🔄 [URL替换] 完成，生成了 {len(url_mapping)} 个映射")
        return processed_history, url_mapping

    def _restore_historical_media_urls(self, execution_plan: ExecutionPlan, url_mapping: Dict[str, str]) -> ExecutionPlan:
        """恢复历史媒体的真实URL，保留步骤间依赖"""
        print(f"🔄 [URL恢复] 开始恢复历史媒体URL，映射表大小: {len(url_mapping)}")
        
        print("🔄 [URL恢复] 可用的URL映射:")
        for placeholder, url in url_mapping.items():
            print(f"   {placeholder} -> {url[:50]}...")
        
        for tool_idx, tool_call in enumerate(execution_plan.tool_calls):
            print(f"   🔧 处理工具{tool_idx+1}: {tool_call.tool_name}")
            
            for param_key, param_value in tool_call.parameters.items():
                if isinstance(param_value, str):
                    # 替换历史媒体引用
                    if param_value in url_mapping:
                        original_url = url_mapping[param_value]
                        tool_call.parameters[param_key] = original_url
                        print(f"      🔗 恢复参数 {param_key}: {param_value} -> {original_url[:50]}...")
                    elif param_value.startswith("{{step_") and param_value.endswith("_output}}"):
                        print(f"      ⏭️ 保留步骤依赖: {param_key} = {param_value}")
                    elif param_value.startswith("@"):
                        print(f"      🔍 保留引用格式: {param_key} = {param_value} (需要在ExecuteNode中处理)")
                elif isinstance(param_value, list):
                    # 处理列表参数（如reference_images）
                    for item_idx, item in enumerate(param_value):
                        if isinstance(item, str):
                            if item in url_mapping:
                                original_url = url_mapping[item]
                                tool_call.parameters[param_key][item_idx] = original_url
                                print(f"      🔗 恢复列表项 {param_key}[{item_idx}]: {item} -> {original_url[:50]}...")
                            elif item.startswith("@"):
                                print(f"      🔍 保留引用格式: {param_key}[{item_idx}] = {item} (需要在ExecuteNode中处理)")
        
        print(f"🔄 [URL恢复] 完成")
        return execution_plan

    def _convert_llm_plan_to_execution_plan(self, llm_plan: Dict[str, Any], state: AgentState) -> ExecutionPlan:
        """将LLM返回的计划转换为ExecutionPlan对象"""
        task_analysis = llm_plan.get("task_analysis", {})
        execution_plan = llm_plan.get("execution_plan", {})
        
        # 检查是否为聊天对话
        task_type_str = task_analysis.get("task_type", "text_to_image")
        if task_type_str == "chat":
            # 对于聊天对话，返回一个特殊的执行计划
            return ExecutionPlan(
                task_type=GenerationTaskType.TEXT_TO_IMAGE,  # 占位符
                tool_calls=[],  # 空的工具调用列表
                estimated_time=0.0,
                description="聊天对话，应由ChatAgent处理"
            )
        
        # 检查execution_plan是否为None
        if execution_plan is None:
            execution_plan = {}
        
        # 确定任务类型
        task_type = self._parse_task_type(task_type_str)
        
        # 转换工具调用
        tool_calls = []
        # 获取权威的URL来源
        authoritative_source_image = state.get("user_requirements", {}).get("source_image")
        authoritative_source_video = state.get("user_requirements", {}).get("source_video")

        for i, tool_call_data in enumerate(execution_plan.get("tool_calls", []), 1):
            
            # --- 简化修正逻辑：统一信任LLM的语义选择 ---
            if authoritative_source_image and "source_image" in tool_call_data["parameters"]:
                current_source = tool_call_data["parameters"]["source_image"]
                
                # 🎯 简化逻辑：只有当LLM返回空值或明显错误时才修正
                # 其他情况完全信任LLM的语义选择（包括context_message格式）
                if not current_source or current_source.strip() == "" or current_source == "None":
                    logger.warning(f"修正Plan LLM空值：将source_image从 "
                                   f"'{current_source}' 修正为 '{authoritative_source_image}'")
                    tool_call_data["parameters"]["source_image"] = authoritative_source_image
                else:
                    # 信任LLM的选择，不进行任何修正
                    logger.info(f"🎯 信任LLM的媒体选择: {current_source}")

            if authoritative_source_video and "source_video" in tool_call_data["parameters"]:
                current_source = tool_call_data["parameters"]["source_video"]
                
                # 同样的简化逻辑应用于视频
                if not current_source or current_source.strip() == "" or current_source == "None":
                    logger.warning(f"修正Plan LLM空值：将source_video从 "
                                   f"'{current_source}' 修正为 '{authoritative_source_video}'")
                    tool_call_data["parameters"]["source_video"] = authoritative_source_video
                else:
                    # 信任LLM的选择，不进行任何修正
                    logger.info(f"🎯 信任LLM的媒体选择: {current_source}")
            # --- 简化修正结束 ---

            tool_call = ToolCall(
                tool_name=tool_call_data["tool_name"],
                parameters=tool_call_data["parameters"],
                description=tool_call_data.get("description", f"步骤{i}"),
                order=tool_call_data.get("step", i)
            )
            tool_calls.append(tool_call)
        
        # 创建ExecutionPlan对象
        execution_plan_obj = ExecutionPlan(
            task_type=task_type,
            tool_calls=tool_calls,
            estimated_time=execution_plan.get("estimated_time", 5.0),
            description=execution_plan.get("description", "LLM生成的执行计划")
        )
        
        # 🎯 新增：恢复历史媒体的真实URL
        url_mapping = state.get("url_mapping", {})
        if url_mapping:
            execution_plan_obj = self._restore_historical_media_urls(execution_plan_obj, url_mapping)
        
        return execution_plan_obj

    def _parse_task_type(self, task_type_str: str) -> GenerationTaskType:
        """解析任务类型字符串"""
        type_mapping = {
            "text_to_image": GenerationTaskType.TEXT_TO_IMAGE,
            "text_to_video": GenerationTaskType.TEXT_TO_VIDEO,
            "image_edit": GenerationTaskType.IMAGE_EDIT,
            "video_edit": GenerationTaskType.VIDEO_EDIT,
            "image_to_video": GenerationTaskType.IMAGE_TO_VIDEO,
            "multi_step_composite": GenerationTaskType.MULTI_STEP_COMPOSITE,
            "img_gen": GenerationTaskType.TEXT_TO_IMAGE,
            "video_gen": GenerationTaskType.TEXT_TO_VIDEO,
            "img_edit": GenerationTaskType.IMAGE_EDIT,
            "img2video": GenerationTaskType.IMAGE_TO_VIDEO
        }
        
        return type_mapping.get(task_type_str.lower(), GenerationTaskType.TEXT_TO_IMAGE)

    async def _validate_plan_async(self, execution_plan: ExecutionPlan, state: AgentState) -> Dict[str, Any]:
        """异步验证执行计划（包括URL验证）"""
        validation = {
            "valid": True,
            "error": None,
            "warnings": []
        }
        
        if not execution_plan.tool_calls:
            validation["valid"] = False
            validation["error"] = "执行计划为空"
            return validation
        
        # 收集所有需要验证的URL
        urls_to_validate = []
        
        for tool_call in execution_plan.tool_calls:
            # 检查工具是否存在
            if tool_call.tool_name not in self.available_tools:
                validation["valid"] = False
                validation["error"] = f"未知工具: {tool_call.tool_name}"
                return validation
            
            # 检查必需参数
            required_params = self._get_required_parameters(tool_call.tool_name)
            
            # 特殊处理img_edit工具的图片输入参数
            if tool_call.tool_name == "img_edit":
                has_source_image = "source_image" in tool_call.parameters
                has_reference_images = "reference_images" in tool_call.parameters
                
                if not has_source_image and not has_reference_images:
                    validation["valid"] = False
                    validation["error"] = f"工具{tool_call.tool_name}缺少图片输入参数: 需要source_image或reference_images"
                    return validation
                
                # 收集需要验证的图片URL（跳过占位符）
                if has_source_image:
                    source_img = tool_call.parameters["source_image"]
                    if not self._is_placeholder(source_img):
                        urls_to_validate.append(("image", source_img))
                if has_reference_images and isinstance(tool_call.parameters["reference_images"], list):
                    for ref_img in tool_call.parameters["reference_images"]:
                        if not self._is_placeholder(ref_img):
                            urls_to_validate.append(("image", ref_img))
            
            # 收集其他工具的URL参数（跳过占位符）
            for param_name, param_value in tool_call.parameters.items():
                if param_name in ["source_image"] and tool_call.tool_name != "img_edit":
                    if not self._is_placeholder(param_value):
                        urls_to_validate.append(("image", param_value))
                elif param_name in ["source_video"]:
                    if not self._is_placeholder(param_value):
                        urls_to_validate.append(("video", param_value))
            
            # 检查必需参数
            for param in required_params:
                if param not in tool_call.parameters:
                    validation["valid"] = False
                    validation["error"] = f"工具{tool_call.tool_name}缺少必需参数: {param}"
                    return validation
            
            # 检查参数值的合理性
            param_validation = self._validate_parameters(tool_call.tool_name, tool_call.parameters)
            if not param_validation["valid"]:
                validation["valid"] = False
                validation["error"] = param_validation["error"]
                return validation
            
            if param_validation.get("warnings"):
                validation["warnings"].extend(param_validation["warnings"])
        
        # 异步验证所有收集到的URL
        if urls_to_validate:
            invalid_urls = await self._validate_urls_async(urls_to_validate)
            if invalid_urls:
                # 将错误格式化为字符串
                error_str = ", ".join([f"{media_type}:{url}" for media_type, url in invalid_urls])
                # validation["valid"] = False
                # validation["error"] = f"URL验证失败: {error_str}"
                # return validation
                warning_message = f"URL验证失败，但这不会中断流程: {error_str}"
                logger.warning(warning_message)
                validation["warnings"].append(warning_message)

        return validation

    async def _validate_urls_async(self, urls_to_validate: List[tuple]) -> List[tuple]:
        """异步验证URL列表，返回无效的URL"""
        invalid_urls = []
        
        # 并发验证所有URL
        validation_tasks = []
        for url_type, url in urls_to_validate:
            if url_type == "image":
                task = self._validate_single_image_url(url)
            elif url_type == "video":
                task = self._validate_single_video_url(url)
            else:
                continue
            validation_tasks.append((url_type, url, task))
        
        # 等待所有验证完成
        for url_type, url, task in validation_tasks:
            try:
                is_valid = await task
                if not is_valid:
                    invalid_urls.append((url_type, url))
                    logger.warning(f"URL验证失败: {url_type} - {url}")
                else:
                    logger.info(f"URL验证成功: {url_type} - {url}")
            except Exception as e:
                invalid_urls.append((url_type, url))
                logger.error(f"URL验证异常: {url_type} - {url} - {e}")
        
        return invalid_urls

    async def _validate_single_image_url(self, url: str) -> bool:
        """验证单个图片URL"""
        try:
            return await validate_image_url(url)
        except Exception as e:
            logger.error(f"图片URL验证异常: {url} - {e}")
            return False

    async def _validate_single_video_url(self, url: str) -> bool:
        """验证单个视频URL"""
        try:
            return await validate_video_url(url)
        except Exception as e:
            logger.error(f"视频URL验证异常: {url} - {e}")
            return False
    
    def _is_placeholder(self, value: str) -> bool:
        """检查字符串是否为占位符"""
        if not isinstance(value, str):
            return False
        
        # 检查是否为各种类型的placeholder
        import re
        placeholder_patterns = [
            r'\{\{step_\d+_output\}\}',          # 步骤间依赖
            r'\{\{placeholder_\d+\}\}',          # URL替换placeholder
        ]
        
        for pattern in placeholder_patterns:
            if re.match(pattern, value.strip()):
                return True
        
        return False

    def _validate_plan(self, execution_plan: ExecutionPlan) -> Dict[str, Any]:
        """同步验证执行计划（保留原有接口）"""
        try:
            # 尝试在当前事件循环中运行
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果已经在事件循环中，返回简化验证结果
                return {"valid": True, "error": None, "warnings": []}
            else:
                return asyncio.run(self._validate_plan_async(execution_plan, None))
        except RuntimeError:
            # 如果有问题，使用简化验证
            return {"valid": True, "error": None, "warnings": []}

    def _get_required_parameters(self, tool_name: str) -> List[str]:
        """获取工具的必需参数"""
        required_params = {
            "img_gen": ["prompt"],
            "video_gen": ["prompt"],
            "img_edit": ["prompt"],  # img_edit需要prompt，但source_image和reference_images二选一
            "video_edit": ["source_video", "prompt"],
            "img2video": ["source_image"]
        }
        return required_params.get(tool_name, [])

    def _validate_parameters(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证工具参数"""
        validation = {
            "valid": True,
            "error": None,
            "warnings": []
        }
        
        if tool_name == "img_gen":
            if "width" in parameters and parameters["width"] > 1024:
                validation["warnings"].append("图像宽度超过推荐值1024")
            if "height" in parameters and parameters["height"] > 1024:
                validation["warnings"].append("图像高度超过推荐值1024")
        
        elif tool_name == "video_gen":
            if "duration" in parameters and parameters["duration"] > 30:
                validation["valid"] = False
                validation["error"] = "视频时长不能超过30秒"
        
        elif tool_name in ["img_edit", "video_edit"]:
            source_path = parameters.get("source_image" if tool_name == "img_edit" else "source_video")
            if source_path and not source_path.strip():
                validation["valid"] = False
                validation["error"] = "源文件路径不能为空"
        
        elif tool_name == "img2video":
            source_path = parameters.get("source_image")
            if source_path and not source_path.strip():
                validation["valid"] = False
                validation["error"] = "源图像路径不能为空"
            if "duration" in parameters and (parameters["duration"] < 1 or parameters["duration"] > 10):
                validation["warnings"].append("图生视频时长建议在1-10秒之间")
        
        return validation

    def _classify_error_type(self, error_message: str) -> str:
        """分类错误类型"""
        if "URL验证失败" in error_message:
            return "url_validation_failure"
        elif "缺少必需参数" in error_message:
            return "missing_parameters"
        elif "未知工具" in error_message:
            return "unknown_tool"
        elif "执行计划为空" in error_message:
            return "empty_plan"
        else:
            return "general_validation_failure"
    
    def _get_suggested_action(self, error_message: str) -> str:
        """根据错误类型获取建议的行动"""
        if "URL验证失败" in error_message:
            return "request_valid_media"
        elif "缺少必需参数" in error_message:
            return "collect_missing_info"
        elif "未知工具" in error_message:
            return "clarify_task_type"
        else:
            return "clarify_requirements"
    
    def _create_user_friendly_message(self, error_message: str) -> str:
        """创建用户友好的错误消息"""
        if "URL验证失败" in error_message:
            if "image" in error_message.lower():
                return "您提供的图片链接无法访问，请确认图片文件存在且链接正确，或者重新上传图片。"
            elif "video" in error_message.lower():
                return "您提供的视频链接无法访问，请确认视频文件存在且链接正确，或者重新上传视频。"
            else:
                return "您提供的媒体文件链接无法访问，请检查文件是否存在或重新上传。"
        elif "缺少必需参数" in error_message:
            return "您的描述中缺少一些必要信息，请提供更详细的要求。"
        elif "执行计划为空" in error_message:
            return "系统无法为您的需求制定执行计划，请重新描述您想要的内容。"
        else:
            return "系统在处理您的请求时遇到问题，请重新描述您的需求或提供更多信息。"

    def _print_plan_details(self, execution_plan: ExecutionPlan) -> None:
        """打印计划详情"""
        print("\n📋 执行计划详情:")
        print(f"   任务类型: {execution_plan.task_type.value}")
        print(f"   描述: {execution_plan.description}")
        print(f"   预估时间: {execution_plan.estimated_time:.1f}秒")
        print(f"   工具调用数量: {len(execution_plan.tool_calls)}")
        
        for i, tool_call in enumerate(execution_plan.tool_calls, 1):
            print(f"\n   步骤{i}: {tool_call.description}")
            print(f"   工具: {tool_call.tool_name}")
            print(f"   参数: {tool_call.parameters}")
        print() 
