"""
Replan Agent - 重新规划智能体
使用LLM基于问题分析进行智能重新规划
"""

import json
import logging
from typing import Dict, Any
from app.core.state import (
    AgentState, ExecutionPlan, ToolCall, GenerationTaskType, 
    PlanStatus, log_agent_action
)
from app.core.llm_client import get_llm_client

logger = logging.getLogger(__name__)

class ReplanAgent:
    """Replan Agent - 基于LLM的智能重新规划"""
    
    def __init__(self, model: str = None):
        self.llm_client = get_llm_client(model)
        self.max_replan_attempts = 3

    def process(self, state: AgentState) -> AgentState:
        """基于问题分析进行重新规划"""
        log_agent_action(state, "ReplanAgent", "开始重新规划")
        
        # 检查重新规划次数
        replan_count = state.get("replan_count", 0)
        if replan_count >= self.max_replan_attempts:
            log_agent_action(state, "ReplanAgent", 
                           f"已达到最大重新规划次数({self.max_replan_attempts})，停止重新规划")
            state["plan_status"] = PlanStatus.REJECTED
            state["last_error"] = "达到最大重新规划次数限制"
            return state
        
        # 增加重新规划计数
        state["replan_count"] = replan_count + 1
        
        try:
            # 使用LLM进行重新规划
            replan_result = self._replan_with_llm(state)
            
            if replan_result["success"]:
                # 提取重新规划结果
                replan_data = replan_result["replan"]
                
                # 创建新的执行计划
                new_execution_plan = self._create_execution_plan_from_replan(replan_data)
                
                # 更新状态
                state["execution_plan"] = new_execution_plan
                state["plan_status"] = PlanStatus.PENDING
                state["replan_result"] = replan_data
                
                # 打印重新规划结果
                self._print_replan_results(replan_data, replan_count + 1)
                
                log_agent_action(state, "ReplanAgent", 
                               f"重新规划完成(第{replan_count + 1}次)，包含{len(new_execution_plan.tool_calls)}个工具调用")
            else:
                # LLM重新规划失败，使用备用逻辑
                log_agent_action(state, "ReplanAgent", 
                               f"LLM重新规划失败: {replan_result['error']}")
                new_execution_plan = self._fallback_replan(state)
                state["execution_plan"] = new_execution_plan
                state["plan_status"] = PlanStatus.PENDING
                
        except Exception as e:
            log_agent_action(state, "ReplanAgent", f"重新规划失败: {str(e)}")
            state["plan_status"] = PlanStatus.NEEDS_REPLAN
            state["last_error"] = str(e)
        
        return state

    def _replan_with_llm(self, state: AgentState) -> Dict[str, Any]:
        """使用LLM进行重新规划"""
        original_intent = state.get("user_intent", "")
        original_requirements = state.get("user_requirements", {})
        failed_execution_plan = state.get("execution_plan")
        analysis_result = state.get("quality_analysis")
        generation_history = state.get("generation_history", [])
        
        # 转换失败的执行计划为字典格式
        failed_plan_dict = {}
        if failed_execution_plan:
            failed_plan_dict = {
                "task_type": failed_execution_plan.task_type.value if hasattr(failed_execution_plan.task_type, 'value') else str(failed_execution_plan.task_type),
                "description": failed_execution_plan.description,
                "estimated_time": failed_execution_plan.estimated_time,
                "tool_calls": [
                    {
                        "tool_name": call.tool_name,
                        "parameters": call.parameters,
                        "description": call.description,
                        "order": call.order
                    }
                    for call in failed_execution_plan.tool_calls
                ]
            }
        
        # 转换分析结果为字典格式
        analysis_dict = {}
        if analysis_result:
            analysis_dict = {
                "quality_analysis": {
                    "overall_score": analysis_result.overall_score,
                    "content_accuracy": analysis_result.content_accuracy,
                    "visual_quality": analysis_result.visual_quality,
                    "style_consistency": analysis_result.style_consistency
                },
                "problem_analysis": {
                    "critical_issues": [],  # 从specific_issues中提取
                    "moderate_issues": analysis_result.specific_issues[:3] if analysis_result.specific_issues else [],
                    "minor_issues": analysis_result.specific_issues[3:] if len(analysis_result.specific_issues) > 3 else [],
                    "overall_assessment": f"总体评分: {analysis_result.overall_score}/10"
                },
                "decision": {
                    "next_action": "REPLAN",
                    "reasoning": "需要重新规划执行方案",
                    "suggestions": analysis_result.improvement_suggestions
                }
            }
        
        # 格式化生成历史
        formatted_history = []
        for record in generation_history:
            formatted_history.append({
                "round_number": record.round_number,
                "user_input": record.user_input,
                "description": record.description,
                "result_path": record.result_path,
                "task_type": record.task_type.value,
                "success": record.success
            })
        
        # 调用LLM进行重新规划
        return self.llm_client.generate_replan(
            original_intent=original_intent,
            original_requirements=original_requirements,
            failed_execution_plan=failed_plan_dict,
            analysis_result=analysis_dict,
            generation_history=formatted_history
        )

    def _create_execution_plan_from_replan(self, replan_data: Dict[str, Any]) -> ExecutionPlan:
        """根据LLM重新规划结果创建执行计划对象"""
        new_plan = replan_data.get("new_execution_plan", {})
        
        # 确定任务类型
        task_type_str = new_plan.get("task_type", "text_to_image")
        task_type = self._parse_task_type(task_type_str)
        
        # 转换工具调用
        tool_calls = []
        for i, tool_call_data in enumerate(new_plan.get("tool_calls", []), 1):
            tool_call = ToolCall(
                tool_name=tool_call_data["tool_name"],
                parameters=tool_call_data["parameters"],
                description=tool_call_data.get("description", f"重新规划步骤{i}"),
                order=tool_call_data.get("step", i)
            )
            tool_calls.append(tool_call)
        
        # 创建ExecutionPlan对象
        return ExecutionPlan(
            task_type=task_type,
            tool_calls=tool_calls,
            estimated_time=new_plan.get("estimated_time", 10.0),
            description=new_plan.get("description", "LLM重新规划的执行计划")
        )

    def _parse_task_type(self, task_type_str: str) -> GenerationTaskType:
        """解析任务类型字符串"""
        type_mapping = {
            "text_to_image": GenerationTaskType.TEXT_TO_IMAGE,
            "text_to_video": GenerationTaskType.TEXT_TO_VIDEO,
            "image_edit": GenerationTaskType.IMAGE_EDIT,
            "video_edit": GenerationTaskType.VIDEO_EDIT,
            "image_to_video": GenerationTaskType.IMAGE_TO_VIDEO,
            "multi_step_composite": GenerationTaskType.MULTI_STEP_COMPOSITE,
            "img_gen": GenerationTaskType.TEXT_TO_IMAGE,
            "video_gen": GenerationTaskType.TEXT_TO_VIDEO,
            "img_edit": GenerationTaskType.IMAGE_EDIT,
            "video_edit": GenerationTaskType.VIDEO_EDIT,
            "img2video": GenerationTaskType.IMAGE_TO_VIDEO
        }
        
        return type_mapping.get(task_type_str.lower(), GenerationTaskType.TEXT_TO_IMAGE)

    def _fallback_replan(self, state: AgentState) -> ExecutionPlan:
        """备用重新规划逻辑（当LLM失败时）"""
        log_agent_action(state, "ReplanAgent", "使用备用重新规划逻辑")
        
        original_plan = state.get("execution_plan")
        user_requirements = state.get("user_requirements", {})
        
        # 简单的备用重新规划：调整参数
        if original_plan and original_plan.tool_calls:
            # 复制原有工具调用但调整参数
            new_tool_calls = []
            for call in original_plan.tool_calls:
                new_parameters = call.parameters.copy()
                
                # 尝试一些通用的优化
                if call.tool_name == "img_gen":
                    # 提高图像质量设置
                    if "width" in new_parameters and new_parameters["width"] < 1024:
                        new_parameters["width"] = 1024
                        new_parameters["height"] = 1024
                
                elif call.tool_name == "video_gen":
                    # 调整视频参数
                    if "duration" in new_parameters and new_parameters["duration"] < 5:
                        new_parameters["duration"] = 5
                
                new_tool_call = ToolCall(
                    tool_name=call.tool_name,
                    parameters=new_parameters,
                    description=f"备用重新规划: {call.description}",
                    order=call.order
                )
                new_tool_calls.append(new_tool_call)
            
            return ExecutionPlan(
                task_type=original_plan.task_type,
                tool_calls=new_tool_calls,
                estimated_time=original_plan.estimated_time * 1.2,  # 增加时间估算
                description="备用重新规划的执行计划"
            )
        else:
            # 创建一个基本的备用计划
            if "prompt" in user_requirements:
                tool_call = ToolCall(
                    tool_name="img_gen",
                    parameters={
                        "prompt": user_requirements.get("prompt", ""),
                        "style": "realistic",
                        "width": 1024,
                        "height": 1024
                    },
                    description="备用重新规划：基础图像生成",
                    order=1
                )
                
                return ExecutionPlan(
                    task_type=GenerationTaskType.TEXT_TO_IMAGE,
                    tool_calls=[tool_call],
                    estimated_time=5.0,
                    description="备用重新规划的基础执行计划"
                )
        
        # 默认空计划
        return ExecutionPlan(
            task_type=GenerationTaskType.TEXT_TO_IMAGE,
            tool_calls=[],
            estimated_time=0.0,
            description="空的备用执行计划"
        )

    def _print_replan_results(self, replan_data: Dict[str, Any], attempt_number: int) -> None:
        """打印重新规划结果详情"""
        problem_analysis = replan_data.get("problem_analysis", {})
        replan_strategy = replan_data.get("replan_strategy", {})
        new_plan = replan_data.get("new_execution_plan", {})
        optimization_details = replan_data.get("optimization_details", {})
        success_probability = replan_data.get("success_probability", 0)
        
        print(f"\n🔄 重新规划结果 (第{attempt_number}次):")
        
        # 问题分析
        print(f"\n📋 问题分析:")
        identified_issues = problem_analysis.get("identified_issues", [])
        if identified_issues:
            for issue in identified_issues:
                severity_emoji = {"critical": "🔴", "moderate": "🟡", "minor": "🟢"}.get(issue.get("severity", "minor"), "🔵")
                print(f"  {severity_emoji} {issue.get('issue_type', 'unknown')}: {issue.get('description', '')}")
        
        primary_problem = problem_analysis.get("primary_problem", "")
        if primary_problem:
            print(f"  主要问题: {primary_problem}")
        
        # 重新规划策略
        print(f"\n🎯 重新规划策略:")
        strategy_type = replan_strategy.get("strategy_type", "unknown")
        print(f"  策略类型: {strategy_type}")
        print(f"  策略原因: {replan_strategy.get('reasoning', '')}")
        
        key_changes = replan_strategy.get("key_changes", [])
        if key_changes:
            print(f"  主要改变:")
            for change in key_changes:
                print(f"    - {change}")
        
        # 新执行计划
        print(f"\n📝 新执行计划:")
        print(f"  任务类型: {new_plan.get('task_type', 'unknown')}")
        print(f"  描述: {new_plan.get('description', '')}")
        print(f"  预估时间: {new_plan.get('estimated_time', 0)}秒")
        print(f"  工具调用数: {len(new_plan.get('tool_calls', []))}")
        
        # 优化详情
        print(f"\n⚙️ 优化详情:")
        prompt_improvements = optimization_details.get("prompt_improvements", [])
        if prompt_improvements:
            print(f"  提示词优化:")
            for improvement in prompt_improvements:
                print(f"    - {improvement}")
        
        parameter_adjustments = optimization_details.get("parameter_adjustments", {})
        if parameter_adjustments:
            print(f"  参数调整:")
            for param, changes in parameter_adjustments.items():
                print(f"    - {param}: {changes.get('old_value', '')} → {changes.get('new_value', '')} ({changes.get('reason', '')})")
        
        tool_changes = optimization_details.get("tool_changes", {})
        replaced_tools = tool_changes.get("replaced_tools", [])
        if replaced_tools:
            print(f"  工具替换:")
            for replacement in replaced_tools:
                print(f"    - {replacement.get('from', '')} → {replacement.get('to', '')} ({replacement.get('reason', '')})")
        
        # 成功率预测
        print(f"\n📊 成功率预测: {success_probability:.1%}")
        
        # 备选方案
        alternative_approaches = replan_data.get("alternative_approaches", [])
        if alternative_approaches:
            print(f"\n🔀 备选方案:")
            for approach in alternative_approaches:
                recommendation_emoji = "✅" if approach.get("recommendation") == "推荐" else "⚠️"
                print(f"  {recommendation_emoji} {approach.get('approach', '')}")
        
        print() 