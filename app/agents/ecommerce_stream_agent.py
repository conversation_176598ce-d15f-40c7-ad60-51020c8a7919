"""
电商流Stream Agent - 多步骤交互式图文增强流程
类似Cursor的多Agent协作模式
"""
import logging
import uuid
import asyncio
from datetime import datetime
from typing import Dict, List, Any
from django.core.cache import cache
from app.tools.ocr_text_enhance import ocr_enhancer

logger = logging.getLogger(__name__)

class EcommerceStreamState:
    """电商流程状态管理"""
    def __init__(self, stream_id: str, user_id: str):
        self.stream_id = stream_id
        self.user_id = user_id
        self.current_step = "upload"
        self.context = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()

    def to_dict(self):
        return {
            'stream_id': self.stream_id,
            'user_id': self.user_id,
            'current_step': self.current_step,
            'context': self.context,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    @classmethod
    def from_dict(cls, data: dict):
        state = cls(data['stream_id'], data['user_id'])
        state.current_step = data['current_step']
        state.context = data['context']
        return state

    def save(self):
        """保存状态到Redis"""
        cache.set(f"ecom_stream_{self.stream_id}", self.to_dict(), timeout=3600)
        self.updated_at = datetime.now()

    @classmethod
    def load(cls, stream_id: str):
        """从Redis加载状态"""
        data = cache.get(f"ecom_stream_{stream_id}")
        if data:
            return cls.from_dict(data)
        return None

class EcommerceStreamAgent:
    """电商流Stream处理器"""
    
    STEPS = {
        "upload": "图片上传",
        "ocr_extract": "OCR文案提取", 
        "user_confirm": "用户确认文案",
        "image_type_select": "选择图片类型",
        "image_generate": "图像生成",
        "image_enhance": "图像增强",
        "completed": "流程完成"
    }

    IMAGE_TYPES = {
        "main": "主图",
        "selling_points": "卖点图", 
        "lifestyle": "生活场景图",
        "specs": "参数汇总图"
    }

    def __init__(self):
        self.current_state = None

    def start_stream(self, user_id: str, uploaded_images: List[str]) -> Dict[str, Any]:
        """启动电商流Stream"""
        stream_id = str(uuid.uuid4())
        
        # 创建新的流程状态
        self.current_state = EcommerceStreamState(stream_id, user_id)
        self.current_state.context = {
            'uploaded_images': uploaded_images,
            'extracted_texts': [],
            'confirmed_copy': '',
            'selected_image_type': '',
            'reference_image': '',
            'generated_images': [],
            'final_images': []
        }
        self.current_state.save()

        logger.info(f"🚀 [EcomStream] 启动电商流 - ID: {stream_id}, 图片数: {len(uploaded_images)}")

        # 自动进入OCR提取步骤
        return self.process_step("ocr_extract")

    def process_step(self, step_name: str, user_input: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理特定步骤"""
        if not self.current_state:
            return {'error': '流程状态不存在'}

        logger.info(f"🔄 [EcomStream] 处理步骤: {step_name}")

        try:
            if step_name == "ocr_extract":
                return self._step_ocr_extract()
            elif step_name == "user_confirm":
                return self._step_user_confirm(user_input)
            elif step_name == "image_type_select":
                return self._step_image_type_select(user_input)
            elif step_name == "image_generate":
                return self._step_image_generate()
            elif step_name == "image_enhance":
                return self._step_image_enhance()
            else:
                return {'error': f'未知步骤: {step_name}'}

        except Exception as e:
            logger.error(f"❌ [EcomStream] 步骤处理失败 {step_name}: {str(e)}")
            return {'error': f'步骤处理失败: {str(e)}'}

    def _step_ocr_extract(self) -> Dict[str, Any]:
        """步骤1: OCR文案提取"""
        try:
            uploaded_images = self.current_state.context['uploaded_images']
            
            # 使用现有的OCR工具
            result = asyncio.run(ocr_enhancer.extract_and_enhance_text(uploaded_images, "ecommerce"))
            
            self.current_state.context['extracted_texts'] = result.get('extracted_texts', [])
            self.current_state.context['selling_points'] = result.get('selling_points', [])
            self.current_state.current_step = "user_confirm"
            self.current_state.save()

            return {
                'status': 'success',
                'step': 'user_confirm',
                'data': {
                    'extracted_texts': result.get('extracted_texts', []),
                    'selling_points': result.get('selling_points', []),
                    'next_action': '请选择或修改您想要的卖点文案'
                }
            }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ [EcomStream] OCR提取失败: {error_msg}")
            
            # 检查是否是OCR服务不可用的错误
            if "OCR服务暂不可用" in error_msg or "PaddleOCR" in error_msg:
                return {
                    'status': 'error',
                    'error': 'OCR文字识别服务暂时不可用，请稍后再试。我们正在努力修复这个问题。'
                }
            else:
                return {
                    'status': 'error',
                    'error': f'图片分析失败，请检查图片格式是否正确：{error_msg}'
                }

    def _step_user_confirm(self, user_input: Dict[str, Any]) -> Dict[str, Any]:
        """步骤2: 用户确认文案"""
        confirmed_copy = user_input.get('confirmed_copy', '')
        reference_image = user_input.get('reference_image', '')

        self.current_state.context['confirmed_copy'] = confirmed_copy
        self.current_state.context['reference_image'] = reference_image
        self.current_state.current_step = "image_type_select"
        self.current_state.save()

        return {
            'status': 'success',
            'step': 'image_type_select',
            'data': {
                'confirmed_copy': confirmed_copy,
                'image_types': self.IMAGE_TYPES,
                'next_action': '请选择要制作的图片类型'
            }
        }

    def _step_image_type_select(self, user_input: Dict[str, Any]) -> Dict[str, Any]:
        """步骤3: 选择图片类型"""
        selected_type = user_input.get('image_type', '')
        
        if selected_type not in self.IMAGE_TYPES:
            return {
                'status': 'error',
                'error': '无效的图片类型'
            }

        self.current_state.context['selected_image_type'] = selected_type
        self.current_state.current_step = "image_generate"
        self.current_state.save()

        # 自动进入图像生成步骤
        return self.process_step("image_generate")

    def _step_image_generate(self) -> Dict[str, Any]:
        """步骤4: 图像生成"""
        try:
            confirmed_copy = self.current_state.context['confirmed_copy']
            image_type = self.current_state.context['selected_image_type']
            # reference_image = self.current_state.context.get('reference_image', '')  # 暂时不使用

            # 根据图片类型生成不同的提示词
            image_prompts = self._generate_prompts_by_type(confirmed_copy, image_type)

            # 生成图像（这里需要实现具体的图像生成逻辑）
            generated_images = []
            for prompt in image_prompts:
                # 调用图像生成API
                # from app.adapters.image_gen import generate_image
                # image_result = await generate_image(prompt)
                # generated_images.append(image_result)
                
                # 临时返回模拟结果
                generated_images.append({
                    'prompt': prompt,
                    'image_url': f'/media/generated_{uuid.uuid4().hex[:8]}.jpg',
                    'type': image_type
                })

            self.current_state.context['generated_images'] = generated_images
            self.current_state.current_step = "image_enhance"
            self.current_state.save()

            return {
                'status': 'success',
                'step': 'image_enhance',
                'data': {
                    'generated_images': generated_images,
                    'next_action': '图片生成完成，请确认是否需要进一步增强'
                }
            }

        except Exception as e:
            logger.error(f"❌ [EcomStream] 图像生成失败: {str(e)}")
            return {
                'status': 'error',
                'error': f'图像生成失败: {str(e)}'
            }

    def _step_image_enhance(self) -> Dict[str, Any]:
        """步骤5: 图像增强"""
        # 图像增强逻辑（文案叠加、去水印、加logo等）
        generated_images = self.current_state.context['generated_images']
        
        # 这里实现具体的图像增强逻辑
        enhanced_images = []
        for image in generated_images:
            # 使用Pillow进行图像增强
            enhanced_image = {
                **image,
                'enhanced_url': f'/media/enhanced_{uuid.uuid4().hex[:8]}.jpg',
                'enhancements': ['文案叠加', '色彩优化', '品牌标识']
            }
            enhanced_images.append(enhanced_image)

        self.current_state.context['final_images'] = enhanced_images
        self.current_state.current_step = "completed"
        self.current_state.save()

        return {
            'status': 'success',
            'step': 'completed',
            'data': {
                'final_images': enhanced_images,
                'download_links': [img['enhanced_url'] for img in enhanced_images],
                'next_action': '流程完成！您可以下载生成的图片'
            }
        }

    def _generate_prompts_by_type(self, copy_text: str, image_type: str) -> List[str]:
        """根据图片类型生成相应的提示词"""
        base_prompt = f"商品描述: {copy_text}"
        
        type_prompts = {
            "main": [
                f"{base_prompt}, 电商主图, 白底, 产品居中, 高清摄影, 专业光线",
                f"{base_prompt}, 主图设计, 简洁背景, 突出产品特色, 商业摄影风格"
            ],
            "selling_points": [
                f"{base_prompt}, 卖点图, 图文结合, 突出功能特点, 营销设计",
                f"{base_prompt}, 产品卖点展示, 信息图表风格, 清晰文字说明"
            ],
            "lifestyle": [
                f"{base_prompt}, 生活场景, 真实使用环境, 人物互动, 自然光线",
                f"{base_prompt}, 生活方式展示, 场景化使用, 温馨氛围"
            ],
            "specs": [
                f"{base_prompt}, 参数汇总图, 技术规格展示, 数据可视化, 专业图表",
                f"{base_prompt}, 产品参数表, 规格对比, 详细信息展示"
            ]
        }

        return type_prompts.get(image_type, [base_prompt])

    def get_stream_status(self, stream_id: str) -> Dict[str, Any]:
        """获取流程状态"""
        state = EcommerceStreamState.load(stream_id)
        if not state:
            return {'error': '流程不存在'}

        return {
            'stream_id': stream_id,
            'current_step': state.current_step,
            'step_name': self.STEPS.get(state.current_step, '未知'),
            'context': state.context,
            'updated_at': state.updated_at
        }

# 全局单例
ecommerce_stream_agent = EcommerceStreamAgent() 