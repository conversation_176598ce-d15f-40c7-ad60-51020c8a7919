"""
Chat Agent - 对话引导智能体
使用ReAct模式与用户交互，引导用户明确视觉生成意图
基于LLM进行智能意图分析和对话管理
包含内容安全审查功能，确保生成内容合规
"""

import logging
from typing import Dict, Any, Optional
from app.core.state import AgentState, add_to_chat_history, log_agent_action, GenerationTaskType
from app.core.llm_client import get_llm_client

logger = logging.getLogger(__name__)

class ChatAgent:
    """Chat Agent - 对话引导智能体"""
    
    def __init__(self, model: str = None,concurrent_mode: bool = False):
        self.llm_client = get_llm_client(model)
        self.concurrent_mode = concurrent_mode  # 并发模式标志
        # 并发模式下的优化配置
        if concurrent_mode:
            # 降低温度以提高一致性
            self.llm_client.default_config['temperature'] = 0.2
            # 增加重试次数
            self.llm_client.max_retries = 5
            # 调整延迟配置
            self.llm_client.base_delay = 0.8
            self.llm_client.max_delay = 8.0
            print("🔧 [ChatAgent] 已启用并发优化模式")

        # 任务类型映射
        self.task_type_mapping = {
            "text_to_image": GenerationTaskType.TEXT_TO_IMAGE,
            "text_to_video": GenerationTaskType.TEXT_TO_VIDEO,
            "image_edit": GenerationTaskType.IMAGE_EDIT,
            "video_edit": GenerationTaskType.VIDEO_EDIT,
            "image_to_video": GenerationTaskType.IMAGE_TO_VIDEO,
            "multi_step_composite": GenerationTaskType.MULTI_STEP_COMPOSITE,
        }
        
        # 必需信息定义
        self.required_info = {
            GenerationTaskType.TEXT_TO_IMAGE: ["prompt", "style", "size"],
            GenerationTaskType.TEXT_TO_VIDEO: ["prompt", "duration", "style"],
            GenerationTaskType.IMAGE_EDIT: ["source_image", "edit_description"],
            GenerationTaskType.VIDEO_EDIT: ["source_video", "edit_description"],
            GenerationTaskType.IMAGE_TO_VIDEO: ["source_image", "motion_description", "duration"],
            GenerationTaskType.MULTI_STEP_COMPOSITE: ["step1_reference_images", "step1_edit_description", "step2_motion_description"]
        }
        
        pass  # 安全审查现在统一在SafetyAgent中处理

    def process(self, state: AgentState) -> AgentState:
        """处理用户输入，使用ReAct模式结合LLM分析"""
        log_agent_action(state, "ChatAgent", "开始分析用户输入")
        
        # 🎯 **CRITICAL FIX**: 确保enhanced_chat_history正确传递到下一个Agent
        enhanced_chat_history = state.get("enhanced_chat_history", [])
        logger.warning(f"🔧 [ChatAgent] enhanced_chat_history传递检查: {len(enhanced_chat_history)}条消息")
        
        # 检查SafetyAgent是否生成了专业回复
        llm_generated_response = state.get("llm_generated_response")
        if llm_generated_response:
            print(f"🎓 [ChatAgent Debug] 发现SafetyAgent生成的专业回复，长度: {len(llm_generated_response)} 字符")
            # 直接使用专业回复
            state["final_response"] = llm_generated_response
            state["is_intent_clear"] = False  # 非视觉任务，意图不明确但有专业回复
            add_to_chat_history(state, "user", state["user_input"])
            add_to_chat_history(state, "assistant", llm_generated_response)
            print(f"🤖 助手: {llm_generated_response}")
            log_agent_action(state, "ChatAgent", "使用SafetyAgent生成的专业回复")
            # 🎯 确保enhanced_chat_history保持传递
            state["enhanced_chat_history"] = enhanced_chat_history
            return state
        
        # 检查对话历史中是否已经包含了当前用户输入，避免重复添加
        chat_history = state.get("chat_history", [])
        if state["user_input"] and (not chat_history or chat_history[-1].get("content") != state["user_input"]):
            add_to_chat_history(state, "user", state["user_input"])
        
        # ReAct循环：思考-行动-观察
        thought = self._think(state)
        action = self._act(state, thought)
        observation = self._observe(state, action, thought)
        
        # 根据观察结果更新状态
        if observation.get("intent_clear", False):
            state["is_intent_clear"] = True
            state["user_intent"] = observation["intent"]
            state["user_requirements"] = observation["requirements"]
            log_agent_action(state, "ChatAgent", f"意图已明确: {observation['intent']}")
        else:
            state["is_intent_clear"] = False
            # 使用LLM生成的引导性问题
            guidance_question = observation.get("next_question") or "请告诉我您希望创建什么样的视觉内容？"
            add_to_chat_history(state, "assistant", guidance_question)
            print(f"\n🤖 助手: {guidance_question}")
            log_agent_action(state, "ChatAgent", "生成引导性问题")
            # 注意：_observe方法可能已经设置了final_response，这里不要覆盖它
        
        # 🎯 **CRITICAL FIX**: 确保enhanced_chat_history在所有返回路径中都正确传递
        state["enhanced_chat_history"] = enhanced_chat_history
        logger.warning(f"🔧 [ChatAgent] 最终确认enhanced_chat_history传递: {len(enhanced_chat_history)}条消息")
        
        return state

    def _think(self, state: AgentState) -> Dict[str, Any]:
        """思考阶段：使用LLM分析当前情况"""
        # 使用安全审查后的输入内容，如果安全审查未通过则使用原始输入
        user_input = state.get("processed_input", state["user_input"])
        chat_history = state.get("chat_history", [])
        current_requirements = state.get("user_requirements", {})
        last_generation_result = state.get("last_generation_result")
        generation_history = state.get("generation_history", [])
        

        
        # 将生成历史转换为字典格式供LLM使用
        formatted_history = []
        for record in generation_history:
            formatted_history.append({
                "round_number": record.round_number,
                "user_input": record.user_input,
                "description": record.description,
                "result_path": record.result_path,
                "task_type": record.task_type.value,
                "success": record.success
            })

        try:
            # 🔍 调试状态中的所有媒体上下文
            self._debug_state_media_context(state)
            
            # 🎯 **FIX**: 直接使用Graph.py已经正确分类的媒体数据，避免重复处理
            # Graph.py已经在create_initial_state中正确处理了所有媒体分类
            all_media = state.get("attachments", [])  # Graph.py已经包含了所有媒体
            current_media = state.get("current_media", [])  # 当前消息的媒体
            historical_media = state.get("historical_media", [])  # 历史媒体
            media_stats = state.get("media_stats", {})  # 媒体统计
            
            # 📊 使用Graph.py提供的正确媒体统计
            logger.warning(f"🔍 [ChatAgent媒体正确统计] 总媒体数量: {len(all_media)}")
            logger.warning(f"🔍 [ChatAgent媒体正确统计] 当前消息媒体: {len(current_media)}个")  
            logger.warning(f"🔍 [ChatAgent媒体正确统计] 历史媒体: {len(historical_media)}个")
            logger.warning(f"🔍 [ChatAgent媒体正确统计] 媒体统计: {media_stats}")
            
            # 详细列出当前消息的媒体（用于调试）
            if current_media:
                logger.warning("🎯 [ChatAgent] 当前消息包含的媒体:")
                for i, media in enumerate(current_media):
                    source_info = f"({media.get('source', 'unknown')})"
                    if media.get('reference_id'):
                        source_info += f" @{media['reference_id']}"
                    logger.warning(f"   {i+1}. {media.get('type', 'unknown')}: {media.get('url', '')[:50]}... {source_info}")
            
            # 调用LLM进行意图分析，传递Graph.py已经正确处理的媒体上下文
            llm_result = self.llm_client.analyze_user_intent(
                user_input=user_input,
                chat_history=chat_history,
                current_requirements=current_requirements,
                last_generation_result=last_generation_result,
                generation_history=formatted_history,
                attachments=all_media  # 使用Graph.py已经正确分类的所有媒体
            )
            
            if llm_result['success']:
                analysis = llm_result['analysis']
                
                # 构建思考结果
                thought = {
                    "llm_success": True,
                    "analysis": analysis,
                    "task_type": self._map_task_type(analysis.get("task_type")),
                    "confidence": analysis.get("confidence", 0.0),
                    "intent_clear": analysis.get("intent_clear", False),
                    "missing_info": analysis.get("missing_info", []),
                    "next_question": analysis.get("next_question", ""),
                    "raw_response": llm_result.get('raw_response', '')
                }
                

                
                log_agent_action(state, "ChatAgent", f"LLM分析成功，置信度: {thought['confidence']}")
                return thought
            else:
                # LLM调用失败，使用简化错误处理
                logger.warning(f"LLM分析失败: {llm_result.get('error', 'Unknown error')}")
                return {
                    "llm_success": False,
                    "analysis": {},
                    "confidence": 0.0,
                    "intent_clear": False
                }
                
        except Exception as e:
            logger.error(f"LLM分析过程出错: {str(e)}")
            # 简化错误处理，返回基本结构让observe方法处理
            return {
                "llm_success": False,
                "analysis": {},
                "confidence": 0.0,
                "intent_clear": False
            }

    def _act(self, state: AgentState, thought: Dict[str, Any]) -> str:
        """行动阶段：基于LLM分析结果决定行动"""
        if thought.get("llm_success", False):
            # 基于LLM分析结果决定行动
            analysis = thought["analysis"]
            
            if analysis.get("intent_clear", False):
                return "confirm_intent"
            else:
                return "request_info"
        else:
            # LLM失败时的默认行动
            return "request_info"

    def _observe(self, state: AgentState, action: str, thought: Dict[str, Any]) -> Dict[str, Any]:
        """观察阶段：基于LLM分析结果生成观察"""
        observation = {
            "intent_clear": False,
            "intent": None,
            "requirements": {},
            "next_question": ""
        }
        
        # 优先处理当前LLM分析结果
        if thought.get("llm_success", False):
            # 直接使用LLM分析结果
            analysis = thought["analysis"]
            observation["intent_clear"] = analysis.get("intent_clear", False)
            observation["intent"] = analysis.get("user_intent", "")
            observation["requirements"] = analysis.get("requirements", {})
            observation["next_question"] = analysis.get("next_question", "")
            
            # 如果意图明确，验证和补充requirements
            if observation["intent_clear"]:
                task_type = thought.get("task_type")
                if task_type:
                    observation["requirements"] = self._validate_and_complete_requirements(
                        observation["requirements"], task_type
                    )
                # 清除之前的失败状态，因为现在意图已明确
                state["plan_failure_reason"] = None
                state["plan_failure_details"] = {}
                log_agent_action(state, "ChatAgent", "意图已明确，清除之前的失败状态")
            else:
                # 意图不明确时，检查是否有PlanAgent传递的失败信息
                plan_failure_reason = state.get("plan_failure_reason")
                plan_failure_details = state.get("plan_failure_details", {})
                
                if plan_failure_reason:
                    # 优先使用LLM生成的针对性回复，如果没有则使用PlanAgent失败处理
                    if observation["next_question"]:
                        # LLM生成了针对性回复，使用它
                        guidance_message = observation["next_question"]
                        print(f"🔧 [ChatAgent Debug] 使用LLM生成的针对性回复: {len(guidance_message)} 字符")
                    else:
                        # LLM没有生成回复，使用PlanAgent失败处理
                        guidance_message = self._handle_plan_failure(plan_failure_reason, plan_failure_details)
                        observation["next_question"] = guidance_message
                        print(f"🔧 [ChatAgent Debug] 使用PlanAgent失败处理回复: {len(guidance_message)} 字符")
                    
                    # 设置到final_response
                    state["final_response"] = guidance_message
                    # 清除失败信息，避免重复处理
                    state["plan_failure_reason"] = None
                    state["plan_failure_details"] = {}
                    log_agent_action(state, "ChatAgent", f"处理PlanAgent失败: {plan_failure_reason}")
                else:
                    # 优先使用LLM生成的引导问题，只有在没有时才使用默认引导
                    if observation["next_question"]:
                        # LLM生成了针对性回复（可能是非视觉任务的专业回复），使用它
                        guidance_message = observation["next_question"]
                        state["final_response"] = guidance_message
                        print(f"🔧 [ChatAgent Debug] 使用LLM生成的专业回复: {len(guidance_message)} 字符")
                    else:
                        # LLM没有生成回复，使用默认引导
                        guidance_message = self._get_helpful_guidance_message()
                        observation["next_question"] = guidance_message
                        state["final_response"] = guidance_message
                        print(f"🔧 [ChatAgent Debug] 使用默认引导回复: {len(guidance_message)} 字符")
        else:
            # LLM分析失败时，检查是否有PlanAgent传递的失败信息
            plan_failure_reason = state.get("plan_failure_reason")
            plan_failure_details = state.get("plan_failure_details", {})
            
            if plan_failure_reason:
                # PlanAgent失败，处理错误并提供针对性指导
                guidance_message = self._handle_plan_failure(plan_failure_reason, plan_failure_details)
                observation["next_question"] = guidance_message
                # 对于PlanAgent失败的引导回复，也设置到final_response
                state["final_response"] = guidance_message
                print(f"🔧 [ChatAgent Debug] 设置final_response (LLM失败+PlanAgent失败): {len(guidance_message)} 字符")
                # 清除失败信息，避免重复处理
                state["plan_failure_reason"] = None
                state["plan_failure_details"] = {}
                log_agent_action(state, "ChatAgent", f"处理PlanAgent失败: {plan_failure_reason}")
            else:
                # LLM失败时的默认响应 - 使用更好的引导
                guidance_message = self._get_helpful_guidance_message()
                observation["next_question"] = guidance_message
                # 对于非视觉任务的引导回复，也设置到final_response
                state["final_response"] = guidance_message
                print(f"🔧 [ChatAgent Debug] 设置final_response (LLM失败): {len(guidance_message)} 字符")
        
        return observation

    def _handle_plan_failure(self, failure_reason: str, failure_details: Dict[str, Any]) -> str:
        """处理PlanAgent失败，生成针对性的用户指导"""
        error_type = failure_details.get("error_type", "general_validation_failure")
        user_friendly_message = failure_details.get("user_friendly_message", "")
        suggested_action = failure_details.get("suggested_action", "clarify_requirements")
        original_intent = failure_details.get("original_intent", "")
        
        print("🔧 [ChatAgent] 处理PlanAgent失败:")
        print(f"   错误类型: {error_type}")
        print(f"   原始意图: {original_intent}")
        print(f"   建议行动: {suggested_action}")
        
        # 根据错误类型生成针对性回复
        if error_type == "url_validation_failure":
            return self._generate_url_failure_guidance(user_friendly_message, failure_reason)
        elif error_type == "missing_parameters":
            return self._generate_missing_params_guidance(original_intent, failure_reason)
        elif error_type == "llm_generation_failure":
            return self._generate_llm_failure_guidance(original_intent)
        elif error_type == "unclear_intent":
            # 对于意图不明确的情况，使用改进的引导信息
            return self._get_helpful_guidance_message()
        else:
            return f"{user_friendly_message}\n\nPlease redescribe your requirements, and I'll do my best to help you with your creation."
    
    def _generate_url_failure_guidance(self, user_friendly_message: str, failure_reason: str) -> str:
        """生成URL验证失败的指导"""
        base_message = user_friendly_message
        
        # 提供具体的解决方案
        if "image" in failure_reason.lower():
            suggestions = [
                "• Re-upload the image file",
                "• Confirm the image link is accessible",
                "• Supported image formats: JPG, PNG, GIF, etc.",
                "• If it's a web image, ensure the link is valid and publicly accessible"
            ]
        elif "video" in failure_reason.lower():
            suggestions = [
                "• Re-upload the video file", 
                "• Confirm the video link is accessible",
                "• Supported video formats: MP4, AVI, MOV, etc.",
                "• If it's a web video, ensure the link is valid and publicly accessible"
            ]
        else:
            suggestions = [
                "• Re-upload the media file",
                "• Confirm the file link is accessible",
                "• Check if the file format is supported"
            ]
        
        suggestion_text = "\n".join(suggestions)
        return f"{base_message}\n\nSolutions:\n{suggestion_text}\n\nYou can re-upload the file, or generate new image/video content first."
    
    def _generate_missing_params_guidance(self, original_intent: str, failure_reason: str) -> str:
        """生成缺少参数的指导"""
        base_message = "Your request is missing some essential information. Let me help you complete it:"
        
        # 根据缺少的参数类型提供具体建议
        if "prompt" in failure_reason:
            return f"{base_message}\n\nPlease describe in detail what you want to create, such as:\n• Specific scenes and subjects\n• Visual style (realistic, cartoon, artistic style, etc.)\n• Color preferences\n• Other special requirements"
        elif "source_image" in failure_reason:
            return f"{base_message}\n\nYou need to provide a source image for editing. Please:\n• Upload the image you want to edit\n• Or tell me which previously generated image you want to edit"
        elif "source_video" in failure_reason:
            return f"{base_message}\n\nYou need to provide a source video for editing. Please:\n• Upload the video you want to edit\n• Or tell me which previously generated video you want to edit"
        else:
            return f"{base_message}\n\nPlease provide more detailed descriptions, including the specific effects and style preferences you want."
    
    def _generate_llm_failure_guidance(self, original_intent: str) -> str:
        """生成LLM分析失败的指导"""
        return """I'm having some difficulty understanding your request. To better assist you, please:

• Describe your needs in simple and direct language
• Specify whether you want to generate images or videos
• If editing existing content, clearly state what you want to edit
• Provide specific details

For example: "Generate a mountain landscape at sunset, realistic style" or "Turn this image into a 5-second animated video"."""

    def _map_task_type(self, task_type_str: Optional[str]) -> Optional[GenerationTaskType]:
        """映射LLM返回的任务类型字符串到枚举"""
        if not task_type_str:
            return None
        
        # 直接映射
        if task_type_str in self.task_type_mapping:
            return self.task_type_mapping[task_type_str]
        
        # 模糊匹配
        task_type_lower = task_type_str.lower()
        if "text_to_image" in task_type_lower or "文生图" in task_type_str:
            return GenerationTaskType.TEXT_TO_IMAGE
        elif "text_to_video" in task_type_lower or "文生视频" in task_type_str:
            return GenerationTaskType.TEXT_TO_VIDEO
        elif "image_edit" in task_type_lower or "图像编辑" in task_type_str:
            return GenerationTaskType.IMAGE_EDIT
        elif "video_edit" in task_type_lower or "视频编辑" in task_type_str:
            return GenerationTaskType.VIDEO_EDIT
        elif "image_to_video" in task_type_lower or "图生视频" in task_type_str:
            return GenerationTaskType.IMAGE_TO_VIDEO
        
        return None

    def _validate_and_complete_requirements(self, requirements: Dict[str, Any], 
                                          task_type: GenerationTaskType) -> Dict[str, Any]:
        """验证和补充需求信息，提供智能默认值"""
        validated_reqs = requirements.copy()
        required_fields = self.required_info.get(task_type, [])
        
        # 智能默认值设置
        intelligent_defaults = {
            # 图像生成默认值
            "style": "realistic",  # 写实风格最受欢迎
            "size": "1024x1024",   # 高质量正方形，适合社交媒体
            
            # 视频生成默认值
            "duration": 5,         # 5秒适合大多数场景
            "fps": 24,             # 标准帧率
            
            # 动作描述默认值
            "motion_description": "smooth natural movement",
            
            # 编辑默认值
            "edit_intensity": "moderate"  # 中等强度编辑
        }
        
        # 任务类型特定的智能补全
        task_specific_enhancements = {
            GenerationTaskType.TEXT_TO_IMAGE: {
                "aspect_ratio": "1:1",
                "quality": "high"
            },
            GenerationTaskType.TEXT_TO_VIDEO: {
                "motion_intensity": "medium",
                "camera_movement": "static"
            },
            GenerationTaskType.IMAGE_TO_VIDEO: {
                "motion_type": "natural",
                "preserve_style": True
            }
        }
        
        # 应用默认值
        for field in required_fields:
            if field not in validated_reqs or not validated_reqs[field]:
                if field in intelligent_defaults:
                    validated_reqs[field] = intelligent_defaults[field]
        
        # 应用任务特定增强
        if task_type in task_specific_enhancements:
            for key, value in task_specific_enhancements[task_type].items():
                if key not in validated_reqs:
                    validated_reqs[key] = value
        
        return validated_reqs



    def _debug_state_media_context(self, state: AgentState) -> None:
        """调试状态中的媒体上下文"""
        logger.warning("🔍 [ChatAgent状态检查] ==================")
        logger.warning(f"🔍 [ChatAgent状态检查] user_input: {state.get('user_input', '')[:100]}...")
        
        # 🎯 使用新的媒体统计信息
        media_stats = state.get("media_stats", {})

        # 🎯 检查图文交织的enhanced_chat_history
        enhanced_chat_history = state.get("enhanced_chat_history", [])
        logger.warning(f"🔍 [ChatAgent状态检查] enhanced_chat_history长度: {len(enhanced_chat_history)}")
        
        total_enhanced_media = 0
        for i, msg in enumerate(enhanced_chat_history):
            msg_media = msg.get("media", [])
            if msg_media:
                total_enhanced_media += len(msg_media)
                logger.warning(f"🔍 [ChatAgent状态检查] 增强消息[{i}]: role={msg.get('role')}, 媒体数量={len(msg_media)}")
                for j, media in enumerate(msg_media):
                    logger.warning(f"   媒体{j+1}: {media.get('type')} - {media.get('url', '')[:30]}... (来源: {media.get('source')})")
        
        logger.warning(f"🔍 [ChatAgent状态检查] enhanced_chat_history中媒体总数: {total_enhanced_media}")
        
        # 🎯 检查当前消息包含的媒体
        current_media = state.get("current_media", [])
        if current_media:
            logger.warning(f"🎯 [ChatAgent] 当前消息包含的媒体: {len(current_media)}个")
            for i, media in enumerate(current_media):
                source_info = f"({media.get('source')})"
                if media.get('reference_id'):
                    source_info += f" @{media.get('reference_id')}"
                logger.warning(f"   {i+1}. {media.get('type')}: {source_info}")
        else:
            logger.warning("🎯 [ChatAgent] 当前消息包含的媒体: 无")
        
        # 🎯 检查历史媒体
        historical_media = state.get("historical_media", [])
        if historical_media:
            logger.warning(f"🎯 [ChatAgent] 历史媒体: {len(historical_media)}个")
            for i, media in enumerate(historical_media):
                logger.warning(f"   {i+1}. {media.get('type')}: {media.get('url', '')[:30]}... (来源: {media.get('source')})")
        
        logger.warning("🔍 [ChatAgent状态检查] ==================")

    def _get_helpful_guidance_message(self) -> str:
        """获取有用的引导消息"""
        return """I can help you create various types of visual content! Please tell me what you'd like:

🎨 **Generate Images** - For example: Create a landscape photo, draw a cartoon character, make abstract artwork
🎬 **Generate Videos** - For example: Create animated videos, product showcase videos, nature scenery videos  
✏️ **Edit Images** - For example: Change image colors, add effects, combine multiple images
🎞️ **Image to Video** - For example: Animate static images, add dynamic effects

What specific type of creation would you like? Please describe your ideas in detail!"""
