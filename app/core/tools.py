"""
真实视觉生成工具模块
包含所有可用的生成和编辑工具的真实调用
"""

import os
import asyncio
import tempfile
from typing import Dict, Any, List, Union
from dataclasses import dataclass
from .state import GenerationResult

# 导入真实工具
try:
    from app.tools.image_gen_openai import generate_image, OpenAIImageGenError as ImageGenError
    from app.tools.video_gen_volcengine import generate_video as generate_video_volcengine, VolcengineVideoGenError as VideoGenVolcengineError
    from app.tools.video_gen_veo3 import generate_video as generate_video_veo3, VideoGenVeo3Error
    from app.tools.img_edit_openai import edit_image, OpenAIImageEditError
    from app.tools.img2video_volcengine import image_to_video as image_to_video_volcengine, VolcengineImg2VideoError as Img2VideoVolcengineError
    from app.tools.img2video_veo3 import image_to_video as image_to_video_veo3, Img2VideoVeo3Error
    from app.tools.img2video_kling import image_to_video as image_to_video_kling, Img2VideoError as Img2VideoKlingError
    from app.tools.img2video_wx import image_to_video as image_to_video_wx, Img2VideoError as Img2VideoWxError
    from app.tools.video_keyframe import extract_keyframe, VideoKeyframeError
    from app.tools.language_adapter import convert_response_language
    from app.tools.oss_upload import upload_and_get_signed_url
    from app.config.settings import settings
    REAL_TOOLS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 无法导入真实工具: {e}")
    REAL_TOOLS_AVAILABLE = False
    
    # 定义模拟异常类
    class ImageGenError(Exception): pass
    class VideoGenVolcengineError(Exception): pass
    class VideoGenVeo3Error(Exception): pass
    class OpenAIImageEditError(Exception): pass
    class Img2VideoVolcengineError(Exception): pass
    class Img2VideoVeo3Error(Exception): pass
    class Img2VideoKlingError(Exception): pass
    class Img2VideoWxError(Exception): pass
    class VideoKeyframeError(Exception): pass
    
    # 定义模拟函数
    async def generate_image(*args, **kwargs):
        raise ImageGenError("真实工具不可用，这是模拟调用")
    
    async def generate_video_volcengine(*args, **kwargs):
        raise VideoGenVolcengineError("真实工具不可用，这是模拟调用")
    
    async def generate_video_veo3(*args, **kwargs):
        raise VideoGenVeo3Error("真实工具不可用，这是模拟调用")
    
    async def edit_image(*args, **kwargs):
        raise OpenAIImageEditError("真实工具不可用，这是模拟调用")
        
    async def image_to_video_volcengine(*args, **kwargs):
        raise Img2VideoVolcengineError("真实工具不可用，这是模拟调用")
    
    async def image_to_video_veo3(*args, **kwargs):
        raise Img2VideoVeo3Error("真实工具不可用，这是模拟调用")
    
    async def image_to_video_kling(*args, **kwargs):
        raise Img2VideoKlingError("真实工具不可用，这是模拟调用")
    
    async def image_to_video_wx(*args, **kwargs):
        raise Img2VideoWxError("真实工具不可用，这是模拟调用")
        
    async def extract_keyframe(*args, **kwargs):
        raise VideoKeyframeError("真实工具不可用，这是模拟调用")
        
    def convert_response_language(*args, **kwargs):
        return "模拟语言转换结果"
        
    def upload_and_get_signed_url(*args, **kwargs):
        return "https://example.com/mock_upload.jpg"

@dataclass
class ToolInfo:
    """工具信息"""
    name: str
    description: str
    parameters: Dict[str, str]
    capabilities: List[str]
    limitations: List[str]

class RealVisualTools:
    """真实视觉生成工具类"""
    
    def __init__(self):
        # 使用/tmp目录或者app目录下的outputs
        if os.path.exists("/app"):
            # Docker环境
            self.output_dir = "/app/outputs"
        else:
            # 本地环境
            self.output_dir = "outputs"
        
        if not os.path.exists(self.output_dir):
            try:
                os.makedirs(self.output_dir, mode=0o755)
            except PermissionError:
                # 如果没有权限创建，使用临时目录
                import tempfile
                self.output_dir = tempfile.mkdtemp(prefix="miragemakers_")
                print(f"⚠️ 使用临时目录: {self.output_dir}")
    
    async def img_gen(self, prompt: str, style: str = "realistic", 
                      width: int = 1024, height: int = 1024, **kwargs) -> GenerationResult:
        """文生图工具，支持多参考图像"""
        # 检查多参考图像
        reference_images = kwargs.get('reference_images', [])
        if reference_images:
            print(f"🔗 使用参考图像: {len(reference_images)} 张")
            for ref in reference_images:
                if isinstance(ref, dict):
                    print(f"   - {ref.get('description', '未知')} ({ref.get('path', '无路径')})")
        
        print(f"🎨 正在生成图像...")
        print(f"   提示词: {prompt}")
        print(f"   风格: {style}")
        print(f"   尺寸: {width}x{height}")
        
        try:
            # 设置尺寸格式 - OpenAI使用"x"分隔
            size = f"{width}x{height}"
            
            # 调用OpenAI API
            result = await generate_image(
                prompt=prompt,
                size=size,
                model="gpt-image-1",
                quality="high",
                output_format="png"
            )
            
            generation_result = GenerationResult(
                tool_name="img_gen",
                result_path=result["image_url"],
                metadata={
                    "prompt": prompt,
                    "style": style,
                    "width": width,
                    "height": height,
                    "task_id": result["task_id"],
                    "reference_count": len(reference_images) if reference_images else 0
                },
                success=True
            )
            
            print(f"✅ 图像生成完成: {result['image_url']}")
            return generation_result
            
        except ImageGenError as e:
            print(f"❌ 图像生成失败: {e}")
            return GenerationResult(
                tool_name="img_gen",
                result_path="",
                success=False,
                error_message=str(e)
            )
        except Exception as e:
            print(f"❌ 意外错误: {e}")
            return GenerationResult(
                tool_name="img_gen",
                result_path="",
                success=False,
                error_message=f"意外错误: {str(e)}"
            )

    async def video_gen(self, prompt: str, duration: int = 5, 
                        fps: int = 30, style: str = "realistic", **kwargs) -> GenerationResult:
        """文生视频工具"""
        print(f"🎬 正在生成视频...")
        print(f"   提示词: {prompt}")
        print(f"   时长: {duration}秒")
        print(f"   帧率: {fps}fps")
        print(f"   风格: {style}")
        
        # 根据API版本选择工具
        api_version = settings.api_version
        print(f"   API版本: {api_version}")
        
        try:
            if api_version == "max":
                # 使用VEO3 API (满血版)
                print(f"   使用VEO3 API")
                result = await generate_video_veo3(
                    prompt=prompt,
                    duration=duration,
                    size="1280*720"
                )
                error_type = VideoGenVeo3Error
            elif api_version == "plus":
                # 使用火山方舟API (增强版)
                print(f"   使用火山方舟API")
                result = await generate_video_volcengine(
                    prompt=prompt,
                    duration=duration,
                    size="1280*720"
                )
                error_type = VideoGenVolcengineError
            else:
                # 使用万象API (自动版)
                from app.tools.video_gen import generate_video as generate_video_wanx, VideoGenError as WanxVideoGenError
                print(f"   使用万象API")
                result = await generate_video_wanx(
                    prompt=prompt,
                    duration=duration,
                    model="wanx2.1-t2v-turbo"
                )
                error_type = WanxVideoGenError
            
            generation_result = GenerationResult(
                tool_name="video_gen",
                result_path=result["video_url"],
                metadata={
                    "prompt": prompt,
                    "duration": duration,
                    "fps": fps,
                    "style": style,
                    "task_id": result["task_id"],
                    "api_version": api_version
                },
                success=True
            )
            
            print(f"✅ 视频生成完成: {result['video_url']}")
            return generation_result
            
        except Exception as e:
            print(f"❌ 视频生成失败: {e}")
            return GenerationResult(
                tool_name="video_gen",
                result_path="",
                success=False,
                error_message=str(e)
            )

    async def img_edit(self, image_path: Union[str, List[str]], edit_prompt: str, 
                       edit_type: str = "modify", **kwargs) -> GenerationResult:
        """图像编辑工具，支持多图合成"""
        print(f"🖼️ 正在编辑图像...")
        source_description = image_path if isinstance(image_path, list) else [image_path]
        print(f"   源图像: {', '.join(source_description)}")
        print(f"   编辑描述: {edit_prompt}")
        print(f"   编辑类型: {edit_type}")
        
        # 检查多参考图像（用于合成）
        reference_images = kwargs.get('reference_images', [])
        if reference_images and edit_type == "composite":
            print(f"🔗 使用多图合成模式: {len(reference_images)} 张参考图")
            for ref in reference_images:
                if isinstance(ref, dict):
                    print(f"   - {ref.get('description', '未知')}: {ref.get('path', '无路径')}")
        
        try:
            # 策略1: 优先尝试OpenAI API（支持多参考图像）
            print(f"🔄 尝试OpenAI图像编辑API...")
            result = await edit_image(
                image_url=image_path,  # 直接传递单URL或URL列表
                prompt=edit_prompt,
                model="gpt-image-1",
                reference_images=reference_images if reference_images else None
            )
            
            generation_result = GenerationResult(
                tool_name="img_edit",
                result_path=result["image_url"],
                metadata={
                    "source_image": image_path,
                    "edit_prompt": edit_prompt,
                    "edit_type": edit_type,
                    "task_id": result["task_id"],
                    "reference_count": len(reference_images) if reference_images else 0,
                    "api_used": "openai"
                },
                success=True
            )
            
            print(f"✅ OpenAI图像编辑完成: {result['image_url']}")
            return generation_result
            
        except OpenAIImageEditError as e:
            error_msg = str(e)
            print(f"❌ OpenAI图像编辑失败: {error_msg}")
            
            generation_result = GenerationResult(
                tool_name="img_edit",
                result_path=None,
                metadata={
                    "source_image": image_path,
                    "edit_prompt": edit_prompt,
                    "edit_type": edit_type,
                    "error": error_msg
                },
                success=False
            )
            
            return generation_result
        except Exception as e:
            print(f"❌ 意外错误: {e}")
            return GenerationResult(
                tool_name="img_edit",
                result_path="",
                success=False,
                error_message=f"意外错误: {str(e)}"
            )

    async def video_edit(self, video_path: str, edit_prompt: str, 
                         edit_type: str = "modify", **kwargs) -> GenerationResult:
        """视频编辑工具 - 通过三步流程实现：关键帧提取 -> 图像编辑 -> 图生视频"""
        print(f"🎞️ 正在编辑视频...")
        print(f"   原视频: {video_path}")
        print(f"   编辑指令: {edit_prompt}")
        print(f"   编辑类型: {edit_type}")
        print(f"   执行三步流程: 关键帧提取 -> 图像编辑 -> 图生视频")
        
        try:
            # 步骤1: 关键帧提取
            print(f"🔍 步骤1/3: 提取视频关键帧...")
            keyframe_result = await extract_keyframe(video_path)
            
            if keyframe_result.get("task_status") != "SUCCEEDED":
                raise VideoKeyframeError("关键帧提取失败")
            
            keyframe_url = keyframe_result["image_url"]
            print(f"✅ 关键帧提取完成: {keyframe_url}")
            
                         # 步骤2: 图像编辑
            print(f"🖼️ 步骤2/3: 编辑关键帧图像...")
            edit_result = await edit_image(
                image_url=keyframe_url,
                prompt=edit_prompt,
                model="gpt-image-1",
                reference_images=kwargs.get('reference_images', None)
            )
            
            edited_image_url = edit_result["image_url"]
            print(f"✅ 图像编辑完成: {edited_image_url}")
            
            # 步骤3: 图生视频
            print(f"🎥 步骤3/3: 将编辑后图像转换为视频...")
            
            # 根据API版本选择工具
            api_version = settings.api_version
            if api_version == "max":
                # 使用火山方舟API (满血版)
                print(f"   使用火山方舟API")
                video_result = await image_to_video_volcengine(
                    prompt=edit_prompt,
                    img_url=edited_image_url,
                    size="1280*720",
                    duration=5
                )
            else:
                # 使用可灵API (自动版)
                print(f"   使用可灵API")
                video_result = await image_to_video_kling(
                    prompt=edit_prompt,
                    img_url=edited_image_url,
                    caption=None
                )
            
            final_video_url = video_result["video_url"]
            print(f"✅ 视频编辑完成: {final_video_url}")
            
            generation_result = GenerationResult(
                tool_name="video_edit",
                result_path=final_video_url,
                metadata={
                    "original_video": video_path,
                    "edit_prompt": edit_prompt,
                    "edit_type": edit_type,
                    "keyframe_url": keyframe_url,
                    "edited_image_url": edited_image_url,
                    "video_caption": keyframe_result.get("video_caption", ""),
                    "steps": ["keyframe_extraction", "image_editing", "img2video"]
                },
                success=True
            )
            
            return generation_result
            
        except (VideoKeyframeError, OpenAIImageEditError, Img2VideoVolcengineError, Img2VideoKlingError) as e:
            print(f"❌ 视频编辑失败: {e}")
            return GenerationResult(
                tool_name="video_edit",
                result_path="",
                success=False,
                error_message=str(e)
            )
        except Exception as e:
            print(f"❌ 意外错误: {e}")
            return GenerationResult(
                tool_name="video_edit",
                result_path="",
                success=False,
                error_message=f"意外错误: {str(e)}"
            )

    async def img2video(self, image_path: str, motion_prompt: str = "", 
                        duration: int = 5, **kwargs) -> GenerationResult:
        """图生视频工具"""
        print(f"🎥 正在将图像转换为视频...")
        print(f"   输入图像: {image_path}")
        print(f"   运动描述: {motion_prompt}")
        print(f"   时长: {duration}秒")
        
        # 根据API版本选择工具
        api_version = settings.api_version
        print(f"   API版本: {api_version}")
        
        try:
            if api_version == "max":
                # 使用VEO3 API (满血版)
                print(f"   使用VEO3 API")
                result = await image_to_video_veo3(
                    prompt=motion_prompt,
                    img_url=image_path,
                    resolution="720P"
                )
                error_type = Img2VideoVeo3Error
            elif api_version == "plus":
                # 使用火山方舟API (增强版)
                print(f"   使用火山方舟API")
                result = await image_to_video_volcengine(
                    prompt=motion_prompt,
                    img_url=image_path,
                    size="1280*720",
                    duration=duration
                )
                error_type = Img2VideoVolcengineError
            else:
                # 使用可灵/万象API (自动版)
                print(f"   使用可灵API")
                result = await image_to_video_kling(
                    prompt=motion_prompt,
                    img_url=image_path,
                    caption=None  # 让工具自动生成caption
                )
                error_type = Img2VideoKlingError
            
            generation_result = GenerationResult(
                tool_name="img2video",
                result_path=result["video_url"],
                metadata={
                    "input_image": image_path,
                    "motion_prompt": motion_prompt,
                    "duration": duration,
                    "task_id": result["task_id"],
                    "final_prompt": result.get("prompt", motion_prompt),
                    "api_version": api_version
                },
                success=True
            )
            
            print(f"✅ 图生视频完成: {result['video_url']}")
            return generation_result
            
        except Exception as e:
            print(f"❌ 图生视频失败: {e}")
            return GenerationResult(
                tool_name="img2video",
                result_path="",
                success=False,
                error_message=str(e)
            )

    async def video_keyframe(self, video_path: str, sample_fps: float = 2.0, **kwargs) -> GenerationResult:
        """视频关键帧提取工具"""
        print(f"🎬 正在提取视频关键帧...")
        print(f"   输入视频: {video_path}")
        print(f"   采样帧率: {sample_fps}fps")
        
        try:
            # 调用真实API
            result = await extract_keyframe(
                video_url=video_path,
                sample_fps=sample_fps
            )
            
            generation_result = GenerationResult(
                tool_name="video_keyframe",
                result_path=result["image_url"],
                metadata={
                    "input_video": video_path,
                    "sample_fps": sample_fps,
                    "video_caption": result.get("video_caption", ""),
                    "img_caption": result.get("img_caption", ""),
                    "frame_idx": result.get("frame_idx", 0),
                    "task_id": result["task_id"]
                },
                success=True
            )
            
            print(f"✅ 关键帧提取完成: {result['image_url']}")
            return generation_result
            
        except VideoKeyframeError as e:
            print(f"❌ 关键帧提取失败: {e}")
            return GenerationResult(
                tool_name="video_keyframe",
                result_path="",
                success=False,
                error_message=str(e)
            )
        except Exception as e:
            print(f"❌ 意外错误: {e}")
            return GenerationResult(
                tool_name="video_keyframe",
                result_path="",
                success=False,
                error_message=f"意外错误: {str(e)}"
            )

    async def language_adapter(self, text: str, history_messages: list = None, **kwargs) -> GenerationResult:
        """语言转换工具"""
        print(f"🌐 正在进行语言转换...")
        print(f"   输入文本: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        try:
            # 调用语言转换功能
            if history_messages is None:
                history_messages = []
            
            converted_text = convert_response_language(
                history_messages=history_messages,
                response_text=text
            )
            
            generation_result = GenerationResult(
                tool_name="language_adapter",
                result_path=converted_text,  # 对于文本转换，结果放在result_path中
                metadata={
                    "original_text": text,
                    "converted_text": converted_text,
                    "history_count": len(history_messages)
                },
                success=True
            )
            
            print(f"✅ 语言转换完成")
            return generation_result
            
        except Exception as e:
            print(f"❌ 语言转换失败: {e}")
            return GenerationResult(
                tool_name="language_adapter",
                result_path=text,  # 失败时返回原文
                success=False,
                error_message=str(e)
            )

# 全局工具实例
visual_tools = RealVisualTools()

# 工具信息映射
TOOL_INFO_MAP = {
    "img_gen": ToolInfo(
        name="img_gen",
        description="根据文本描述生成图像，使用OpenAI GPT-Image模型",
        parameters={
            "prompt": "图像描述提示词",
            "style": "艺术风格 (realistic, cartoon, artistic等)",
            "width": "图像宽度 (默认1024)",
            "height": "图像高度 (默认1024)"
        },
        capabilities=["文本到图像生成", "多种艺术风格", "自定义尺寸", "高质量输出", "透明背景支持"],
        limitations=["需要网络连接", "生成时间30-60秒", "依赖OpenAI API"]
    ),
    "video_gen": ToolInfo(
        name="video_gen", 
        description="根据文本描述生成视频，使用万象模型",
        parameters={
            "prompt": "视频描述提示词",
            "duration": "视频时长(秒)",
            "fps": "帧率",
            "style": "视频风格"
        },
        capabilities=["文本到视频生成", "自定义时长", "多种风格", "高质量视频"],
        limitations=["需要网络连接", "生成时间5-10分钟", "依赖万象API"]
    ),
    "img_edit": ToolInfo(
        name="img_edit",
        description="编辑现有图像，使用OpenAI GPT-Image模型",
        parameters={
            "image_path": "原图像路径",
            "edit_prompt": "编辑指令",
            "edit_type": "编辑类型"
        },
        capabilities=["局部编辑", "风格转换", "对象添加/删除", "多图合成"],
        limitations=["需要网络连接", "依赖OpenAI API", "处理时间1-3分钟"]
    ),
    "video_edit": ToolInfo(
        name="video_edit",
        description="编辑现有视频，通过关键帧提取->图像编辑->图生视频三步流程", 
        parameters={
            "video_path": "原视频路径",
            "edit_prompt": "编辑指令",
            "edit_type": "编辑类型"
        },
        capabilities=["智能关键帧选择", "场景编辑", "风格转换", "保持视频连贯性"],
        limitations=["需要网络连接", "处理时间10-15分钟", "依赖多个API服务"]
    ),
    "img2video": ToolInfo(
        name="img2video",
        description="将静态图像转换为动态视频，使用可灵模型",
        parameters={
            "image_path": "输入图像路径",
            "motion_prompt": "运动描述",
            "duration": "视频时长"
        },
        capabilities=["静态图动画化", "自定义运动效果", "智能场景理解"],
        limitations=["需要网络连接", "时长限制5-10秒", "依赖可灵API"]
    ),
    "video_keyframe": ToolInfo(
        name="video_keyframe",
        description="从视频中提取关键帧图像，用于视频编辑",
        parameters={
            "video_path": "输入视频路径",
            "sample_fps": "采样帧率(默认2.0)"
        },
        capabilities=["智能关键帧选择", "视频内容理解", "自动匹配最佳帧"],
        limitations=["需要网络连接", "依赖视觉理解模型", "处理时间1-3分钟"]
    ),
    "language_adapter": ToolInfo(
        name="language_adapter", 
        description="根据用户历史消息进行语言转换",
        parameters={
            "text": "待转换的文本",
            "history_messages": "用户历史消息列表"
        },
        capabilities=["多语言检测", "智能语种转换", "保持语调一致"],
        limitations=["需要网络连接", "依赖LLM模型", "转换准确性取决于上下文"]
    )
}

def get_available_tools() -> Dict[str, ToolInfo]:
    """获取所有可用工具信息"""
    return TOOL_INFO_MAP

async def execute_tool(tool_name: str, **kwargs) -> GenerationResult:
    """执行指定工具"""
    if tool_name not in TOOL_INFO_MAP:
        return GenerationResult(
            tool_name=tool_name,
            result_path="",
            success=False,
            error_message=f"未知工具: {tool_name}"
        )
    
    try:
        tool_method = getattr(visual_tools, tool_name)
        if asyncio.iscoroutinefunction(tool_method):
            return await tool_method(**kwargs)
        else:
            return tool_method(**kwargs)
    except Exception as e:
        return GenerationResult(
            tool_name=tool_name,
            result_path="",
            success=False,
            error_message=f"工具执行失败: {str(e)}"
        ) 