"""
LangGraph主流程
协调Chat Agent、Plan Agent、Replan Agent和结果反思Agent的工作
"""

import asyncio
import logging
import os
from typing import Dict, Any, List
from langgraph.graph import StateGraph, END
from .state import (
    AgentState, create_initial_state, PlanStatus, 
    log_agent_action, add_to_chat_history, add_generation_to_history, GenerationResult
)
from app.agents import ChatAgent, PlanAgent, ReplanAgent, ResultAnalysisAgent, SafetyAgent
from app.agents.prompt_enhance_agent import prompt_enhance_agent_node
from .tools import execute_tool
from app.config.settings import Settings
from app.utils.debug_formatter import format_state_keys, format_attachments, format_json_debug

logger = logging.getLogger(__name__)

class VisualGenerationGraph:
    """视觉生成多Agent流程图"""
    
    def __init__(self, model: str = None, concurrent_mode: bool = False, skip_safety_check: bool = False):
        self.concurrent_mode = concurrent_mode
        self.skip_safety_check = skip_safety_check
        
        # 在并发模式下使用优化配置
        self.safety_agent = SafetyAgent(model=model)
        self.chat_agent = ChatAgent(model=model, concurrent_mode=concurrent_mode)
        self.plan_agent = PlanAgent(model=model)
        
        # 控制变量：是否跳过token检查（用于离线测试）
        self.skip_token_check = False
        
        # 构建状态图
        self.graph = self._build_graph()
        
        # 生成可视化图（仅在非并发模式下生成，避免文件冲突）
        if not concurrent_mode:
            self._generate_graph_visualization()

    def _build_graph(self) -> StateGraph:
        """构建LangGraph状态图"""
        # 创建状态图
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("safety", self._safety_node)
        workflow.add_node("chat", self._chat_node)
        workflow.add_node("prompt_enhance", self._prompt_enhance_node)
        workflow.add_node("plan", self._plan_node)
        workflow.add_node("token_check", self._token_check_node)  # 新增token检查节点
        workflow.add_node("execute", self._execute_node)
        
        # 设置入口点：根据skip_safety_check决定是否跳过安全检查
        if self.skip_safety_check:
            workflow.set_entry_point("chat")
            print("🔧 [Graph] 跳过安全检查，直接进入ChatAgent")
        else:
            workflow.set_entry_point("safety")

        # 定义边的连接
        if not self.skip_safety_check:
            workflow.add_conditional_edges(
                "safety",
                self._after_safety_check,
                {
                    "continue": "chat",
                    "forbidden": END,
                }
            )
        
        workflow.add_conditional_edges(
            "chat",
            self._after_chat_analysis,
            {
                "plan": "prompt_enhance",
                "token_check": "token_check",  # 所有AI任务都要进行token检查
                "continue": END,
                "complete": END,
            }
        )
        
        # 添加prompt_enhance到plan的边
        workflow.add_edge("prompt_enhance", "plan")
        
        workflow.add_conditional_edges(
            "plan", 
            self._after_plan_generation,
            {
                "token_check": "token_check",  # 先进行token检查
                "execute": "execute",  # 直接执行（跳过token检查时）
                "complete": END
            }
        )
        
        workflow.add_conditional_edges(
            "token_check",
            self._after_token_check,
            {
                "execute": "execute",
                "complete": END
            }
        )
        
        workflow.add_edge("execute", END)
        
        # 编译图
        compiled_graph = workflow.compile(checkpointer=None)
        
        # 配置递归限制（如果支持的话）
        try:
            if hasattr(compiled_graph, 'config'):
                compiled_graph.config['recursion_limit'] = 3 
        except:
            pass
            
        return compiled_graph

    def _generate_graph_visualization(self):
        """生成并保存工作流程图 - 使用临时目录避免权限问题"""
        try:
            import tempfile
            
            # 使用临时目录避免权限问题
            with tempfile.TemporaryDirectory() as temp_dir:
                # 使用LangGraph自带的可视化功能
                graph_png = self.graph.get_graph().draw_mermaid_png()
                output_path = os.path.join(temp_dir, "visual_generation_graph.png")
                
                with open(output_path, "wb") as f:
                    f.write(graph_png)
                
                print(f"✅ 流程图已保存到临时目录: {output_path}")
            
        except Exception as e:
            print(f"⚠️ 生成流程图失败: {str(e)}")
            # 不阻断主流程

    def _chat_node(self, state: AgentState) -> AgentState:
        """Chat Agent节点"""
        state["current_agent"] = "chat"
        print(f"\n🤖 [ChatAgent] 开始处理用户输入")
        
        # 保存当前task_type（如果存在）
        original_task_type = state.get("task_type")
        
        updated_state = self.chat_agent.process(state)
        
        # 如果ChatAgent生成了final_response，标记为聊天任务
        if updated_state.get("final_response"):
            updated_state["task_type"] = "chat"
            print(f"   🔍 [ChatNode] 检测到聊天回复，强制设置task_type = 'chat'")
        elif original_task_type:
            # 恢复原有的task_type（如果ChatAgent没有设置新的）
            updated_state["task_type"] = original_task_type
            print(f"   🔍 [ChatNode] 恢复原有task_type = '{original_task_type}'")
        
        # 确保task_type被正确设置
        final_task_type = updated_state.get("task_type", "generation")
        print(f"   🔍 [ChatNode] 最终task_type = '{final_task_type}'")
        
        return updated_state

    def _prompt_enhance_node(self, state: AgentState) -> AgentState:
        """Prompt Enhance Agent节点"""
        state["current_agent"] = "prompt_enhance"
        print(f"\n🔧 [PromptEnhanceAgent] 开始提示词增强")
        # 调用异步函数的同步包装，处理事件循环问题
        import asyncio
        import concurrent.futures
        
        try:
            # 检查是否已有运行的事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 在运行的事件循环中，使用线程池执行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, prompt_enhance_agent_node(state))
                    return future.result()
            else:
                return asyncio.run(prompt_enhance_agent_node(state))
        except RuntimeError:
            # 如果无法获取事件循环，直接创建新的
            return asyncio.run(prompt_enhance_agent_node(state))

    def _plan_node(self, state: AgentState) -> AgentState:
        """Plan Agent节点"""
        state["current_agent"] = "plan"
        print(f"\n📋 [PlanAgent] 开始制定执行计划")
        return self.plan_agent.process(state)

    def _after_chat_analysis(self, state: AgentState) -> str:
        """ChatAgent分析后的路由判断"""
        is_intent_clear = state.get("is_intent_clear", False)
        final_response = state.get("final_response")
        
        print(f"\n🔀 [Router] ChatAgent分析完成:")
        print(f"   - 意图是否明确: {is_intent_clear}")
        print(f"   - 用户意图: {state.get('user_intent', 'N/A')}")
        print(f"   - 是否有最终回复: {bool(final_response)}")
        print(format_state_keys(list(state.keys())))
        if 'final_response' in state:
            print(f"   - state['final_response']: {type(state['final_response'])}, 值: {repr(state['final_response'][:100] if state['final_response'] else None)}")
        if final_response:
            print(f"   - 最终回复长度: {len(final_response)} 字符")
            print(f"   - 最终回复前50字符: {final_response[:50]}...")
        
        if is_intent_clear:
            print(f"   ➡️  路由到PromptEnhanceAgent")
            return "plan"
        elif final_response:
            # ChatAgent生成了聊天回复，需要进行Token检查和扣除
            print(f"   ➡️  聊天回复已生成，需要先检查Token余额")
            # 注意：路由函数不能修改状态，task_type将在token_check_node中设置
            return "token_check"
        else:
            print(f"   ➡️  继续ChatAgent对话")
            return "continue"

    def _after_plan_generation(self, state: AgentState) -> str:
        """PlanAgent处理后的路由判断: 要么token检查，要么直接执行，要么结束"""
        plan_status = state.get("plan_status", PlanStatus.PENDING)
        execution_plan = state.get("execution_plan")
        
        print(f"\n🔀 [Router] PlanAgent处理完成:")
        print(f"   - 计划状态: {plan_status}")
        print(f"   - 跳过token检查: {self.skip_token_check}")
        
        # 如果计划成功且有工具调用
        if plan_status == PlanStatus.PENDING and execution_plan and execution_plan.tool_calls:
            if self.skip_token_check:
                print(f"   ➡️  跳过token检查，直接执行工具 ({len(execution_plan.tool_calls)}个工具)")
                return "execute"
            else:
                print(f"   ➡️  计划制定成功，进行token检查 ({len(execution_plan.tool_calls)}个工具)")
                return "token_check"
        
        # 处理其他所有情况都直接结束
        print(f"   ❌ 计划制定失败或无需工具执行，状态: {plan_status}")
        return "complete"
        
    def _token_check_node(self, state: AgentState) -> AgentState:
        """Token检查节点 - 统一检查logic，根据execution_plan自动判断"""
        state["current_agent"] = "token_check"
        print(f"\n💰 [TokenCheck] 开始检查token余额")
        
        user = state.get("user")
        execution_plan = state.get("execution_plan")
        
        print(f"   🔍 [Token Check Debug] state.keys(): {list(state.keys())}")
        print(f"   🔍 [Token Check Debug] user from state: {user}")
        print(f"   🔍 [Token Check Debug] user type: {type(user)}")
        print(f"   🔍 [Token Check Debug] execution_plan存在: {execution_plan is not None}")
        if execution_plan:
            tool_calls = getattr(execution_plan, 'tool_calls', None)
            print(f"   🔍 [Token Check Debug] tool_calls存在: {tool_calls is not None}")
            if tool_calls:
                print(f"   🔍 [Token Check Debug] tool_calls数量: {len(tool_calls)}")
            print(f"   🔍 [Token Check Debug] execution_plan类型: {type(execution_plan)}")
        print(f"   🔍 [Token Check Debug] 最终判断有执行计划: {bool(execution_plan and getattr(execution_plan, 'tool_calls', None))}")
        
        # 检查是否跳过工具执行（测试模式）
        if state.get("skip_tool_execution"):
            print("   ⏭️  检测到 'skip_tool_execution'，跳过余额检查")
            state["token_check_result"] = "skip"
            return state
        
        if not user:
            print("   ⚠️  无法获取用户信息，跳过余额检查")
            state["token_check_result"] = "no_user"
            return state
        
        from core.services.token_service import TokenService
        print(f"   🔍 检查用户token状态: {user.email}")
        
        # 1. 首先检查token是否过期
        if user.is_tokens_expired:
            print(f"   ⏰ Token已过期: {user.tokens_expires_at}, 清空余额")
            # 自动清空过期token
            user.tokens = 0
            user.save()
            
            # 设置过期状态
            state["last_error"] = f"Tokens expired on {user.tokens_expires_at}"
            state["plan_status"] = PlanStatus.REJECTED
            state["plan_failure_reason"] = "tokens_expired"
            state["plan_failure_details"] = {
                "expiry_date": user.tokens_expires_at.isoformat() if user.tokens_expires_at else None,
                "current_balance": 0
            }
            state["is_complete"] = True
            
            # 生成token过期的专门回复
            expired_response = self._generate_token_expired_response(user)
            state["final_response"] = expired_response
            state["response_type"] = "tokens_expired"
            state["token_check_result"] = "expired"
            
            print(f"   ➡️  Token已过期，设置错误状态")
            return state
        
        # 2. 统一的Token检查逻辑：根据是否有执行计划自动判断
        print(f"   💰 检查用户余额: {user.email} (当前: {user.tokens} tokens)")
        
        # 修正逻辑：检查执行计划和工具调用
        has_valid_plan = execution_plan and getattr(execution_plan, 'tool_calls', None) and len(getattr(execution_plan, 'tool_calls', [])) > 0
        print(f"   🔍 [Token Check Debug] 有效执行计划判断: {has_valid_plan}")
        
        if has_valid_plan:
            # 有执行计划：使用计划检查（生成任务）
            is_sufficient, required_tokens, error_msg = TokenService.check_plan_balance(user, execution_plan)
            print(f"   🎨 生成任务Token检查: 需要 {required_tokens} tokens")
            state["task_type"] = "generation"  # 设置类型仅用于后续路由
            print(f"   🔍 [Token Check Debug] 设置task_type为: generation")
        else:
            # 无执行计划：使用聊天检查（对话任务）
            is_sufficient, required_tokens, error_msg = TokenService.check_balance(user, "chat_api")
            print(f"   💬 对话任务Token检查: 需要 {required_tokens} tokens")
            state["task_type"] = "chat"  # 设置类型仅用于后续路由
            print(f"   🔍 [Token Check Debug] 设置task_type为: chat")
        
        if not is_sufficient:
            print(f"   ❌ 余额不足: {error_msg}")
            # 设置错误状态
            state["last_error"] = error_msg
            state["plan_status"] = PlanStatus.REJECTED
            state["plan_failure_reason"] = "insufficient_balance"
            state["plan_failure_details"] = {
                "required_tokens": required_tokens,
                "current_balance": user.tokens,
                "deficit": required_tokens - user.tokens
            }
            state["is_complete"] = True
            
            # 生成余额不足的专门回复
            balance_error_response = self._generate_balance_insufficient_response(
                required_tokens, user.tokens, required_tokens - user.tokens
            )
            state["final_response"] = balance_error_response
            state["response_type"] = "balance_insufficient"
            state["token_check_result"] = "insufficient"
            
            print(f"   🔍 [Debug] 设置state值:")
            print(f"   🔍 [Debug] response_type: {state.get('response_type')}")
            print(f"   🔍 [Debug] final_response长度: {len(state.get('final_response', ''))}")
            print(f"   🔍 [Debug] plan_failure_details: {state.get('plan_failure_details')}")
            print(f"   🔍 [Debug] token_check_result: {state.get('token_check_result')}")
            print(f"   ➡️  余额不足，设置错误状态")
            
            # 直接修改原始state对象，确保LangGraph能正确传递状态
            print(f"   🔍 [Debug] 最终设置token_check_result为: insufficient")
            return state
        else:
            print(f"   ✅ 余额充足: 需要 {required_tokens} tokens, 当前 {user.tokens} tokens")
            state["token_check_result"] = "sufficient"
            print(f"   🔍 [Debug] 最终设置token_check_result为: sufficient")
            return state

    def _after_token_check(self, state: AgentState) -> str:
        """Token检查后的路由判断 - 简化版本"""
        token_check_result = state.get("token_check_result", "unknown")
        task_type = state.get("task_type", "chat")  # 默认为chat
        execution_plan = state.get("execution_plan")
        
        print(f"\n🔀 [Router] Token检查完成:")
        print(f"   - 检查结果: {token_check_result}")
        print(f"   - 任务类型: {task_type}")
        print(f"   - 执行计划存在: {execution_plan is not None}")
        if execution_plan:
            tool_calls = getattr(execution_plan, 'tool_calls', None)
            print(f"   - 工具调用存在: {tool_calls is not None}")
            if tool_calls:
                print(f"   - 工具调用数量: {len(tool_calls)}")
        print(f"   - state中的所有键: {list(state.keys())}")
        
        # 修正逻辑：重新验证任务类型，以执行计划为准
        has_valid_plan = execution_plan and getattr(execution_plan, 'tool_calls', None) and len(getattr(execution_plan, 'tool_calls', [])) > 0
        actual_task_type = "generation" if has_valid_plan else "chat"
        print(f"   🔍 [Router Debug] 重新验证任务类型: {actual_task_type} (基于执行计划: {has_valid_plan})")
        
        if actual_task_type != task_type:
            print(f"   ⚠️ [Router Debug] 任务类型不匹配! state中的: {task_type}, 实际应为: {actual_task_type}")
            task_type = actual_task_type  # 使用重新验证的任务类型
        
        if token_check_result in ["insufficient", "expired"]:
            print(f"   ➡️  Token不足或过期，流程结束")
            return "complete"
        elif token_check_result in ["sufficient", "skip", "no_user"]:
            if task_type == "chat":
                # 对话任务：Token检查通过后直接完成
                print(f"   ➡️  对话任务Token检查通过，标记完成")
                state["is_complete"] = True
                state["response_type"] = "non_visual_task"
                return "complete"
            else:
                # 生成任务：继续执行工具
                print(f"   ➡️  生成任务Token检查通过，继续执行工具")
                return "execute"
        else:
            print(f"   ⚠️  未知的检查结果，默认结束流程")
            return "complete"
    
    def _generate_non_visual_task_response(self, state: AgentState) -> str:
        """使用LLM为非视觉生成任务生成专门的英语回复"""
        
        try:
            from app.core.llm_client import LLMClient
            llm_client = LLMClient()
            
            # 获取用户原始输入
            user_input = state.get("user_input", "")
            
            # 从配置文件加载system prompt
            system_prompt = self._load_prompt_from_file("non_visual_task_system.md")
            
            # 从配置文件加载用户prompt模板
            user_prompt_template = self._load_prompt_from_file("non_visual_task_user.md")
            user_prompt = user_prompt_template.format(user_input=user_input)

            # 调用LLM生成回复
            response = llm_client.get_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=300,
                temperature=0.8
            )
            
            if response and response.strip():
                print(f"   🤖 [LLM] 成功生成非视觉任务回复 ({len(response)} 字符)")
                return response.strip()
            else:
                print(f"   ⚠️ [LLM] 生成失败，使用备用回复")
                return self._generate_fallback_non_visual_response()
                
        except Exception as e:
            print(f"   ❌ [LLM] 生成非视觉任务回复时出错: {e}")
            return self._generate_fallback_non_visual_response()

    def _generate_fallback_non_visual_response(self) -> str:
        """备用的非视觉任务回复（当LLM不可用时）"""
        return self._load_prompt_from_file("fallback_non_visual_response.md")

    def _generate_plan_failure_response(self, plan_status: PlanStatus, failure_reason: str, failure_details: Dict[str, Any]) -> str:
        """使用LLM为计划失败生成详细的英语回复"""
        
        try:
            from app.core.llm_client import LLMClient
            llm_client = LLMClient()
            
            # 从配置文件加载system prompt
            system_prompt = self._load_prompt_from_file("plan_failure_system.md")
            
            # 从配置文件加载用户prompt模板
            user_prompt_template = self._load_prompt_from_file("plan_failure_user.md")
            user_prompt = user_prompt_template.format(
                plan_status=plan_status.value,
                failure_reason=failure_reason,
                failure_details=failure_details
            )

            # 调用LLM生成回复
            response = llm_client.get_completion(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=500,
                temperature=0.7
            )
            
            if response and response.strip():
                print(f"   🤖 [LLM] 成功生成计划失败说明 ({len(response)} 字符)")
                return response.strip()
            else:
                print(f"   ⚠️ [LLM] 生成失败，使用备用回复")
                return self._generate_fallback_plan_failure_response(plan_status, failure_reason, failure_details)
                
        except Exception as e:
            print(f"   ❌ [LLM] 生成计划失败说明时出错: {e}")
            return self._generate_fallback_plan_failure_response(plan_status, failure_reason, failure_details)

    def _load_prompt_from_file(self, filename: str) -> str:
        """从配置文件加载prompt"""
        prompt_file_path = os.path.join("app", "config", "prompts", filename)
        
        try:
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    raise ValueError(f"Prompt文件 {filename} 为空")
                return content
        except FileNotFoundError:
            raise FileNotFoundError(f"缺少必需的prompt配置文件: {prompt_file_path}")
        except Exception as e:
            raise Exception(f"加载prompt文件 {filename} 失败: {e}")
    


    def _generate_fallback_plan_failure_response(self, plan_status: PlanStatus, failure_reason: str, failure_details: Dict[str, Any]) -> str:
        """Generate fallback plan failure response when file loading fails"""
        if plan_status == PlanStatus.NEEDS_MORE_INFO:
            return "Sorry, I need more information to help you generate content. Please provide more detailed descriptions or upload relevant image/video materials."
        elif plan_status == PlanStatus.FAILED:
            if "unsupported_format" in failure_reason.lower():
                return "Sorry, the file format you uploaded is not supported. Please upload common image formats (JPG, PNG) or video formats (MP4, MOV)."
            elif "file_too_large" in failure_reason.lower():
                return "Sorry, the file you uploaded is too large. Please upload images smaller than 10MB or videos smaller than 50MB."
            elif "invalid_content" in failure_reason.lower():
                return "Sorry, inappropriate content detected. Please ensure your request complies with our content policy."
            else:
                return f"Sorry, there was a problem generating the plan: {failure_reason}. Please rephrase your request or try again later."
        elif plan_status == PlanStatus.NO_VISUAL_TASK:
            return "Based on your description, this doesn't seem to be a task that requires image or video generation. I'm an AI assistant specialized in visual content creation.\n\nIf you need to generate images or videos, please describe specific visual requirements, such as:\n• Generate an image: describe scene content, style, etc.\n• Create a video: describe actions, scenes, effects, etc.\n• Edit an image: upload an image and explain modification requirements\n• Image to video: upload an image and describe desired animation effects"
        else:
            return "Sorry, I'm unable to process your request at the moment. Please rephrase your request or try again later."

    def _generate_balance_insufficient_response(self, required_tokens: int, current_balance: int, deficit: int) -> str:
        """Generate insufficient balance response in English"""
        return f"""💳 **Insufficient Token Balance**

Your current token balance is **{current_balance}**, but this operation requires **{required_tokens}** tokens. You need **{deficit}** more tokens.

💡 **Solutions:**
• Purchase more tokens immediately
• Choose operations that consume fewer tokens
• View your usage history and balance details

🔋 **How to Purchase:**
Click on your profile avatar (top right) → Billing Center → Choose a suitable plan

⚡ **Token Costs:**
Different operations consume different amounts of tokens:
• Text to Image: 400 tokens
• Image Editing: 400 tokens  
• Text to Video: 800 tokens
• Image to Video: 700 tokens
• Multi-image Video: 1100 tokens

Thank you for your understanding! You can continue using all features after purchasing more tokens."""

    def _generate_token_expired_response(self, user) -> str:
        """Generate token expired response in English"""
        expiry_date = user.tokens_expires_at.strftime('%Y-%m-%d %H:%M:%S') if user.tokens_expires_at else "N/A"
        
        return f"""⏰ **Your Tokens Have Expired**

Your tokens expired on **{expiry_date}** and have been automatically cleared from your account.

**Current Status:**
• Token balance: 0 (expired tokens cleared)
• Account status: Active, but needs token renewal

💡 **What You Can Do:**
• **Purchase new tokens** - Get fresh tokens with a new expiry date
• **Subscribe to a plan** - Enjoy regular token renewals and better value
• **Contact support** - If you believe this is an error

🔋 **How to Purchase:**
Click on your profile avatar (top right) → Billing Center → Choose a suitable plan

⚡ **Token Plans Available:**
All our plans come with fresh tokens and extended validity periods. Subscription plans offer the best value with automatic renewals.

Thank you for your understanding! Once you purchase new tokens, you can immediately resume using all features."""

    def _safety_node(self, state: AgentState) -> AgentState:
        """Safety Agent节点"""
        state["current_agent"] = "safety"
        
        if self.skip_safety_check:
            print(f"\n🛡️  [SafetyAgent] 跳过安全审查模式")
            # 直接设置为安全状态
            state["safety_status"] = "safe"
            state["safety_message"] = "安全检查已跳过"
            return state
        else:
            print(f"\n🛡️  [SafetyAgent] 开始安全审查")
            return self.safety_agent.process(state)

    def _after_safety_check(self, state: AgentState) -> str:
        """安全审查后的路由判断"""
        safety_status = state.get("safety_status", "safe")
        print(f"\n🔀 [Router] SafetyAgent审查完成:")
        print(f"   - 安全状态: {safety_status}")
        
        if safety_status == "forbidden":
            print("   ➡️  内容违规，流程终止")
            state["is_complete"] = True
            state["last_error"] = state.get("safety_message", "内容安全策略禁止")
            return "forbidden"
        else:
            print("   ➡️  内容安全，继续到ChatAgent")
            return "continue"

    def _replan_node(self, state: AgentState) -> AgentState:
        """Replan Agent节点"""
        state["current_agent"] = "replan"
        return self.replan_agent.process(state)

    def _execute_node(self, state: AgentState) -> AgentState:
        """工具执行节点 - 异步调用包装器"""
        try:
            # 检查当前是否在异步上下文中
            import asyncio
            try:
                loop = asyncio.get_running_loop()
                # 如果在异步上下文中，使用线程池执行异步函数
                import concurrent.futures
                import threading
                
                def run_async_in_thread():
                    # 在新线程中创建新的事件循环
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self._execute_node_async(state))
                    finally:
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_async_in_thread)
                    return future.result()
                    
            except RuntimeError:
                # 没有运行中的事件循环，直接运行
                return asyncio.run(self._execute_node_async(state))
                
        except Exception as e:
            logger.error(f"Execute node wrapper failed: {e}")
            # 返回错误状态
            state["generated_results"] = []
            state["last_error"] = f"Execute node failed: {str(e)}"
            return state

    async def _execute_node_async(self, state: AgentState) -> AgentState:
        """工具执行节点"""
        state["current_agent"] = "execute"
        log_agent_action(state, "ExecuteNode", "开始执行工具调用")
        
        # 检查是否跳过工具执行
        skip_execution = state.get("skip_tool_execution", False)
        print(f"\n🔍 Debug: skip_tool_execution = {skip_execution}")
        if skip_execution:
            log_agent_action(state, "ExecuteNode", "跳过工具执行模式 - 仅模拟执行")
            print(f"\n⚠️  跳过工具执行模式")
            if state.get("execution_plan"):
                print(f"   📋 计划: {state['execution_plan'].description}")
                print(f"   🔧 工具调用数量: {len(state['execution_plan'].tool_calls)}")
                # 创建模拟结果
                mock_results = []
                for i, tool_call in enumerate(state["execution_plan"].tool_calls, 1):
                    mock_result = GenerationResult(
                        tool_name=tool_call.tool_name,
                        result_path=f"mock_output_{i}.png",  # 模拟输出路径
                        success=True,
                        error_message=""
                    )
                    mock_results.append(mock_result)
                    print(f"   ✅ 模拟执行 {i}: {tool_call.tool_name} -> {mock_result.result_path}")
                
                state["generated_results"] = mock_results
                print(f"\n✅ 模拟执行完成: {len(mock_results)} 个任务")
            else:
                state["generated_results"] = []
            return state
        
        if not state.get("execution_plan"):
            log_agent_action(state, "ExecuteNode", "没有执行计划")
            return state
        
        execution_plan = state["execution_plan"]
        results = []
        
        print(f"\n🚀 开始执行生成任务...")
        print(f"   计划: {execution_plan.description}")
        print(f"   预估时间: {execution_plan.estimated_time:.1f}秒")
        
        # 按顺序执行工具调用
        step_outputs = {}  # 存储每步的输出URL，用于步骤间依赖
        
        for i, tool_call in enumerate(execution_plan.tool_calls, 1):
            print(f"\n   执行步骤 {i}/{len(execution_plan.tool_calls)}: {tool_call.description}")
            
            current_parameters = tool_call.parameters.copy()
            
            # 🎯 简化的placeholder替换逻辑
            
            # 1. 替换步骤间依赖 (支持多种格式)
            for key, value in current_parameters.items():
                if isinstance(value, str) and value.startswith("{{") and value.endswith("}}"):
                    # 支持多种步骤依赖格式
                    if ("step_" in value and "_output" in value) or ("_from_step_" in value):
                        # 查找匹配的步骤输出
                        found_replacement = None
                        for step_key, step_value in step_outputs.items():
                            if value == step_key:
                                found_replacement = step_value
                                break
                        
                        if found_replacement:
                            current_parameters[key] = found_replacement
                            print(f"   🔗 替换步骤依赖: {key} = {value} -> {current_parameters[key][:50]}...")
                        else:
                            # 尝试智能匹配：从步骤编号中推断
                            import re
                            step_match = re.search(r'step_(\d+)', value)
                            if step_match:
                                step_num = step_match.group(1)
                                fallback_key = f"{{{{step_{step_num}_output}}}}"
                                if fallback_key in step_outputs:
                                    current_parameters[key] = step_outputs[fallback_key]
                                    print(f"   🔗 智能匹配步骤依赖: {key} = {value} -> {current_parameters[key][:50]}...")
                                else:
                                    print(f"   ⚠️ 步骤依赖未找到: {value}")
                            else:
                                print(f"   ⚠️ 步骤依赖未找到: {value}")
            
            # 2. 处理列表参数中的步骤依赖
            for key, value in current_parameters.items():
                if isinstance(value, list):
                    for item_idx, item in enumerate(value):
                        if isinstance(item, str) and item.startswith("{{") and item.endswith("}}"):
                            if ("step_" in item and "_output" in item) or ("_from_step_" in item):
                                # 查找匹配的步骤输出
                                found_replacement = None
                                for step_key, step_value in step_outputs.items():
                                    if item == step_key:
                                        found_replacement = step_value
                                        break
                                
                                if found_replacement:
                                    current_parameters[key][item_idx] = found_replacement
                                    print(f"   🔗 替换列表项步骤依赖: {key}[{item_idx}] = {item} -> {current_parameters[key][item_idx][:50]}...")
                                else:
                                    # 尝试智能匹配
                                    import re
                                    step_match = re.search(r'step_(\d+)', item)
                                    if step_match:
                                        step_num = step_match.group(1)
                                        fallback_key = f"{{{{step_{step_num}_output}}}}"
                                        if fallback_key in step_outputs:
                                            current_parameters[key][item_idx] = step_outputs[fallback_key]
                                            print(f"   🔗 智能匹配列表项步骤依赖: {key}[{item_idx}] = {item} -> {current_parameters[key][item_idx][:50]}...")
                                        else:
                                            print(f"   ⚠️ 列表项步骤依赖未找到: {item}")
                                    else:
                                        print(f"   ⚠️ 列表项步骤依赖未找到: {item}")
            
            # 注意：历史媒体的URL已经在PlanAgent阶段被恢复为真实URL，这里不需要额外处理
            
            try:
                # 动态修复img_edit的参数名
                if tool_call.tool_name == "img_edit":
                    print("   🔧 适配img_edit参数...")
                    
                    # 处理图片路径参数 - 优先级: reference_images > source_image
                    if "reference_images" in current_parameters:
                        current_parameters["image_path"] = current_parameters.pop("reference_images")
                        print(f"      - 'reference_images' 重命名为 'image_path' 以支持多图输入")
                    elif "source_image" in current_parameters:
                        current_parameters["image_path"] = current_parameters.pop("source_image")
                        print(f"      - 'source_image' 重命名为 'image_path'")
                    
                    # 移除多余的source_image参数（如果image_path已存在）
                    if "image_path" in current_parameters and "source_image" in current_parameters:
                        removed_source = current_parameters.pop("source_image")
                        print(f"      - 移除多余的 'source_image' 参数: {removed_source}")

                    # 统一将prompt重命名为edit_prompt
                    if "prompt" in current_parameters and "edit_prompt" not in current_parameters:
                         current_parameters["edit_prompt"] = current_parameters.pop("prompt")
                         print(f"      - 'prompt' 重命名为 'edit_prompt'")

                # 动态修复img2video的参数名
                if tool_call.tool_name == "img2video":
                    print("   🔧 适配img2video参数...")
                    if "source_image" in current_parameters:
                        current_parameters["image_path"] = current_parameters.pop("source_image")
                        print(f"      - 'source_image' 重命名为 'image_path'")
                    if "prompt" in current_parameters:
                        current_parameters["motion_prompt"] = current_parameters.pop("prompt")
                        print(f"      - 'prompt' 重命名为 'motion_prompt'")
                
                # 动态修复video_keyframe的参数名
                if tool_call.tool_name == "video_keyframe":
                    print("   🔧 适配video_keyframe参数...")
                    if "source_video" in current_parameters:
                        current_parameters["video_path"] = current_parameters.pop("source_video")
                        print(f"      - 'source_video' 重命名为 'video_path'")
                    # frame_count 参数重命名为 sample_fps (如果存在)
                    if "frame_count" in current_parameters:
                        # frame_count 转换为 sample_fps 的简单逻辑：假设每秒取 frame_count/总时长 帧
                        # 这里我们使用一个合理的默认值，因为没有视频时长信息
                        frame_count = current_parameters.pop("frame_count")
                        current_parameters["sample_fps"] = 2.0  # 使用默认值，忽略frame_count
                        print(f"      - 'frame_count' ({frame_count}) 转换为 'sample_fps' (2.0)")
                
                # 异步执行工具
                print(f"   🔍 Debug: 实际调用工具 '{tool_call.tool_name}' 使用参数: {list(current_parameters.keys())}")
                result = await execute_tool(tool_call.tool_name, **current_parameters)
                results.append(result)
                
                # 保存成功结果的输出URL，用于后续步骤
                if result.success:
                    # 统一使用 {{step_i_output}} 格式
                    output_placeholder = f"{{{{step_{i}_output}}}}"
                    step_outputs[output_placeholder] = result.result_path
                    print(f"   📝 保存步骤输出: {output_placeholder} = {result.result_path}")
                    log_agent_action(state, "ExecuteNode", f"工具{tool_call.tool_name}执行成功")
                else:
                    log_agent_action(state, "ExecuteNode", f"工具{tool_call.tool_name}执行失败: {result.error_message}")
                
            except Exception as e:
                log_agent_action(state, "ExecuteNode", f"工具执行异常: {str(e)}")
                result = GenerationResult(
                    tool_name=tool_call.tool_name,
                    result_path="",
                    success=False,
                    error_message=str(e)
                )
                results.append(result)
        
        state["generated_results"] = results
        
        # 统计执行结果
        successful = sum(1 for r in results if r.success)
        total = len(results)
        print(f"\n✅ 执行完成: {successful}/{total} 个任务成功")
        
        # 将成功的结果添加到生成历史
        if execution_plan and successful > 0:
            # 找到第一个成功的结果
            successful_result = next((r for r in results if r.success), None)
            if successful_result:
                add_generation_to_history(
                    state=state,
                    user_input=state.get("user_input", ""),
                    description=execution_plan.description,
                    result_path=successful_result.result_path,
                    task_type=execution_plan.task_type,
                    success=True
                )
                print(f"📝 已添加到生成历史: 第{state['current_round']}轮 - {execution_plan.description}")
        
        return state

    def run(self, user_input: str) -> Dict[str, Any]:
        """运行完整的视觉生成流程"""
        # 创建初始状态
        initial_state = create_initial_state()
        initial_state["user_input"] = user_input
        
        print(f"\n🎯 开始处理用户请求: {user_input}")
        
        try:
            # 运行状态图
            final_state = self.graph.invoke(initial_state)
            
            # 返回结果摘要
            return self._create_result_summary(final_state)
            
        except Exception as e:
            print(f"\n❌ 流程执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "state": initial_state
            }

    def run_with_state(self, state: AgentState) -> Dict[str, Any]:
        """使用现有状态运行流程"""
        try:
            # 运行状态图
            final_state = self.graph.invoke(state)
            
            # 返回结果摘要
            result = self._create_result_summary(final_state)
            result["state"] = final_state
            return result
            
        except Exception as e:
            print(f"\n❌ 流程执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "state": state
            }

    def run_with_conversation(self, conversation: List[Dict], skip_tool_execution: bool = False, skip_safety_check: bool = False) -> Dict[str, Any]:
        """使用现有对话历史运行图（同步版本）"""
        initial_state = create_initial_state(conversation, skip_tool_execution, skip_safety_check)
        
        print(f"🔍 [Graph Debug] Skip Execution Flag: {skip_tool_execution}")
        print(f"🔍 [Graph Debug] Skip Safety Check Flag: {skip_safety_check}")

        # 运行图
        final_state = self.graph.invoke(initial_state)
        
        # 返回结果摘要
        result = self._create_result_summary(final_state)
        result["state"] = final_state
        return result

    async def arun_with_conversation(self, conversation: List[Dict], skip_tool_execution: bool = False, skip_safety_check: bool = False, user_data: Dict = None) -> Dict[str, Any]:
        """
        使用现有对话历史运行图（异步版本）
        🎯 主要调用入口：由Celery任务 execute_langchain_workflow 调用
        """
        initial_state = create_initial_state(conversation, skip_tool_execution, skip_safety_check)
        
        # 添加用户数据到state中，直接设置user字段
        if user_data and user_data.get("user"):
            initial_state["user"] = user_data["user"]
            user = user_data["user"]
            if hasattr(user, 'email') and user.email:
                print(f"🔍 [Graph Debug] User: {user.email}")
            elif hasattr(user, 'username') and user.username:
                print(f"🔍 [Graph Debug] User: {user.username}")
            else:
                print(f"🔍 [Graph Debug] User: {type(user).__name__}")
        else:
            print(f"🔍 [Graph Debug] User: Anonymous")
        
        print(f"🔍 [Graph Debug] Skip Execution Flag: {skip_tool_execution}")
        print(f"🔍 [Graph Debug] Skip Safety Check Flag: {skip_safety_check}")

        # 异步运行图
        final_state = await self.graph.ainvoke(initial_state)
        
        # 返回结果摘要
        result = self._create_result_summary(final_state)
        result["state"] = final_state
        return result

    def _create_result_summary(self, final_state: AgentState) -> Dict[str, Any]:
        """创建结果摘要"""
        # 检查是否有错误
        has_error = final_state.get("last_error") is not None
        
        # 检查生成结果
        generated_results = final_state.get("generated_results", [])
        successful_results = [r for r in generated_results if r.success] if generated_results else []
        
        # 确定整体成功状态
        # 1. 如果有错误，则失败
        # 2. 如果有生成任务但没有成功的结果，则失败
        # 3. 如果是非视觉任务（只有对话），则成功引用媒体抽取
        overall_success = True
        error_message = None
        
        if has_error:
            overall_success = False
            error_message = final_state.get("last_error")
        elif generated_results and len(successful_results) == 0:
            # 有生成任务但都失败了
            overall_success = False
            # 从失败的结果中获取错误信息
            failed_results = [r for r in generated_results if not r.success]
            if failed_results:
                error_message = failed_results[0].error_message
            else:
                error_message = "Generation failed"
        
        summary = {
            "success": overall_success,
            "is_complete": final_state.get("is_complete", False),
            "user_intent": final_state.get("user_intent"),
            "execution_plan": final_state.get("execution_plan"),
            "generated_results": generated_results,
            "chat_history": final_state.get("chat_history", []),
            "agent_logs": final_state.get("agent_logs", [])
        }
        
        # 添加错误信息
        if error_message:
            summary["error"] = error_message
        
        # 处理专门回复（包括非视觉任务和token错误）
        response_type = final_state.get("response_type")
        
        print(f"🔍 [Summary Debug] response_type from final_state: {response_type}")
        print(f"🔍 [Summary Debug] final_response存在: {bool(final_state.get('final_response'))}")
        print(f"🔍 [Summary Debug] plan_failure_details: {final_state.get('plan_failure_details')}")
        
        # 优先处理token相关错误，即使没有final_response
        if response_type in ["balance_insufficient", "tokens_expired"]:
            summary["response_type"] = response_type
            
            # 确保有最终回复
            if final_state.get("final_response"):
                summary["final_response"] = final_state["final_response"]
            else:
                # 如果没有最终回复，生成一个简单的提示
                if response_type == "balance_insufficient":
                    plan_failure_details = final_state.get("plan_failure_details", {})
                    required = plan_failure_details.get("required_tokens", 0)
                    current = plan_failure_details.get("current_balance", 0)
                    summary["final_response"] = f"Insufficient token balance. Required {required} tokens, current balance {current} tokens."
                else:
                    summary["final_response"] = "Your tokens have expired. Please purchase new tokens to continue."
            
            # 添加token相关详细信息
            plan_failure_details = final_state.get("plan_failure_details", {})
            if plan_failure_details:
                summary["tokens_required"] = plan_failure_details.get("required_tokens", 0)
                summary["current_balance"] = plan_failure_details.get("current_balance", 0)
                summary["deficit"] = plan_failure_details.get("deficit", 0)
                if "expiry_date" in plan_failure_details:
                    summary["expiry_date"] = plan_failure_details["expiry_date"]
                    
        elif final_state.get("final_response"):
            summary["final_response"] = final_state["final_response"]
            summary["response_type"] = "non_visual_task"
        
        # 统计结果
        if generated_results:
            summary["total_results"] = len(generated_results)
            summary["successful_results"] = len(successful_results)
            summary["success_rate"] = len(successful_results) / len(generated_results)
        else:
            summary["total_results"] = 0
            summary["successful_results"] = 0
            summary["success_rate"] = 0.0
        
        return summary


def create_graph(model: str = None, skip_safety_check: bool = False) -> StateGraph:
    """创建并返回编译好的图实例"""
    visual_graph = VisualGenerationGraph(model=model, skip_safety_check=skip_safety_check)
    return visual_graph.graph 

def create_initial_state(conversation: List[Dict], skip_tool_execution: bool = False, skip_safety_check: bool = False) -> AgentState:
    """根据对话历史创建初始状态 - 增强版本支持前端引用媒体"""
    
    # 确保 conversation 是一个列表
    if not isinstance(conversation, list):
        raise ValueError("conversation must be a list")

    latest_user_message = next((msg for msg in reversed(conversation) if msg["role"] == "user"), None)
    if not latest_user_message:
        latest_user_message = {
            "role": "user",
            "content": "",
            "timestamp": "0"
        }
    
    # 增强版媒体处理：支持新的media字段和旧的image_url/video_url字段
    all_media = []
    current_media = []
    historical_media = []
    
    # 🎯 新增：为PlanAgent构造图文交织的chat_history
    enhanced_chat_history = []
    
    if conversation:
        for msg in conversation:
            msg_media = []
            
            # 处理新格式的media字段
            if msg.get("media") and isinstance(msg["media"], list):
                for media in msg["media"]:
                    # 验证media格式
                    if not isinstance(media, dict) or "type" not in media or "url" not in media:
                        continue
                    
                    # 设置默认值
                    media_info = {
                        "type": media["type"],
                        "url": media["url"],
                        "source": media.get("source", "user"),
                        "role": msg["role"],
                        "content": msg.get("content", ""),
                        "description": media.get("description", f"{media['type']} from {msg['role']}")
                    }
                    
                    # 如果有reference_id，保留它
                    if "reference_id" in media:
                        media_info["reference_id"] = media["reference_id"]
                    
                    msg_media.append(media_info)
                    all_media.append(media_info)
                    
                    # 分类媒体：最新用户消息的媒体归类为current，其他为historical
                    if msg["role"] == "user" and msg == latest_user_message:
                        current_media.append(media_info)
                    else:
                        historical_media.append(media_info)
            
            # 🎯 构造图文交织的enhanced_chat_history消息
            enhanced_msg = {
                "role": msg["role"],
                "content": msg.get("content", ""),
                "timestamp": msg.get("timestamp", ""),
                "media": msg_media  # 将媒体重新嵌入到对应的消息中
            }
            enhanced_chat_history.append(enhanced_msg)

    # 统计和日志输出
    total_media = len(all_media)
    current_media_count = len(current_media)
    historical_media_count = len(historical_media)
    
    # 按类型统计
    image_count = len([m for m in all_media if m["type"] == "image"])
    video_count = len([m for m in all_media if m["type"] == "video"])
    
    # 按来源统计
    upload_count = len([m for m in current_media if m["source"] == "upload"])
    reference_count = len([m for m in current_media if m["source"] == "reference"])
    
    if total_media > 0:
        print(f"🔍 [Graph Debug] 媒体处理完成:")
        print(f"   总媒体数: {total_media} (图片: {image_count}, 视频: {video_count})")
        print(f"   当前消息: {current_media_count} (上传: {upload_count}, 引用: {reference_count})")
        print(f"   历史媒体: {historical_media_count}")
        
        # 详细列出当前消息的媒体
        if current_media:
            print(f"🎯 [Graph Debug] 当前消息包含的媒体:")
            for i, media in enumerate(current_media):
                source_info = f"({media['source']})"
                if media.get('reference_id'):
                    source_info += f" @{media['reference_id']}"
                if media.get('display_name'):
                    source_info += f" - {media['display_name']}"
                print(f"   {i+1}. {media['type']}: {media['url'][:50]}... {source_info}")
                
        # 详细列出引用的历史媒体
        referenced_historical = [m for m in current_media if m['source'] == 'reference']
        if referenced_historical:
            print(f"🔗 [Graph Debug] 引用的历史媒体:")
            for i, media in enumerate(referenced_historical):
                ref_info = f"@{media.get('reference_id', 'unknown')}"
                if media.get('display_name'):
                    ref_info += f" ({media['display_name']})"
                print(f"   {i+1}. {media['type']}: {ref_info} -> {media['url'][:50]}...")
        
        # 🎯 调试图文交织的enhanced_chat_history
        print(f"🔍 [Graph Debug] 增强的图文交织历史 (enhanced_chat_history):")
        for i, msg in enumerate(enhanced_chat_history):
            media_info = f"({len(msg.get('media', []))}个媒体)" if msg.get('media') else "(无媒体)"
            print(f"   {i+1}. {msg['role']}: {msg.get('content', '')[:50]}... {media_info}")
            if msg.get('media'):
                for j, media in enumerate(msg['media']):
                    print(f"      媒体{j+1}: {media['type']} - {media.get('url', '')[:30]}...")
            
    return {
        "user_input": latest_user_message["content"],
        "chat_history": conversation,  # 保持原始格式用于ChatAgent
        "enhanced_chat_history": enhanced_chat_history,  # 🎯 新增：图文交织格式用于PlanAgent
        "attachments": all_media,  # 🔧 修复：传递所有媒体，通过source字段区分来源
        "current_media": current_media,  # 新增：当前消息的媒体
        "historical_media": historical_media,  # 新增：历史媒体
        "media_stats": {  # 新增：媒体统计信息
            "total": total_media,
            "current": current_media_count,
            "historical": historical_media_count,
            "images": image_count,
            "videos": video_count,
            "uploads": upload_count,
            "references": reference_count
        },
        "user": None,  # 用户对象，需要外部设置
        "safety_status": None,
        "safety_message": None,
        "original_input": None,
        "processed_input": None,
        "user_intent": None,
        "user_requirements": None,
        "is_intent_clear": False,
        "execution_plan": None,
        "plan_status": PlanStatus.PENDING,
        "plan_evaluation": None,
        "generated_results": [],
        "generation_history": [],
        "current_round": 0,
        "last_generation_result": None,
        "quality_analysis": None,
        "replan_count": 0,
        "max_replan_attempts": 3,
        "chat_revert_count": 0,
        "current_agent": None,
        "is_complete": False,
        "last_error": None,
        "plan_failure_reason": None,
        "plan_failure_details": None,
        "token_check_result": None,  # Token检查结果初始化为None
        "agent_logs": [],
        "skip_tool_execution": skip_tool_execution,
        "skip_safety_check": skip_safety_check
    } 
