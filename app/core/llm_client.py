"""
LLM客户端模块
封装Qwen API调用和其他LLM服务
"""

import os
import json
import logging
import time
import random
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
import dashscope
from dashscope import Generation

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class LLMClient:
    """LLM客户端类"""
    
    def __init__(self, model: str = None):
        # 配置Qwen API
        self.api_key = os.getenv("DASHSCOPE_API_KEY")
        self.model = model or os.getenv("LLM_MODEL", "qwen-turbo")
        
        if not self.api_key:
            raise ValueError("请在.env文件中设置DASHSCOPE_API_KEY")
        
        dashscope.api_key = self.api_key
        
        # 默认配置 - 针对并发环境优化
        self.default_config = {
            'model': self.model,
            'temperature': 0.3,  # 降低随机性，提高响应一致性
            'max_tokens': 8000,  
            'top_p': 0.8,
        }
        
        # 并发控制配置
        self.max_retries = 3
        self.base_delay = 0.5  # 基础延迟时间（秒）
        self.max_delay = 5.0   # 最大延迟时间（秒）

    def _add_request_delay(self):
        """添加随机延迟以避免并发请求过于集中"""
        delay = random.uniform(0.1, 0.3)  # 100-300ms的随机延迟
        time.sleep(delay)

    def _calculate_backoff_delay(self, attempt: int) -> float:
        """计算指数退避延迟时间"""
        delay = self.base_delay * (2 ** attempt) + random.uniform(0, 1)
        return min(delay, self.max_delay)

    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       system_prompt: Optional[str] = None,
                       **kwargs) -> Dict[str, Any]:
        """
        调用聊天完成API（带重试机制）
        
        Args:
            messages: 对话历史
            system_prompt: 系统提示词
            **kwargs: 其他配置参数
            
        Returns:
            LLM响应结果
        """
        # 添加请求延迟
        self._add_request_delay()
        
        # 合并配置
        config = {**self.default_config, **kwargs}
        
        # 构建消息列表
        formatted_messages = []
        
        # 添加系统提示词
        if system_prompt:
            formatted_messages.append({
                'role': 'system',
                'content': system_prompt
            })
        
        # 添加对话历史
        formatted_messages.extend(messages)
        
        # 重试机制
        last_error = None
        for attempt in range(self.max_retries):
            try:
                # 调用API
                response = Generation.call(
                    model=config['model'],
                    messages=formatted_messages,
                    temperature=config['temperature'],
                    max_tokens=config['max_tokens'],
                    top_p=config['top_p'],
                    result_format='message',
                    timeout=30  # 设置30秒超时
                )
                
                # 检查响应状态
                if response.status_code == 200:
                    return {
                        'success': True,
                        'content': response.output.choices[0]['message']['content'],
                        'usage': response.usage,
                        'request_id': response.request_id,
                        'attempt': attempt + 1
                    }
                else:
                    error_msg = f"API调用失败: {response.status_code}, {response.message}"
                    last_error = error_msg
                    logger.warning(f"尝试 {attempt + 1}/{self.max_retries}: {error_msg}")
                    
                    # 如果不是最后一次尝试，进行退避延迟
                    if attempt < self.max_retries - 1:
                        delay = self._calculate_backoff_delay(attempt)
                        logger.info(f"等待 {delay:.2f}s 后重试...")
                        time.sleep(delay)
                    
            except Exception as e:
                last_error = f"LLM调用异常: {str(e)}"
                logger.warning(f"尝试 {attempt + 1}/{self.max_retries}: {last_error}")
                
                # 如果不是最后一次尝试，进行退避延迟
                if attempt < self.max_retries - 1:
                    delay = self._calculate_backoff_delay(attempt)
                    logger.info(f"等待 {delay:.2f}s 后重试...")
                    time.sleep(delay)
        
        # 所有重试都失败
        logger.error(f"LLM调用最终失败: {last_error}")
        print(f"   ❌ LLM API错误详情: {last_error}")
        return {
            'success': False,
            'error': last_error,
            'attempts': self.max_retries
        }

    def safety_check(self, 
                    user_input: str, 
                    chat_history: List[Dict[str, str]] = None,
                    system_prompt: str = None) -> Dict[str, Any]:
        """
        内容安全审查方法
        
        Args:
            user_input: 用户输入内容
            chat_history: 对话历史
            system_prompt: 安全审查系统提示词
            
        Returns:
            安全审查结果
        """
        try:
            # 构建消息 - 暂时只传递用户输入，不传递对话历史以避免API误判
            messages = []
            
            # 暂时注释掉对话历史传递，以避免API误判
            # # 添加对话历史（最近3轮用于上下文理解）
            # if chat_history:
            #     recent_history = chat_history[-6:]  # 最近6条记录（3轮对话）
            #     for msg in recent_history:
            #         messages.append({
            #             'role': msg['role'], 
            #             'content': msg['content']
            #         })
            
            # 直接传递用户输入，避免格式化可能引起的误解
            messages.append({
                'role': 'user',
                'content': user_input
            })
            
            # 打印调试信息
            print("   🔍 安全审查调试：")
            print(f"     - 用户输入: {user_input}")
            print("     - 对话历史数量: 0 (暂时禁用)")
            print(f"     - 消息总数: {len(messages)}")
            
            # 调用LLM进行安全审查
            response = self.chat_completion(
                messages=messages,
                system_prompt=system_prompt,  # 恢复使用配置文件中的系统提示词
                temperature=0.1,  # 更低的温度以确保审查结果稳定，避免误判
                max_tokens=6000    # 减少token数量
            )

            if response['success']:
                # 解析JSON响应
                result = self.parse_json_response(response['content'])
                
                if result:
                    return {
                        'success': True,
                        'result': result,
                        'raw_response': response['content']
                    }
                else:
                    # JSON解析失败，返回保守结果
                    logger.warning(f"安全审查JSON解析失败: {response['content']}")
                    return {
                        'success': True,
                        'result': {
                            'status': 'safe',
                            'message': '',
                            'processed_content': user_input,
                            'risk_level': 'unknown',
                            'violation_type': '解析失败'
                        },
                        'raw_response': response['content']
                    }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Unknown error'),
                    'raw_response': response.get('content', '')
                }
                
        except Exception as e:
            logger.error(f"安全审查异常: {str(e)}")
            return {
                'success': False,
                'error': f"安全审查异常: {str(e)}"
            }

    def parse_json_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        增强的JSON解析方法，专门处理并发环境下的格式不规范响应
        
        Args:
            response_text: LLM返回的文本
            
        Returns:
            解析后的JSON对象，解析失败返回None
        """
        try:
            print(f"🔍 [LLM Debug] 原始文本: {response_text[:200]}...")

            # 1. 首先尝试从原始文本中提取JSON代码块
            start_markers = ['```json', '```']
            end_marker = '```'
            
            for start_marker in start_markers:
                if start_marker in response_text:
                    start_idx = response_text.find(start_marker) + len(start_marker)
                    end_idx = response_text.find(end_marker, start_idx)
                    
                    if end_idx != -1:
                        json_text = response_text[start_idx:end_idx].strip()
                        print(f"🔍 [LLM Debug] 提取的JSON代码块: {json_text}")
                        try:
                            return json.loads(json_text)
                        except json.JSONDecodeError as e:
                            print(f"🔍 [LLM Debug] JSON代码块解析失败: {e}")
                            continue

            # 2. 多种策略尝试解析JSON
            parsing_strategies = [
                # 策略1: 直接解析
                lambda text: json.loads(text),
                
                # 策略2: 清洗后解析
                lambda text: json.loads(text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')),
                
                # 策略3: 查找第一个完整的JSON对象
                lambda text: self._extract_first_json_object(text),
                
                # 策略4: 处理常见的格式问题
                lambda text: self._fix_common_json_issues(text),
                
                # 策略5: 智能提取JSON片段
                lambda text: self._smart_json_extraction(text)
            ]
            
            for i, strategy in enumerate(parsing_strategies):
                try:
                    result = strategy(response_text)
                    if result:
                        print(f"🔍 [LLM Debug] 策略 {i+1} 解析成功")
                        return result
                except (json.JSONDecodeError, Exception) as e:
                    print(f"🔍 [LLM Debug] 策略 {i+1} 失败: {e}")
                    continue
            
            # 如果都失败了，记录详细错误
            print(f"🔍 [LLM Debug] 所有解析策略都失败: {response_text}")
            logger.warning(f"无法解析JSON响应: {response_text}")
            return None
            
        except Exception as e:
            print(f"🔍 [LLM Debug] JSON解析异常: {str(e)}")
            logger.error(f"JSON解析失败: {str(e)}")
            return None

    def _extract_first_json_object(self, text: str) -> Optional[Dict[str, Any]]:
        """提取文本中第一个完整的JSON对象"""
        text = text.strip()
        json_start = text.find('{')
        
        if json_start == -1:
            return None
            
        brace_count = 0
        json_end = json_start
        
        for i in range(json_start, len(text)):
            if text[i] == '{':
                brace_count += 1
            elif text[i] == '}':
                brace_count -= 1
                if brace_count == 0:
                    json_end = i
                    break
        
        if brace_count == 0:
            json_text = text[json_start:json_end + 1]
            return json.loads(json_text)
        
        return None

    def _fix_common_json_issues(self, text: str) -> Optional[Dict[str, Any]]:
        """修复常见的JSON格式问题"""
        # 移除常见的非JSON前缀
        prefixes_to_remove = [
            "根据您的要求，我分析如下：",
            "我来帮您分析：",
            "分析结果如下：",
            "以下是分析结果：",
            "JSON响应：",
            "回复：",
            "结果：",
        ]
        
        cleaned_text = text.strip()
        for prefix in prefixes_to_remove:
            if cleaned_text.startswith(prefix):
                cleaned_text = cleaned_text[len(prefix):].strip()
        
        # 移除常见的后缀
        suffixes_to_remove = [
            "希望这个分析对您有帮助！",
            "请确认以上信息是否正确。",
            "如有其他问题请告诉我。",
        ]
        
        for suffix in suffixes_to_remove:
            if cleaned_text.endswith(suffix):
                cleaned_text = cleaned_text[:-len(suffix)].strip()
        
        # 尝试解析清理后的文本
        return self._extract_first_json_object(cleaned_text)

    def _smart_json_extraction(self, text: str) -> Optional[Dict[str, Any]]:
        """智能JSON提取，处理混合内容"""
        import re
        
        # 查找所有可能的JSON对象
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.finditer(json_pattern, text, re.DOTALL)
        
        for match in matches:
            json_candidate = match.group()
            try:
                result = json.loads(json_candidate)
                # 验证是否包含期望的字段（基本验证）
                if isinstance(result, dict) and len(result) > 0:
                    return result
            except json.JSONDecodeError:
                continue
        
        return None

    def load_system_prompt(self, prompt_file: str) -> str:
        """
        从文件加载系统提示词
        
        Args:
            prompt_file: 提示词文件路径
            
        Returns:
            系统提示词内容
        """
        try:
            # 获取当前文件目录，然后定位到config/prompts
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_dir = os.path.join(current_dir, "..", "config", "prompts")
            prompt_path = os.path.join(config_dir, prompt_file)
            prompt_path = os.path.normpath(prompt_path)  # 规范化路径
            
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            logger.error(f"提示词文件不存在: {prompt_path}")
            return ""
        except Exception as e:
            logger.error(f"加载提示词失败: {str(e)}")
            return ""

    def analyze_user_intent(self, 
                           user_input: str, 
                           chat_history: List[Dict[str, str]] = None,
                           current_requirements: Dict[str, Any] = None,
                           last_generation_result: Optional[str] = None,
                           generation_history: List[Dict[str, Any]] = None,
                           attachments: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        分析用户意图的便捷方法
        
        Args:
            user_input: 用户输入
            chat_history: 对话历史
            current_requirements: 当前已收集的需求信息
            last_generation_result: 上一轮的生成结果路径（向后兼容）
            generation_history: 完整的生成历史记录
            attachments: 当前用户提供的附件信息
            
        Returns:
            意图分析结果
        """
        # 加载系统提示词
        system_prompt = self.load_system_prompt("chat_agent_system.md")
        
        # 构建对话上下文
        messages = []
        
        # 添加对话历史（根据配置截断）
        if chat_history:
            from app.config.settings import Settings
            settings = Settings()
            max_length = settings.max_chat_history_length
            recent_history = chat_history[-max_length:]  # 取最近N条记录
            for msg in recent_history:
                messages.append({
                    'role': msg['role'],
                    'content': msg['content']
                })
        
        # 格式化生成历史
        history_text = "无"
        if generation_history:
            history_lines = []
            for record in generation_history:
                history_lines.append(
                    f"第{record.get('round_number', 0)}轮: {record.get('task_type', 'unknown')} - "
                    f"{record.get('description', '')} -> {record.get('result_path', '')}"
                )
            history_text = "\n".join(history_lines)
        
        # 🎯 ChatAgent需要提供足够的媒体上下文让LLM识别图像编辑任务
        attachment_text = "No media files"
        
        if attachments:
            total_images = len([att for att in attachments if att.get('type') == 'image'])
            total_videos = len([att for att in attachments if att.get('type') == 'video'])
            
            # 🔧 增强媒体上下文：提供历史生成图像的详细描述
            media_details = []
            media_details.append(f"📊 **Media Statistics**: {total_images} images, {total_videos} videos available")
            
            # 🎯 分析历史生成的图像，提供描述信息
            historical_images = [att for att in attachments if att.get('type') == 'image' and att.get('role') != 'user']
            current_uploads = [att for att in attachments if att.get('type') == 'image' and att.get('role') == 'user']
            
            if historical_images:
                media_details.append(f"\n📜 **Historical Generated Images** ({len(historical_images)} images):")
                for i, img in enumerate(historical_images[-3:]):  # 最近3张历史图像
                    description = img.get('description', 'Generated image')
                    content_context = img.get('content', '')[:100] + "..." if len(img.get('content', '')) > 100 else img.get('content', '')
                    media_details.append(f"  - Image {i+1}: {description}")
                    if content_context:
                        media_details.append(f"    Context: \"{content_context}\"")
            
            if current_uploads:
                media_details.append(f"\n📤 **Current User Uploads** ({len(current_uploads)} images):")
                for i, img in enumerate(current_uploads[-2:]):  # 最近2张上传图像
                    description = img.get('description', 'User uploaded image')
                    media_details.append(f"  - Upload {i+1}: {description}")
            
            # 🎯 提供生成历史上下文
            if generation_history:
                media_details.append(f"\n🔄 **Recent Generation History**:")
                for record in generation_history[-2:]:  # 最近2轮生成
                    task_type = record.get('task_type', 'unknown')
                    description = record.get('description', 'No description')
                    user_input = record.get('user_input', '')[:50] + "..." if len(record.get('user_input', '')) > 50 else record.get('user_input', '')
                    success = record.get('success', False)
                    status = "✅ Success" if success else "❌ Failed"
                    media_details.append(f"  - Round {record.get('round_number', 0)}: {task_type} ({status})")
                    media_details.append(f"    Request: \"{user_input}\"")
                    media_details.append(f"    Result: {description}")
            
            attachment_text = "\n".join(media_details)
            
            print(f"🔍 [ChatAgent] Enhanced media context: {total_images} images, {total_videos} videos")
            print(f"🔍 [ChatAgent] Historical images: {len(historical_images)}, Current uploads: {len(current_uploads)}")
        
        # 使用系统提示词模板格式，所有逻辑在系统提示词中处理
        analysis_request = f"""用户输入: {user_input}
                当前用户提供的附件:
                {attachment_text}

                当前已收集的需求信息: {json.dumps(current_requirements or {}, ensure_ascii=False, indent=2)}

                完整生成历史:
                {history_text}

                上一轮生成的结果路径: {last_generation_result or '无'}"""
        
        messages.append({
            'role': 'user',
            'content': analysis_request
        })
        
        # 调用LLM
        response = self.chat_completion(
            messages=messages,
            system_prompt=system_prompt,
            temperature=0.3
        )
        
        if response['success']:
            parsed_result = self.parse_json_response(response['content'])
            if parsed_result:
                # 将placeholder替换回真实URL
                parsed_result = self._restore_urls_from_placeholders(parsed_result, {})
                return {
                    'success': True,
                    'analysis': parsed_result,
                    'raw_response': response['content']
                }
            else:
                return {
                    'success': False,
                    'error': 'JSON解析失败',
                    'raw_response': response['content']
                }
        else:
            return {
                'success': False,
                'error': response['error']
            }

    def generate_execution_plan(self,
                               user_intent: str,
                               user_requirements: Dict[str, Any],
                               generation_history: List[Dict[str, Any]] = None,
                               available_tools: Dict[str, Any] = None,
                               enhanced_chat_history: List[Dict[str, Any]] = None,
                               attachments: List[Dict[str, str]] = None,
                               chat_history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        生成执行计划的专用方法，供PlanAgent使用
        """
        # 加载系统提示词
        system_prompt = self.load_system_prompt("plan_agent_system.md")
        
        # 🎯 简化：直接使用PlanAgent传递的处理后的enhanced_chat_history
        # PlanAgent已经将URL替换为{{media_N}}格式的placeholder
        
        if not enhanced_chat_history:
            print("❌ [PlanAgent] enhanced_chat_history为空，这是数据流问题！")
            return {
                'success': False,
                'error': 'enhanced_chat_history is empty, cannot perform media reference extraction.'
            }
        
        # 🎯 构建简化的媒体信息描述
        media_count = 0
        media_description = []
        media_description.append("📍 **Available Media for Reference:**")
        
        for i, msg in enumerate(enhanced_chat_history):
            if msg.get('media'):
                for j, media in enumerate(msg['media']):
                    media_count += 1
                    media_type = media.get('type', 'unknown')
                    media_url = media.get('url', '')  # 这里应该是{{placeholder_N}}格式
                    role = msg.get('role', 'unknown')
                    message_content = msg.get('content', '')[:100] + "..." if len(msg.get('content', '')) > 100 else msg.get('content', '')
                    
                    media_description.append(f"  - {media_url}: {media_type} from {role} in message {i+1}")
                    media_description.append(f"    Content: \"{message_content}\"")
                    
                    # 添加reference_id信息（如果存在）
                    if media.get('reference_id'):
                        media_description.append(f"    Reference ID: {media['reference_id']} (用户可通过此ID引用)")
                    if media.get('description'):
                        media_description.append(f"    Description: {media['description']}")
                
        if media_count > 0:
            media_info = "\n".join(media_description)
            media_info += f"\n\n📍 **Instructions**: Based on the user's request \"{user_intent}\", intelligently select the most relevant media using the appropriate placeholder. Do not default to the latest image unless explicitly requested."
            print(f"🎯 [PlanAgent] 提供{media_count}个媒体供LLM智能选择")
        else:
            media_info = "No media files provided"
            print("🎯 [PlanAgent] enhanced_chat_history中没有媒体")
        
        # 🎯 构建简化的对话历史
        chat_history_text = "📍 **Enhanced Chat History (with media placeholders):**\n"
        for i, msg in enumerate(enhanced_chat_history):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            media_list = msg.get('media', [])
            
            chat_history_text += f"\n{i+1}. **{role.upper()}**: {content}\n"
            if media_list:
                chat_history_text += f"   📎 Media ({len(media_list)} items):\n"
                for j, media in enumerate(media_list):
                    media_type = media.get('type', 'unknown')
                    media_url = media.get('url', '')  # 应该是{{media_N}}格式
                    chat_history_text += f"      - {media_type} [{media_url}]\n"
        
        # 🎯 构建发送给LLM的请求
        plan_request = f"""User Intent: {user_intent}

{media_info}

{chat_history_text}

User Requirements Details: {json.dumps(user_requirements, ensure_ascii=False, indent=2)}

**Task**: Analyze the user's request and intelligently select the most relevant media based on semantic context. For example, if the user mentions "alien image" or "外星人图像", look for images from messages that contain alien-related content, not just the latest image."""
                                    
        print(f"\n🔍 [PlanAgent Debug] 发送给LLM的消息:")
        print(f"System Prompt前50字符: {system_prompt[:50]}...")
        print(f"User Message: {plan_request}")
        print(f"Media count: {media_count}")
        print(f"Attachments: {attachments}")
        
        messages = [{
            'role': 'user',
            'content': plan_request
        }]
        
        print(f"🔍 [LLM Debug] 发送给LLM的消息: {messages}")
        
        # 调用LLM
        response = self.chat_completion(
            messages=messages,
            system_prompt=system_prompt,
            temperature=0.3
        )
        
        if response['success']:
            parsed_result = self.parse_json_response(response['content'])
            if parsed_result:
                return {
                    'success': True,
                    'plan': parsed_result,
                    'raw_response': response['content']
                }
            else:
                return {
                    'success': False,
                    'error': 'JSON解析失败',
                    'raw_response': response['content']
                }
        else:
            return {
                'success': False,
                'error': response['error']
            }

    def analyze_generation_result(self,
                                 user_intent: str,
                                 user_requirements: Dict[str, Any],
                                 execution_plan: Dict[str, Any],
                                 generation_result: str,
                                 generation_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        分析生成结果质量的专用方法，供ResultAnalysisAgent使用
        """
        # 加载系统提示词
        system_prompt = self.load_system_prompt("result_analysis_agent_system.md")
        
        # 格式化执行计划信息
        plan_info = "无执行计划信息"
        if execution_plan:
            plan_info = f"""
任务类型: {execution_plan.get('task_type', 'unknown')}
工具调用: {len(execution_plan.get('tool_calls', []))}个步骤
描述: {execution_plan.get('description', '')}
预估时间: {execution_plan.get('estimated_time', 0)}秒
"""
        
        # 格式化生成历史
        history_text = "无历史记录"
        if generation_history:
            history_lines = []
            for record in generation_history:
                history_lines.append(
                    f"第{record.get('round_number', 0)}轮: {record.get('task_type', 'unknown')} - "
                    f"{record.get('description', '')} -> {record.get('result_path', '')}"
                )
            history_text = "\n".join(history_lines)
        
        # 使用系统提示词模板格式，所有逻辑在系统提示词中处理
        analysis_request = f"""用户意图: {user_intent}

用户需求: {json.dumps(user_requirements, ensure_ascii=False, indent=2)}

执行计划: {plan_info}

生成结果路径: {generation_result}

生成历史:
{history_text}"""
        
        messages = [{
            'role': 'user',
            'content': analysis_request
        }]
        
        # 调用LLM
        response = self.chat_completion(
            messages=messages,
            system_prompt=system_prompt,
            temperature=0.2
        )
        
        if response['success']:
            parsed_result = self.parse_json_response(response['content'])
            if parsed_result:
                return {
                    'success': True,
                    'analysis': parsed_result,
                    'raw_response': response['content']
                }
            else:
                return {
                    'success': False,
                    'error': 'JSON解析失败',
                    'raw_response': response['content']
                }
        else:
            return {
                'success': False,
                'error': response['error']
            }

    def generate_replan(self,
                       original_intent: str,
                       original_requirements: Dict[str, Any],
                       failed_execution_plan: Dict[str, Any],
                       analysis_result: Dict[str, Any],
                       generation_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成重新规划方案的专用方法，供ReplanAgent使用
        """
        # 加载系统提示词
        system_prompt = self.load_system_prompt("replan_agent_system.md")
        
        # 格式化失败的执行计划
        failed_plan_info = "无执行计划信息"
        if failed_execution_plan:
            failed_plan_info = f"""
            任务类型: {failed_execution_plan.get('task_type', 'unknown')}
            工具调用: {json.dumps(failed_execution_plan.get('tool_calls', []), ensure_ascii=False, indent=2)}
            描述: {failed_execution_plan.get('description', '')}
            预估时间: {failed_execution_plan.get('estimated_time', 0)}秒
            """
        
        # 格式化分析结果
        analysis_info = "无分析结果"
        if analysis_result:
            quality_analysis = analysis_result.get('quality_analysis', {})
            problem_analysis = analysis_result.get('problem_analysis', {})
            decision = analysis_result.get('decision', {})
            
            analysis_info = f"""
质量评分:
- 总体评分: {quality_analysis.get('overall_score', 0)}
- 内容准确性: {quality_analysis.get('content_accuracy', 0)}
- 视觉质量: {quality_analysis.get('visual_quality', 0)}
- 风格一致性: {quality_analysis.get('style_consistency', 0)}

问题分析:
- 严重问题: {problem_analysis.get('critical_issues', [])}
- 中等问题: {problem_analysis.get('moderate_issues', [])}
- 轻微问题: {problem_analysis.get('minor_issues', [])}
- 整体评估: {problem_analysis.get('overall_assessment', '')}

决策建议:
- 下一步行动: {decision.get('next_action', '')}
- 推理过程: {decision.get('reasoning', '')}
"""
        
        # 格式化生成历史
        history_text = "无历史记录"
        if generation_history:
            history_lines = []
            for record in generation_history:
                history_lines.append(
                    f"第{record.get('round_number', 0)}轮: {record.get('task_type', 'unknown')} - "
                    f"{record.get('description', '')} -> {record.get('result_path', '')}"
                )
            history_text = "\n".join(history_lines)
        
        # 使用系统提示词模板格式，所有逻辑在系统提示词中处理
        replan_request = f"""原始用户意图: {original_intent}
            原始用户需求: {json.dumps(original_requirements, ensure_ascii=False, indent=2)}
            失败的执行计划: {failed_plan_info}
            结果分析: {analysis_info}
            生成历史:
            {history_text}"""
        
        messages = [{
            'role': 'user',
            'content': replan_request
        }]
        
        # 调用LLM
        response = self.chat_completion(
            messages=messages,
            system_prompt=system_prompt,
            temperature=0.3
        )
        
        if response['success']:
            parsed_result = self.parse_json_response(response['content'])
            if parsed_result:
                return {
                    'success': True,
                    'replan': parsed_result,
                    'raw_response': response['content']
                }
            else:
                return {
                    'success': False,
                    'error': 'JSON解析失败',
                    'raw_response': response['content']
                }
        else:
            return {
                'success': False,
                'error': response['error']
            }

    def get_completion(self, 
                      system_prompt: str,
                      user_prompt: str,
                      max_tokens: int = 1000,
                      temperature: float = 0.7) -> str:
        """
        简单的完成API调用（兼容旧接口）
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            max_tokens: 最大token数
            temperature: 温度参数
            
        Returns:
            LLM生成的文本内容
        """
        try:
            messages = [{'role': 'user', 'content': user_prompt}]
            
            response = self.chat_completion(
                messages=messages,
                system_prompt=system_prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            if response['success']:
                return response['content']
            else:
                logger.error(f"get_completion调用失败: {response.get('error', 'Unknown error')}")
                return ""
                
        except Exception as e:
            logger.error(f"get_completion异常: {str(e)}")
            return ""

    def _restore_urls_from_placeholders(self, data: Dict[str, Any], url_mapping: Dict[str, str]) -> Dict[str, Any]:
        """将placeholder替换回真实URL"""
        if not url_mapping:
            return data
        
        # 递归处理字典和列表
        def replace_in_value(value):
            if isinstance(value, str):
                # 替换字符串中的placeholder
                for placeholder, real_url in url_mapping.items():
                    value = value.replace(placeholder, real_url)
                return value
            elif isinstance(value, dict):
                return {k: replace_in_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [replace_in_value(item) for item in value]
            else:
                return value
        
        result = replace_in_value(data)
        print(f"🔍 [LLM Debug] URL替换完成，映射数量: {len(url_mapping)}")
        return result

# 全局LLM客户端实例
def get_llm_client(model: str = None) -> LLMClient:
    """获取LLM客户端实例"""
    return LLMClient(model=model)

# 默认实例
llm_client = get_llm_client() 