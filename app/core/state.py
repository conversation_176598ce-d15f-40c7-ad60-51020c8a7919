from typing import List, Dict, Any, Optional, TypedDict
from dataclasses import dataclass, field
from enum import Enum

class GenerationTaskType(Enum):
    """生成任务类型"""
    TEXT_TO_IMAGE = "text_to_image"
    TEXT_TO_VIDEO = "text_to_video"
    IMAGE_EDIT = "image_edit"
    VIDEO_EDIT = "video_edit"
    IMAGE_TO_VIDEO = "image_to_video"
    MULTI_STEP_COMPOSITE = "multi_step_composite"  # 多步骤复合任务

class PlanStatus(Enum):
    """计划状态"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REPLAN = "needs_replan"
    NEEDS_RECHAT = "needs_rechat"

class NextAction(Enum):
    """下一步行动"""
    COMPLETE = "complete"
    REPLAN = "replan"
    REFINE = "refine"
    REGENERATE = "regenerate"

@dataclass
class ToolCall:
    """工具调用定义"""
    tool_name: str
    parameters: Dict[str, Any]
    description: str
    order: int = 0  # 执行顺序

@dataclass
class ExecutionPlan:
    """执行计划"""
    task_type: GenerationTaskType
    tool_calls: List[ToolCall]
    estimated_time: float
    description: str

@dataclass
class GenerationResult:
    """生成结果"""
    tool_name: str
    result_path: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    success: bool = True
    error_message: Optional[str] = None

@dataclass
class HistoryGenerationRecord:
    """历史生成记录"""
    round_number: int  # 对话轮次
    user_input: str    # 用户输入
    description: str   # 生成内容描述
    result_path: str   # 结果文件路径
    task_type: GenerationTaskType  # 任务类型
    timestamp: str     # 生成时间
    success: bool = True

@dataclass
class QualityAnalysis:
    """质量分析结果"""
    overall_score: float  # 0-10分
    content_accuracy: float
    visual_quality: float
    style_consistency: float
    specific_issues: List[str]
    improvement_suggestions: List[str]

class AgentState(TypedDict):
    """LangGraph状态定义"""
    # 用户交互
    user_input: str
    chat_history: List[Dict[str, str]]
    enhanced_chat_history: List[Dict[str, Any]]  # 🎯 图文交织的对话历史，用于PlanAgent
    attachments: List[Dict[str, str]]  # 用户附件（图片、视频URL）
    current_media: List[Dict[str, Any]]  # 当前消息的媒体
    historical_media: List[Dict[str, Any]]  # 历史媒体
    media_stats: Dict[str, int]  # 媒体统计信息
    user: Optional[Any]  # 用户对象，用于token检查
    
    # 安全审查
    safety_status: str  # "safe" | "rewritten" | "forbidden"
    safety_message: str  # 安全提示消息
    original_input: str  # 原始用户输入
    processed_input: str  # 处理后的输入内容
    
    # 提示词增强
    enhanced_prompt: str  # 增强后的提示词
    enhanced_prompt_en: str  # 英文版增强提示词
    enhancement_status: str  # "pending" | "enhanced" | "skipped" | "failed"
    template_used: str  # 使用的模板名称
    template_confidence: float  # 模板匹配置信度
    template_variables: Dict[str, str]  # 提取的模板变量
    
    # 意图和计划
    user_intent: Optional[str]
    user_requirements: Dict[str, Any]
    is_intent_clear: bool
    
    # 执行计划
    execution_plan: Optional[ExecutionPlan]
    plan_status: PlanStatus
    plan_evaluation: Dict[str, Any]
    
    # 执行结果
    generated_results: List[GenerationResult]
    
    # 生成历史管理
    generation_history: List[HistoryGenerationRecord]
    current_round: int
    last_generation_result: Optional[str]  # 保持向后兼容
    
    # 质量分析
    quality_analysis: Optional[QualityAnalysis]
    
    # 控制信息
    replan_count: int
    max_replan_attempts: int
    chat_revert_count: int  # ChatAgent回退计数
    current_agent: str
    is_complete: bool
    
    # 错误处理
    last_error: Optional[str]
    plan_failure_reason: Optional[str]  # PlanAgent失败原因
    plan_failure_details: Dict[str, Any]  # PlanAgent失败详细信息
    
    # Token检查
    token_check_result: Optional[str]  # Token检查结果: "sufficient" | "insufficient" | "expired" | "skip" | "no_user"
    
    # 最终回复
    final_response: Optional[str]  # 用于非视觉任务的最终回复
    llm_generated_response: Optional[str]  # SafetyAgent生成的专业回复
    response_type: Optional[str]  # 响应类型，用于区分不同的结束原因
    
    # 调试信息
    agent_logs: List[str]
    skip_tool_execution: bool  # 是否跳过工具执行

def create_initial_state() -> AgentState:
    """创建初始状态"""
    return AgentState(
        user_input="",
        chat_history=[],
        enhanced_chat_history=[],  # 🎯 图文交织的对话历史初始化
        attachments=[],
        current_media=[],  # 当前消息媒体初始化
        historical_media=[],  # 历史媒体初始化
        media_stats={},  # 媒体统计初始化
        user=None,  # 用户对象初始化为None
        safety_status="safe",
        safety_message="",
        original_input="",
        processed_input="",
        enhanced_prompt="",
        enhanced_prompt_en="",
        enhancement_status="pending",
        template_used="",
        template_confidence=0.0,
        template_variables={},
        user_intent=None,
        user_requirements={},
        is_intent_clear=False,
        execution_plan=None,
        plan_status=PlanStatus.PENDING,
        plan_evaluation={},
        generated_results=[],
        generation_history=[],
        current_round=0,
        last_generation_result=None,
        quality_analysis=None,
        replan_count=0,
        max_replan_attempts=3,
        current_agent="chat",
        is_complete=False,
        last_error=None,
        plan_failure_reason=None,
        plan_failure_details={},
        token_check_result=None,  # Token检查结果初始化为None
        final_response=None,  # 最终回复初始化为None
        llm_generated_response=None,  # 专业回复初始化为None
        response_type=None,  # 响应类型初始化为None
        agent_logs=[],
        chat_revert_count=0,  # ChatAgent回退计数
        skip_tool_execution=False  # 默认不跳过工具执行
    )

def add_skip_tool_execution(state: AgentState) -> None:
    """添加跳过工具执行标志"""
    state["skip_tool_execution"] = True

def add_to_chat_history(state: AgentState, role: str, content: str) -> None:
    """添加对话历史，并自动截断超长历史"""
    from app.config.settings import Settings
    
    # 添加新消息
    state["chat_history"].append({
        "role": role,
        "content": content,
        "timestamp": str(int(__import__("time").time()))
    })
    
    # 检查是否需要截断历史
    settings = Settings()
    max_length = settings.max_chat_history_length
    
    if len(state["chat_history"]) > max_length:
        # 保留最新的消息，删除最旧的
        state["chat_history"] = state["chat_history"][-max_length:]
        print(f"📝 对话历史已截断至最新 {max_length} 条消息")

def log_agent_action(state: AgentState, agent: str, action: str) -> None:
    """记录Agent行动"""
    state["agent_logs"].append(f"[{agent}] {action}")
    print(f"🤖 [{agent}] {action}")

def add_generation_to_history(state: AgentState, 
                              user_input: str,
                              description: str,
                              result_path: str,
                              task_type: GenerationTaskType,
                              success: bool = True) -> None:
    """添加生成结果到历史记录"""
    import time
    
    # 增加轮次计数
    state["current_round"] += 1
    
    # 创建历史记录
    history_record = HistoryGenerationRecord(
        round_number=state["current_round"],
        user_input=user_input,
        description=description,
        result_path=result_path,
        task_type=task_type,
        timestamp=str(int(time.time())),
        success=success
    )
    
    # 添加到历史
    state["generation_history"].append(history_record)
    
    # 更新最新结果（向后兼容）
    if success:
        state["last_generation_result"] = result_path

def get_generation_history_summary(state: AgentState) -> str:
    """获取生成历史的摘要信息"""
    if not state["generation_history"]:
        return "无历史生成记录"
    
    summary_lines = []
    for record in state["generation_history"]:
        status = "✅" if record.success else "❌"
        summary_lines.append(
            f"第{record.round_number}轮: {status} {record.task_type.value} - "
            f"{record.description} -> {record.result_path}"
        )
    
    return "\n".join(summary_lines)

def find_generation_by_description(state: AgentState, keywords: List[str]) -> List[HistoryGenerationRecord]:
    """根据描述关键词查找历史生成记录"""
    matched_records = []
    
    for record in state["generation_history"]:
        if not record.success:
            continue
            
        # 检查用户输入和描述中是否包含关键词
        search_text = f"{record.user_input} {record.description}".lower()
        
        for keyword in keywords:
            if keyword.lower() in search_text:
                matched_records.append(record)
                break 