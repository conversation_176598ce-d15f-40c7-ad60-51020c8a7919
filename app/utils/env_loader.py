import os
from pathlib import Path

def load_env_file(env_path: str = None) -> None:
    """
    将 .env 文件中的所有 KEY=VALUE 自动加载到 os.environ。

    Args:
        env_path: 可选，.env 文件的绝对或相对路径；默认是项目根目录下的 .env
    """
    # 1. 确定 .env 文件路径
    if env_path is None:
        # 假设本文件位于 app/utils/，项目根目录在上两级
        base_dir = Path(__file__).resolve().parent.parent.parent
        env_path = base_dir / ".env"
    else:
        env_path = Path(env_path)

    if not env_path.is_file():
        raise FileNotFoundError(f".env 文件未找到: {env_path}")

    # 2. 读取并设置环境变量
    with env_path.open("r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            # 忽略空行与注释
            if not line or line.startswith("#"):
                continue
            # 只处理 KEY=VALUE
            if "=" not in line:
                continue
            key, val = line.split("=", 1)
            key = key.strip()
            val = val.strip().strip('"').strip("'")
            os.environ[key] = val