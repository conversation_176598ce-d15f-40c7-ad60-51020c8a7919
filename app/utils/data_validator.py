import httpx
from io import BytesIO
from PIL import Image
from typing import Any, Dict
import cv2
import numpy as np
import logging
import base64
import re

# 设置日志
logger = logging.getLogger(__name__)

async def validate_image_url(url, valid_range = [0.1, 10.0]) -> bool:
    """
    验证图片 URL 是否能下载并打开。
    长宽比限制放宽到 [0.1, 10.0] 以支持更多类型的图像（全景图、条幅等）。
    支持字符串URL、Pydantic URL对象和data URI。
    """
    try:
        # 处理Pydantic URL对象或字符串
        if hasattr(url, '__str__'):
            url_str = str(url)
        else:
            url_str = url
            
        # 基本URL格式检查
        if not url_str or not isinstance(url_str, str):
            logger.warning(f"无效的URL格式: {url_str}")
            return False
        
        # 处理data URI
        if url_str.startswith('data:'):
            return await _validate_data_uri_image(url_str, valid_range)
            
        # 检查HTTP/HTTPS URL
        if not (url_str.startswith('http://') or url_str.startswith('https://')):
            logger.warning(f"URL必须以http://、https://或data:开头: {url_str}")
            return False

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        async with httpx.AsyncClient(follow_redirects=True, headers=headers) as client:
            # 增加更长的超时时间，支持慢速网络，自动跟随重定向
            r = await client.get(url_str, timeout=30.0)
            r.raise_for_status()
            data = r.content

        return await _validate_image_data(data, url_str, valid_range)
        
    except httpx.HTTPStatusError as e:
        logger.warning(f"HTTP错误 {e.response.status_code}: {url_str}")
        return False
    except httpx.TimeoutException:
        logger.warning(f"请求超时: {url_str}")
        return False
    except httpx.RequestError as e:
        logger.warning(f"请求错误: {e} for {url_str}")
        return False
    except Exception as e:
        logger.error(f"图像验证未知错误: {e} for {url_str}")
        return False

async def _validate_data_uri_image(url_str: str, valid_range) -> bool:
    """验证data URI格式的图像"""
    try:
        # 解析data URI格式：data:[mediatype][;base64],data
        match = re.match(r'^data:([^;]+)(;base64)?,(.+)$', url_str)
        if not match:
            logger.warning(f"无效的data URI格式: {url_str[:100]}...")
            return False
            
        mediatype, encoding, data_part = match.groups()
        
        # 检查是否为图像类型
        if not mediatype.startswith('image/'):
            logger.warning(f"data URI不是图像类型: {mediatype}")
            return False
        
        # 解码数据
        if encoding == ';base64':
            try:
                data = base64.b64decode(data_part)
            except Exception as e:
                logger.warning(f"base64解码失败: {e}")
                return False
        else:
            # URL编码的数据（较少见）
            from urllib.parse import unquote
            data = unquote(data_part).encode('utf-8')
        
        return await _validate_image_data(data, f"data URI ({mediatype})", valid_range)
        
    except Exception as e:
        logger.error(f"data URI图像验证错误: {e}")
        return False

async def _validate_image_data(data: bytes, source: str, valid_range) -> bool:
    """验证图像数据的通用函数"""
    try:
        # 检查数据是否为空
        if not data:
            logger.warning(f"图像数据为空: {source}")
            return False

        # 检查最小文件大小（至少100字节）
        if len(data) < 100:
            logger.warning(f"图像文件太小: {len(data)} bytes for {source}")
            return False

        # 尝试打开图像
        try:
            im = Image.open(BytesIO(data))
            # 验证图像格式
            im.verify()  # 验证图像完整性
            
            # 重新打开图像获取尺寸（verify后需要重新打开）
            im = Image.open(BytesIO(data))
            w, h = im.size
            
            # 检查图像尺寸合理性
            if w <= 0 or h <= 0:
                logger.warning(f"无效的图像尺寸: {w}x{h} for {source}")
                return False
                
            # 检查图像不能太小（至少16x16像素）
            if w < 16 or h < 16:
                logger.warning(f"图像尺寸太小: {w}x{h} for {source}")
                return False
                
            # 检查图像不能太大（防止内存攻击，最大50MP）
            if w * h > 50_000_000:
                logger.warning(f"图像尺寸太大: {w}x{h} for {source}")
                return False
            
            # 放宽长宽比限制，支持全景图、条幅图等
            ratio = w / h
            if ratio < valid_range[0] or ratio > valid_range[1]:
                logger.warning(f"图像长宽比超出范围: {ratio} (允许范围: {valid_range}) for {source}")
                return False
                
        except Exception as img_error:
            logger.warning(f"图像格式验证失败: {img_error} for {source}")
            return False
            
        logger.info(f"图像验证成功: {w}x{h}, 比例: {ratio:.2f} for {source}")
        return True
        
    except Exception as e:
        logger.error(f"图像数据验证错误: {e} for {source}")
        return False


async def validate_video_url(url, min_size_kb: int = 10) -> bool:
    """
    验证视频 URL 是否有效：
      1. HEAD 请求确认 Content-Type 以 "video/" 开头（如果可用）。
      2. Content-Length 大小要合理（降低到10KB以支持短视频）。
      3. 如果HEAD请求失败，尝试GET请求前几个字节验证。
    支持字符串URL、Pydantic URL对象和data URI。
    """
    try:
        # 处理Pydantic URL对象或字符串
        if hasattr(url, '__str__'):
            url_str = str(url)
        else:
            url_str = url
            
        # 基本URL格式检查
        if not url_str or not isinstance(url_str, str):
            logger.warning(f"无效的视频URL格式: {url_str}")
            return False
        
        # 处理data URI
        if url_str.startswith('data:'):
            return await _validate_data_uri_video(url_str, min_size_kb)
            
        # 检查HTTP/HTTPS URL
        if not (url_str.startswith('http://') or url_str.startswith('https://')):
            logger.warning(f"视频URL必须以http://、https://或data:开头: {url_str}")
            return False

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        async with httpx.AsyncClient(follow_redirects=True, headers=headers) as client:
            try:
                # 先尝试 HEAD 请求，拿 Content-Type、Content-Length
                head = await client.head(url_str, timeout=30.0)
                head.raise_for_status()

                ctype = head.headers.get("Content-Type", "").lower()
                
                # 检查Content-Type（如果有的话）
                if ctype and not (ctype.startswith("video/") or ctype.startswith("application/octet-stream")):
                    # 某些视频可能返回application/octet-stream，所以也接受
                    logger.warning(f"不支持的Content-Type: {ctype} for {url_str}")
                    return False

                # 检查Content-Length（如果有的话）
                clen = head.headers.get("Content-Length")
                if clen is not None:
                    size = int(clen)
                    min_size = min_size_kb * 1024
                    if size < min_size:
                        logger.warning(f"视频文件太小: {size} bytes (最小: {min_size}) for {url_str}")
                        return False
                        
                logger.info(f"视频HEAD验证成功: Content-Type={ctype}, Size={clen} for {url_str}")
                return True
                
            except (httpx.HTTPStatusError, httpx.RequestError):
                # HEAD请求失败，尝试GET请求前几个字节
                logger.info(f"HEAD请求失败，尝试GET验证: {url_str}")
                
                # 请求前1KB数据验证
                range_headers = {"Range": "bytes=0-1023"}
                get_resp = await client.get(url_str, headers=range_headers, timeout=30.0)
                
                # 206 (Partial Content) 或 200 都可以接受
                if get_resp.status_code not in [200, 206]:
                    logger.warning(f"GET请求失败: {get_resp.status_code} for {url_str}")
                    return False
                
                data = get_resp.content
                return await _validate_video_data(data, url_str, min_size_kb)

    except httpx.TimeoutException:
        logger.warning(f"视频验证请求超时: {url_str}")
        return False
    except httpx.RequestError as e:
        logger.warning(f"视频验证请求错误: {e} for {url_str}")
        return False
    except Exception as e:
        logger.error(f"视频验证未知错误: {e} for {url_str}")
        return False

    return False

async def _validate_data_uri_video(url_str: str, min_size_kb: int) -> bool:
    """验证data URI格式的视频"""
    try:
        # 解析data URI格式：data:[mediatype][;base64],data
        match = re.match(r'^data:([^;]+)(;base64)?,(.+)$', url_str)
        if not match:
            logger.warning(f"无效的data URI格式: {url_str[:100]}...")
            return False
            
        mediatype, encoding, data_part = match.groups()
        
        # 检查是否为视频类型
        if not mediatype.startswith('video/'):
            logger.warning(f"data URI不是视频类型: {mediatype}")
            return False
        
        # 解码数据
        if encoding == ';base64':
            try:
                data = base64.b64decode(data_part)
            except Exception as e:
                logger.warning(f"base64解码失败: {e}")
                return False
        else:
            # URL编码的数据（较少见）
            from urllib.parse import unquote
            data = unquote(data_part).encode('utf-8')
        
        return await _validate_video_data(data, f"data URI ({mediatype})", min_size_kb)
        
    except Exception as e:
        logger.error(f"data URI视频验证错误: {e}")
        return False

async def _validate_video_data(data: bytes, source: str, min_size_kb: int) -> bool:
    """验证视频数据的通用函数"""
    try:
        if len(data) < min_size_kb * 1024:
            # 如果数据太少，可能是文件确实很小，我们接受它
            logger.info(f"视频文件较小但可接受: {len(data)} bytes for {source}")
        
        # 基本的视频文件头检查（可选）
        if data:
            # 检查常见视频格式的魔法字节
            video_signatures = [
                b'\x00\x00\x00\x14ftypmp4',  # MP4
                b'\x00\x00\x00\x18ftypmp4',  # MP4
                b'\x00\x00\x00\x20ftypmp4',  # MP4
                b'ftypmp4',  # MP4 (简化)
                b'ftypisom',  # MP4/MOV
                b'ftypM4V',   # M4V
                b'ftypqt',    # QuickTime
                b'\x1a\x45\xdf\xa3',  # WebM/MKV (EBML)
                b'RIFF',      # AVI (starts with RIFF)
            ]
            
            is_video = any(data.startswith(sig) or sig in data[:100] for sig in video_signatures)
            if not is_video:
                logger.warning(f"无法识别的视频格式: {source}")
                # 注意：不直接返回False，因为可能有其他格式
        
        logger.info(f"视频验证成功: {len(data)} bytes for {source}")
        return True
        
    except Exception as e:
        logger.error(f"视频数据验证错误: {e} for {source}")
        return False