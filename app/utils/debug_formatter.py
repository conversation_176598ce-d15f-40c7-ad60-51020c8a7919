"""
Debug Formatter Utility - 调试输出格式化工具
统一所有debug输出为标准JSON格式，便于调试和解析
"""

import json
import logging
from typing import Any, Dict, List

def format_json_debug(data: Any, title: str = "", max_depth: int = 10, max_length: int = 1000) -> str:
    """
    将任意Python对象格式化为标准JSON格式用于调试输出
    
    Args:
        data: 要格式化的数据
        title: 标题（可选）
        max_depth: 最大嵌套深度，防止循环引用
        max_length: 最大输出长度，防止过长输出
    
    Returns:
        格式化后的JSON字符串
    """
    try:
        # 处理特殊对象类型
        clean_data = _clean_data_for_json(data, max_depth)
        
        # 生成JSON字符串
        json_str = json.dumps(clean_data, ensure_ascii=False, indent=2, default=str)
        
        # 限制输出长度
        if len(json_str) > max_length:
            json_str = json_str[:max_length] + "... (truncated)"
        
        # 添加标题
        if title:
            return f"📋 {title}:\n{json_str}"
        else:
            return json_str
            
    except Exception as e:
        # 如果JSON序列化失败，返回字符串表示
        return f"📋 {title} (JSON格式化失败: {str(e)}):\n{str(data)}"

def _clean_data_for_json(data: Any, max_depth: int, current_depth: int = 0) -> Any:
    """
    清理数据以便JSON序列化
    """
    if current_depth >= max_depth:
        return "... (max depth reached)"
    
    if data is None or isinstance(data, (str, int, float, bool)):
        return data
    
    elif isinstance(data, dict):
        return {
            key: _clean_data_for_json(value, max_depth, current_depth + 1) 
            for key, value in data.items()
        }
    
    elif isinstance(data, (list, tuple)):
        return [
            _clean_data_for_json(item, max_depth, current_depth + 1) 
            for item in data
        ]
    
    elif hasattr(data, '__dict__'):
        # 处理自定义对象
        try:
            obj_dict = {}
            for key, value in data.__dict__.items():
                if not key.startswith('_'):  # 跳过私有属性
                    obj_dict[key] = _clean_data_for_json(value, max_depth, current_depth + 1)
            obj_dict['__class__'] = data.__class__.__name__
            return obj_dict
        except Exception:
            return str(data)
    
    else:
        # 其他类型转为字符串
        return str(data)

def format_state_keys(state_keys: List[str]) -> str:
    """格式化state keys为JSON数组"""
    return format_json_debug(state_keys, "State Keys")

def format_attachments(attachments: List[Dict]) -> str:
    """格式化attachments为JSON格式"""
    return format_json_debug(attachments, "Attachments")

def format_plan_debug(plan_data: Dict[str, Any]) -> str:
    """格式化执行计划为JSON格式"""
    return format_json_debug(plan_data, "LLM执行计划 (标准JSON格式)")

def format_tool_parameters(parameters: Dict[str, Any]) -> str:
    """格式化工具参数为JSON格式"""
    return format_json_debug(parameters, "工具参数")

def format_url_mapping(url_mapping: Dict[str, str]) -> str:
    """格式化URL映射为JSON格式"""
    return format_json_debug(url_mapping, "URL映射表")

def format_media_stats(media_stats: Dict[str, int]) -> str:
    """格式化媒体统计为JSON格式"""
    return format_json_debug(media_stats, "媒体统计")

# 便捷的日志输出函数
def log_json_debug(logger: logging.Logger, data: Any, title: str = "", level: int = logging.WARNING):
    """使用logger输出JSON格式的调试信息"""
    formatted = format_json_debug(data, title)
    logger.log(level, formatted)

def print_json_debug(data: Any, title: str = ""):
    """使用print输出JSON格式的调试信息"""
    formatted = format_json_debug(data, title)
    print(formatted) 