import os
import smtplib
import schedule
import time
from email.message import EmailMessage
from dotenv import load_dotenv
from typing import List, Optional

load_dotenv()  # 读取 .env 文件

SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT   = int(os.getenv("SMTP_PORT", "465"))
SMTP_USER   = os.getenv("SMTP_USER")
SMTP_PASS   = os.getenv("SMTP_PASS")

def build_email(
    subject: str,
    body: str,
    to_addrs: List[str],
    *,
    from_addr: Optional[str] = None,
    content_type: str = "plain"  # "plain" 或 "html"
) -> EmailMessage:
    msg = EmailMessage()
    msg["Subject"] = subject
    msg["From"]    = from_addr or SMTP_USER
    msg["To"]      = ", ".join(to_addrs)
    if content_type == "html":
        # HTML 邮件用 add_alternative
        msg.set_content("This is an HTML email. Your client does not support HTML.")
        msg.add_alternative(body, subtype="html")
    else:
        msg.set_content(body)
    return msg


def send_email(msg: EmailMessage):
    """发送邮件（默认使用 SSL）"""
    if SMTP_PORT == 465:
        with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as smtp:
            smtp.login(SMTP_USER, SMTP_PASS)
            smtp.send_message(msg)
    else:  # 587 或其他需要 StartTLS 的端口
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as smtp:
            smtp.ehlo()
            smtp.starttls()
            smtp.login(SMTP_USER, SMTP_PASS)
            smtp.send_message(msg)
    print(f"[√] Sent mail to {msg['To']} with subject “{msg['Subject']}”")


def job_daily_report():
    html = """
    <h2>Daily Report</h2>
    <p>All systems operational ✅</p>
    """
    msg = build_email(
        subject="Daily Ops Report",
        body=html,
        to_addrs=["<EMAIL>", "<EMAIL>"],
        content_type="html"
    )
    send_email(msg)


def run_scheduler():
    """无限循环，按 schedule 队列执行任务"""
    print("Scheduler started… (Ctrl+C to quit)")
    while True:
        schedule.run_pending()
        time.sleep(1)  # 轻量轮询


def job_every_10min():
    msg = build_email(
        subject="10-minute heartbeat",
        body="Hi, this is an automated heartbeat message sent every 10 minutes.",
        to_addrs=["<EMAIL>"]
    )
    send_email(msg)
    
    

if __name__ == "__main__":  
    # ---------- 场景 1：立即发送 ----------
    test_msg = build_email(
        subject="Hello from Python",
        body="这是一封自动发送的测试邮件。",
        to_addrs=["<EMAIL>"]
    )
    send_email(test_msg)
    
    # # ---------- 场景 2：每隔 N 分钟发送 ----------
    # schedule.every(10).minutes.do(job_every_10min)

    # # ---------- 场景 3：每天固定时间发送 ----------
    # schedule.every().day.at("09:00").do(job_daily_report)
    
    # # ----------场景 4：取队列消息，依次发送 ---------
    # run_scheduler()

