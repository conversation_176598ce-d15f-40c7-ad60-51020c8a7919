from uuid import UUID
from typing import Literal, Any, List, Optional, Union
from pydantic import BaseModel, AnyHttpUrl, field_validator
import re

class Attachment(BaseModel):
    type: Literal["image", "video"]
    url: str  # 改为str以支持data URI
    
    @field_validator('url')
    @classmethod
    def validate_url(cls, v: str) -> str:
        """验证URL，支持HTTP/HTTPS和data URI scheme"""
        if not v:
            raise ValueError("URL不能为空")
        
        # 检查是否为data URI
        if v.startswith('data:'):
            # 验证data URI格式：data:[mediatype][;base64],data
            data_uri_pattern = r'^data:[\w/\-\+]+(?:;[\w\-\+=]+)*(?:;base64)?,[\w+/=]+$'
            if not re.match(data_uri_pattern, v):
                raise ValueError("无效的data URI格式")
            return v
        
        # 检查是否为标准HTTP/HTTPS URL
        try:
            # 使用Pydantic的AnyHttpUrl进行验证
            from pydantic_core import Url
            Url(v)  # 这会验证URL格式
            return v
        except Exception:
            raise ValueError("URL必须是有效的HTTP/HTTPS URL或data URI")

class RouteRequest(BaseModel):
    session_id: UUID
    user_id: Union[str, UUID] # TODO: hot_fix 
    utterance: str
    # 将 list[Attachment] | None 改为 Optional[List[Attachment]]
    attachments: Optional[List[Attachment]] = None

class Node(BaseModel):
    id: str
    tool: Literal[
        "img_gen", "img_edit",
        "video_keyframe", "img2video",
        "video_gen"
    ]
    inputs: dict[str, Any]
    # 将 list[str] | None 改为 Optional[List[str]]
    depends_on: Optional[List[str]] = None

class JobPlan(BaseModel):
    nodes: List[Node]
