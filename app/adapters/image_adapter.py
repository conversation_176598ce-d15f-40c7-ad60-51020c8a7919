"""
通用图片处理适配器
支持多种API的图片格式要求：URL清理、Base64转换、公共URL等
"""

import os
import tempfile
import urllib.parse
import base64
import requests
import cv2
import numpy as np
from typing import Dict, Any, Optional, Tuple
import logging

try:
    from PIL import Image as PILImage
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    PILImage = None

logger = logging.getLogger(__name__)

class ImageAdapter:
    """通用图片处理适配器"""
    
    @staticmethod
    def clean_oss_url(url: str) -> str:
        """
        清理OSS URL，移除查询参数
        适用于：Seedance、Kling等直接使用URL的API
        """
        parsed = urllib.parse.urlparse(url)
        clean_url = urllib.parse.urlunparse((
            parsed.scheme,
            parsed.netloc, 
            parsed.path,
            '',  # 移除params
            '',  # 移除query  
            ''   # 移除fragment
        ))
        
        logger.info(f"🔧 URL清理: {url[:50]}... -> {clean_url[:50]}...")
        return clean_url
    
    @staticmethod
    def convert_to_base64(image_url: str) -> Tuple[str, str]:
        """
        下载图片并转换为base64编码
        适用于：Veo3等需要base64的API
        
        Returns:
            (base64_data, mime_type)
        """
        try:
            logger.info(f"🔄 开始下载图片转base64: {image_url[:100]}...")
            
            # 添加适当的请求头，处理OSS等云存储
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            }
            
            # 多次重试下载，处理网络波动
            max_retries = 3
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    logger.info(f"📥 下载尝试 {attempt + 1}/{max_retries}...")
                    
                    # 下载图片，增加超时时间
                    response = requests.get(
                        image_url, 
                        headers=headers,
                        timeout=60,  # 增加超时时间
                        allow_redirects=True,
                        stream=True  # 流式下载，处理大文件
                    )
                    
                    # 检查HTTP状态
                    if response.status_code == 403:
                        logger.error(f"❌ 图片访问被拒绝 (403): {image_url}")
                        raise ValueError(f"图片访问权限不足，可能是OSS签名过期或无效")
                    
                    response.raise_for_status()
                    
                    # 获取所有内容
                    image_content = response.content
                    
                    if len(image_content) == 0:
                        raise ValueError("下载的图片内容为空")
                    
                    logger.info(f"✅ 图片下载成功，大小: {len(image_content)} bytes")
                    break
                    
                except requests.exceptions.RequestException as e:
                    last_exception = e
                    logger.warning(f"⚠️ 下载失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(2 ** attempt)  # 指数退避
                        continue
                    else:
                        raise ValueError(f"图片下载失败，已重试{max_retries}次: {e}")
            
            # 尝试多种方式解码图片
            image = None
            
            # 方法1: 使用OpenCV解码
            try:
                image_array = np.frombuffer(image_content, np.uint8)
                image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                if image is not None:
                    logger.info(f"✅ OpenCV解码成功，尺寸: {image.shape}")
                else:
                    logger.warning("⚠️ OpenCV解码失败，尝试其他方法")
            except Exception as e:
                logger.warning(f"⚠️ OpenCV解码异常: {e}")
            
            # 方法2: 如果OpenCV失败，尝试PIL
            if image is None and PIL_AVAILABLE:
                try:
                    import io
                    
                    pil_image = PILImage.open(io.BytesIO(image_content))
                    # 转换为RGB格式
                    if pil_image.mode != 'RGB':
                        pil_image = pil_image.convert('RGB')
                    
                    # 转换为OpenCV格式
                    image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                    logger.info(f"✅ PIL解码成功，尺寸: {image.shape}")
                    
                except Exception as e:
                    logger.warning(f"⚠️ PIL解码也失败: {e}")
            elif image is None and not PIL_AVAILABLE:
                logger.warning("⚠️ PIL不可用，跳过PIL解码尝试")
            
            # 如果所有解码方法都失败
            if image is None:
                # 作为最后手段，直接base64编码原始数据
                logger.warning(f"⚠️ 图片解码失败，直接编码原始数据")
                base64_data = base64.b64encode(image_content).decode('utf-8')
                
                # 尝试根据内容推断MIME类型
                if image_content.startswith(b'\xFF\xD8\xFF'):
                    mime_type = "image/jpeg"
                elif image_content.startswith(b'\x89PNG'):
                    mime_type = "image/png"
                elif image_content.startswith(b'RIFF') and b'WEBP' in image_content[:12]:
                    mime_type = "image/webp"
                else:
                    mime_type = "image/jpeg"  # 默认
                
                logger.info(f"✅ 原始数据Base64编码完成，类型: {mime_type}")
                return base64_data, mime_type
            
            # 重新编码为JPEG格式确保兼容性
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
            success, buffer = cv2.imencode('.jpg', image, encode_params)
            
            if not success:
                raise ValueError("无法重新编码图像为JPEG格式")
            
            # 转换为base64
            base64_data = base64.b64encode(buffer).decode('utf-8')
            mime_type = "image/jpeg"
            
            logger.info(f"✅ Base64转换成功，编码长度: {len(base64_data)} 字符")
            return base64_data, mime_type
            
        except Exception as e:
            logger.error(f"❌ Base64转换失败: {e}")
            # 提供更详细的错误信息
            if "403" in str(e) or "Forbidden" in str(e):
                raise ValueError(f"图片访问权限不足，请检查OSS配置或签名URL是否有效: {e}")
            elif "timeout" in str(e).lower():
                raise ValueError(f"图片下载超时，请检查网络连接: {e}")
            else:
                raise ValueError(f"图片Base64转换失败: {e}")
    
    @staticmethod  
    def make_public_url(image_url: str) -> str:
        """
        尝试生成公共访问URL（去除认证参数）
        这是一个备选方案，需要OSS桶配置公共读权限
        """
        # 如果已经是公共URL（没有认证参数），直接返回
        parsed = urllib.parse.urlparse(image_url)
        query_params = urllib.parse.parse_qs(parsed.query)
        
        # 检查是否包含OSS认证参数
        auth_params = ['OSSAccessKeyId', 'Expires', 'Signature']
        has_auth = any(param in query_params for param in auth_params)
        
        if not has_auth:
            logger.info(f"🔓 URL已经是公共访问格式")
            return image_url
        
        # 移除认证参数，生成公共URL
        public_url = urllib.parse.urlunparse((
            parsed.scheme,
            parsed.netloc,
            parsed.path,
            '', '', ''
        ))
        
        logger.info(f"🔓 生成公共URL: {image_url[:50]}... -> {public_url[:50]}...")
        return public_url

class APIImageProcessor:
    """针对不同API的图片处理器"""
    
    def __init__(self):
        self.adapter = ImageAdapter()
    
    def process_for_seedance(self, image_url: str) -> Dict[str, Any]:
        """为Seedance API处理图片 - 智能降级策略"""
        try:
            # 优先尝试清理URL（适用于公共桶）
            clean_url = self.adapter.clean_oss_url(image_url)
            return {
                'success': True,
                'type': 'url',
                'data': clean_url,
                'original_url': image_url,
                'strategy': 'clean_url'
            }
        except Exception as e:
            logger.warning(f"⚠️ Seedance URL清理失败，尝试Base64降级: {e}")
            # 降级到Base64编码（适用于私有桶）
            try:
                base64_data, mime_type = self.adapter.convert_to_base64(image_url)
                return {
                    'success': True,
                    'type': 'base64',
                    'data': base64_data,
                    'mime_type': mime_type,
                    'original_url': image_url,
                    'strategy': 'base64_fallback'
                }
            except Exception as fallback_e:
                logger.error(f"❌ Seedance Base64降级也失败: {fallback_e}")
                return {
                    'success': False,
                    'error': f"URL清理失败: {e}, Base64降级失败: {fallback_e}",
                    'original_url': image_url
                }
    
    def process_for_veo3(self, image_url: str) -> Dict[str, Any]:
        """为Veo3 API处理图片"""
        try:
            # Veo3需要base64
            base64_data, mime_type = self.adapter.convert_to_base64(image_url)
            return {
                'success': True,
                'type': 'base64',
                'data': base64_data,
                'mime_type': mime_type,
                'original_url': image_url
            }
        except Exception as e:
            logger.error(f"❌ Veo3图片处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'original_url': image_url
            }
    
    def process_for_kling(self, image_url: str) -> Dict[str, Any]:
        """为Kling API处理图片 - 智能降级策略"""
        try:
            # 优先尝试清理URL（适用于公共桶）
            clean_url = self.adapter.clean_oss_url(image_url)
            return {
                'success': True,
                'type': 'url',
                'data': clean_url,
                'original_url': image_url,
                'strategy': 'clean_url'
            }
        except Exception as e:
            logger.warning(f"⚠️ Kling URL清理失败，尝试Base64降级: {e}")
            # 降级到Base64编码（适用于私有桶）
            try:
                base64_data, mime_type = self.adapter.convert_to_base64(image_url)
                return {
                    'success': True,
                    'type': 'base64',
                    'data': base64_data,
                    'mime_type': mime_type,
                    'original_url': image_url,
                    'strategy': 'base64_fallback'
                }
            except Exception as fallback_e:
                logger.error(f"❌ Kling Base64降级也失败: {fallback_e}")
                return {
                    'success': False,
                    'error': f"URL清理失败: {e}, Base64降级失败: {fallback_e}",
                    'original_url': image_url
                }
    
    def process_for_api(self, image_url: str, api_type: str) -> Dict[str, Any]:
        """根据API类型自动处理图片"""
        api_processors = {
            'seedance': self.process_for_seedance,
            'volcengine': self.process_for_veo3,  # 火山引擎改用Base64策略
            'veo3': self.process_for_veo3,
            'kling': self.process_for_kling,
            'openai': self.process_for_veo3,  # OpenAI也支持base64
        }
        
        processor = api_processors.get(api_type.lower())
        if not processor:
            logger.warning(f"⚠️ 未知API类型: {api_type}，使用默认URL清理")
            return self.process_for_seedance(image_url)  # 默认使用URL清理
        
        logger.info(f"🎯 为 {api_type.upper()} API 处理图片")
        return processor(image_url)

# 全局处理器实例
image_processor = APIImageProcessor()

def process_image_for_api(image_url: str, api_type: str) -> Dict[str, Any]:
    """
    通用图片处理入口函数
    
    Args:
        image_url: 原始图片URL
        api_type: API类型 ('seedance', 'veo3', 'kling', 'volcengine', 'openai')
        
    Returns:
        {
            'success': bool,
            'type': 'url' | 'base64',
            'data': str,  # 处理后的URL或base64数据
            'mime_type': str,  # 仅base64时有效
            'original_url': str,
            'error': str  # 仅失败时有效
        }
    """
    return image_processor.process_for_api(image_url, api_type) 