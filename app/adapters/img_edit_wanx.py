import asyncio
import httpx
from typing import Any, Dict
from app.config.settings import settings

class ImgEditError(RuntimeError):
    """图像编辑任务错误"""
    pass


async def edit_image(
    image_url: str,
    prompt: str,
    function: str = "description_edit",
    model: str = "wanx2.1-imageedit",
    *,
    poll_interval: float = 1.0,
    max_poll_time: float = 900.0,
    max_retries: int = 3
) -> Dict[str, Any]:
    """
    图像编辑 API 调用 + 任务状态轮询
    reference : https://help.aliyun.com/zh/model-studio/wanx-image-edit-api-reference?spm=a2c4g.11186623.help-menu-2400256.d_2_2_2.3eca6ff2KRLd8y
    参数：
        image_url: 输入图像链接
        prompt: 目标风格描述
        function: 编辑类型（默认为 stylization_all）
            stylization_all：全局风格化，当前支持2种风格。风格和提示词技巧

            stylization_local：局部风格化，当前支持8种风格。风格和提示词技巧

            description_edit：指令编辑。通过指令即可编辑图像，简单编辑任务优先推荐这种方式。提示词技巧

            description_edit_with_mask：局部重绘。需要指定编辑区域，适合对编辑范围有精确控制的场景。提示词技巧

            remove_watermark：去文字水印。提示词技巧

            expand：扩图。提示词技巧

            super_resolution：图像超分。提示词技巧

            colorization：图像上色。提示词技巧

            doodle：线稿生图。提示词技巧

            control_cartoon_feature：垫图，当前仅支持卡通形象。提示词技巧
    返回：{"prompt":..., "task_id":..., "task_status":..., "image_url":...}
    """
    post_url = settings.img_edit_base_url
    status_url = settings.img_edit_task_base_url
    headers = {
        "X-DashScope-Async": "enable",
        "Authorization": f"Bearer {settings.img_edit_api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "input": {
            "function": function,
            "prompt": prompt,
            "base_image_url": image_url
        },
        "parameters": {"n": 1}
    }

    attempt = 0
    while attempt < max_retries:
        attempt += 1
        try:
            async with httpx.AsyncClient() as client:
                resp = await client.post(post_url, headers=headers, json=payload)
                resp.raise_for_status()
                result = resp.json()
        except Exception as e:
            if attempt >= max_retries:
                raise ImgEditError(f"[尝试 {attempt}] 提交失败: {e}")
            print(f"[尝试 {attempt}] 提交失败，重试中... ({e})")
            continue

        task_id = result.get("output", {}).get("task_id", "")
        if not task_id:
            raise ImgEditError("未返回 task_id")

        # 开始轮询状态
        elapsed = 0.0
        while elapsed < max_poll_time:
            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

            try:
                async with httpx.AsyncClient() as client:
                    r = await client.get(f"{status_url}/{task_id}", headers={
                        "Authorization": f"Bearer {settings.img_edit_api_key}"
                    })
                    r.raise_for_status()
                    status = r.json().get("output", {})
            except Exception as e:
                print(f"状态查询失败（继续尝试）: {e}")
                continue

            state = status.get("task_status", "UNKNOWN")
            print(f"[图像编辑任务 {task_id}] 状态: {state}，等待 {int(elapsed)}s")
            if state == "SUCCEEDED":
                results = status.get("results", [])
                image_url = results[0]["url"] if results else ""
                return {
                    "prompt": prompt,
                    "task_id": task_id,
                    "task_status": state,
                    "image_url": image_url
                }
            elif state in {"FAILED", "CANCELED"}:
                print("任务失败或取消。错误信息：",
                      status.get("code", ""), status.get("message", ""))
                break

        print(f"[尝试 {attempt}] 编辑失败或超时，准备重试...")

    raise ImgEditError(f"图像编辑任务未成功（尝试 {max_retries} 次后放弃）")
