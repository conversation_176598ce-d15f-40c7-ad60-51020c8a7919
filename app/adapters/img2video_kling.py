import os
import asyncio
import httpx
import traceback
from typing import Any, Dict, Optional
from openai import OpenAI
from app.config.settings import settings
import time
import jwt

class Img2VideoError(RuntimeError):
    """图生视频生成失败"""
    pass

def get_api_token(access_key: str, secret_key: str) -> str:
    """
    生成可灵开放平台API鉴权token
    
    Args:
        access_key: 访问密钥ID
        secret_key: 访问密钥密码
        
    Returns:
        str: 生成的JWT token
    """
    
    headers = {
        "alg": "HS256", 
        "typ": "JWT"
    }
    
    payload = {
        "iss": access_key,
        "exp": int(time.time()) + 1800,  # 30分钟有效期
        "nbf": int(time.time()) - 5  # 生效时间为当前时间前5秒
    }
    
    token = jwt.encode(payload, secret_key, headers=headers)
    return token


async def image_to_video(
    prompt: str,
    img_url: str,
    caption: Optional[str] = None,
    *,
    model_name: str = "kling-v1-6",
    mode: str = "std",
    poll_interval: float = 1.0,
    max_poll_time: float = 600.0,
    max_retries: int = 3
) -> Dict[str, Any]:
    """
    图生视频生成：从图片和文本生成视频，并轮询查询状态。

    返回：{"prompt":..., "task_id":..., "video_url":..., "task_status":...}
    """
    duration = settings.video_duration
    # 1. VLM / LLM 客户端
    vlm_client = OpenAI(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        base_url=os.getenv("DASHSCOPE_BASE_URL")
    )
    llm_client = OpenAI(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        base_url=os.getenv("DASHSCOPE_BASE_URL")
    )

    # 2. 如果 caption 为空，先用 VLM 打图像 caption
    if not caption:
        try:
            resp = vlm_client.chat.completions.create(
                model="qwen-vl-max-latest",
                messages=[
                    {"role": "system", "content": [{"type": "text", "text": "You are a helpful assistant."}]},
                    {"role": "user", "content": [
                        {"type": "image_url", "image_url": {"url": img_url}},
                        {"type": "text", "text": "请一句话描述图中场景。"}
                    ]}
                ]
            )
            caption = resp.choices[0].message.content.strip()
        except Exception:
            tb = traceback.format_exc()
            raise Img2VideoError(f"调用 VLM 获取 caption 失败：\n{tb}")

    # 3. 用 LLM 合并用户 prompt 与 caption
    try:
        merge_prompt = (
            f"请将以下用户意图和图片描述合成为适合视频生成的简洁中文提示：\n"
            f"用户意图：{prompt}\n图片描述：{caption}"
        )
        mresp = llm_client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": merge_prompt}
            ]
        )
        final_prompt = mresp.choices[0].message.content.strip()
    except Exception:
        tb = traceback.format_exc()
        raise Img2VideoError(f"调用 LLM 合并提示词失败：\n{tb}")
    
    print(f"[INFO] 最终视频描述：{prompt} --> {final_prompt} ")

    # 4. 生成API Token
    api_token = get_api_token(settings.keling_api_key, settings.keling_secret_key)
    
    # 5. 准备调用图生视频 API
    post_url = "https://api-beijing.klingai.com/v1/videos/image2video"
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "model_name": model_name,
        "mode": mode,
        "duration": duration,
        "image": img_url,
        "prompt": final_prompt,
        "cfg_scale": 0.5
    }

    # 6. 提交任务（最多重试 max_retries 次）
    async with httpx.AsyncClient() as client:
        result = None
        for attempt in range(1, max_retries + 1):
            try:
                print(f"[img2video] 第{attempt}次提交 → URL={post_url}\n  payload={payload}")
                resp = await client.post(post_url, headers=headers, json=payload)
                print(f"[img2video] HTTP {resp.status_code}, body={resp.text}")
                resp.raise_for_status()
                result = resp.json()
                break
            except Exception as e:
                print(f"[img2video] 第{attempt}次失败：{e!r}")
                traceback.print_exc()
                if attempt == max_retries:
                    raise Img2VideoError(f"[尝试 {attempt}] 请求失败: {e}")
                await asyncio.sleep(poll_interval)

    if not result or result['code'] != 0:
        raise Img2VideoError(f"任务提交异常，返回体：{result!r}")

    task_id = result["data"]["task_id"]
    print(f"[img2video] 任务提交成功，task_id={task_id}")

    # 7. 轮询任务状态，输出定制化日志
    elapsed = 0.0
    async with httpx.AsyncClient() as client:
        while elapsed < max_poll_time:
            try:
                sresp = await client.get(f"{post_url}/{task_id}", headers=headers)
                data = sresp.json()
                # print(f"[img2video] 轮询任务状态，返回体：{data!r}")
            except Exception:
                data = {}

            if not data or data.get("code") != 0:
                raise Img2VideoError(f"查询任务状态异常，返回体：{data!r}")

            task_data = data.get("data", {})
            state = task_data.get("task_status", "UNKNOWN")
            # 定制化日志输出格式
            print(f"[图生视频任务 {task_id}] 状态: {state}，等待 {int(elapsed)}s")
            
            if state == "succeed":
                videos = task_data.get("task_result", {}).get("videos", [])
                if videos and len(videos) > 0:
                    return {
                        "prompt": final_prompt,
                        "task_id": task_id,
                        "task_status": "SUCCEEDED",
                        "video_url": videos[0].get("url", "")
                    }
                    
            if state == "failed":
                raise Img2VideoError(f"任务失败: {task_data!r}")

            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

    raise Img2VideoError(f"超时 {max_poll_time}s 未完成（共尝试 {max_retries} 次）")
