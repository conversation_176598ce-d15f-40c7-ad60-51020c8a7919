import httpx
from typing import Any, Dict
from app.config.settings import settings
import asyncio
import time
from typing import Dict, Any
from google import genai 
from google.genai import types
from PIL import Image
from io import BytesIO
import logging
import requests
from pathlib import Path
import os
import base64
from app.adapters.oss_upload import upload_image_to_oss, upload_and_get_signed_url

class ImgEditError(RuntimeError):
    """图像编辑任务错误"""
    pass


class ImageEditor:
    def __init__(self, api_key: str):
        self.client = genai.Client(api_key=api_key)
        self.logger = logging.getLogger(__name__)
        # 确保输出目录存在
        os.makedirs("data/image_edit", exist_ok=True)

    async def edit_image(
        self,
        image_url: str,      # 输入图片的URL或本地文件路径
        prompt: str,         # 图片编辑的提示词
        poll_interval: float = 1.0,    # 轮询任务状态的时间间隔(秒)
        max_poll_time: float = 900.0,  # 最大等待时间(秒)
        max_retries: int = 3           # 任务失败时的最大重试次数
    ) -> Dict[str, Any]:    # 返回包含任务信息的字典
        """
        异步编辑图片
        
        Args:
            image_url: 输入图片URL或本地路径
            prompt: 编辑提示词
            poll_interval: 轮询间隔时间(秒)
            max_poll_time: 最大等待时间(秒)
            max_retries: 最大重试次数
            
        Returns: #TODO: task_id not finish
            Dict包含prompt、task_id、task_status、image_url、image_binary和response_text
        """
        start_time = time.time()
        retries = 0
        
        while retries < max_retries:
            try:
                # 打开图片 - 支持URL和本地路径
                if image_url.startswith(('http://', 'https://')):
                    # 处理URL图片
                    response = requests.get(image_url)
                    response.raise_for_status()
                    image = Image.open(BytesIO(response.content))
                else:
                    # 处理本地路径
                    image_path = Path(image_url)
                    if not image_path.exists():
                        raise FileNotFoundError(f"找不到图片文件: {image_url}")
                    image = Image.open(image_path)
                
                # 生成内容
                response = self.client.models.generate_content(
                    model="gemini-2.0-flash-preview-image-generation",
                    contents=[prompt, image],
                    config=types.GenerateContentConfig(
                        response_modalities=['TEXT', 'IMAGE']
                    )
                )
                
                # 处理响应
                result = {
                    "prompt": prompt,
                    "task_id": str(int(time.time())),  # 使用时间戳作为任务ID
                    "task_status": "success",
                    "image_url": None,
                    "image_binary": None,
                    "response_text": response.text if hasattr(response, 'text') else ""
                }
                
                for part in response.candidates[0].content.parts:
                    if part.inline_data is not None:
                        # 保存生成的图片并返回URL
                        image_binary = part.inline_data.data
                        output_image = Image.open(BytesIO(image_binary))
                        output_path = f"data/image_edit/output_{result['task_id']}.png"
                        output_image.save(output_path)
                        result["image_url"] = upload_and_get_signed_url(output_path)
                        result["image_binary"] = base64.b64encode(image_binary).decode('utf-8')
                        return result
                        
                return result
                
            except Exception as e:
                self.logger.error(f"处理失败: {str(e)}")
                retries += 1
                if retries >= max_retries:
                    return {
                        "prompt": prompt,
                        "task_id": None,
                        "task_status": "failed",
                        "image_url": None,
                        "image_binary": None,
                        "response_text": str(e)
                    }
                    
                # 检查是否超时
                if time.time() - start_time > max_poll_time:
                    return {
                        "prompt": prompt,
                        "task_id": None,
                        "task_status": "timeout",
                        "image_url": None,
                        "image_binary": None,
                        "response_text": "任务超时"
                    }
                    
                # 等待后重试
                await asyncio.sleep(poll_interval)

# 使用示例
async def main():
    # 初始化编辑器
    editor = ImageEditor(api_key="AIzaSyCY7sNRZhBAPuRaewyEU4vDFcmOqyUQFYc")
    
    # 调用编辑函数
    result = await editor.edit_image(
        image_url="media/generated/201f4719-5abf-439e-a7f1-b3225c6e15aa3991781873.png",
        prompt="把猫变成白色",
        poll_interval=1.0,
        max_poll_time=900.0,
        max_retries=3
    )
    
    # 打印结果
    print("编辑结果:", result["image_url"])


# 为router提供的直接函数接口
async def edit_image(
    image_url: str,
    prompt: str,
    poll_interval: float = 1.0,
    max_poll_time: float = 900.0,
    max_retries: int = 3
) -> Dict[str, Any]:
    """
    图片编辑的直接函数接口，供router调用
    """
    # 从设置中获取API密钥
    api_key = settings.gemini_api_key
    if not api_key or api_key == "your_gemini_api_key_here":
        raise ImgEditError("Gemini API密钥未配置")
    
    # 创建编辑器实例并调用编辑方法
    editor = ImageEditor(api_key=api_key)
    return await editor.edit_image(
        image_url=image_url,
        prompt=prompt,
        poll_interval=poll_interval,
        max_poll_time=max_poll_time,
        max_retries=max_retries
    )


# 运行示例
if __name__ == "__main__":
    asyncio.run(main())