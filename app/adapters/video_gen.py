# app/adapters/video_gen.py

import asyncio
import time
from typing import Any, Dict

import httpx
from app.config.settings import settings

class VideoGenError(RuntimeError):
    """Video generation adapter error"""
    pass

async def generate_video(
    prompt: str,
    *,
    size: str = "1280*720",
    duration: int = 5,
    model: str = "wanx2.1-t2v-turbo", # wanx2.1-t2v-plus（0.70元/秒），wanx2.1-t2v-turbo（0.24元/秒） 
    poll_interval: float = 1.0,      # 轮询间隔（秒）
    max_poll_time: float = 600.0,     # 轮询最长等待时间（秒）
    max_retries: int = 3             # 失败后最大重试次数
) -> Dict[str, Any]:
    """
    文本生成视频，异步调用并轮询任务状态。
    reference: https://help.aliyun.com/zh/model-studio/wanx-video-generation-api-reference?spm=a2c4g.11186623.help-menu-2400256.d_2_2_2.3eca6ff2KRLd8y
    返回:
        {
            "prompt": prompt,
            "task_id": "...",
            "task_status": "...",
            "video_url": "..."
        }
    抛出 VideoGenError on unrecoverable failure.
    """
    post_url  = settings.video_gen_base_url
    status_url = settings.video_task_base_url
    headers = {
        "X-DashScope-Async": "enable",
        "Authorization": f"Bearer {settings.video_gen_api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "input": {"prompt": prompt},
        "parameters": {"size": size, "duration": duration}
    }

    attempt = 0
    while attempt < max_retries:
        attempt += 1
        # 1) 发送生成请求
        try:
            async with httpx.AsyncClient() as client:
                resp = await client.post(post_url, headers=headers, json=payload)
                resp.raise_for_status()
                data = resp.json()
        except Exception as e:
            if attempt >= max_retries:
                raise VideoGenError(f"[Attempt {attempt}] Generation request failed: {e}")
            else:
                print(f"[Attempt {attempt}] Generation request failed, retrying... ({e})")
                continue

        task = data.get("output", {})
        task_id     = task.get("task_id", "")
        task_status = task.get("task_status", "")
        if not task_id:
            # Maximum retry attempts exceeded, with error details
            error_code = status_data.get("code", "UNKNOWN")
            error_msg  = status_data.get("message", "Task failed for unknown reason")
            raise VideoGenError(f"Final failure: {error_code} - {error_msg}")

        # 2) 轮询查询状态
        elapsed = 0.0
        while elapsed < max_poll_time:
            async with httpx.AsyncClient() as client:
                try:
                    status_resp = await client.get(f"{status_url}/{task_id}", headers={
                        "Authorization": f"Bearer {settings.video_gen_api_key}"
                    })
                    status_resp.raise_for_status()
                    status_data = status_resp.json().get("output", {})
                except Exception as e:
                    print(f"Status query error ({e}), continuing polling...")
                    # 不计入重试次数，直接等待下一轮
                else:
                    state = status_data.get("task_status", "UNKNOWN")
                    print(f"[Task {task_id}] Status: {state}, Elapsed: {int(elapsed)}s")
                    if state == "SUCCEEDED":
                        video_url = status_data.get("video_url", "")
                        if not video_url:
                            raise VideoGenError(f"[Task {task_id}] Succeeded but no video_url returned")
                        return {
                            "prompt": prompt,
                            "task_id": task_id,
                            "task_status": state,
                            "video_url": video_url
                        }
                    if state in {"FAILED", "CANCELED"}:
                        error_code = status_data.get("code", "")
                        error_msg  = status_data.get("message", "")
                        print(f"[Task {task_id}] Status {state}, Error code: {error_code}, Message: {error_msg}, preparing to retry...")
                        break  # 跳出轮询，进入重试循环

            # 等待下一次轮询
            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

        # 如果超过 max_poll_time 或状态为 FAILED/CANCELED，下一轮重试
        print(f"[Attempt {attempt}] Task did not complete within {int(max_poll_time)}s or failed, retrying...")

    # Maximum retry attempts exceeded
    raise VideoGenError(f"Maximum retry attempts exceeded ({max_retries}), video generation failed")
