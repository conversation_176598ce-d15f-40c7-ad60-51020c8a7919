import os
import asyncio
import httpx
import traceback
from typing import Any, Dict, Optional
from openai import OpenAI
from app.config.settings import settings

class Img2VideoError(RuntimeError):
    """图生视频生成失败"""
    pass

async def image_to_video(
    prompt: str,
    img_url: str,
    caption: Optional[str] = None,
    *,
    resolution: str = "720P",
    model: str = "wanx2.1-i2v-turbo",
    poll_interval: float = 1.0,
    max_poll_time: float = 600.0,
    max_retries: int = 3
) -> Dict[str, Any]:
    """
    图生视频生成：从图片和文本生成视频，并轮询查询状态。
    如果未传入 caption，则先调用 VLM API 对图像打 caption。
    再调用 LLM 融合用户 prompt 与 image caption 生成新的 prompt。

    返回：{"prompt":..., "task_id":..., "video_url":..., "task_status":...}
    """

    # 1. VLM / LLM 客户端
    vlm_client = OpenAI(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        base_url=os.getenv("DASHSCOPE_BASE_URL")
    )
    llm_client = OpenAI(
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        base_url=os.getenv("DASHSCOPE_BASE_URL")
    )

    # 2. 如果 caption 为空，先用 VLM 打图像 caption
    if not caption:
        try:
            resp = vlm_client.chat.completions.create(
                model="qwen-vl-max-latest",
                messages=[
                    {"role": "system", "content": [{"type": "text", "text": "You are a helpful assistant."}]},
                    {"role": "user", "content": [
                        {"type": "image_url", "image_url": {"url": img_url}},
                        {"type": "text", "text": "请一句话描述图中场景。"}
                    ]}
                ]
            )
            caption = resp.choices[0].message.content.strip()
        except Exception:
            tb = traceback.format_exc()
            raise Img2VideoError(f"调用 VLM 获取 caption 失败：\n{tb}")

    # 3. 用 LLM 合并用户 prompt 与 caption
    try:
        merge_prompt = (
            f"请将以下用户意图和图片描述合成为适合视频生成的简洁中文提示：\n"
            f"用户意图：{prompt}\n图片描述：{caption}"
        )
        mresp = llm_client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": merge_prompt}
            ]
        )
        final_prompt = mresp.choices[0].message.content.strip()
    except Exception:
        tb = traceback.format_exc()
        raise Img2VideoError(f"调用 LLM 合并提示词失败：\n{tb}")

    print(f"[INFO] 最终视频描述：{final_prompt}")

    # 4. 准备调用图生视频 API
    post_url   = settings.img2video_base_url         # IMG2VIDEO_BASE_URL
    status_url = settings.img2video_task_base_url    # IMG2VIDEO_TASK_BASE_URL
    headers = {
        "X-DashScope-Async": "enable",
        "Authorization": f"Bearer {settings.img2video_api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "input": {"prompt": final_prompt, "img_url": img_url},
        "parameters": {"resolution": resolution, "prompt_extend": True}
    }

    # 5. 提交任务（最多重试 max_retries 次）
    async with httpx.AsyncClient() as client:
        result = None
        for attempt in range(1, max_retries + 1):
            try:
                print(f"[img2video] 第{attempt}次提交 → URL={post_url}\n  payload={payload}")
                resp = await client.post(post_url, headers=headers, json=payload)
                print(f"[img2video] HTTP {resp.status_code}, body={resp.text}")
                resp.raise_for_status()
                result = resp.json()
                break
            except Exception as e:
                print(f"[img2video] 第{attempt}次失败：{e!r}")
                traceback.print_exc()
                if attempt == max_retries:
                    raise Img2VideoError(f"[尝试 {attempt}] 请求失败: {e}")
                await asyncio.sleep(poll_interval)

    if not result or "output" not in result or not result["output"].get("task_id"):
        raise Img2VideoError(f"任务提交异常，返回体：{result!r}")

    task_id = result["output"]["task_id"]
    print(f"[img2video] 任务提交成功，task_id={task_id}")

    # 6. 轮询任务状态，输出定制化日志
    elapsed = 0.0
    async with httpx.AsyncClient() as client:
        while elapsed < max_poll_time:
            try:
                sresp = await client.get(f"{status_url}/{task_id}", headers=headers)
                data = sresp.json().get("output", {})
            except Exception:
                data = {}

            state = data.get("task_status", "UNKNOWN")
            # 定制化日志输出格式
            print(f"[图生视频任务 {task_id}] 状态: {state}，等待 {int(elapsed)}s")
            if state == "SUCCEEDED":
                return {
                    "prompt": final_prompt,
                    "task_id": task_id,
                    "task_status": state,
                    "video_url": data.get("video_url", "")
                }
            if state in {"FAILED", "CANCELED"}:
                raise Img2VideoError(f"任务 {state}: {data!r}")

            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

    raise Img2VideoError(f"超时 {max_poll_time}s 未完成（共尝试 {max_retries} 次）")
