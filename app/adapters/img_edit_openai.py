import os
import uuid
import base64
import tempfile
import asyncio
import httpx
from typing import Any, Dict
from openai import AsyncOpenAI
from app.adapters.oss_upload import upload_and_get_signed_url
from app.config.settings import settings

class OpenAIImageEditError(RuntimeError):
    """OpenAI 图像编辑 API 调用失败"""
    pass


async def edit_image(
    image_url: str,
    prompt: str,
    *,
    model: str = "gpt-image-1",
    size: str = "1024x1024",
    n: int = 1,
    timeout: float = 1200.0,
    max_retries: int = 3
) -> Dict[str, Any]:
    """
    使用 OpenAI 的 gpt-image-1 images.edit 接口对 image_url 对应的图片进行编辑。

    返回值举例：
    {
        "prompt": "用户传入的 prompt 文本",
        "task_id": "随机生成的 UUID",
        "task_status": "SUCCEEDED",
        "image_url": "/tmp/tmpabcd1234.png"   # 编辑后保存到本地临时文件的路径
    }
    """

    # 1. 从环境中读取 API Key and baseurl
    api_key = settings.openai_api_key
    base_url = settings.openai_base_url

    if not api_key:
        raise OpenAIImageEditError("缺少环境变量 OPENAI_API_KEY，请先设置。")

    # 3. 异步下载原图到本地临时文件
    try:
        async with httpx.AsyncClient(timeout=timeout) as http_client:
            resp = await http_client.get(image_url)
            resp.raise_for_status()
            img_bytes = resp.content
    except Exception as e:
        raise OpenAIImageEditError(f"下载原图失败: {e}")

    tmp_in = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
    tmp_in.write(img_bytes)
    tmp_in.flush()
    tmp_in.close()

    # 4. 异步调用 AsyncOpenAI.images.edit
    client = AsyncOpenAI(
        api_key=api_key,
        base_url=base_url,
        timeout=timeout,
        max_retries=max_retries
    )

    try:
        # 注意：OpenAI SDK 要求把文件句柄放进列表里，即使只有一张图
        with open(tmp_in.name, "rb") as f:
            result = await client.images.edit(
                model=model,
                image=[f],
                prompt=prompt,
                size=size,
                n=n
            )
    except Exception as e:
        raise OpenAIImageEditError(f"调用 OpenAI 图像编辑失败: {e}")

    # 5. 解析返回结果
    if not getattr(result, "data", None):
        raise OpenAIImageEditError("OpenAI 返回数据缺失 data 字段，无法继续。")
    item = result.data[0]

    # 5.1 如果返回了 URL，就异步下载该 URL 对应的图像
    if getattr(item, "url", None):
        try:
            async with httpx.AsyncClient(timeout=timeout) as http_client:
                r2 = await http_client.get(item.url)
                r2.raise_for_status()
                out_bytes = r2.content
        except Exception as e:
            raise OpenAIImageEditError(f"下载编辑后远程图像失败: {e}")

    # 5.2 否则如果返回了 b64_json，就直接 Base64 解码
    elif getattr(item, "b64_json", None):
        try:
            out_bytes = base64.b64decode(item.b64_json)
        except Exception as e:
            raise OpenAIImageEditError(f"Base64 解码失败: {e}")

    else:
        raise OpenAIImageEditError("OpenAI 返回的数据中既无 url 也无 b64_json，无法获取图像内容。")

    # 6. 将编辑后的字节写入本地临时文件，并返回该路径
    tmp_out = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
    tmp_out.write(out_bytes)
    tmp_out.flush()
    tmp_out.close()

    # 上传到 OSS 并获取签名 URL
    try:
        signed_url = upload_and_get_signed_url(tmp_out.name, object_prefix="tmp_imgs/")
    except Exception as e:
        raise OpenAIImageEditError(f"上传到 OSS 失败: {e}")

    return {
        "prompt": prompt,
        "task_id": str(uuid.uuid4()),
        "task_status": "SUCCEEDED",
        "image_url": signed_url
    }


# 如果想单独在命令行测试，可运行以下代码
if __name__ == "__main__":
    async def _test():
        ori = "https://ugen.oss-cn-beijing.aliyuncs.com/tmp_imgs/output_1748178785.png?OSSAccessKeyId=LTAI5tE3G5dKW4XgYNQTV62q&Expires=1752099713&Signature=9Bq9ZroAd9iH4Duoj%2Flxuro0jWU%3D"
        prompt = "change the cat to dog"
        try:
            res = await edit_image(ori, prompt)
            print("编辑完成，结果：", res)
        except Exception as e:
            print("编辑失败：", e)

    asyncio.run(_test())