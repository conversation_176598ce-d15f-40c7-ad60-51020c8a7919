# app/adapters/oss_upload.py
import os
import json
import tempfile
import urllib.parse
import logging

import oss2

# 获取模块 logger
logger = logging.getLogger(__name__)


def load_oss_config(config_path="oss_config.json"):
    with open(config_path, 'r') as f:
        return json.load(f)


def upload_and_get_signed_url(local_file_path, object_prefix="tmp_imgs/", expire_seconds=3600*24*180):
    """
    上传文件到 OSS 并生成签名 URL，最后将路径部分解码把 %2F 还原成 /
    """
    cfg = load_oss_config()
    auth = oss2.Auth(cfg["access_key_id"], cfg["access_key_secret"])
    bucket = oss2.Bucket(auth, cfg["endpoint"], cfg["bucket_name"])

    file_name = os.path.basename(local_file_path)
    object_name = f"{object_prefix}{file_name}"

    try:
        # 先把本地文件上传到 OSS
        bucket.put_object_from_file(object_name, local_file_path)
    except Exception as e:
        logger.error(f"OSS 上传失败: object_name={object_name}, error={e}")
        raise RuntimeError(f"OSS 上传失败: {e}") from e

    try:
        # 生成签名 URL
        signed_url = bucket.sign_url('GET', object_name, expire_seconds)
    except Exception as e:
        logger.error(f"OSS 签名 URL 生成失败: object_name={object_name}, error={e}")
        raise RuntimeError(f"签名 URL 生成失败: {e}") from e

    # 拆分并解码路径
    parsed = urllib.parse.urlsplit(signed_url)
    decoded_path = urllib.parse.unquote(parsed.path)

    # 强制 https
    scheme = 'https'
    rebuilt = urllib.parse.urlunsplit((
        scheme,
        parsed.netloc,
        decoded_path,
        parsed.query,
        parsed.fragment
    ))
    return rebuilt


def upload_image_to_oss(file):
    """
    将 Django 上传的 InMemoryUploadedFile 写到临时文件，
    用 oss2 推到 OSS，然后删盘并返回“解码后”的签名 URL。
    如果过程中出现任何错误，会记录日志并抛出异常。
    """
    # 将上传文件写入临时文件
    suffix = os.path.splitext(file.name)[1]  # 保留扩展名
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
        try:
            for chunk in file.chunks():
                tmp.write(chunk)
            tmp_path = tmp.name
        except Exception as e:
            logger.error(f"写入临时文件失败: filename={file.name}, error={e}")
            raise RuntimeError(f"写入临时文件失败: {e}") from e

    try:
        signed_url = upload_and_get_signed_url(tmp_path)
        logger.info("----------------------------------> signed_url = ", signed_url)
    except Exception:
        # upload_and_get_signed_url 已经记录了日志并抛出了 RuntimeError
        raise
    finally:
        # 清理临时文件
        try:
            os.remove(tmp_path)
        except Exception as e:
            logger.warning(f"临时文件删除失败: path={tmp_path}, error={e}")

    return signed_url
