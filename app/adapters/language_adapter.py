import json
import re
from typing import List, Dict, Any
from openai import OpenAI
from app.config.settings import settings

# 初始化客户端 - 延迟初始化，避免导入时的环境变量问题
client = None

def _get_client():
    """获取OpenAI客户端，延迟初始化"""
    global client
    if client is None:
        client = OpenAI(
            api_key=settings.dashscope_api_key,
            base_url=settings.dashscope_base_url,
        )
    return client

def call_qwen_language_conversion(
    history_messages: List[Dict[str, str]],
    input_text: str,
    model: str = "qwen-plus"
) -> dict:
    """
    通过 LLM 进行语种转换
    
    Args:
        history_messages: 用户历史消息列表
        input_text: 待转换的文本
        model: 使用的模型名称
        
    Returns:
        dict: 包含转换结果的字典
    """
    # 构造prompt
    history_json = json.dumps(history_messages, ensure_ascii=False)
    prompt = settings.LANGUAGE_CONVERSION_PROMPT.format(
        history_messages=history_json,
        input_text=input_text
    )

    try:
        completion = _get_client().chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a language conversion assistant, outputting only JSO<PERSON>."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.0,
        )
        raw = completion.choices[0].message.content.strip()
        
        # JSON提取和清理逻辑（参考call_qwen_multi_reference_detection）
        json_block_match = re.search(r"```(?:json)?\s*([\s\S]*?)```", raw)
        if json_block_match:
            json_str = json_block_match.group(1).strip()
        else:
            m = re.search(r"\{[\s\S]*\}", raw)
            if not m:
                raise ValueError("未找到 JSON")
            json_str = m.group(0)
        
        # 清理JSON格式
        json_str = re.sub(r'[\u2018\u2019]', "'", json_str)
        json_str = re.sub(r'[\u201c\u201d]', '"', json_str)
        json_str = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', json_str)
        json_str = re.sub(r',\s*(?=[}\]])', '', json_str)
        
        result = json.loads(json_str)
        
        # 验证返回结果的结构
        if not isinstance(result, dict):
            raise ValueError("返回结果不是字典格式")
            
        # 确保包含必要字段
        if "converted_text" not in result:
            result["converted_text"] = input_text
        if "detected_language" not in result:
            result["detected_language"] = "未知"
        if "confidence" not in result:
            result["confidence"] = "low"
            
        return result
        
    except Exception as e:
        print(f"❌ 语种转换失败: {e}")
        # 出错时返回原文，避免破坏用户体验
        return {
            "detected_language": "未知",
            "converted_text": input_text,
            "confidence": "low",
            "error": str(e)
        }

def convert_response_language(
    history_messages: List[Dict[str, str]], 
    response_text: str
) -> str:
    """
    将响应文本转换为与用户历史消息一致的语种
    
    Args:
        history_messages: 用户历史消息列表
        response_text: 待转换的响应文本
        
    Returns:
        str: 转换后的文本
    """
    # 如果历史消息为空或响应文本为空，直接返回原文
    if not history_messages or not response_text.strip():
        return response_text
    
    # 过滤出用户消息（role="user"）
    user_messages = [
        msg for msg in history_messages 
        if msg.get("role") == "user" and msg.get("content", "").strip()
    ]
    # 如果没有用户消息，返回原文
    if not user_messages:
        return response_text
    
    try:
        result = call_qwen_language_conversion(user_messages, response_text)
        # 确保从结果字典中提取字符串，并提供默认值
        converted_text = result.get("converted_text", response_text)
        
        # 记录转换信息（用于调试）
        detected_lang = result.get("detected_language", "未知")
        confidence = result.get("confidence", "low")
        print(f"🌐 语种转换: {detected_lang} (置信度: {confidence})")
        
        # 始终返回一个字符串
        return converted_text if isinstance(converted_text, str) else response_text
        
    except Exception as e:
        print(f"❌ 语种转换出错，返回原文: {e}")
        return response_text

def extract_user_messages_from_history(history: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """
    从历史记录中提取用户消息，用于语种检测
    
    Args:
        history: 完整的历史记录
        
    Returns:
        List[Dict[str, str]]: 用户消息列表
    """
    user_messages = []
    
    if not isinstance(history, list):
        print("⚠️ 历史记录不是列表格式")
        return user_messages
    
    for msg in history:
        try:
            if not isinstance(msg, dict):
                continue
                
            role = msg.get("role")
            content = msg.get("content")
            
            if role == "user" and isinstance(content, str) and content.strip():
                user_messages.append({
                    "role": "user",
                    "content": content.strip()
                })
        except Exception as e:
            print(f"⚠️ 处理历史消息时出错: {e}")
            continue
    
    return user_messages

# 便捷函数，直接传入router的history格式
def auto_convert_response(
    history: List[Dict[str, str]], 
    response_text: str
) -> str:
    """
    根据用户历史消息，自动转换响应文本的语种
    """
    # 1. 检测用户最后一条消息的语种
    last_user_message = next(
        (msg['content'] for msg in reversed(history) if msg.get('role') == 'user' and msg.get('content')), 
        None
    )
    
    # 如果没有用户历史或响应为空，不转换
    if not last_user_message or not response_text:
        return response_text

    # 2. 调用LLM进行转换
    try:
        # 显式调用转换函数
        result = call_qwen_language_conversion(history, response_text)
        
        # 确保从结果字典中提取字符串，并提供默认值
        converted_text = result.get("converted_text", response_text)
        
        # 记录调试信息
        detected_lang = result.get("detected_language", "未知")
        confidence = result.get("confidence", "未知")
        print(f"🌐 语种转换: 检测到用户语言 '{detected_lang}' (置信度: {confidence}), 已转换响应。")

        # 始终返回一个字符串
        return converted_text if isinstance(converted_text, str) else response_text

    except Exception as e:
        print(f"❌ 语种转换服务调用出错，返回原文: {e}")
        return response_text 