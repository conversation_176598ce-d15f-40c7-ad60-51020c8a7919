import os
from celery import Celery
from django.conf import settings
import django

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "generator.settings")

# 初始化Django
django.setup()

app = Celery("visual_agent",
             broker=settings.CELERY_BROKER_URL,
             backend=settings.CELERY_RESULT_BACKEND)

# 更新配置 - 单队列架构
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_default_queue='default',  # 统一使用default队列
    
    # 简化配置 - 所有任务都使用同一队列
    task_routes={
        # 所有任务统一路由到default队列
        'execute_langchain_workflow': {'queue': 'default'},
        'execute_jobplan': {'queue': 'default'},
        'monitor_task_completion': {'queue': 'default'},
        'monitor_video_generation': {'queue': 'default'},
    },
    
    # 提高并发数以补偿失去的队列优先级
    worker_prefetch_multiplier=1,  # 保持1，确保任务分发均匀
    
    # 队列配置
    task_create_missing_queues=True,
    
    # 任务结果过期时间
    result_expires=3600,  # 1小时后过期
)

# 手动发现任务
app.autodiscover_tasks(['app'], force=True)

# 移除手动导入任务的部分，避免循环导入