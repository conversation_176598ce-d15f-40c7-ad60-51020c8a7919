import logging
import concurrent.futures
from django.core.cache import cache
from datetime import datetime
import json
import time


from app.celery_app import app
from app.core.graph import VisualGenerationGraph
from core.models import ChatMessage, TokenConsumption
from core.websocket_utils import (
    notify_task_started, notify_task_progress, 
    notify_task_completed, notify_task_failed
)

# 新增OCR和文案增强任务
from app.tools.ocr_text_enhance import ocr_enhancer

logger = logging.getLogger(__name__)

# 🔧 关键修复：为长时间任务添加数据库连接恢复机制
def safe_db_operation(operation_func, operation_name, max_retries=3):
    """安全执行数据库操作，包含重试机制"""
    for attempt in range(max_retries):
        try:
            from django.db import connections
            from django.db.utils import OperationalError, InterfaceError
            
            # 检查连接状态
            db_conn = connections['default']
            if db_conn.connection is None or not db_conn.is_usable():
                logger.warning(f"🔄 [DB] {operation_name} - 数据库连接异常，尝试重连 (尝试 {attempt + 1}/{max_retries})")
                db_conn.close()
                db_conn.connect()
            
            return operation_func()
            
        except (OperationalError, InterfaceError) as e:
            logger.error(f"❌ [DB] {operation_name} - 数据库操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
                # 强制关闭并重新连接
                from django.db import connections
                connections['default'].close()
            else:
                logger.error(f"❌ [DB] {operation_name} - 达到最大重试次数，操作失败")
                raise
        except Exception as e:
            logger.error(f"❌ [DB] {operation_name} - 非数据库错误: {e}")
            raise

@app.task(name="execute_jobplan")
def execute_jobplan(plan_id: str):
    """
    执行AI任务计划，支持代币预留和确认机制
    """
    try:
        # 1) 从缓存获取计划数据
        plan_data = cache.get(f"plan:{plan_id}")
        if plan_data is None:
            raise RuntimeError(f"找不到 plan_id={plan_id}")
        
        # 兼容旧格式和新格式
        if isinstance(plan_data, dict):
            plan = plan_data.get('plan')
            user_id = plan_data.get('user_id')
            session_id = plan_data.get('session_id')
        else:
            # 旧格式：直接是plan对象
            plan = plan_data
            user_id = None
            session_id = None
        
        logger.info(f"▶▶▶▶▶▶ EXECUTE_JOBPLAN START, plan_id={plan_id}, user_id={user_id}")
        logger.info(f"Plan类型: {type(plan)}, 是否有nodes属性: {hasattr(plan, 'nodes')}")
        
        # 如果plan是字典，需要重新构造JobPlan对象
        if isinstance(plan, dict):
            logger.info("Plan是字典，尝试重新构造JobPlan对象")
            # 这里应该有相应的反序列化逻辑
            # 临时处理：直接使用字典数据
            nodes_data = plan.get('nodes', [])
            logger.info(f"节点数据: {nodes_data}")
        else:
            logger.info(f"Plan对象正常，nodes数量: {len(plan.nodes) if hasattr(plan, 'nodes') else 'no nodes'}")
        
        # 2) 执行AI任务 - TODO: 需要替换QwenRouter
        # router = QwenRouter()  # TODO: 考虑传入history信息
        # result = async_to_sync(router.execute)(plan)
        
        # 临时返回错误，直到QwenRouter被正确实现
        result = {
            "error": "QwenRouter not implemented",
            "logs": ["QwenRouter execution not available"],
            "success": False,
            "tokens_consumed": 0
        }
        
        logger.info(f"Plan {plan_id} 执行完毕，输出：{result}")
        
        # 3) 根据执行结果直接消费代币（简化逻辑）
        if user_id:
            try:
                from core.models import User, ChatSession
                from core.services.token_service import TokenService
                
                user = User.objects.get(id=user_id)
                chat_session = None
                if session_id:
                    try:
                        chat_session = ChatSession.objects.get(id=session_id)
                    except ChatSession.DoesNotExist:
                        logger.warning(f"Chat session not found: {session_id}")
                
                # 分析执行结果
                success_results = []
                
                if not result.get("error") and (result.get("video_url") or result.get("image_url")):
                    # 执行成功，构建成功结果
                    nodes_to_check = None
                    if hasattr(plan, 'nodes'):
                        nodes_to_check = plan.nodes
                    elif isinstance(plan, dict) and 'nodes' in plan:
                        nodes_to_check = plan['nodes']
                    
                    if nodes_to_check:
                        for node in nodes_to_check:
                            node_tool = node.tool if hasattr(node, 'tool') else node.get('tool')
                            success_results.append({
                                "node_type": node_tool,
                                "result_url": result.get("video_url") or result.get("image_url") or "",
                                "prompt": f"Plan {plan_id} execution"
                            })
                    
                    # 直接消费代币（仅对成功的服务）
                    consume_success, total_consumed, error_msg, consumption_records = TokenService.consume_tokens_after_execution(
                        user, plan, success_results, session=chat_session
                    )
                    
                    if consume_success and total_consumed > 0:
                        logger.info(f"异步任务成功，消费 {total_consumed} 代币，用户 {user.email} 余额: {user.tokens}")
                        result["tokens_consumed"] = total_consumed
                        result["consumption_count"] = len(consumption_records)
                    elif not consume_success:
                        logger.error(f"代币消费失败: {error_msg}")
                        result["token_error"] = error_msg
                else:
                    # 执行失败，不消费代币
                    logger.info(f"任务执行失败，不消费代币: {result.get('error', 'Unknown error')}")
                    result["tokens_consumed"] = 0
                        
            except Exception as token_error:
                logger.error(f"代币处理失败: {token_error}")
                result["token_error"] = str(token_error)
                # 代币处理失败不应该影响主要结果返回
        
        return result
        
    except Exception as e:
        logger.error(f"Celery任务执行失败 {plan_id}: {e}")
        
        # 任务完全失败时，不消费代币（新逻辑下无需退还）
        logger.info(f"Celery任务失败，不消费代币: {str(e)}")
        
        # 返回错误结果
        return {
            "error": str(e),
            "logs": [f"Celery task execution failed: {str(e)}"],
            "success": False,
            "tokens_consumed": 0
        }

# 邮件发送任务已移除 - 前端现在可以直接通过轮询获取结果
# @app.task(bind=True)
# def send_video_completion_notification(self, user_email, video_url, prompt, session_id):
#     """
#     发送视频生成完成通知邮件的Celery任务 - 已废弃
#     """
#     pass

@app.task(bind=True)
def monitor_task_completion(self, monitor_data):
    """
    通用任务监控函数，支持图片编辑和视频生成
    """
    try:
        celery_task_id = monitor_data['celery_task_id']
        session_id = monitor_data['session_id']
        task_type = monitor_data.get('task_type', 'image_editing')
        
        # 检查原始任务状态
        from celery.result import AsyncResult
        async_result = AsyncResult(celery_task_id)
        
        if async_result.ready():
            if async_result.successful():
                result = async_result.result
                image_url = result.get('image_url')
                video_url = result.get('video_url')
                
                if image_url or video_url:
                    # 更新数据库中的消息
                    try:
                        from core.models import ChatSession
                        chat_session = ChatSession.objects.get(id=session_id)
                        
                        # 添加完成消息
                        if image_url:
                            content = "Image generated"
                        elif video_url:
                            content = "Video generated"
                        else:
                            content = "Your request has been completed!"
                            
                        ChatMessage.objects.create(
                            session=chat_session,
                            type='assistant',
                            content=content,
                            image_url=image_url,
                            video_url=video_url,
                            metadata={
                                'task_id': celery_task_id,
                                'tokens_consumed': result.get('tokens_consumed', 0),
                                'task_type': task_type,
                                'direct_consumption': True
                            }
                        )
                        logger.info(f"{task_type} 完成消息已保存到会话 {session_id}")
                        
                        # 邮件通知已移除 - 前端现在可以直接获取结果
                        logger.info(f"✅ {task_type} 结果已保存到数据库，前端可通过轮询获取")
                            
                    except Exception as e:
                        logger.error(f"保存{task_type}完成消息失败: {e}")
                        
                    logger.info(f"{task_type} 监控完成: {celery_task_id}")
                    return True
            else:
                # 任务失败
                logger.error(f"{task_type} 任务失败: {celery_task_id}")
                
                # 失败通知邮件已移除 - 前端通过轮询可以获取失败状态
                logger.info(f"❌ {task_type} 任务失败，前端将通过轮询获取失败状态")
                    
                return False
        else:
            # 任务还在运行，重新调度监控
            max_retries = 40 if task_type == 'video_generation' else 20  # 视频生成等待更长时间
            self.retry(countdown=30, max_retries=max_retries)
            
    except Exception as e:
        logger.error(f"{task_type} 监控任务失败: {e}")
        return False

@app.task(bind=True)
def monitor_video_generation(self, monitor_data):
    """
    监控视频生成任务，完成后保存结果到数据库供前端轮询获取
    """
    try:
        celery_task_id = monitor_data['celery_task_id']
        session_id = monitor_data['session_id']
        
        # 检查原始任务状态
        from celery.result import AsyncResult
        async_result = AsyncResult(celery_task_id)
        
        if async_result.ready():
            if async_result.successful():
                result = async_result.result
                video_url = result.get('video_url')
                
                if video_url:
                    # 邮件通知已移除 - 前端现在可以直接获取结果
                    logger.info("✅ 视频生成完成，前端可通过轮询获取结果")
                    
                    # 更新数据库中的消息
                    try:
                        from core.models import ChatSession
                        chat_session = ChatSession.objects.get(id=session_id)
                        
                        # 添加视频完成消息
                        ChatMessage.objects.create(
                            session=chat_session,
                            type='assistant',
                            content='Your video generation is complete!',
                            video_url=video_url,
                            metadata={
                                'task_id': celery_task_id,
                                'tokens_consumed': result.get('tokens_consumed', 0),
                                'direct_consumption': True
                            }
                        )
                        logger.info(f"视频完成消息已保存到会话 {session_id}")
                    except Exception as e:
                        logger.error(f"保存视频完成消息失败: {e}")
                        
                    logger.info(f"视频生成监控完成: {celery_task_id}")
                    return True
            else:
                # 任务失败 - 代币退还已在execute_jobplan中处理
                logger.error(f"视频生成任务失败: {celery_task_id}")
                
                # 失败通知邮件已移除 - 前端通过轮询可以获取失败状态
                logger.info(f"❌ 视频生成任务失败，前端将通过轮询获取失败状态")
                    
                return False
        else:
            # 任务还在运行，重新调度监控
            self.retry(countdown=30, max_retries=20)  # 30秒后重试，最多20次（10分钟）
            
    except Exception as e:
        logger.error(f"视频生成监控任务失败: {e}")
        return False

@app.task(name="execute_langchain_workflow")
def execute_langchain_workflow(task_data: dict):
    """
    执行LangChain工作流的Celery任务 (异步版本)
    
    Args:
        task_data: 包含执行所需数据的字典
            - conversation: 对话历史
            - user_id: 用户ID  
            - session_id: 会话ID
            - message_id: 消息ID (用于更新状态)
    """
    
    logger.info("🚀 [LangChain] 开始执行工作流任务")
    
    try:
        # 提取任务数据
        conversation = task_data.get("conversation", [])
        user_id = task_data.get("user_id")
        session_id = task_data.get("session_id") 
        message_id = task_data.get("message_id")
        
        # 获取当前Celery任务ID
        current_task = execute_langchain_workflow.request
        task_id = current_task.id if current_task else "unknown"
        
        logger.info(f"📝 [任务参数] 用户: {user_id}, 会话: {session_id}, 消息: {message_id}")
        logger.info(f"💬 [对话历史] {len(conversation)} 条消息")
        
        # 🔔 发送任务开始通知
        if user_id:
            notify_task_started(
                str(user_id), 
                task_id, 
                "开始处理您的请求..."
            )
        
        # 获取用户对象
        user = None
        if user_id:
            try:
                from core.models import User
                user = User.objects.get(id=user_id)
                logger.info(f"👤 [用户] {user.email}, 余额: {user.tokens} tokens")
            except User.DoesNotExist:
                logger.error(f"❌ 用户不存在: {user_id}")
        
        # 创建事件循环并运行Graph
        def run_graph_async():
            import asyncio
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 创建Graph实例
                from app.config.settings import settings
                graph = VisualGenerationGraph(skip_safety_check=settings.skip_safety_check)
                logger.info(f"🔧 安全检查设置: {'跳过' if settings.skip_safety_check else '启用'}")
                
                # 准备用户数据
                user_data = {"user": user} if user else None
                
                # 输出标准JSON格式用于调试（更易于阅读和解析）
                print("=" * 80)
                print("📋 CONVERSATION DEBUG (标准JSON格式):")
                print(json.dumps(conversation, ensure_ascii=False, indent=2))
                print("=" * 80)
                print(f"👤 USER_DATA: {json.dumps(user_data, ensure_ascii=False, default=str) if user_data else None}")
                print("=" * 80)
                # 运行完整的Graph工作流
                result = loop.run_until_complete(
                    graph.arun_with_conversation(
                        conversation=conversation,
                        skip_tool_execution=False,
                        skip_safety_check=settings.skip_safety_check,
                        user_data=user_data
                    )
                )
                
                return result
                
            finally:
                loop.close()
        
        # 🔔 发送进度更新：开始处理
        if user_id:
            notify_task_progress(
                str(user_id), 
                task_id, 
                20, 
                "分析请求", 
                "正在分析您的请求..."
            )
        
        # 在线程池中执行异步Graph
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(run_graph_async)
            result = future.result()
        
        logger.info(f"✅ Graph工作流执行完成，成功: {result.get('success', False)}")
        
        # 🔔 发送进度更新：任务处理完成
        if user_id:
            notify_task_progress(
                str(user_id), 
                task_id, 
                80, 
                "处理完成", 
                "正在准备结果..."
            )
        
        # 处理结果并更新数据库
        if result.get('success', False):
            
            # 🚀 【重要】先执行Token扣费逻辑 - 在所有return之前执行
            tokens_consumed = 0
            consumption_records = []

            logger.info(f"🔍 [Debug] 开始billing逻辑")
            try:
                from core.services.token_service import TokenService
                
                # 获取用户输入
                user_prompt = task_data.get('user_input', 'Task execution')
                
                # 🎯 第一步：每次消息都扣除chat_api基础费用（2 tokens）
                chat_api_cost = TokenService.get_token_cost("chat_api")  # 2 tokens
                logger.info(f"💬 [基础扣费] 每次消息固定扣费: chat_api, {chat_api_cost} tokens")
                logger.info(f"🔍 [Debug] 用户当前余额: {user.tokens} tokens")
                
                # 执行基础扣费
                # 🔍 [Debug] 检查基础扣费条件: {user.tokens} >= {chat_api_cost} = {user.tokens >= chat_api_cost}
                if user.tokens >= chat_api_cost:
                    logger.info("🔍 [Debug] 开始执行基础扣费...")
                    
                    # 🔧 使用安全数据库操作
                    def basic_token_deduction():
                        user.tokens -= chat_api_cost
                        user.save()
                        return user.tokens
                    
                    try:
                        new_balance = safe_db_operation(basic_token_deduction, "基础Token扣费")
                        logger.info(f"🔍 [Debug] 基础扣费完成，用户新余额: {new_balance}")
                    except Exception as e:
                        logger.error(f"💰 基础Token处理异常: {str(e)}", exc_info=True)
                        # Token处理失败不应该影响主要结果返回
                        new_balance = user.tokens
                    
                    # 创建基础消费记录
                    def create_base_consumption():
                        return TokenConsumption.objects.create(
                            user=user,
                            service_type="chat_api",
                            tokens_used=chat_api_cost,
                            prompt=user_prompt[:1000],
                            result_url="",  # 基础处理无媒体输出
                            metadata={
                                'status': 'COMPLETED',
                                'session_id': session_id,
                                'message_id': message_id,
                                'task_type': 'chat_api',
                                'billing_stage': 'base_chat_processing',
                                'description': '消息处理基础费用'
                            }
                        )
                    
                    try:
                        base_consumption = safe_db_operation(create_base_consumption, "创建基础消费记录")
                        logger.info(f"🔍 [Debug] 基础消费记录创建成功: {base_consumption.id}")
                        
                        tokens_consumed = chat_api_cost
                        consumption_records = [base_consumption]
                        logger.info(f"🔍 [Debug] 设置tokens_consumed = {tokens_consumed}")
                        
                        logger.info(f"✅ 基础扣费成功: {chat_api_cost} tokens，用户 {user.email} 新余额: {new_balance}")
                    except Exception as token_error:
                        logger.error(f"💰 基础消费记录创建异常: {str(token_error)}", exc_info=True)
                        # 消费记录创建失败不应该影响主要结果返回
                        tokens_consumed = chat_api_cost
                        consumption_records = []
                else:
                    logger.error(f"❌ Token余额不足以支付基础费用: 需要{chat_api_cost}, 当前{user.tokens}")
                    # 余额不足时仍返回结果，但不执行任何工具
                    return {
                        'success': False,
                        'error': 'Insufficient tokens for basic chat processing',
                        'session_id': session_id,
                        'message_id': message_id,
                        'tokens_required': chat_api_cost,
                        'current_balance': user.tokens
                    }
                
            except Exception as token_error:
                logger.error(f"💰 基础Token处理异常: {str(token_error)}", exc_info=True)
                # Token处理失败不应该影响主要结果返回

            # 处理成功结果
            generated_results = result.get('generated_results', [])
            
            # 🎯 第二步：处理工具扣费（如果有生成任务）
            try:
                if generated_results:
                    successful_results = [r for r in generated_results if getattr(r, 'success', False)]
                    
                    logger.info(f"🔍 [额外扣费] 发现 {len(successful_results)} 个成功的工具调用，开始依次扣费")
                    
                    for idx, result_data in enumerate(successful_results, 1):
                        actual_tool_name = getattr(result_data, 'tool_name', '')
                        result_path = getattr(result_data, 'result_path', '')
                        
                        if actual_tool_name:
                            service_type = actual_tool_name
                            tool_cost = TokenService.get_token_cost(service_type)
                            
                            logger.info(f"🔧 [额外扣费 {idx}/{len(successful_results)}] 工具: {service_type}, 费用: {tool_cost} tokens")
                            
                            # 执行额外扣费
                            if user.tokens >= tool_cost:
                                
                                # 🔧 使用安全数据库操作进行工具扣费
                                def tool_token_deduction():
                                    user.tokens -= tool_cost
                                    user.save()
                                    return user.tokens
                                
                                try:
                                    new_balance = safe_db_operation(tool_token_deduction, f"工具扣费_{service_type}")
                                    logger.info(f"🔧 工具扣费成功，新余额: {new_balance}")
                                except Exception as e:
                                    logger.error(f"💰 工具Token处理异常: {str(e)}", exc_info=True)
                                    continue  # 跳过这个工具的扣费，继续下一个
                                
                                # 创建消费记录
                                def create_tool_consumption():
                                    return TokenConsumption.objects.create(
                                        user=user,
                                        service_type=service_type,
                                        tokens_used=tool_cost,
                                        prompt=user_prompt[:1000],
                                        result_url=result_path,
                                        metadata={
                                            'status': 'COMPLETED',
                                            'session_id': session_id,
                                            'message_id': message_id,
                                            'task_type': service_type,
                                            'billing_stage': 'tool_execution',
                                            'actual_tool_used': actual_tool_name,
                                            'step_order': idx,
                                            'total_steps': len(successful_results)
                                        }
                                    )
                                
                                try:
                                    consumption_record = safe_db_operation(create_tool_consumption, f"创建工具消费记录_{service_type}")
                                    
                                    tokens_consumed += tool_cost
                                    consumption_records.append(consumption_record)
                                    logger.info(f"🔍 [Debug] 工具扣费后tokens_consumed = {tokens_consumed}")
                                    
                                    logger.info(f"✅ [额外扣费 {idx}/{len(successful_results)}] 成功: {service_type} {tool_cost} tokens，用户 {user.email} 新余额: {new_balance}")
                                except Exception as token_error:
                                    logger.error(f"💰 工具消费记录创建异常: {str(token_error)}", exc_info=True)
                                    # 消费记录创建失败不应该影响主要结果返回
                        else:
                            logger.warning(f"⚠️ [额外扣费 {idx}/{len(successful_results)}] 无法获取工具名称，跳过扣费: {result_path}")
                    
                    if len(consumption_records) > 1:  # 基础扣费 + 工具扣费
                        additional_tokens = tokens_consumed - chat_api_cost  # 除去基础费用的额外费用
                        logger.info(f"💰 [扣费汇总] 基础chat_api: {chat_api_cost} tokens + 工具扣费: {additional_tokens} tokens = 总计: {tokens_consumed} tokens，共{len(consumption_records)}条记录")
                    else:
                        logger.info(f"💰 [扣费汇总] 仅基础扣费: {tokens_consumed} tokens (纯聊天任务)")
                else:
                    logger.info(f"💰 [扣费汇总] 仅基础扣费: {tokens_consumed} tokens (纯聊天任务，无工具执行)")
                    
            except Exception as tool_token_error:
                logger.error(f"💰 工具Token处理异常: {str(tool_token_error)}", exc_info=True)
            final_response = result.get('final_response', '')
            response_type = result.get('response_type', '')
            
            # 提取媒体URL
            image_url = None
            video_url = None
            
            if generated_results:
                # 找到成功的结果
                successful_results = [r for r in generated_results if getattr(r, 'success', False)]
                if successful_results:
                    result_data = successful_results[-1]  # 使用最后一个成功结果
                    result_path = getattr(result_data, 'result_path', '')
                    tool_name = getattr(result_data, 'tool_name', '')
                    
                    logger.info(f"🎯 最终结果: {tool_name} -> {result_path}")
                    
                    # 根据工具类型确定媒体类型
                    if tool_name in ['img_gen', 'img_edit', 'text_to_image']:
                        image_url = result_path
                    elif tool_name in ['video_gen', 'img2video', 'text_to_video']:
                        video_url = result_path
                    elif result_path.endswith(('.jpg', '.png', '.jpeg')):
                        image_url = result_path
                    elif result_path.endswith(('.mp4', '.mov')):
                        video_url = result_path
            
            # 检查特殊响应类型（token错误、非视觉任务等）
            if response_type in ["balance_insufficient", "tokens_expired", "non_visual_task"]:
                # 特殊响应类型，使用final_response作为内容
                content = final_response or 'Task completed with special status.'
                success_status = response_type == "non_visual_task"  # 只有非视觉任务算成功
                
                logger.info(f"🔔 特殊响应类型: {response_type}")
                
                # 更新消息
                if message_id:
                    try:
                        message = ChatMessage.objects.get(id=message_id)
                        
                        # 记录更新前的状态
                        old_content = message.content
                        old_image_url = message.image_url
                        old_video_url = message.video_url
                        
                        # 更新内容和媒体URL
                        message.content = content
                        message.image_url = image_url
                        message.video_url = video_url
                        message.metadata.update({
                            'status': 'completed' if success_status else 'failed',
                            'response_type': response_type,
                            'success': success_status,
                            'updated_at': datetime.now().isoformat(),
                            'original_content': old_content
                        })
                        
                        # 添加token相关信息
                        if response_type in ["balance_insufficient", "tokens_expired"]:
                            if 'tokens_required' in result:
                                message.metadata['tokens_required'] = result['tokens_required']
                            if 'current_balance' in result:
                                message.metadata['current_balance'] = result['current_balance']
                        
                        # 保存到数据库
                        message.save()
                        
                        # 详细日志记录更新结果
                        logger.info(f"📱 消息更新成功: {message_id}")
                        logger.info(f"   📝 内容: '{old_content}' -> '{content}'")
                        logger.info(f"   🖼️ 图片URL: '{old_image_url}' -> '{image_url}'")
                        logger.info(f"   🎬 视频URL: '{old_video_url}' -> '{video_url}'")
                        
                        # 验证数据库更新
                        message.refresh_from_db()
                        logger.info(f"   ✅ 数据库验证 - 图片URL: {message.image_url}")
                        logger.info(f"   ✅ 数据库验证 - 视频URL: {message.video_url}")
                        
                    except ChatMessage.DoesNotExist:
                        logger.error(f"❌ 消息不存在: {message_id}")
                    except Exception as db_error:
                        logger.error(f"❌ 数据库更新失败: {str(db_error)}", exc_info=True)
                
                # 更新消息metadata中的token信息
                if message_id and tokens_consumed > 0:
                    try:
                        message = ChatMessage.objects.get(id=message_id)
                        if not message.metadata:
                            message.metadata = {}
                        message.metadata.update({
                            'tokens_consumed': tokens_consumed,
                            'consumption_count': len(consumption_records),
                            'token_consumption_time': datetime.now().isoformat()
                        })
                        message.save()
                        logger.info(f"📝 更新消息metadata: tokens_consumed={tokens_consumed}")
                    except ChatMessage.DoesNotExist:
                        logger.warning(f"消息不存在，无法更新metadata: {message_id}")
                    except Exception as metadata_error:
                        logger.error(f"更新消息metadata失败: {str(metadata_error)}", exc_info=True)
                
                return {
                    'success': success_status,
                    'response_type': response_type,
                    'chat_response': content,
                    'session_id': session_id,
                    'message_id': message_id,
                    'tokens_consumed': tokens_consumed,
                    'tokens_required': result.get('tokens_required', 0),
                    'current_balance': result.get('current_balance', 0)
                }
            
            else:
                # 正常的生成任务结果
                content = final_response or 'Task completed successfully!'
                if image_url:
                    content = 'Image generated successfully!'
                elif video_url:
                    content = 'Video generated successfully!'
                
                # 更新数据库消息
                if message_id:
                    # 🔧 使用安全数据库操作更新消息
                    def update_message_content():
                        # 强制刷新数据库连接
                        from django.db import connections
                        connections['default'].ensure_connection()
                        
                        try:
                            message = ChatMessage.objects.get(id=message_id)
                        except ChatMessage.DoesNotExist:
                            # 如果查询失败，尝试重新连接数据库后再查询
                            logger.warning(f"🔄 消息查询失败，尝试重新连接数据库: {message_id}")
                            connections['default'].close()
                            connections['default'].ensure_connection()
                        message = ChatMessage.objects.get(id=message_id)
                        
                        # 记录更新前的状态
                        old_content = message.content
                        old_image_url = message.image_url
                        old_video_url = message.video_url
                        
                        # 更新内容和媒体URL
                        message.content = content
                        message.image_url = image_url
                        message.video_url = video_url
                        message.metadata.update({
                            'status': 'completed',
                            'success': True,
                            'updated_at': datetime.now().isoformat(),
                            'original_content': old_content
                        })
                        
                        # 保存到数据库
                        message.save()
                        
                        # 验证数据库更新
                        message.refresh_from_db()
                        
                        return {
                            'old_content': old_content,
                            'old_image_url': old_image_url,
                            'old_video_url': old_video_url,
                            'new_image_url': message.image_url,
                            'new_video_url': message.video_url
                        }
                    
                    try:
                        update_result = safe_db_operation(update_message_content, "更新消息内容")
                        
                        # 详细日志记录更新结果
                        logger.info(f"📱 消息更新成功: {message_id}")
                        logger.info(f"   📝 内容: '{update_result['old_content']}' -> '{content}'")
                        logger.info(f"   🖼️ 图片URL: '{update_result['old_image_url']}' -> '{update_result['new_image_url']}'")
                        logger.info(f"   🎬 视频URL: '{update_result['old_video_url']}' -> '{update_result['new_video_url']}'")
                        
                    except ChatMessage.DoesNotExist:
                        logger.error(f"❌ 消息不存在: {message_id}")
                    except Exception as db_error:
                        logger.error(f"❌ 数据库更新失败: {str(db_error)}", exc_info=True)
            
            # 更新消息metadata中的token信息
            if message_id and tokens_consumed > 0:
                try:
                    message = ChatMessage.objects.get(id=message_id)
                    if not message.metadata:
                        message.metadata = {}
                    message.metadata.update({
                        'tokens_consumed': tokens_consumed,
                        'consumption_count': len(consumption_records),
                        'token_consumption_time': datetime.now().isoformat()
                    })
                    message.save()
                    logger.info(f"📝 最终更新消息metadata: tokens_consumed={tokens_consumed}")
                except ChatMessage.DoesNotExist:
                    logger.warning(f"消息不存在，无法更新metadata: {message_id}")
                except Exception as metadata_error:
                    logger.error(f"更新消息metadata失败: {str(metadata_error)}", exc_info=True)
            
            # 🔔 发送任务完成通知
            if user_id:
                notify_task_completed(str(user_id), task_id, {
                    'success': True,
                    'image_url': image_url,
                    'video_url': video_url,
                    'chat_response': content,
                    'session_id': session_id,
                    'message_id': message_id,
                    'tokens_consumed': tokens_consumed
                })
            
            return {
                'success': True,
                'image_url': image_url,
                'video_url': video_url,
                'chat_response': content,
                'session_id': session_id,
                'message_id': message_id,
                'tokens_consumed': tokens_consumed
            }
        
        else:
            # Graph执行失败
            error_message = result.get('error', 'LangChain workflow failed')
            logger.error(f"❌ Graph工作流执行失败: {error_message}")
            
            # 🔔 发送任务失败通知
            if user_id:
                notify_task_failed(
                    str(user_id), 
                    task_id, 
                    error_message, 
                    "workflow_execution_failed"
                )
            
            # 转换为用户友好的错误信息
            user_friendly_error = _convert_to_user_friendly_error(error_message)
            
            # 更新失败状态
            if message_id:
                try:
                    message = ChatMessage.objects.get(id=message_id)
                    message.content = user_friendly_error
                    message.metadata.update({
                        'status': 'failed',
                        'error': user_friendly_error,
                        'technical_error': error_message,
                        'success': False
                    })
                    message.save()
                except ChatMessage.DoesNotExist:
                    logger.error(f"消息不存在: {message_id}")
            
            return {
                'success': False,
                'error': user_friendly_error,
                'session_id': session_id,
                'message_id': message_id
            }
        
    except Exception as e:
        logger.error(f"❌ LangChain工作流执行失败: {str(e)}", exc_info=True)
        
        # 转换为用户友好的错误信息
        user_friendly_error = _convert_to_user_friendly_error(str(e))
        
        # 🔔 发送任务失败通知
        user_id = task_data.get("user_id")
        current_task = execute_langchain_workflow.request
        task_id = current_task.id if current_task else "unknown"
        
        if user_id:
            notify_task_failed(
                str(user_id), 
                task_id, 
                user_friendly_error, 
                "system_exception"
            )
        
        # 更新失败状态
        if task_data.get('message_id'):
            try:
                message_id = task_data.get('message_id')
                message = ChatMessage.objects.get(id=message_id)
                message.content = user_friendly_error
                message.metadata.update({
                    'status': 'failed',
                    'error': user_friendly_error,
                    'technical_error': str(e),
                    'success': False
                })
                message.save()
            except ChatMessage.DoesNotExist:
                logger.error(f"消息不存在: {task_data.get('message_id')}")
        
        return {
            'success': False,
            'error': user_friendly_error,
            'session_id': task_data.get('session_id'),
            'message_id': task_data.get('message_id')
        }

def _convert_to_user_friendly_error(error_message: str) -> str:
    """
    将技术错误信息转换为用户友好的错误信息
    """
    error_lower = error_message.lower()
    
    # Token相关错误
    if 'token' in error_lower and ('insufficient' in error_lower or 'not enough' in error_lower):
        return "Sorry, you don't have enough tokens to complete this request. Please purchase more tokens to continue."
    
    if 'token' in error_lower and ('expired' in error_lower or 'expire' in error_lower):
        return "Your tokens have expired. Please purchase new tokens to continue using our services."
    
    # 网络连接错误
    if any(keyword in error_lower for keyword in ['connection', 'network', 'timeout', 'unreachable']):
        return "We're experiencing network issues. Please try again in a few moments."
    
    # 服务不可用错误
    if any(keyword in error_lower for keyword in ['service unavailable', '503', 'server error', '500']):
        return "Our AI service is temporarily unavailable. Please try again later."
    
    # 文件格式错误
    if any(keyword in error_lower for keyword in ['format', 'invalid file', 'unsupported']):
        return "The file format is not supported. Please upload a common image format (JPG, PNG) or video format (MP4, MOV)."
    
    # 文件大小错误
    if any(keyword in error_lower for keyword in ['file too large', 'size limit', 'exceed']):
        return "The file is too large. Please upload files smaller than 10MB for images or 50MB for videos."
    
    # 内容安全错误
    if any(keyword in error_lower for keyword in ['safety', 'inappropriate', 'content policy', 'forbidden']):
        return "Sorry, we cannot process this request as it may violate our content policy. Please try a different request."
    
    # 队列相关错误
    if any(keyword in error_lower for keyword in ['queue full', 'busy', 'overload']):
        return "Our service is currently busy. Please try again in a few minutes."
    
    # 通用错误
    return "Sorry, something went wrong. Our team has been notified and will fix this issue."


# 🔧 新增：OCR文本增强任务
@app.task(bind=True, max_retries=3)
def process_image_ocr_enhance(self, image_paths, scene_type="auto"):
    """
    处理图片OCR文本增强任务

    Args:
        image_paths (list): 图片路径列表
        scene_type (str): 场景类型 ("auto" 或 "ecommerce")

    Returns:
        dict: 处理结果
    """
    try:
        logger.info(f"🔍 [OCR] 开始处理图片OCR增强任务 - 图片数量: {len(image_paths)}, 场景: {scene_type}")

        # 调用OCR增强工具
        result = ocr_enhancer.extract_and_enhance_text(image_paths, scene_type)

        logger.info(f"✅ [OCR] OCR增强任务完成 - 提取文本: {len(result.get('extracted_texts', []))}条")
        return {
            'status': 'success',
            'result': result
        }

    except Exception as e:
        logger.error(f"❌ [OCR] OCR增强任务失败: {str(e)}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"🔄 [OCR] 重试OCR任务 ({self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=30, exc=e)

        # 返回失败结果
        return {
            'status': 'error',
            'error': f"OCR处理失败: {str(e)}"
        }


@app.task(bind=True, max_retries=3)
def batch_image_compress(self, image_paths, quality=85, max_size=(1920, 1080)):
    """
    批量图片压缩任务

    Args:
        image_paths (list): 图片路径列表
        quality (int): 压缩质量 (1-100)
        max_size (tuple): 最大尺寸 (width, height)

    Returns:
        dict: 压缩结果
    """
    try:
        logger.info(f"🗜️ [COMPRESS] 开始批量图片压缩任务 - 图片数量: {len(image_paths)}")

        compressed_results = []

        for image_path in image_paths:
            try:
                # 调用OCR增强工具的压缩功能
                compressed_result = ocr_enhancer._compress_image(image_path, quality, max_size)
                compressed_results.append({
                    'original_path': image_path,
                    'compressed_path': compressed_result['compressed_path'],
                    'original_size': compressed_result['original_size'],
                    'compressed_size': compressed_result['compressed_size'],
                    'compression_ratio': compressed_result['compression_ratio']
                })

            except Exception as img_error:
                logger.error(f"❌ [COMPRESS] 压缩单张图片失败 {image_path}: {str(img_error)}")
                compressed_results.append({
                    'original_path': image_path,
                    'error': str(img_error)
                })

        success_count = len([r for r in compressed_results if 'error' not in r])
        logger.info(f"✅ [COMPRESS] 批量压缩完成 - 成功: {success_count}/{len(image_paths)}")

        return {
            'status': 'success',
            'results': compressed_results,
            'success_count': success_count,
            'total_count': len(image_paths)
        }

    except Exception as e:
        logger.error(f"❌ [COMPRESS] 批量压缩任务失败: {str(e)}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"🔄 [COMPRESS] 重试压缩任务 ({self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=15, exc=e)

        # 返回失败结果
        return {
            'status': 'error',
            'error': f"批量压缩失败: {str(e)}"
        }
