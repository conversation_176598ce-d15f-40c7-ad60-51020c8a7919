import os
import uuid
import base64
import tempfile
import asyncio
import httpx
from typing import Any, Dict
from openai import AsyncOpenAI
from app.tools.oss_upload import upload_and_get_signed_url
from app.config.settings import settings

class OpenAIImageGenError(RuntimeError):
    """OpenAI 文生图异常"""
    pass

async def generate_image(
    prompt: str,
    *,
    size: str = "1024x1024",
    model: str = "gpt-image-1",
    n: int = 1,
    quality: str = "auto",
    output_format: str = "png",
    output_compression: int = None,
    background: str = "opaque",
    timeout: float = 900.0,
    max_retries: int = 3,
    # 保持与原始接口兼容的参数（但在OpenAI中无效）
    poll_interval: float = 1.0,
    max_poll_time: float = 900.0
) -> Dict[str, Any]:
    """
    使用 OpenAI 的 gpt-image-1 模型进行文本生成图像
    
    参数:
        prompt: 图像生成描述
        size: 图像尺寸，支持 "1024x1024", "1536x1024", "1024x1536", "auto"
        model: 模型名称，默认"gpt-image-1"
        n: 生成图像数量，默认1
        quality: 图像质量，"low", "medium", "high", "auto"(默认)
        output_format: 输出格式，"png", "jpeg", "webp"
        output_compression: 压缩级别，0-100，仅对jpeg和webp有效
        background: 背景，"opaque"(默认)或"transparent"
        timeout: 超时时间
        max_retries: 最大重试次数
        poll_interval: 兼容参数（OpenAI是同步的，此参数无效）
        max_poll_time: 兼容参数（OpenAI是同步的，此参数无效）
    
    返回:
        {
            "prompt": prompt,
            "task_id": "生成的UUID",
            "task_status": "SUCCEEDED",
            "image_url": "生成图像的URL"
        }
    
    抛出 OpenAIImageGenError on failure.
    """
    
    # 从settings获取OpenAI配置
    api_key = settings.openai_api_key
    base_url = settings.openai_base_url
    
    # 如果配置为空，抛出错误
    if not api_key:
        raise OpenAIImageGenError("OpenAI配置不完整，请检查settings.py中的openai_api_key配置")
    
    # 验证size参数
    valid_sizes = ["1024x1024", "1536x1024", "1024x1536", "auto"]
    if size not in valid_sizes:
        print(f"⚠️ [OpenAI] 尺寸 {size} 不被支持，使用默认尺寸 1024x1024")
        size = "1024x1024"
    
    # 验证quality参数
    if quality not in ["low", "medium", "high", "auto"]:
        print(f"⚠️ [OpenAI] 质量 {quality} 不被支持，使用默认质量 auto")
        quality = "auto"
    
    # 验证output_format参数
    if output_format not in ["png", "jpeg", "webp"]:
        print(f"⚠️ [OpenAI] 输出格式 {output_format} 不被支持，使用默认格式 png")
        output_format = "png"
    
    # 验证background参数
    if background not in ["opaque", "transparent"]:
        print(f"⚠️ [OpenAI] 背景 {background} 不被支持，使用默认背景 opaque")
        background = "opaque"
    
    # 初始化OpenAI客户端
    client = AsyncOpenAI(
        api_key=api_key,
        base_url=base_url,
        timeout=timeout,
        max_retries=max_retries
    )
    
    print(f"🎨 [OpenAI] 开始生成图像...")
    print(f"   - 模型: {model}")
    print(f"   - 提示词: {prompt}")
    print(f"   - 尺寸: {size}")
    print(f"   - 质量: {quality}")
    print(f"   - 输出格式: {output_format}")
    print(f"   - 背景: {background}")
    print(f"   - 数量: {n}")
    if output_compression:
        print(f"   - 压缩级别: {output_compression}")
    
    try:
        # 构建API参数
        api_params = {
            "model": model,
            "prompt": prompt,
            "size": size,
            "quality": quality,
            "output_format": output_format,
            "background": background
        }
        
        # 仅在有效时添加可选参数
        if output_compression is not None:
            api_params["output_compression"] = output_compression
        
        # 调用OpenAI图像生成API
        result = await client.images.generate(**api_params)
        
        if not result.data or len(result.data) == 0:
            raise OpenAIImageGenError("OpenAI返回空结果")
        
        # 获取第一张图像的base64数据
        image_item = result.data[0]
        if not hasattr(image_item, 'b64_json') or not image_item.b64_json:
            raise OpenAIImageGenError("OpenAI返回的图像数据缺失")
        
        # 解码base64图像数据
        try:
            image_bytes = base64.b64decode(image_item.b64_json)
        except Exception as e:
            raise OpenAIImageGenError(f"Base64解码失败: {e}")
        
        # 将图像写入临时文件
        tmp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
        try:
            tmp_file.write(image_bytes)
            tmp_file.flush()
            tmp_file.close()
            
            # 上传到OSS并获取签名URL
            try:
                signed_url = upload_and_get_signed_url(tmp_file.name, object_prefix="openai_imgs/")
                print(f"✅ [OpenAI] 图像生成成功，已上传到OSS: {signed_url}")
            except Exception as e:
                raise OpenAIImageGenError(f"上传到OSS失败: {e}")
        
        finally:
            # 清理临时文件
            try:
                os.unlink(tmp_file.name)
            except:
                pass
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 返回与image_gen.py兼容的格式
        return {
            "prompt": prompt,
            "task_id": task_id,
            "task_status": "SUCCEEDED",
            "image_url": signed_url,
            "model": model,
            "size": size,
            "quality": quality,
            "output_format": output_format,
            "background": background,
            "output_compression": output_compression
        }
        
    except Exception as e:
        # 如果是OpenAIImageGenError，直接抛出
        if isinstance(e, OpenAIImageGenError):
            raise
        # 其他异常包装后抛出
        error_msg = f"OpenAI图像生成失败: {type(e).__name__} - {str(e)}"
        print(f"❌ [OpenAI] {error_msg}")
        raise OpenAIImageGenError(error_msg)

# 为了保持完全兼容，提供一个别名
generate_image_openai = generate_image

async def main():
    """测试函数"""
    print("🚀 OpenAI文生图API测试开始")
    print("=" * 60)
    
    # 检查配置
    print("🔧 配置检查:")
    print(f"   Base URL: {settings.openai_base_url}")
    print(f"   API Key: {settings.openai_api_key[:20]}..." if settings.openai_api_key else "   API Key: 未配置")
    print()
    
    if not settings.openai_api_key:
        print("❌ 错误: OpenAI API Key未配置，请检查settings.py")
        return
    
    # 测试用例
    test_cases = [
        {
            "name": "基础测试",
            "prompt": "A children's book drawing of a veterinarian using a stethoscope to listen to the heartbeat of a baby otter.",
            "size": "1024x1024",
            "quality": "medium",
            "output_format": "png",
            "background": "opaque"
        },
        {
            "name": "高清测试",
            "prompt": "A majestic mountain landscape with snow-capped peaks and a crystal clear lake reflecting the sky.",
            "size": "1536x1024",
            "quality": "high",
            "output_format": "jpeg",
            "output_compression": 90,
            "background": "opaque"
        },
        {
            "name": "创意测试（透明背景）",
            "prompt": "A futuristic robot character on a transparent background, cyberpunk style.",
            "size": "1024x1536", 
            "quality": "high",
            "output_format": "png",
            "background": "transparent"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🎨 测试案例 {i}: {test_case['name']}")
        print(f"   提示词: {test_case['prompt']}")
        print(f"   参数: {test_case['size']}, {test_case['quality']}, {test_case['output_format']}, {test_case['background']}")
        
        try:
            # 构建参数字典
            params = {
                "prompt": test_case['prompt'],
                "size": test_case['size'],
                "quality": test_case['quality'],
                "output_format": test_case['output_format'],
                "background": test_case['background']
            }
            
            # 添加可选参数
            if 'output_compression' in test_case:
                params['output_compression'] = test_case['output_compression']
            
            result = await generate_image(**params)
            
            print(f"   ✅ 成功 - 任务ID: {result['task_id']}")
            print(f"   📷 图像URL: {result['image_url']}")
            
            results.append({
                'test_case': test_case['name'],
                'success': True,
                'result': result
            })
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            results.append({
                'test_case': test_case['name'],
                'success': False,
                'error': str(e)
            })
        
        # 避免API限流，稍作延迟
        if i < len(test_cases):
            print("   ⏳ 等待2秒避免限流...")
            await asyncio.sleep(2)
    
    # 测试总结
    print("\n" + "=" * 60)
    print("🎯 测试完成总结:")
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"   {result['test_case']}: {status}")
        if not result['success']:
            print(f"      错误: {result['error']}")
    
    print(f"   总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    print("✅ OpenAI文生图测试完成!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}") 