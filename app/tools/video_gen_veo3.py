# app/tools/video_gen_veo3.py

import asyncio
import time
import re
import json
import tempfile
import os
from typing import Any, Dict

import httpx
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential
from app.config.settings import settings
from app.tools.oss_upload import upload_and_get_signed_url
from app.tools.oss_upload import upload_and_get_signed_url

class VideoGenVeo3Error(RuntimeError):
    """VEO3 文生视频 Adapter 错误"""
    pass

def retry_error_callback(retry_state):
    """重试错误回调"""
    exception = retry_state.outcome.exception()
    print(f"VEO3 重试第 {retry_state.attempt_number} 次失败: {type(exception).__name__} - {str(exception)}")
    return None

def _parse_veo3_response(content: str) -> Dict[str, Any]:
    """解析VEO3返回的响应内容 - 通用格式，支持任何语言"""
    result = {
        'raw_content': content,
        'task_id': None,
        'preview_url': None,
        'high_quality_url': None,
        'source_url': None,
        'status': 'UNKNOWN'
    }
    
    # 通用Task ID提取 - 匹配任何语言格式的veo3任务ID
    task_id_match = re.search(r'`(veo3:[a-f0-9-]+)`', content)
    if task_id_match:
        result['task_id'] = task_id_match.group(1)
    
    # 通用URL提取 - 基于markdown链接格式和关键图标/关键词
    # 提取所有markdown格式的链接：[任意文本](URL)
    all_links = re.findall(r'\[([^\]]+)\]\((https://[^)]+)\)', content)
    
    for link_text, url in all_links:
        link_text_lower = link_text.lower()
        
        # 识别下载链接 (最高优先级)
        if any(keyword in link_text for keyword in ['⏬', '下载', 'download']) or \
           any(keyword in link_text_lower for keyword in ['download', 'dl']):
            result['high_quality_url'] = url
            print(f"🔗 [VEO3] 识别下载链接: {link_text} -> {url}")
    
        # 识别在线观看链接 (高优先级)
        elif any(keyword in link_text for keyword in ['▶️', '在线观看', 'watch']) or \
             any(keyword in link_text_lower for keyword in ['watch', 'play', 'stream']):
            if not result['high_quality_url']:  # 只有没有下载链接时才使用
                result['high_quality_url'] = url
            print(f"🔗 [VEO3] 识别观看链接: {link_text} -> {url}")
        
        # 识别预览链接 (中优先级)
        elif any(keyword in link_text for keyword in ['📺', '预览', 'preview']) or \
             any(keyword in link_text_lower for keyword in ['preview', 'view']):
            result['preview_url'] = url
            print(f"🔗 [VEO3] 识别预览链接: {link_text} -> {url}")
        
        # 识别数据源链接 (低优先级)
        elif any(keyword in link_text_lower for keyword in ['data', 'source', 'src', '数据', '源']):
            result['source_url'] = url
            print(f"🔗 [VEO3] 识别数据源链接: {link_text} -> {url}")
        
        # 通用视频链接备用匹配 (最低优先级)
        else:
            # 检查URL是否包含视频相关关键词
            if any(keyword in url.lower() for keyword in ['.mp4', '.mov', '.avi', 'video', 'cdn']):
                if not result['high_quality_url'] and not result['preview_url']:
                    result['preview_url'] = url
                    print(f"🔗 [VEO3] 通用视频链接: {link_text} -> {url}")
    
    # 如果没有高质量链接，使用预览链接作为备用
    if not result['high_quality_url'] and result['preview_url']:
        result['high_quality_url'] = result['preview_url']
        print(f"🔄 [VEO3] 使用预览链接作为高质量链接")
                
    # 提取数据预览链接 - 支持中英文
    data_patterns = [
        r'\[数据预览\]\((https://[^)]+)\)',         # 中文
        r'\[Data Preview\]\((https://[^)]+)\)'      # 英文
    ]
    for pattern in data_patterns:
        data_preview_match = re.search(pattern, content)
        if data_preview_match:
            result['source_url'] = data_preview_match.group(1)
            break
    
    # 通用状态判断 - 基于关键词而非特定语言
    content_lower = content.lower()
    
    # 成功状态指示符
    success_indicators = ['✅', '🎉', 'success', 'complete', 'generated', 'finished', '成功', '完成', '已生成', 'quality video']
    # 处理中状态指示符  
    processing_indicators = ['🔄', '⏳', '🎬', 'processing', 'generating', 'waiting', '处理中', '生成中', '等待', 'starting']
    # 失败状态指示符
    failed_indicators = ['❌', '❗', '⚠️', 'failed', 'error', 'timeout', '失败', '错误', '超时']
    
    if any(indicator in content or indicator in content_lower for indicator in success_indicators):
        result['status'] = 'SUCCEEDED'
    elif any(indicator in content or indicator in content_lower for indicator in processing_indicators):
        result['status'] = 'PROCESSING'
    elif any(indicator in content or indicator in content_lower for indicator in failed_indicators):
        result['status'] = 'FAILED'
        # 提取错误信息
        error_match = re.search(r'(?:Error|错误): ([^\n]+)', content)
        if error_match:
            result['error'] = error_match.group(1)
    else:
        # 如果有视频链接，认为是成功的
        if result['high_quality_url'] or result['preview_url']:
            result['status'] = 'SUCCEEDED'
        else:
            result['status'] = 'UNKNOWN'
    
    print(f"📊 [VEO3] 解析完成 - 状态: {result['status']}, 链接数: {len([url for url in [result['high_quality_url'], result['preview_url'], result['source_url']] if url])}")
    
    return result

async def _get_video_details(source_url: str) -> Dict[str, Any]:
    """从源数据URL获取视频详细信息"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(source_url)
            response.raise_for_status()
            
            video_data = response.json()
            
            return {
                'video_media_id': video_data.get('video_media_id'),
                'video_generation_id': video_data.get('video_generation_id'),
                'video_generation_status': video_data.get('video_generation_status'),
                'completed_at': video_data.get('completed_at'),
                'created_at': video_data.get('created_at'),
                'status': video_data.get('status'),
                'req': video_data.get('req', {}),
                'full_data': video_data
            }
    except Exception as e:
        print(f"⚠️ [VEO3] 获取视频详情失败: {e}")
        return {}

async def _download_and_upload_video(video_url: str) -> str:
    """下载VEO3视频并上传到OSS，返回OSS URL"""
    if not video_url:
        raise VideoGenVeo3Error("视频URL为空")
    
    try:
        print(f"📥 [VEO3] 开始下载视频: {video_url}")
        
        # 下载视频文件
        async with httpx.AsyncClient(timeout=1500.0) as client:
            response = await client.get(video_url)
            response.raise_for_status()
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
                tmp_file.write(response.content)
                tmp_path = tmp_file.name
        
        print(f"✅ [VEO3] 视频下载完成，大小: {len(response.content)} bytes")
        
        try:
            # 上传到OSS
            print(f"📤 [VEO3] 开始上传视频到OSS...")
            oss_url = upload_and_get_signed_url(tmp_path, object_prefix="veo3_videos/")
            print(f"✅ [VEO3] 视频上传OSS成功: {oss_url}")
            return oss_url
            
        finally:
            # 清理临时文件
            try:
                os.unlink(tmp_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ [VEO3] 视频下载上传失败: {e}")
        raise VideoGenVeo3Error(f"视频下载上传失败: {e}")

async def generate_video(
    prompt: str,
    *,
    size: str = "1280*720",
    duration: int = 5,
    model: str = None,               # 默认从settings读取
    poll_interval: float = 1.0,      # 保持接口兼容性，但VEO3是同步的
    max_poll_time: float = 1200.0,     # 保持接口兼容性，但VEO3是同步的
    max_retries: int = None          # 默认从settings读取
) -> Dict[str, Any]:
    """
    使用VEO3模型进行文本生成视频，保持与video_gen.py相同的接口。
    
    参数:
        prompt: 视频生成描述
        size: 视频尺寸（保持兼容性，VEO3可能不支持）
        duration: 视频时长（保持兼容性，VEO3可能不支持）
        model: 模型名称，默认"veo3"
        poll_interval: 轮询间隔（VEO3是同步的，此参数无效）
        max_poll_time: 最大轮询时间（VEO3是同步的，此参数无效）
        max_retries: 最大重试次数
    
    返回:
        {
            "prompt": prompt,
            "task_id": "veo3_generated",
            "task_status": "SUCCEEDED",
            "video_url": "..."
        }
    
    抛出 VideoGenVeo3Error on unrecoverable failure.
    """
    
    # 从settings获取VEO3配置
    base_url = settings.veo3_base_url
    api_key = settings.veo3_api_key
    
    # 设置默认值
    if model is None:
        model = settings.veo3_model
    if max_retries is None:
        max_retries = settings.veo3_max_retries
    
    # 如果配置为空，抛出错误
    if not base_url or not api_key:
        raise VideoGenVeo3Error("VEO3配置不完整，请检查settings.py中的veo3_base_url和veo3_api_key配置")
    
    # 初始化OpenAI客户端 - 设置更长的超时时间（VEO3需要约13分钟）
    client = AsyncOpenAI(
        base_url=base_url, 
        api_key=api_key,
        timeout=httpx.Timeout(timeout=1200.0)  
    )
    
    @retry(
        stop=stop_after_attempt(max_retries), 
        wait=wait_exponential(multiplier=1, min=1, max=15), 
        retry_error_callback=retry_error_callback
    )
    async def _generate_with_retry() -> str:
        """带重试的VEO3视频生成"""
        try:
            print(f"🎬 [VEO3] 开始生成视频...")
            print(f"   - 模型: {model}")
            print(f"   - 提示词: {prompt}")
            print(f"   - 尺寸: {size} (可能不被VEO3支持)")
            print(f"   - 时长: {duration}秒 (可能不被VEO3支持)")
            
            # 构建完整的提示词，包含尺寸和时长信息
            enhanced_prompt = f"{prompt}"
            if size and size != "1280*720":
                enhanced_prompt += f" Resolution: {size}."
            if duration and duration != 5:
                enhanced_prompt += f" Duration: {duration} seconds."
            
            # 调用VEO3 API
            response = await client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": enhanced_prompt}
                ]
            )
            
            # 提取响应内容
            video_content = response.choices[0].message.content
            
            if not video_content:
                raise VideoGenVeo3Error("VEO3返回空内容")
            
            # 解析VEO3返回的内容
            print(f"🔍 [VEO3] 原始响应内容: {video_content[:500]}...")
            parsed_result = _parse_veo3_response(video_content)
            
            print(f"✅ [VEO3] 视频生成完成，状态: {parsed_result['status']}")
            print(f"🔍 [VEO3] 解析结果: preview_url={parsed_result.get('preview_url', 'None')}, high_quality_url={parsed_result.get('high_quality_url', 'None')}, source_url={parsed_result.get('source_url', 'None')}")
            return parsed_result
            
        except Exception as e:
            error_msg = f"VEO3视频生成失败: {type(e).__name__} - {str(e)}"
            print(f"❌ [VEO3] {error_msg}")
            raise VideoGenVeo3Error(error_msg)
    
    try:
        # 执行生成
        start_time = time.time()
        veo3_result = await _generate_with_retry()
        end_time = time.time()
        
        print(f"🎉 [VEO3] 视频生成完成，耗时: {end_time - start_time:.1f}秒")
        
        # 检查生成是否成功
        if veo3_result['status'] == 'FAILED':
            error_msg = veo3_result.get('error', '未知错误')
            raise VideoGenVeo3Error(f"VEO3视频生成失败: {error_msg}")
        
        # 获取详细的视频信息
        video_details = {}
        if veo3_result['source_url'] and veo3_result['status'] == 'SUCCEEDED':
            print(f"📋 [VEO3] 获取视频详细信息...")
            video_details = await _get_video_details(veo3_result['source_url'])
        
        # 确定最终的视频URL - 优先使用高质量视频
        video_url_to_download = veo3_result.get('high_quality_url') or veo3_result.get('preview_url') or veo3_result.get('source_url')
        
        print(f"🔗 [VEO3] 原始视频URL: {video_url_to_download}")
        
        # 强制下载视频并上传到OSS
        final_video_url = None
        if video_url_to_download:
            # 多次重试OSS上传，确保成功
            upload_attempts = 3
            for attempt in range(upload_attempts):
                try:
                    print(f"📤 [VEO3] OSS上传尝试 {attempt + 1}/{upload_attempts}...")
                    final_video_url = await _download_and_upload_video(video_url_to_download)
                    print(f"✅ [VEO3] 视频已成功上传到OSS: {final_video_url}")
                    break
                except Exception as e:
                    print(f"❌ [VEO3] OSS上传失败 (尝试 {attempt + 1}/{upload_attempts}): {e}")
                    if attempt < upload_attempts - 1:
                        print(f"🔄 [VEO3] 等待3秒后重试...")
                        await asyncio.sleep(3)
                    else:
                        # 最后一次尝试失败，抛出错误
                        raise VideoGenVeo3Error(f"OSS上传失败，已重试{upload_attempts}次: {e}")
        else:
            # 如果没有获取到任何视频URL，抛出错误而不是返回兜底内容
            raise VideoGenVeo3Error("VEO3未返回有效的视频下载链接")
        
        # 验证OSS URL有效性
        if not final_video_url or not final_video_url.startswith('http'):
            raise VideoGenVeo3Error(f"OSS上传返回无效URL: {final_video_url}")
        
        # 返回与video_gen.py兼容的格式 - 确保总是返回OSS URL
        return {
            "prompt": prompt,
            "task_id": veo3_result.get('task_id') or f"veo3_{int(start_time)}_{hash(prompt) % 10000:04d}",
            "task_status": veo3_result.get('status', 'SUCCEEDED'),  # 如果到达这里说明成功了
            "video_url": final_video_url,  # 总是OSS URL，已验证有效性
            "model": model,
            "generation_time": end_time - start_time,
            "enhanced_prompt": enhanced_prompt if 'enhanced_prompt' in locals() else prompt,
            # VEO3特有的字段
            "veo3_task_id": veo3_result.get('task_id'),
            "veo3_preview_url": veo3_result.get('preview_url'),
            "veo3_high_quality_url": veo3_result.get('high_quality_url'),
            "veo3_source_url": veo3_result.get('source_url'),
            "veo3_raw_content": veo3_result.get('raw_content'),
            "veo3_original_video_url": video_url_to_download,  # 原始VEO3视频URL
            "veo3_video_media_id": video_details.get('video_media_id'),
            "veo3_video_generation_id": video_details.get('video_generation_id'),
            "veo3_video_details": video_details,
            "storage_type": "oss"  # 标识这是OSS存储的视频
        }
        
    except Exception as e:
        # 如果是VideoGenVeo3Error，直接抛出
        if isinstance(e, VideoGenVeo3Error):
            raise
        # 其他异常包装后抛出
        raise VideoGenVeo3Error(f"VEO3视频生成过程中发生未知错误: {type(e).__name__} - {str(e)}")

# 为了保持完全兼容，提供一个别名
generate_video_veo3 = generate_video

if __name__ == "__main__":
    # 测试代码
    async def test():
        try:
            result = await generate_video(
                "In StarCraft, the Protoss unsheathe their psi-blades against the overwhelming Zerg swarm.",
                duration=10,
                size="1920*1080"
            )
            print("测试结果:")
            print(f"  Task ID: {result['task_id']}")
            print(f"  Status: {result['task_status']}")
            print(f"  Video URL: {result['video_url'][:100]}...")
            print(f"  Generation Time: {result.get('generation_time', 0):.1f}s")
        except Exception as e:
            print(f"测试失败: {e}")
    
    # asyncio.run(test())
    print("VEO3视频生成模块已加载") 