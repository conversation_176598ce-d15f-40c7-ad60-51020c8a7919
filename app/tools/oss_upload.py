# app/adapters/oss_upload.py
import os
import json
import tempfile
import urllib.parse
import logging
import aiohttp
import asyncio
from datetime import datetime

import oss2

# 获取模块 logger
logger = logging.getLogger(__name__)


def load_oss_config(config_path="oss_config.json"):
    with open(config_path, 'r') as f:
        return json.load(f)


def upload_and_get_signed_url(local_file_path, object_prefix="tmp_imgs/", expire_seconds=3600*24*180):
    """
    上传文件到 OSS 并生成签名 URL，最后将路径部分解码把 %2F 还原成 /
    """
    cfg = load_oss_config()
    auth = oss2.Auth(cfg["access_key_id"], cfg["access_key_secret"])
    bucket = oss2.Bucket(auth, cfg["endpoint"], cfg["bucket_name"])

    file_name = os.path.basename(local_file_path)
    object_name = f"{object_prefix}{file_name}"

    try:
        # 先把本地文件上传到 OSS
        bucket.put_object_from_file(object_name, local_file_path)
    except Exception as e:
        logger.error(f"OSS 上传失败: object_name={object_name}, error={e}")
        raise RuntimeError(f"OSS 上传失败: {e}") from e

    try:
        # 生成签名 URL
        signed_url = bucket.sign_url('GET', object_name, expire_seconds)
    except Exception as e:
        logger.error(f"OSS 签名 URL 生成失败: object_name={object_name}, error={e}")
        raise RuntimeError(f"签名 URL 生成失败: {e}") from e

    # 拆分并解码路径
    parsed = urllib.parse.urlsplit(signed_url)
    decoded_path = urllib.parse.unquote(parsed.path)

    # 强制 https
    scheme = 'https'
    rebuilt = urllib.parse.urlunsplit((
        scheme,
        parsed.netloc,
        decoded_path,
        parsed.query,
        parsed.fragment
    ))
    return rebuilt


def upload_image_to_oss(file):
    """
    将 Django 上传的 InMemoryUploadedFile 写到临时文件，
    用 oss2 推到 OSS，然后删盘并返回“解码后”的签名 URL。
    如果过程中出现任何错误，会记录日志并抛出异常。
    """
    # 将上传文件写入临时文件
    suffix = os.path.splitext(file.name)[1]  # 保留扩展名
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
        try:
            for chunk in file.chunks():
                tmp.write(chunk)
            tmp_path = tmp.name
        except Exception as e:
            logger.error(f"写入临时文件失败: filename={file.name}, error={e}")
            raise RuntimeError(f"写入临时文件失败: {e}") from e

    try:
        signed_url = upload_and_get_signed_url(tmp_path)
        logger.info("----------------------------------> signed_url = ", signed_url)
    except Exception:
        # upload_and_get_signed_url 已经记录了日志并抛出了 RuntimeError
        raise
    finally:
        # 清理临时文件
        try:
            os.remove(tmp_path)
        except Exception as e:
            logger.warning(f"临时文件删除失败: path={tmp_path}, error={e}")

    return signed_url


async def direct_copy_video_to_oss(video_url, object_prefix="videos/seedance/", expire_seconds=3600*24*180):
    """
    直接从火山视频URL复制到OSS，使用流式传输避免本地存储
    
    参数:
        video_url: 火山视频URL
        object_prefix: OSS存储路径前缀，默认为"videos/seedance/"
        expire_seconds: 签名URL过期时间（秒）
    
    返回:
        str: OSS签名URL
    """
    if not video_url:
        raise ValueError("视频URL不能为空")
    
    # 生成唯一的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"video_{timestamp}.mp4"
    object_name = f"{object_prefix}{file_name}"
    
    try:
        print(f"🔄 开始流式复制视频到OSS: {video_url}")
        print(f"🎯 OSS目标路径: {object_name}")
        
        # 加载OSS配置
        cfg = load_oss_config()
        auth = oss2.Auth(cfg["access_key_id"], cfg["access_key_secret"])
        bucket = oss2.Bucket(auth, cfg["endpoint"], cfg["bucket_name"])
        
        # 使用内存流式传输：将视频内容读入内存后直接上传
        timeout = aiohttp.ClientTimeout(total=600)  # 10分钟超时
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
            async with session.get(video_url) as response:
                if response.status != 200:
                    raise RuntimeError(f"视频下载失败，HTTP状态码: {response.status}")
                
                # 获取内容长度
                content_length = response.headers.get('content-length')
                if content_length:
                    total_size = int(content_length)
                    print(f"📊 视频文件大小: {total_size / (1024*1024):.2f} MB")
                
                # 读取全部内容到内存
                print(f"📥 开始读取视频内容...")
                video_data = await response.read()
                print(f"✅ 视频内容读取完成: {len(video_data)} 字节")
                
                # 使用OSS的put_object方法直接上传
                print(f"⬆️ 开始上传到OSS...")
                result = bucket.put_object(
                    object_name, 
                    video_data,
                    headers={'Content-Type': 'video/mp4'}
                )
                
                print(f"✅ 视频上传成功: {result.status}")
        
        # 生成签名URL
        signed_url = bucket.sign_url('GET', object_name, expire_seconds)
        
        # 拆分并解码路径
        parsed = urllib.parse.urlsplit(signed_url)
        decoded_path = urllib.parse.unquote(parsed.path)
        
        # 强制 https
        scheme = 'https'
        rebuilt = urllib.parse.urlunsplit((
            scheme,
            parsed.netloc,
            decoded_path,
            parsed.query,
            parsed.fragment
        ))
        
        print(f"🌐 OSS签名URL生成成功: {rebuilt}")
        return rebuilt
        
    except Exception as e:
        print(f"❌ 流式复制失败: {e}")
        # 如果流式复制失败，回退到下载方式
        print(f"🔄 回退到下载方式...")
        return await download_and_upload_video_to_oss(video_url, object_prefix, expire_seconds)


async def download_and_upload_video_to_oss(video_url, object_prefix="videos/seedance/", expire_seconds=3600*24*180):
    """
    从视频URL下载视频文件，上传到OSS并生成签名URL（备用方案）
    
    参数:
        video_url: 火山视频URL
        object_prefix: OSS存储路径前缀，默认为"videos/seedance/"
        expire_seconds: 签名URL过期时间（秒）
    
    返回:
        str: OSS签名URL
    """
    if not video_url:
        raise ValueError("视频URL不能为空")
    
    # 生成唯一的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"video_{timestamp}.mp4"
    
    # 创建临时文件
    tmp_fd, tmp_path = tempfile.mkstemp(suffix=".mp4")
    
    try:
        # 下载视频文件
        print(f"📥 开始下载视频: {video_url}")
        logger.info(f"开始下载视频: {video_url}")
        
        # 设置更长的超时时间
        timeout = aiohttp.ClientTimeout(total=600, sock_read=60)  # 10分钟总超时，60秒读取超时
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
            async with session.get(video_url) as response:
                if response.status != 200:
                    raise RuntimeError(f"视频下载失败，HTTP状态码: {response.status}")
                
                # 获取文件大小用于进度显示
                content_length = response.headers.get('content-length')
                if content_length:
                    total_size = int(content_length)
                    print(f"📊 视频文件大小: {total_size / (1024*1024):.2f} MB")
                    logger.info(f"视频文件大小: {total_size / (1024*1024):.2f} MB")
                
                # 分块下载并写入临时文件
                downloaded = 0
                with os.fdopen(tmp_fd, 'wb') as tmp_file:
                    async for chunk in response.content.iter_chunked(8192):
                        tmp_file.write(chunk)
                        downloaded += len(chunk)
                        
                        # 每下载10MB显示一次进度
                        if content_length and downloaded % (10 * 1024 * 1024) == 0:
                            progress = (downloaded / total_size) * 100
                            print(f"📈 下载进度: {progress:.1f}%")
                            logger.info(f"下载进度: {progress:.1f}%")
        
        print(f"✅ 视频下载完成: {tmp_path}")
        logger.info(f"视频下载完成: {tmp_path}")
        
        # 上传到OSS
        print(f"⬆️ 开始上传到OSS: {object_prefix}{file_name}")
        logger.info(f"开始上传到OSS: {object_prefix}{file_name}")
        signed_url = upload_and_get_signed_url(
            tmp_path, 
            object_prefix=object_prefix, 
            expire_seconds=expire_seconds
        )
        
        print(f"🌐 视频上传成功: {signed_url}")
        logger.info(f"视频上传成功: {signed_url}")
        return signed_url
        
    except Exception as e:
        print(f"❌ 视频下载或上传失败: {e}")
        logger.error(f"视频下载或上传失败: {e}")
        import traceback
        traceback.print_exc()
        raise RuntimeError(f"视频处理失败: {e}") from e
    finally:
        # 清理临时文件
        try:
            os.remove(tmp_path)
            print(f"🗑️ 临时文件已删除: {tmp_path}")
            logger.info(f"临时文件已删除: {tmp_path}")
        except Exception as e:
            print(f"⚠️ 临时文件删除失败: {tmp_path}, error={e}")
            logger.warning(f"临时文件删除失败: {tmp_path}, error={e}")


def upload_video_to_oss(local_video_path, object_prefix="videos/seedance/", expire_seconds=3600*24*180):
    """
    上传本地视频文件到OSS并生成签名URL
    
    参数:
        local_video_path: 本地视频文件路径
        object_prefix: OSS存储路径前缀
        expire_seconds: 签名URL过期时间（秒）
    
    返回:
        str: OSS签名URL
    """
    if not os.path.exists(local_video_path):
        raise FileNotFoundError(f"本地视频文件不存在: {local_video_path}")
    
    return upload_and_get_signed_url(
        local_video_path, 
        object_prefix=object_prefix, 
        expire_seconds=expire_seconds
    )
