# app/tools/img2video_volcengine.py

import asyncio
import json
import time
import aiohttp
from typing import Any, Dict, Optional
import urllib.parse

try:
    from volcenginesdkarkruntime import Ark
except ImportError:
    print("请安装火山方舟SDK: pip install volcengine-python-sdk[ark]")
    raise

from app.config.settings import settings
from app.adapters.image_adapter import process_image_for_api
from app.tools.oss_upload import download_and_upload_video_to_oss

class VolcengineImg2VideoError(RuntimeError):
    """火山方舟图生视频 API 错误"""
    pass

# 已迁移到通用图片适配器 app/adapters/image_adapter.py
# def clean_image_url_for_seedance(url: str) -> str:

async def image_to_video(
    prompt: str,
    img_url: str,
    *,
    size: str = "1280*720",       # 保持原有的size参数名
    duration: int = 5,            # 保持原有的duration参数名
    model: str = None,
    poll_interval: float = 10.0,     # 轮询间隔（秒）
    max_poll_time: float = 1800.0,    # 轮询最长等待时间（秒，30分钟）
    max_retries: int = None          # 失败后最大重试次数
) -> Dict[str, Any]:
    """
    火山方舟图片生成视频，使用官方HTTP API。
    
    参数:
        prompt: 文本提示词
        img_url: 输入图片URL
        size: 视频尺寸，支持: "1280*720", "720*1280", "960*960" 等，会自动映射为ratio参数
        duration: 视频时长（秒），支持: 5, 10，会自动映射为dur参数
        model: 模型名称，默认使用settings中的配置
        poll_interval: 轮询间隔（秒）
        max_poll_time: 轮询最长等待时间（秒，30分钟）
        max_retries: 失败后最大重试次数
    
    返回:
        Dict[str, Any]: 包含生成结果的字典
    """
    # 使用settings中的配置
    if model is None:
        model = settings.volcengine_video_model_i2v
    if max_retries is None:
        max_retries = getattr(settings, 'volcengine_max_retries', 1)
    print(f"🔧 I2V 使用模型: {model}")
    
    # 🔧 参数映射：size -> ratio, duration -> dur
    def map_size_to_ratio(size_str: str) -> str:
        """
        将size参数映射为火山引擎的ratio参数
        根据火山引擎文档，ratio支持: adaptive, 16:9, 9:16, 1:1 等
        """
        if not size_str:
            return "adaptive"
        
        size_mapping = {
            # 常见16:9格式
            "1280*720": "adaptive",  # 默认使用adaptive
            "1920*1080": "adaptive",
            "832*480": "adaptive",
            # 常见9:16格式  
            "720*1280": "adaptive",
            "480*832": "adaptive",
            # 常见1:1格式
            "960*960": "adaptive", 
            "624*624": "adaptive",
            "1024*1024": "adaptive",
            # 其他格式
            "832*1088": "adaptive",  # 3:4
            "1088*832": "adaptive",  # 4:3
        }
        
        # 直接映射
        if size_str in size_mapping:
            return size_mapping[size_str]
        
        # 尝试解析宽度和高度
        try:
            if '*' in size_str:
                width, height = map(int, size_str.split('*'))
            elif 'x' in size_str:
                width, height = map(int, size_str.split('x'))
            else:
                return "adaptive"
            
            # 计算长宽比
            ratio = width / height
            if abs(ratio - 16/9) < 0.1:
                return "adaptive"  # 16:9 比例使用adaptive
            elif abs(ratio - 9/16) < 0.1:
                return "adaptive"  # 9:16 比例使用adaptive  
            elif abs(ratio - 1) < 0.1:
                return "adaptive"  # 1:1 比例使用adaptive
            else:
                return "adaptive"  # 其他比例也使用adaptive
        except:
            return "adaptive"
    
    # 🔧 duration 参数验证：火山方舟模型只支持特定时长
    def validate_duration(dur: int) -> int:
        """验证并调整duration参数，确保火山方舟模型支持"""
        supported_durations = [5, 10]  # 火山方舟支持的时长
        
        if dur in supported_durations:
            return dur
        elif dur < 5:
            print(f"   ⚠️ duration {dur}秒 < 最小值5秒，调整为5秒")
            return 5
        elif dur <= 10:
            print(f"   ⚠️ duration {dur}秒不支持，调整为10秒")
            return 10
        else:
            print(f"   ⚠️ duration {dur}秒 > 最大值10秒，调整为10秒")
            return 10
    
    # 映射参数
    ratio = map_size_to_ratio(size)
    dur = validate_duration(duration)  # 验证并调整duration
    
    api_key = settings.volcengine_api_key
    
    if not api_key:
        raise VolcengineImg2VideoError("缺少火山方舟API密钥")
    
    # 初始化Ark客户端
    client = Ark(api_key=settings.volcengine_api_key, 
                 base_url=settings.volcengine_base_url)
    
    for attempt in range(1, max_retries + 1):
        try:
            # 处理图片为火山引擎所需格式（base64）
            final_img_url = process_image_for_api(img_url, "volcengine")
            
            if not final_img_url.get('success'):
                raise VolcengineImg2VideoError(f"图片处理失败: {final_img_url.get('error', '未知错误')}")
            
            # 构建base64格式的图片URL
            if final_img_url['type'] == 'base64':
                base64_data = final_img_url['data']
                mime_type = final_img_url.get('mime_type', 'image/jpeg')
                final_img_url = f"data:{mime_type};base64,{base64_data}"
            else:
                final_img_url = final_img_url['data']
            
            print(f"[尝试 {attempt}] 创建图生视频任务...")
            print(f"提示词: {prompt}")
            print(f"原始图片URL: {img_url}")
            print(f"图片处理: Base64编码")
            print(f"模型: {model}")
            print(f"视频参数: --ratio {ratio} --dur {dur}")
            
            # 构建符合官方格式的提示词（包含参数）
            full_prompt = f"{prompt} --ratio {ratio} --dur {dur}"
            
            # 1. 创建图生视频任务 (使用官方SDK)
            create_result = client.content_generation.tasks.create(
                model=model,
                content=[
                    {
                        "type": "text",
                        "text": full_prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": final_img_url
                        }
                    }
                ]
            )
            
            task_id = create_result.id
            print(f"[图生视频任务 {task_id}] 创建成功")
            
            # 2. 轮询任务状态 (使用官方SDK)
            elapsed = 0
            while elapsed < max_poll_time:
                try:
                    # 查询任务状态
                    get_result = client.content_generation.tasks.get(task_id=task_id)
                    status = get_result.status
                    
                    print(f"[任务 {task_id}] 状态: {status}, 已等待: {int(elapsed)}s")
                    
                    if status == "succeeded":
                        # 智能查找video_url，优先检查content字段
                        video_url = None
                        
                        # 首先检查嵌套的content字段
                        if hasattr(get_result, 'content') and hasattr(get_result.content, 'video_url'):
                            video_url = get_result.content.video_url
                            print(f"[DEBUG] 找到视频URL在content.video_url: {video_url}")
                        
                        if video_url:
                            print(f"✅ [{task_id}] 图生视频成功: {video_url}")
                            
                            # 下载视频并上传到OSS
                            try:
                                print(f"📥 [{task_id}] 开始下载视频并上传到OSS...")
                                oss_video_url = await download_and_upload_video_to_oss(
                                    video_url, 
                                    object_prefix="videos/seedance/"
                                )
                                print(f"🌐 [{task_id}] OSS视频URL: {oss_video_url}")
                                
                                return {
                                    "success": True,
                                    "video_url": oss_video_url,  # 返回OSS URL
                                    "original_video_url": video_url,  # 保留原始火山URL
                                    "task_id": task_id,
                                    "prompt": prompt,
                                    "task_status": status,
                                    "created_at": getattr(get_result, 'created_at', ''),
                                    "updated_at": getattr(get_result, 'updated_at', ''),
                                    "duration": duration,
                                    "ratio": ratio,
                                    "model": model
                                }
                            except Exception as e:
                                print(f"⚠️ [{task_id}] OSS上传失败，返回原始URL: {e}")
                                # 如果OSS上传失败，仍然返回原始URL
                                return {
                                    "success": True,
                                    "video_url": video_url,
                                    "task_id": task_id,
                                    "prompt": prompt,
                                    "task_status": status,
                                    "created_at": getattr(get_result, 'created_at', ''),
                                    "updated_at": getattr(get_result, 'updated_at', ''),
                                    "duration": duration,
                                    "ratio": ratio,
                                    "model": model,
                                    "oss_upload_error": str(e)
                                }
                        else:
                            print(f"❌ [{task_id}] 成功但未返回 video_url")
                            raise VolcengineImg2VideoError(f"[{task_id}] 成功但未返回 video_url")
                    
                    elif status in {"failed", "cancelled"}:
                        error_msg = getattr(get_result, 'error', 'Unknown error')
                        print(f"[任务 {task_id}] 状态 {status}，错误: {error_msg}，准备重试...")
                        break  # 跳出轮询，进入重试循环
                    
                    elif status in {"queued", "running"}:
                        # 继续等待
                        await asyncio.sleep(poll_interval)
                        elapsed += poll_interval
                    else:
                        print(f"[任务 {task_id}] 未知状态: {status}，继续轮询...")
                        await asyncio.sleep(poll_interval)
                        elapsed += poll_interval
                        
                except Exception as e:
                    print(f"状态查询出错 ({e})，继续轮询...")
                    await asyncio.sleep(poll_interval)
                    elapsed += poll_interval
                    continue
            
            # 如果超过 max_poll_time 或状态为 failed/cancelled，下一轮重试
            print(f"[attempt {attempt}] 任务未在 {int(max_poll_time)}s 内完成，或状态失败，重试...")
            await asyncio.sleep(poll_interval)
            
        except Exception as e:
            if attempt >= max_retries:
                raise VolcengineImg2VideoError(f"[attempt {attempt}] generation request failed: {e}")
            else:
                print(f"[attempt {attempt}] generation request failed, retrying... ({e})")
                await asyncio.sleep(poll_interval)
                continue

    # 超过最大重试次数仍未成功
    raise VolcengineImg2VideoError(f"Maximum retry attempts exceeded ({max_retries}), img2video generation failed")


async def main():
    """测试函数"""
    try:
        print("开始测试火山方舟图生视频...")
        
        # 测试图片URL
        test_img_url = "https://example.com/test.jpg"
        
        result = await image_to_video(
            prompt="A beautiful sunset over the ocean with waves gently crashing",
            img_url=test_img_url,
            size="1280*720",
            duration=5
        )
        
        print(f"成功生成视频: {result}")
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 