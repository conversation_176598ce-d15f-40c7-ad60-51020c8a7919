 # app/tools/img2video_veo3.py

import asyncio
import time
import re
import json
import base64
import cv2
import numpy as np
import tempfile
import os
from typing import Any, Dict, Optional, Tuple

import httpx
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential
from app.config.settings import settings
from app.tools.oss_upload import upload_and_get_signed_url
from app.adapters.image_adapter import process_image_for_api

class Img2VideoVeo3Error(RuntimeError):
    """VEO3 图生视频错误"""
    pass

def retry_error_callback(retry_state):
    """重试错误回调"""
    exception = retry_state.outcome.exception()
    print(f"VEO3 图生视频重试第 {retry_state.attempt_number} 次失败: {type(exception).__name__} - {str(exception)}")
    # 不返回任何值，让tenacity继续抛出原始异常

def _parse_veo3_response(content: str) -> Dict[str, Any]:
    """解析VEO3返回的响应内容 - 通用格式，支持任何语言"""
    result = {
        'raw_content': content,
        'task_id': None,
        'preview_url': None,
        'high_quality_url': None,
        'source_url': None,
        'status': 'UNKNOWN'
    }
    print(f"🔍 [VEO3] 解析VEO3返回的响应内容: {content}") 
    # 通用Task ID提取 - 匹配任何语言格式的veo3任务ID
    task_id_match = re.search(r'`(veo3:[a-f0-9-]+)`', content)
    if task_id_match:
        result['task_id'] = task_id_match.group(1)
    
    # 通用URL提取 - 基于markdown链接格式和关键图标/关键词
    # 提取所有markdown格式的链接：[任意文本](URL)
    all_links = re.findall(r'\[([^\]]+)\]\((https://[^)]+)\)', content)
    
    for link_text, url in all_links:
        link_text_lower = link_text.lower()
        
        # 识别下载链接 (最高优先级)
        if any(keyword in link_text for keyword in ['⏬', '下载', 'download']) or \
           any(keyword in link_text_lower for keyword in ['download', 'dl']):
            result['high_quality_url'] = url
            print(f"🔗 [VEO3] 识别下载链接: {link_text} -> {url}")
        
        # 识别在线观看链接 (高优先级)
        elif any(keyword in link_text for keyword in ['▶️', '在线观看', 'watch']) or \
             any(keyword in link_text_lower for keyword in ['watch', 'play', 'stream']):
            if not result['high_quality_url']:  # 只有没有下载链接时才使用
                result['high_quality_url'] = url
            print(f"🔗 [VEO3] 识别观看链接: {link_text} -> {url}")
        
        # 识别预览链接 (中优先级)
        elif any(keyword in link_text for keyword in ['📺', '预览', 'preview']) or \
             any(keyword in link_text_lower for keyword in ['preview', 'view']):
            result['preview_url'] = url
            print(f"🔗 [VEO3] 识别预览链接: {link_text} -> {url}")
        
        # 识别数据源链接 (低优先级)
        elif any(keyword in link_text_lower for keyword in ['data', 'source', 'src', '数据', '源']):
            result['source_url'] = url
            print(f"🔗 [VEO3] 识别数据源链接: {link_text} -> {url}")
        
        # 通用视频链接备用匹配 (最低优先级)
        else:
            # 检查URL是否包含视频相关关键词
            if any(keyword in url.lower() for keyword in ['.mp4', '.mov', '.avi', 'video', 'cdn']):
                if not result['high_quality_url'] and not result['preview_url']:
                    result['preview_url'] = url
                    print(f"🔗 [VEO3] 通用视频链接: {link_text} -> {url}")
    
        # 如果没有高质量链接，使用预览链接作为备用
    if not result['high_quality_url'] and result['preview_url']:
        result['high_quality_url'] = result['preview_url']
        print(f"🔄 [VEO3] 使用预览链接作为高质量链接")
    
    # 通用状态判断 - 基于关键词而非特定语言
    content_lower = content.lower()
    
    # 成功状态指示符
    success_indicators = ['✅', '🎉', 'success', 'complete', 'generated', 'finished', '成功', '完成', '已生成', 'quality video']
    # 处理中状态指示符  
    processing_indicators = ['🔄', '⏳', '🎬', 'processing', 'generating', 'waiting', '处理中', '生成中', '等待', 'starting']
    # 失败状态指示符
    failed_indicators = ['❌', '❗', '⚠️', 'failed', 'error', 'timeout', '失败', '错误', '超时']
    
    if any(indicator in content or indicator in content_lower for indicator in success_indicators):
        result['status'] = 'SUCCEEDED'
    elif any(indicator in content or indicator in content_lower for indicator in processing_indicators):
        result['status'] = 'PROCESSING'
    elif any(indicator in content or indicator in content_lower for indicator in failed_indicators):
        result['status'] = 'FAILED'
        # 提取错误信息
        error_match = re.search(r'(?:Error|错误): ([^\n]+)', content)
        if error_match:
            result['error'] = error_match.group(1)
    else:
        # 如果有视频链接，认为是成功的
        if result['high_quality_url'] or result['preview_url']:
            result['status'] = 'SUCCEEDED'
        else:
            result['status'] = 'UNKNOWN'
    
    print(f"📊 [VEO3] 解析完成 - 状态: {result['status']}, 链接数: {len([url for url in [result['high_quality_url'], result['preview_url'], result['source_url']] if url])}")
    return result

async def _get_video_details(source_url: str) -> Dict[str, Any]:
    """从源数据URL获取视频详细信息"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(source_url)
            response.raise_for_status()
            
            video_data = response.json()
            
            return {
                'video_media_id': video_data.get('video_media_id'),
                'video_generation_id': video_data.get('video_generation_id'),
                'video_generation_status': video_data.get('video_generation_status'),
                'completed_at': video_data.get('completed_at'),
                'created_at': video_data.get('created_at'),
                'status': video_data.get('status'),
                'req': video_data.get('req', {}),
                'full_data': video_data
            }
    except Exception as e:
        print(f"⚠️ [VEO3] 获取视频详情失败: {e}")
        return {}

async def _download_and_upload_video(video_url: str) -> str:
    """下载VEO3视频并上传到OSS，返回OSS URL"""
    if not video_url:
        raise Img2VideoVeo3Error("视频URL为空")
    
    try:
        print(f"📥 [VEO3] 开始下载视频: {video_url}")
        
        # 下载视频文件
        async with httpx.AsyncClient(timeout=300.0) as client:
            response = await client.get(video_url)
            response.raise_for_status()
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
                tmp_file.write(response.content)
                tmp_path = tmp_file.name
        
        print(f"✅ [VEO3] 视频下载完成，大小: {len(response.content)} bytes")
        
        try:
            # 上传到OSS
            print(f"📤 [VEO3] 开始上传视频到OSS...")
            oss_url = upload_and_get_signed_url(tmp_path, object_prefix="veo3_videos/")
            print(f"✅ [VEO3] 视频上传OSS成功: {oss_url}")
            return oss_url
            
        finally:
            # 清理临时文件
            try:
                os.unlink(tmp_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ [VEO3] 视频下载上传失败: {e}")
        raise Img2VideoVeo3Error(f"视频下载上传失败: {e}")



async def image_to_video(
    prompt: str,
    img_url: str,
    caption: Optional[str] = None,
    *,
    resolution: str = "720P",
    model: str = "veo3-pro-frames",
    poll_interval: float = 1.0,
    max_poll_time: float = 1200.0,
    max_retries: int = 1
) -> Dict[str, Any]:
    """
    VEO3图生视频生成：从图片和文本生成视频。
    
    参数:
        prompt: 视频生成描述
        img_url: 输入图片的URL
        caption: 图片描述（可选，如果不提供会自动生成）
        resolution: 视频分辨率（保持兼容性）
        model: 模型名称，默认"veo3"
        poll_interval: 轮询间隔（VEO3是同步的，此参数无效）
        max_poll_time: 最大轮询时间（VEO3是同步的，此参数无效）
        max_retries: 最大重试次数

    返回：{"prompt":..., "task_id":..., "video_url":..., "task_status":...}
    """

    # 输入参数验证
    if not prompt or not prompt.strip():
        raise Img2VideoVeo3Error("prompt参数不能为空")
    
    if not img_url or not img_url.strip():
        raise Img2VideoVeo3Error("img_url参数不能为空")
    
    # 验证图片URL格式
    if not (img_url.startswith('http://') or img_url.startswith('https://')):
        raise Img2VideoVeo3Error(f"无效的图片URL格式: {img_url}")
    
    # 验证图片文件类型（通过URL扩展名进行初步检查）
    supported_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.tif']
    url_lower = img_url.lower()
    has_valid_extension = any(ext in url_lower for ext in supported_extensions)
    if not has_valid_extension:
        print(f"⚠️ [VEO3] 图片URL未包含常见图片扩展名，将尝试自动处理: {img_url}")
    
    # 验证prompt长度（避免过长导致API错误）
    if len(prompt) > 1000:
        print(f"⚠️ [VEO3] 提示词较长({len(prompt)}字符)，建议控制在1000字符以内")
    
    # 从settings获取VEO3配置
    base_url = settings.veo3_base_url
    api_key = settings.veo3_api_key
    
    # 如果配置为空，抛出错误
    if not base_url or not api_key:
        raise Img2VideoVeo3Error("VEO3配置不完整，请检查settings.py中的veo3_base_url和veo3_api_key配置")
    
    # 初始化OpenAI客户端 - 设置更长的超时时间（VEO3需要约13分钟）
    client = AsyncOpenAI(
        base_url=base_url, 
        api_key=api_key,
        timeout=httpx.Timeout(timeout=1200.0)  
    )

    # 直接使用原始prompt，不进行caption生成和合并
    print(f"🚫 [VEO3] Caption功能已禁用，直接使用原始prompt")
    final_prompt = prompt
    caption = ""  # 清空caption

    @retry(
        stop=stop_after_attempt(max_retries), 
        wait=wait_exponential(multiplier=1, min=1, max=15), 
        retry_error_callback=retry_error_callback
    )
    async def _generate_with_retry() -> Dict[str, Any]:
        """带重试的VEO3图生视频生成"""
        try:
            print(f"🎬 [VEO3] 开始图生视频...")
            print(f"   - 模型: {model}")
            print(f"   - 最终提示词: {final_prompt}")
            print(f"   - 图片URL: {img_url}")
            
            # 使用通用图片适配器转换为base64
            image_result = process_image_for_api(img_url, 'veo3')
            if not image_result['success']:
                raise Img2VideoVeo3Error(f"图片处理失败: {image_result['error']}")
            base64_image = image_result['data']
            mime_type = image_result.get('mime_type', 'image/jpeg')
            print(f"✅ [VEO3] 图片适配器处理完成，准备发送API请求")
            
            # 构建content数组，完全按照示例的格式
            content = [{"type": "text", "text": final_prompt}]
            
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            })
            
            print(f"📋 [VEO3] 发送请求内容: text={final_prompt[:50]}..., image_base64_length={len(base64_image)}")
            
            # 调用VEO3 API进行图生视频 - 完全按照示例格式
            response = await client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": content}]
            )
            print("response = ", response)
            # 提取响应内容
            video_content = response.choices[0].message.content
            
            if not video_content:
                raise Img2VideoVeo3Error("VEO3返回空内容")
            
            # 解析VEO3返回的内容
            parsed_result = _parse_veo3_response(video_content)
            
            print(f"✅ [VEO3] 图生视频完成，状态: {parsed_result['status']}")
            return parsed_result
            
        except Exception as e:
            error_msg = f"VEO3图生视频失败: {type(e).__name__} - {str(e)}"
            print(f"❌ [VEO3] {error_msg}")
            raise Img2VideoVeo3Error(error_msg)
    
    try:
        # 执行生成
        start_time = time.time()
        veo3_result = await _generate_with_retry()
        end_time = time.time()
        
        print(f"🎉 [VEO3] 图生视频完成，耗时: {end_time - start_time:.1f}秒")
        
        # 检查veo3_result是否为None（重试失败的情况）
        if veo3_result is None:
            raise Img2VideoVeo3Error("VEO3图生视频重试失败，未获得有效响应")
        
        # 检查生成是否成功
        if veo3_result.get('status') == 'FAILED':
            error_msg = veo3_result.get('error', '未知错误')
            raise Img2VideoVeo3Error(f"VEO3图生视频失败: {error_msg}")
        
        # 获取详细的视频信息
        video_details = {}
        if veo3_result.get('source_url') and veo3_result.get('status') == 'SUCCEEDED':
            try:
                print(f"📋 [VEO3] 获取视频详细信息...")
                # 添加超时控制，避免卡住
                video_details = await asyncio.wait_for(
                    _get_video_details(veo3_result['source_url']), 
                    timeout=30.0  # 30秒超时
                )
                print(f"✅ [VEO3] 视频详情获取成功")
            except asyncio.TimeoutError:
                print(f"⚠️ [VEO3] 获取视频详情超时，跳过详情获取")
                video_details = {}
            except Exception as e:
                print(f"⚠️ [VEO3] 获取视频详情失败: {e}，跳过详情获取")
                video_details = {}
        
        # 确定最终的视频URL - 优先使用高质量视频
        video_url_to_download = veo3_result.get('high_quality_url') or veo3_result.get('preview_url') or veo3_result.get('source_url')
        
        print(f"🔗 [VEO3] 原始视频URL: {video_url_to_download}")
        
        # 强制下载视频并上传到OSS
        final_video_url = None
        if video_url_to_download:
            # 多次重试OSS上传，确保成功
            upload_attempts = 3
            for attempt in range(upload_attempts):
                try:
                    print(f"📤 [VEO3] OSS上传尝试 {attempt + 1}/{upload_attempts}...")
                    final_video_url = await _download_and_upload_video(video_url_to_download)
                    print(f"✅ [VEO3] 视频已成功上传到OSS: {final_video_url}")
                    break
                except Exception as e:
                    print(f"❌ [VEO3] OSS上传失败 (尝试 {attempt + 1}/{upload_attempts}): {e}")
                    if attempt < upload_attempts - 1:
                        print(f"🔄 [VEO3] 等待3秒后重试...")
                        await asyncio.sleep(3)
                    else:
                        # 最后一次尝试失败，抛出错误
                        raise Img2VideoVeo3Error(f"OSS上传失败，已重试{upload_attempts}次: {e}")
        else:
            # 如果没有获取到任何视频URL，抛出错误而不是返回兜底内容
            raise Img2VideoVeo3Error("VEO3未返回有效的视频下载链接")
        
        # 验证OSS URL有效性
        if not final_video_url or not final_video_url.startswith('http'):
            raise Img2VideoVeo3Error(f"OSS上传返回无效URL: {final_video_url}")
        
        # 构建返回结果 - 确保总是返回OSS URL
        result = {
            "prompt": final_prompt,
            "task_id": veo3_result.get('task_id') or f"veo3_i2v_{int(start_time)}_{hash(prompt) % 10000:04d}",
            "task_status": veo3_result.get('status', 'SUCCEEDED'),  # 如果到达这里说明成功了
            "video_url": final_video_url,  # 总是OSS URL，已验证有效性
            "model": model,
            "generation_time": end_time - start_time,
            "original_prompt": prompt,
            "image_caption": caption,
            "image_url": img_url,  # 原始图片URL
            # VEO3特有的字段
            "veo3_task_id": veo3_result.get('task_id'),
            "veo3_preview_url": veo3_result.get('preview_url'),
            "veo3_high_quality_url": veo3_result.get('high_quality_url'),
            "veo3_source_url": veo3_result.get('source_url'),
            "veo3_raw_content": veo3_result.get('raw_content'),
            "veo3_original_video_url": video_url_to_download,  # 原始VEO3视频URL
            "veo3_video_media_id": video_details.get('video_media_id'),
            "veo3_video_generation_id": video_details.get('video_generation_id'),
            "veo3_video_details": video_details,
            "storage_type": "oss"  # 标识这是OSS存储的视频
        }
        
        print(f"📤 [VEO3] 准备返回结果，task_id: {result['task_id']}, status: {result['task_status']}")
        return result
        
    except Exception as e:
        # 如果是Img2VideoVeo3Error，直接抛出
        if isinstance(e, Img2VideoVeo3Error):
            raise
        # 其他异常包装后抛出
        raise Img2VideoVeo3Error(f"VEO3图生视频过程中发生未知错误: {type(e).__name__} - {str(e)}")


if __name__ == "__main__":
    # 测试代码
    async def test():
        try:
            result = await image_to_video(
                prompt="一只兔子正在在跳伞",
                img_url="https://ugen.oss-cn-beijing.aliyuncs.com/tmp_imgs/tmp0390xd83.jpg?OSSAccessKeyId=LTAI5tE3G5dKW4XgYNQTV62q&Expires=2109108168&Signature=bWHEtxHXr8G61kD%2BHBey%2Bm%2BWIl0%3D"
            )
            print("result = ", result)
            print("测试结果:")
            print(f"  Task ID: {result['task_id']}")
            print(f"  Status: {result['task_status']}")
            print(f"  Video URL: {result['video_url'][:100]}...")
            print(f"  Generation Time: {result.get('generation_time', 0):.1f}s")
        except Exception as e:
            print(f"测试失败: {e}")
    
    # asyncio.run(test())
    print("VEO3图生视频模块已加载") 