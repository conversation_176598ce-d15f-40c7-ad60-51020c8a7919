import asyncio
import httpx
from typing import Any, Dict
from app.config.settings import settings

class ImageGenError(RuntimeError):
    """文生图异常"""
    pass


async def generate_image(
    prompt: str,
    *,
    size: str = "1024*1024",
    model: str = "wanx2.1-t2i-turbo",
    poll_interval: float = 1.0,
    max_poll_time: float = 900.0,
    max_retries: int = 3
) -> Dict[str, Any]:
    """
    文本生成图像
    返回：{"prompt":..., "task_id":..., "task_status":..., "image_url":...}
    """
    post_url = settings.image_gen_base_url
    status_url = settings.image_gen_task_base_url
    headers = {
        "X-DashScope-Async": "enable",
        "Authorization": f"Bearer {settings.image_gen_api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "input": {"prompt": prompt},
        "parameters": {"size": size, "n": 1}
    }

    attempt = 0
    while attempt < max_retries:
        attempt += 1
        try:
            async with httpx.AsyncClient() as client:
                resp = await client.post(post_url, headers=headers, json=payload)
                resp.raise_for_status()
                result = resp.json()
        except Exception as e:
            if attempt >= max_retries:
                raise ImageGenError(f"[尝试 {attempt}] 请求失败: {e}")
            print(f"[尝试 {attempt}] 请求失败，重试中... ({e})")
            continue

        task_id = result.get("output", {}).get("task_id", "")
        if not task_id:
            raise ImageGenError("未返回 task_id")

        # 开始轮询状态
        elapsed = 0.0
        while elapsed < max_poll_time:
            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

            try:
                async with httpx.AsyncClient() as client:
                    r = await client.get(f"{status_url}/{task_id}", headers={
                        "Authorization": f"Bearer {settings.image_gen_api_key}"
                    })
                    r.raise_for_status()
                    status = r.json().get("output", {})
            except Exception as e:
                print(f"状态查询失败（继续尝试）: {e}")
                continue

            state = status.get("task_status", "UNKNOWN")
            print(f"[文生图任务 {task_id}] 状态: {state}，等待 {int(elapsed)}s")
            if state == "SUCCEEDED":
                results = status.get("results", [])
                image_url = results[0]["url"] if results else ""
                return {
                    "prompt": prompt,
                    "task_id": task_id,
                    "task_status": state,
                    "image_url": image_url
                }
            elif state in {"FAILED", "CANCELED"}:
                print("任务失败或取消。错误信息：",
                      status.get("code", ""), status.get("message", ""))
                break

        print(f"[尝试 {attempt}] 文生图任务未成功，准备重试...")

    raise ImageGenError(f"文生图任务未成功（尝试 {max_retries} 次后放弃）")
