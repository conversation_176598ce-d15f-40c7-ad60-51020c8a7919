
# app/adapters/video_keyframe.py
import os
import cv2
import tempfile
import requests
from typing import Dict, Optional, List
from urllib.parse import urlparse
import oss2
import json
from openai import OpenAI

class VideoKeyframeError(RuntimeError):
    pass

def load_oss_config(config_path="oss_config.json"):
    with open(config_path, 'r') as f:
        return json.load(f)

def upload_and_get_signed_url(local_file_path: str,
                              object_prefix: str = "tmp_imgs/",
                              expire_seconds: int = 3600) -> str:
    config = load_oss_config()
    auth = oss2.Auth(config["access_key_id"], config["access_key_secret"])
    bucket = oss2.Bucket(auth, config["endpoint"], config["bucket_name"])
    fname = os.path.basename(local_file_path)
    obj_name = os.path.join(object_prefix, fname).replace("\\", "/")
    bucket.put_object_from_file(obj_name, local_file_path)
    return bucket.sign_url('GET', obj_name, expire_seconds)

async def extract_keyframe(
    video_url: str,
    sample_fps: float = 2.0
) -> Dict[str, str]:
    """
    下载视频，每秒抽取 sample_fps 帧，先打视频Caption，
    再依次对每帧画面打Caption并用LLM判断是否与视频Caption主体一致。
    首个匹配的帧立即返回；若都不匹配，返回最中间那帧。
    返回：{"task_id": "video_keyframe", "task_status": "SUCCEEDED", "image_url": "..."}
    """

    try:
        # 1. 下载视频到临时文件
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_vid:
            vid_path = tmp_vid.name
            r = requests.get(video_url, stream=True)
            r.raise_for_status()
            for chunk in r.iter_content(chunk_size=8192):
                tmp_vid.write(chunk)

        # 2. 用 OpenAI SDK 调用视频理解模型，生成视频 Caption
        vlm_client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        vid_completion = vlm_client.chat.completions.create(
            model="qwen-vl-max-latest",
            messages=[
                {"role": "system", "content": [{"type": "text", "text": "You are a helpful assistant."}]},
                {"role": "user", "content": [
                    {"type": "video_url", "video_url": {"url": video_url}},
                    {"type": "text", "text": "请用一句话描述这段视频的主要内容。"}
                ]}
            ]
        )
        video_caption = vid_completion.choices[0].message.content.strip()

        # 3. 打开视频，准备按秒抽帧
        cap = cv2.VideoCapture(vid_path)
        fps = cap.get(cv2.CAP_PROP_FPS) or 25.0
        interval_frames = max(int(fps / sample_fps), 1)
        frames_info: List[Dict] = []
        idx = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            if idx % interval_frames == 0:
                t_sec = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0
                frames_info.append({"frame": frame, "time": t_sec})
            idx += 1
        cap.release()
        os.remove(vid_path)

        if not frames_info:
            raise VideoKeyframeError("视频过短，未抽到任何帧。")

        # 4. 依次对每帧做：上传→图像Caption→LLM一致性判断
        count = 0
        llm_client = vlm_client  # 同一个 client 即可
        for info in frames_info:
            count += 1
            # 保存单帧到本地
            with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_img:
                img_path = tmp_img.name
                cv2.imwrite(img_path, info["frame"])

            # 上传到 OSS
            img_url = upload_and_get_signed_url(img_path)
            os.remove(img_path)

            # 图像理解模型生成图片Caption
            img_completion = vlm_client.chat.completions.create(
                model="qwen-vl-max-latest",
                messages=[
                    {"role": "system", "content": [{"type": "text", "text": "You are a helpful assistant."}]},
                    {"role": "user", "content": [
                        {"type": "image_url", "image_url": {"url": img_url}},
                        {"type": "text", "text": "请用一句话描述图中所示的场景或主体。"}
                    ]}
                ]
            )
            img_caption = img_completion.choices[0].message.content.strip()

            # LLM 判断两者主体是否一致
            cmp_completion = llm_client.chat.completions.create(
                model="qwen-plus",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user",
                     "content": f"请判断下面两句话的主要主体是否一致，若一致请只回答“一致”，否则回答“不一致”。\n"
                                f"视频描述：{video_caption}\n图片描述：{img_caption}"}
                ]
            )
            decision = cmp_completion.choices[0].message.content.strip()
            print(f"视频描述：{video_caption}\n图片描述：{img_caption}\n判断结果：{decision}")
            if "不一致" not in decision:
                return {
                    "video_caption": video_caption,
                    "img_caption": img_caption,
                    "frame_idx": count,
                    "task_id": "video_keyframe",
                    "task_status": "SUCCEEDED",
                    "image_url": img_url
                }

        # 5. 若都不匹配，返回最中间一帧
        mid = len(frames_info) // 2
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_img:
            mid_path = tmp_img.name
            cv2.imwrite(mid_path, frames_info[mid]["frame"])
        mid_url = upload_and_get_signed_url(mid_path)
        os.remove(mid_path)

        return {
            "video_caption": video_caption,
            "img_caption": "",
            "frame_idx": mid,
            "task_id": "video_keyframe",
            "task_status": "SUCCEEDED",
            "image_url": mid_url
        }

    except Exception as e:
        raise VideoKeyframeError(f"处理失败: {e}")
