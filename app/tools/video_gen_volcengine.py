# app/tools/video_gen_volcengine.py

import asyncio
import os
import time
from typing import Any, Dict

try:
    from volcenginesdkarkruntime import Ark
except ImportError:
    print("请安装火山方舟SDK: pip install volcengine-python-sdk[ark]")
    raise

from app.config.settings import settings
from app.tools.oss_upload import download_and_upload_video_to_oss

class VolcengineVideoGenError(RuntimeError):
    """火山方舟文生视频 API 错误"""
    pass

async def generate_video(
    prompt: str,
    *,
    size: str = "1280*720",
    duration: int = 5,
    model: str = None,
    poll_interval: float = 10.0,     # 轮询间隔（秒）
    max_poll_time: float = 1200.0,    # 轮询最长等待时间（秒，15分钟）
    max_retries: int = None          # 失败后最大重试次数
) -> Dict[str, Any]:
    """
    火山方舟文本生成视频，使用官方SDK。
    
    参数:
        prompt: 文本提示词
        size: 视频分辨率，支持: "1280*720", "720*1280", "960*960", "832*1088", "1088*832"
        duration: 视频时长（秒），支持: 5, 10
        model: 模型名称，默认使用settings中的配置
        poll_interval: 轮询间隔（秒）
        max_poll_time: 轮询最长等待时间（秒）
        max_retries: 失败后最大重试次数
    
    返回:
        {
            "prompt": prompt,
            "task_id": "...",
            "task_status": "...",
            "video_url": "...",
            "created_at": "...",
            "updated_at": "...",
            "duration": duration,
            "size": size,
            "model": model
        }
    
    抛出 VolcengineVideoGenError on unrecoverable failure.
    """
    # 使用配置文件中的默认值
    if model is None:
        model = settings.volcengine_video_model
    if max_retries is None:
        max_retries = settings.volcengine_max_retries
    
    # 初始化Ark客户端
    client = Ark(api_key=settings.volcengine_api_key, 
                 base_url=settings.volcengine_base_url)
    
    # 构建提示词（包含参数）
    if size and duration:
        # 根据文档示例，参数需要添加到提示词中
        size_param = f"--size {size}" if size != "1280*720" else ""
        duration_param = f"--dur {duration}" if duration != 5 else ""
        watermark_param = "--wm false"  # 禁用水印
        
        full_prompt = f"{prompt} {watermark_param} {size_param} {duration_param}".strip()
    else:
        full_prompt = f"{prompt} --wm false"

    attempt = 0
    while attempt < max_retries:
        attempt += 1
        
        try:
            print(f"[尝试 {attempt}] 创建视频生成任务...")
            print(f"提示词: {full_prompt}")
            print(f"模型: {model}")
            
            # 1. 创建视频生成任务
            create_result = client.content_generation.tasks.create(
                model=model,
                content=[
                    {
                        "type": "text",
                        "text": full_prompt
                    }
                ]
            )
            
            task_id = create_result.id
            print(f"[任务 {task_id}] 创建成功")
            
            # 2. 轮询查询状态
            elapsed = 0.0
            while elapsed < max_poll_time:
                try:
                    # 查询任务状态
                    get_result = client.content_generation.tasks.get(task_id=task_id)
                    status = get_result.status
                    
                    print(f"[任务 {task_id}] 状态: {status}, 已等待: {int(elapsed)}s")
                    
                    if status == "succeeded":
                        video_url = ""
                        if hasattr(get_result, 'content') and hasattr(get_result.content, 'video_url'):
                            video_url = get_result.content.video_url
                        
                        if not video_url:
                            raise VolcengineVideoGenError(f"[任务 {task_id}] 成功但未返回 video_url")
                        
                        # 下载视频并上传到OSS
                        try:
                            print(f"📥 [{task_id}] 开始下载视频并上传到OSS...")
                            oss_video_url = await download_and_upload_video_to_oss(
                                video_url, 
                                object_prefix="videos/seedance/"
                            )
                            print(f"🌐 [{task_id}] OSS视频URL: {oss_video_url}")
                            
                            return {
                                "prompt": prompt,
                                "task_id": task_id,
                                "task_status": status,
                                "video_url": oss_video_url,  # 返回OSS URL
                                "original_video_url": video_url,  # 保留原始火山URL
                                "created_at": getattr(get_result, 'created_at', ''),
                                "updated_at": getattr(get_result, 'updated_at', ''),
                                "duration": duration,
                                "size": size,
                                "model": model
                            }
                        except Exception as e:
                            print(f"⚠️ [{task_id}] OSS上传失败，返回原始URL: {e}")
                            # 如果OSS上传失败，仍然返回原始URL
                            return {
                                "prompt": prompt,
                                "task_id": task_id,
                                "task_status": status,
                                "video_url": video_url,
                                "created_at": getattr(get_result, 'created_at', ''),
                                "updated_at": getattr(get_result, 'updated_at', ''),
                                "duration": duration,
                                "size": size,
                                "model": model,
                                "oss_upload_error": str(e)
                            }
                    
                    elif status in {"failed", "cancelled"}:
                        error_msg = getattr(get_result, 'error', 'Unknown error')
                        print(f"[任务 {task_id}] 状态 {status}，错误: {error_msg}，准备重试...")
                        break  # 跳出轮询，进入重试循环
                    
                    elif status in {"queued", "running"}:
                        # 继续等待
                        await asyncio.sleep(poll_interval)
                        elapsed += poll_interval
                    else:
                        print(f"[任务 {task_id}] 未知状态: {status}，继续轮询...")
                        await asyncio.sleep(poll_interval)
                        elapsed += poll_interval
                        
                except Exception as e:
                    print(f"状态查询出错 ({e})，继续轮询...")
                    await asyncio.sleep(poll_interval)
                    elapsed += poll_interval
                    continue
            
            # 如果超过 max_poll_time 或状态为 failed/cancelled，下一轮重试
            print(f"[attempt {attempt}] 任务未在 {int(max_poll_time)}s 内完成，或状态失败，重试...")
            await asyncio.sleep(poll_interval)
            
        except Exception as e:
            if attempt >= max_retries:
                raise VolcengineVideoGenError(f"[attempt {attempt}] generation request failed: {e}")
            else:
                print(f"[attempt {attempt}] generation request failed, retrying... ({e})")
                await asyncio.sleep(poll_interval)
                continue

    # 超过最大重试次数仍未成功
    raise VolcengineVideoGenError(f"Maximum retry attempts exceeded ({max_retries}), video generation failed")


async def main():
    """测试函数"""
    try:
        print("开始测试火山方舟视频生成...")
        
        result = await generate_video(
            prompt="Van Gogh's 'Starry Night' come to life: swirling galaxies pulse with thick oil-paint textures, cypress trees sway in digital brushstrokes, and stars explode into dabs of chrome yellow. The entire canvas rotates slowly as new constellations form. Render in 8K with visible impasto ridges",
            size="1280*720",
            duration=5
        )
        
        print("生成成功!")
        print(f"任务ID: {result['task_id']}")
        print(f"视频URL: {result['video_url']}")
        print(f"创建时间: {result['created_at']}")
        print(f"更新时间: {result['updated_at']}")
        print(f"模型: {result['model']}")
        
    except VolcengineVideoGenError as e:
        print(f"生成失败: {e}")
    except Exception as e:
        print(f"未知错误: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 