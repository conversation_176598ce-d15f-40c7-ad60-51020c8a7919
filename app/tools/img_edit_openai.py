import os
import uuid
import base64
import tempfile
import asyncio
import httpx
import io
import time
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from typing import Any, Dict, List, Union, Optional
from openai import AsyncOpenAI
from app.tools.oss_upload import upload_and_get_signed_url
from app.config.settings import settings

# 🔧 注册AVIF支持 - 修复图片编辑问题
try:
    import pillow_avif
    from PIL import Image
    from pillow_avif import AvifImagePlugin
    
    # 手动注册AVIF插件以确保支持
    Image.register_decoder('AVIF', AvifImagePlugin.AvifImageFile)
    Image.register_extension('AVIF', '.avif')
    Image.register_mime('AVIF', 'image/avif')
    print("✅ [img_edit] AVIF支持已注册")
except ImportError:
    print("⚠️ [img_edit] pillow-avif-plugin未安装，可能无法处理AVIF格式图像")
except Exception as e:
    print(f"⚠️ [img_edit] AVIF注册失败: {e}")

from PIL import Image

class OpenAIImageEditError(RuntimeError):
    """OpenAI 图像编辑 API 调用失败"""
    pass

def _is_oss_url_expired(url: str) -> bool:
    """
    检测OSS签名URL是否已过期
    
    Args:
        url: OSS签名URL
        
    Returns:
        bool: True表示已过期，False表示未过期或不是OSS URL
    """
    try:
        # 解析URL
        parsed = urlparse(url)
        
        # 检查是否是阿里云OSS URL
        if 'aliyuncs.com' not in parsed.netloc:
            return False
        
        # 解析查询参数
        query_params = parse_qs(parsed.query)
        
        # 检查是否包含Expires参数
        if 'Expires' not in query_params:
            return False
        
        # 获取过期时间戳
        expires_timestamp = int(query_params['Expires'][0])
        current_timestamp = int(time.time())
        
        # 检查是否过期（提前5分钟警告）
        if expires_timestamp <= current_timestamp + 300:
            print(f"   ⚠️ OSS URL即将过期或已过期:")
            print(f"      过期时间: {datetime.fromtimestamp(expires_timestamp)}")
            print(f"      当前时间: {datetime.fromtimestamp(current_timestamp)}")
            return True
        
        return False
        
    except (ValueError, KeyError, IndexError) as e:
        print(f"   ⚠️ OSS URL过期检测失败: {e}")
        return False

async def edit_image(
    image_url: Union[str, List[str]],
    prompt: str,
    *,
    model: str = "gpt-image-1",
    size: str = "1024x1024",
    n: int = 1,
    timeout: float = 1200.0,
    max_retries: int = 3,
    reference_images: Optional[List[Dict]] = None
) -> Dict[str, Any]:
    """
    使用 OpenAI 的 gpt-image-1 images.edit 接口对图像进行编辑。
    支持单图编辑和多图合成。

    Args:
        image_url: 单个图像URL或多个图像URL列表
        prompt: 编辑指令
        model: 使用的模型
        size: 输出尺寸
        n: 生成图像数量
        timeout: 超时时间
        max_retries: 最大重试次数
        reference_images: 多参考图像信息（用于多图合成）

    返回值举例：
    {
        "prompt": "用户传入的 prompt 文本",
        "task_id": "随机生成的 UUID",
        "task_status": "SUCCEEDED",
        "image_url": "编辑后图像的URL"
    }
    """

    # 1. 从环境中读取 API Key and baseurl
    api_key = settings.openai_api_key
    base_url = settings.openai_base_url

    if not api_key:
        raise OpenAIImageEditError("缺少环境变量 OPENAI_API_KEY，请先设置。")

    # 2. 处理输入图像URLs
    if isinstance(image_url, str):
        image_urls = [image_url]
    else:
        image_urls = image_url

    # 如果有reference_images参数，添加到图像列表中
    if reference_images:
        print(f"🔗 检测到多参考图像: {len(reference_images)} 张")
        for ref in reference_images:
            if isinstance(ref, dict) and 'path' in ref:
                image_urls.append(ref['path'])
                print(f"   - 添加参考图: {ref.get('description', '未知')} ({ref['path']})")

    print(f"📥 准备处理 {len(image_urls)} 张图像进行编辑")
    
    # 3. 异步下载所有图像到本地临时文件
    temp_files = []
    try:
        async with httpx.AsyncClient(timeout=timeout) as http_client:
            for i, url in enumerate(image_urls):
                print(f"   下载图像 {i+1}/{len(image_urls)}: {url}")
                try:
                    # 🔧 添加OSS URL过期检测
                    if _is_oss_url_expired(url):
                        print(f"   ⚠️ 检测到OSS URL已过期: {url}")
                        raise OpenAIImageEditError(f"图像URL已过期，请使用有效的图像链接")
                    
                    # 🔧 添加适当的请求头，特别针对OSS和CDN
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                    
                    # 🔧 增加重试机制，处理网络波动
                    max_retries = 3
                    last_error = None
                    
                    for attempt in range(max_retries):
                        try:
                            print(f"   📥 下载尝试 {attempt + 1}/{max_retries}...")
                            resp = await http_client.get(url, headers=headers, follow_redirects=True)
                            
                            # 🔧 详细的HTTP状态码处理
                            if resp.status_code == 403:
                                if "oss" in url.lower() or "aliyuncs" in url.lower():
                                    raise OpenAIImageEditError(f"OSS图像访问被拒绝，可能是签名过期或权限不足")
                                else:
                                    raise OpenAIImageEditError(f"图像访问被拒绝 (403)，请检查URL权限")
                            elif resp.status_code == 404:
                                raise OpenAIImageEditError(f"图像不存在 (404)，请检查URL是否正确")
                            elif resp.status_code == 429:
                                if attempt < max_retries - 1:
                                    wait_time = 2 ** attempt
                                    print(f"   ⏳ 遇到限流，等待 {wait_time} 秒后重试...")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    raise OpenAIImageEditError(f"请求过于频繁 (429)，请稍后重试")
                            
                            resp.raise_for_status()
                            img_bytes = resp.content
                            
                            if len(img_bytes) == 0:
                                raise OpenAIImageEditError("下载的图像内容为空")
                            
                            print(f"   ✅ 图像下载成功，大小: {len(img_bytes)} bytes")
                            break
                            
                        except httpx.HTTPStatusError as e:
                            last_error = e
                            if attempt < max_retries - 1 and e.response.status_code in [429, 502, 503, 504]:
                                wait_time = 2 ** attempt
                                print(f"   ⏳ HTTP错误 {e.response.status_code}，等待 {wait_time} 秒后重试...")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                raise
                        except Exception as e:
                            last_error = e
                            if attempt < max_retries - 1:
                                wait_time = 2 ** attempt
                                print(f"   ⚠️ 下载失败，等待 {wait_time} 秒后重试: {e}")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                raise

                    # 🔧 修复AVIF处理：用PIL打开并转换为PNG
                    try:
                        # 使用PIL打开图像（支持AVIF）
                        img = Image.open(io.BytesIO(img_bytes))
                        
                        # 创建临时PNG文件
                        tmp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
                        
                        # 确保图像模式兼容PNG
                        if img.mode in ('RGBA', 'LA'):
                            # 已经有透明度，直接保存
                            img.save(tmp_file.name, 'PNG')
                        elif img.mode == 'P':
                            # 调色板模式，转换为RGBA
                            img = img.convert('RGBA')
                            img.save(tmp_file.name, 'PNG')
                        else:
                            # RGB等模式，直接保存
                            img.save(tmp_file.name, 'PNG')
                        
                        tmp_file.close()
                        temp_files.append(tmp_file.name)
                        print(f"   ✅ 图像转换成功: {img.format} -> PNG ({img.size})")
                        
                    except Exception as pil_error:
                        # 如果PIL处理失败，尝试直接保存字节（兼容旧逻辑）
                        print(f"   ⚠️ PIL处理失败，回退到直接保存: {pil_error}")
                        tmp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
                        tmp_file.write(img_bytes)
                        tmp_file.flush()
                        tmp_file.close()
                        temp_files.append(tmp_file.name)
                    
                except Exception as e:
                    # 清理已下载的文件
                    for temp_file in temp_files:
                        try:
                            os.unlink(temp_file)
                        except:
                            pass
                    raise OpenAIImageEditError(f"下载图像 {i+1} 失败: {e}")

    except Exception as e:
        raise OpenAIImageEditError(f"批量下载图像失败: {e}")

    # 4. 异步调用 AsyncOpenAI.images.edit
    client = AsyncOpenAI(
        api_key=api_key,
        base_url=base_url,
        timeout=timeout,
        max_retries=max_retries
    )

    try:
        # 打开所有图像文件
        file_handles = []
        for temp_file in temp_files:
            file_handles.append(open(temp_file, "rb"))

        try:
            # 构建编辑提示词
            if len(image_urls) > 1:
                edit_prompt = f"基于提供的 {len(image_urls)} 张参考图像，{prompt}"
                print(f"🎨 多图合成编辑: {edit_prompt}")
            else:
                edit_prompt = prompt
                print(f"🖼️ 单图编辑: {edit_prompt}")

            # 调用OpenAI API
            result = await client.images.edit(
                model=model,
                image=file_handles,  # 传入文件句柄列表
                prompt=edit_prompt,
                size=size,
                n=n
            )
        finally:
            # 关闭所有文件句柄
            for fh in file_handles:
                fh.close()

    except Exception as e:
        raise OpenAIImageEditError(f"调用 OpenAI 图像编辑失败: {e}")
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass

    # 5. 解析返回结果
    if not getattr(result, "data", None):
        raise OpenAIImageEditError("OpenAI 返回数据缺失 data 字段，无法继续。")
    
    item = result.data[0]

    # 5.1 如果返回了 URL，就异步下载该 URL 对应的图像
    if getattr(item, "url", None):
        try:
            async with httpx.AsyncClient(timeout=timeout) as http_client:
                r2 = await http_client.get(item.url)
                r2.raise_for_status()
                out_bytes = r2.content
        except Exception as e:
            raise OpenAIImageEditError(f"下载编辑后远程图像失败: {e}")

    # 5.2 否则如果返回了 b64_json，就直接 Base64 解码
    elif getattr(item, "b64_json", None):
        try:
            out_bytes = base64.b64decode(item.b64_json)
        except Exception as e:
            raise OpenAIImageEditError(f"Base64 解码失败: {e}")

    else:
        raise OpenAIImageEditError("OpenAI 返回的数据中既无 url 也无 b64_json，无法获取图像内容。")

    # 6. 将编辑后的字节写入本地临时文件，并上传到OSS
    tmp_out = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
    tmp_out.write(out_bytes)
    tmp_out.flush()
    tmp_out.close()

    try:
        # 上传到 OSS 并获取签名 URL
        signed_url = upload_and_get_signed_url(tmp_out.name, object_prefix="tmp_imgs/")
        print(f"✅ 编辑完成，已上传到OSS: {signed_url}")
    except Exception as e:
        raise OpenAIImageEditError(f"上传到 OSS 失败: {e}")
    finally:
        # 清理最终临时文件
        try:
            os.unlink(tmp_out.name)
        except:
            pass

    return {
        "prompt": edit_prompt,
        "task_id": str(uuid.uuid4()),
        "task_status": "SUCCEEDED",
        "image_url": signed_url,
        "input_image_count": len(image_urls)
    }


# 如果想单独在命令行测试，可运行以下代码
if __name__ == "__main__":
    async def _test_single():
        """测试单图编辑"""
        ori = "https://example.com/test.jpg"
        prompt = "change the cat to dog"
        try:
            res = await edit_image(ori, prompt)
            print("单图编辑完成，结果：", res)
        except Exception as e:
            print("单图编辑失败：", e)

    async def _test_multi():
        """测试多图合成"""
        images = [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        ]
        prompt = "将所有图像中的元素合成到一个场景中"
        try:
            res = await edit_image(images, prompt)
            print("多图合成完成，结果：", res)
        except Exception as e:
            print("多图合成失败：", e)

    async def _test_with_references():
        """测试带参考图像的编辑"""
        main_image = "https://example.com/main.jpg"
        reference_images = [
            {"path": "https://example.com/ref1.jpg", "description": "背景参考"},
            {"path": "https://example.com/ref2.jpg", "description": "风格参考"}
        ]
        prompt = "根据参考图像调整风格和背景"
        try:
            res = await edit_image(main_image, prompt, reference_images=reference_images)
            print("参考图像编辑完成，结果：", res)
        except Exception as e:
            print("参考图像编辑失败：", e)

    print("🧪 运行测试用例...")
    asyncio.run(_test_single())
    asyncio.run(_test_multi())
    asyncio.run(_test_with_references())