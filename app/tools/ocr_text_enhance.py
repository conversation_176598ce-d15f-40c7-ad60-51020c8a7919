"""
OCR + GPT + Pillow 文案增强工具
支持PaddleOCR文字提取、GPT文案优化、Pillow图像编辑
专为Celery容器环境优化，修复段错误问题
"""

import os
import tempfile
import asyncio
import logging
from typing import List, Dict, Any, Tuple, Optional
from io import BytesIO
import json

# 图像处理
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import requests

# OCR引擎 - 修复版本
try:
    from paddleocr import PaddleOCR
    PADDLE_OCR_AVAILABLE = True
except ImportError:
    PADDLE_OCR_AVAILABLE = False
    print("⚠️ PaddleOCR未安装，OCR功能将不可用")

# AI模型
from openai import AsyncOpenAI
from app.config.settings import settings
from app.tools.oss_upload import upload_and_get_signed_url

logger = logging.getLogger(__name__)

class OCRTextEnhanceError(RuntimeError):
    """OCR文案增强错误"""
    pass

class OCRTextEnhancer:
    """OCR + GPT + Pillow 文案增强器 - 修复版本"""
    
    def __init__(self):
        # 初始化PaddleOCR（延迟加载）
        self._ocr = None
        self._ocr_initialized = False
        self._ocr_failed = False  # 恢复OCR功能
        
        # 初始化OpenAI客户端
        self.openai_client = AsyncOpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            timeout=60.0
        )
        
        logger.info("🔧 OCR文案增强器初始化完成")
    
    @property
    def ocr(self):
        """延迟初始化PaddleOCR，使用最稳定的配置"""
        if not PADDLE_OCR_AVAILABLE or self._ocr_failed:
            raise OCRTextEnhanceError("PaddleOCR不可用或已失败")
        
        if not self._ocr_initialized:
            logger.info("🚀 正在初始化PaddleOCR...")
            
            try:
                # 设置环境变量以提高稳定性和避免段错误
                os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'  # 解决OpenMP冲突
                os.environ['OMP_NUM_THREADS'] = '1'          # 限制线程数避免段错误
                os.environ['OPENBLAS_NUM_THREADS'] = '1'     # 限制BLAS线程
                os.environ['MKL_NUM_THREADS'] = '1'          # 限制MKL线程
                os.environ['NUMEXPR_NUM_THREADS'] = '1'      # 限制NumExpr线程
                
                # 使用最稳定的PaddleOCR配置
                self._ocr = PaddleOCR(
                    use_angle_cls=False,     # 禁用角度分类器，减少内存使用
                    lang='ch',               # 中文
                    use_gpu=False,           # 强制使用CPU，避免GPU相关段错误
                    show_log=False,          # 关闭详细日志
                    enable_mkldnn=False,     # 禁用MKLDNN优化，避免段错误
                    cpu_threads=1,           # 限制CPU线程数为1
                    use_tensorrt=False,      # 禁用TensorRT
                    warmup=False,            # 跳过预热阶段
                    det_db_score_mode='fast' # 使用快速检测模式
                )
                self._ocr_initialized = True
                logger.info("✅ PaddleOCR初始化成功")
            except Exception as e:
                logger.error(f"❌ PaddleOCR初始化失败: {e}")
                self._ocr_failed = True
                raise OCRTextEnhanceError(f"PaddleOCR初始化失败: {e}")
        
        return self._ocr
    
    async def extract_and_enhance_text(
        self, 
        image_urls: List[str], 
        scenario: str = "auto"
    ) -> Dict[str, Any]:
        """
        完整的OCR + 文案增强流程
        """
        try:
            logger.info(f"🔍 开始处理 {len(image_urls)} 张图片，场景: {scenario}")
            
            # 1. 批量OCR文本提取
            extracted_texts = []
            for i, image_url in enumerate(image_urls):
                logger.info(f"📝 正在处理第 {i+1}/{len(image_urls)} 张图片")
                ocr_result = await self._extract_text_from_image(image_url)
                extracted_texts.append(ocr_result)
            
            # 2. 合并所有文本
            combined_text = self._combine_extracted_texts(extracted_texts)
            logger.info(f"📄 总提取文字: {len(combined_text)} 字符")
            
            # 3. 生成高质量卖点（限制3个）
            selling_points = await self._generate_selling_points(
                combined_text, scenario, image_urls
            )
            
            # 4. 可选：生成增强图片
            enhanced_images = []
            if scenario == "ecommerce" and len(selling_points) > 0:
                enhanced_images = await self._create_enhanced_images(
                    image_urls[:3], selling_points  # 最多处理3张图片
                )
            
            result = {
                "extracted_texts": extracted_texts,
                "selling_points": selling_points[:3],  # 确保只返回3个
                "enhanced_images": enhanced_images,
                "scenario": scenario,
                "total_text_length": len(combined_text)
            }
            
            logger.info(f"✅ 处理完成，生成 {len(selling_points)} 个卖点")
            return result
            
        except Exception as e:
            logger.error(f"❌ OCR文案增强失败: {str(e)}")
            raise OCRTextEnhanceError(f"处理失败: {e}")
    
    async def _extract_text_from_image(self, image_url: str) -> Dict[str, Any]:
        """使用PaddleOCR提取图片中的文字"""
        
        try:
            # 修复容器环境中的URL访问
            if image_url.startswith('http://localhost:8000'):
                # 替换为容器内访问地址
                image_url = image_url.replace('http://localhost:8000', 'http://django:8000')
            elif image_url.startswith('http://127.0.0.1:8000'):
                # 替换为容器内访问地址
                image_url = image_url.replace('http://127.0.0.1:8000', 'http://django:8000')
            elif image_url.startswith('http://127.0.0.1:80'):
                image_url = image_url.replace('http://127.0.0.1:80', 'http://django:8000')
            elif image_url.startswith('http://127.0.0.1'):
                image_url = image_url.replace('http://127.0.0.1', 'http://django:8000')
            
            logger.info(f"🔍 正在下载图片: {image_url}")
            
            # 下载图片
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
                tmp.write(response.content)
                tmp_path = tmp.name
            
            try:
                # PaddleOCR识别 - 增加异常处理
                logger.info("🔍 PaddleOCR开始识别...")
                
                # 使用更安全的方式调用OCR
                try:
                    results = self.ocr.ocr(tmp_path)
                except Exception as ocr_error:
                    logger.error(f"PaddleOCR段错误: {ocr_error}")
                    # 如果出现段错误，标记OCR失败并抛出友好错误
                    self._ocr_failed = True
                    raise OCRTextEnhanceError("OCR服务暂不可用，请稍后再试")
                
                texts = []
                coordinates = []
                confidences = []
                
                if results and results[0]:
                    for line in results[0]:
                        # line[0]: 坐标, line[1]: (文字, 置信度)
                        if len(line) >= 2 and line[1]:
                            text, confidence = line[1]
                            texts.append(text)
                            coordinates.append(line[0])
                            confidences.append(confidence)
                
                logger.info(f"📝 识别到文字: {len(texts)} 条")
                for i, text in enumerate(texts[:3]):  # 只显示前3条
                    logger.info(f"   {i+1}. {text} (置信度: {confidences[i]:.2f})")
                
                result = {
                    "image_url": image_url,
                    "texts": texts,
                    "coordinates": coordinates,
                    "confidences": confidences,
                    "total_texts": len(texts),
                    "error": None
                }
                
                return result
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(tmp_path)
                except:
                    pass
            
        except Exception as e:
            error_msg = f"OCR识别失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise OCRTextEnhanceError(f"OCR服务暂不可用，请稍后再试: {error_msg}")
    
    def _combine_extracted_texts(self, extracted_texts: List[Dict]) -> str:
        """合并所有提取的文本"""
        all_texts = []
        
        for extract_result in extracted_texts:
            if extract_result.get("texts"):
                all_texts.extend(extract_result["texts"])
        
        # 去重并保持顺序
        unique_texts = []
        seen = set()
        for text in all_texts:
            if text not in seen and len(text.strip()) > 1:
                unique_texts.append(text)
                seen.add(text)
        
        return "\n".join(unique_texts)
    
    async def _generate_selling_points(
        self, 
        extracted_text: str, 
        scenario: str,
        image_urls: List[str] = None
    ) -> List[str]:
        """使用GPT生成3个高质量卖点"""
        
        # 根据场景选择提示词
        if scenario == "ecommerce":
            if not extracted_text.strip():
                # 没有文字时，生成通用的产品卖点建议
                logger.info("📝 没有提取到文字，生成通用产品卖点")
                user_prompt = f"""我上传了 {len(image_urls) if image_urls else 1} 张产品图片，但OCR没有识别到任何文字。

请根据一般产品营销规律，生成3个通用的产品卖点模板，用户可以根据自己的产品特点进行修改：

图片数量: {len(image_urls) if image_urls else 1} 张
要求: 生成实用的卖点模板，让用户可以灵活应用"""
            else:
                user_prompt = f"提取的产品文本内容：\n{extracted_text[:2000]}"
                
            system_prompt = """
你是专业的电商文案专家。根据产品信息，生成3个最具吸引力的卖点。

要求：
1. 突出产品核心竞争优势
2. 语言生动有力，具有购买冲动
3. 每个卖点15-25字
4. 避免夸大不实描述
5. 如果没有具体产品信息，生成通用实用的卖点模板

返回JSON格式：
{
    "selling_points": [
        "卖点1文案",
        "卖点2文案", 
        "卖点3文案"
    ]
}
"""
        else:
            if not extracted_text.strip():
                logger.info("📝 没有提取到文字，生成通用要点建议")
                user_prompt = "请生成3个通用的要点模板，用户可以根据自己的内容进行修改"
            else:
                user_prompt = f"提取的文本内容：\n{extracted_text[:2000]}"
                
            system_prompt = """
你是文案优化专家。从文本中提取并优化出3个关键要点。

要求：
1. 保持原意，提升表达力
2. 突出最重要信息
3. 语言简洁有力
4. 按重要性排序
5. 如果没有具体文本，生成通用模板

返回JSON格式：
{
    "selling_points": [
        "要点1",
        "要点2",
        "要点3"
    ]
}
"""
        
        try:
            logger.info("🤖 正在生成AI文案...")
            
            response = await self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=800,
                timeout=30
            )
            
            content = response.choices[0].message.content
            logger.info(f"🤖 AI回复: {content[:100]}...")
            
            # 解析JSON响应
            try:
                result = json.loads(content)
                selling_points = result.get("selling_points", [])
                
                # 确保只返回3个卖点，并转换为统一格式
                processed_points = []
                for i, point in enumerate(selling_points[:3]):
                    if isinstance(point, str):
                        # 新格式：直接是字符串
                        processed_points.append(point)
                    elif isinstance(point, dict) and 'title' in point:
                        # 旧格式：包含title的对象
                        processed_points.append(point['title'])
                    else:
                        # 其他格式，转为字符串
                        processed_points.append(str(point))
                
                logger.info(f"✅ 生成 {len(processed_points)} 个卖点")
                for i, point in enumerate(processed_points):
                    logger.info(f"   {i+1}. {point}")
                
                return processed_points
                
            except json.JSONDecodeError as je:
                logger.warning(f"⚠️ JSON解析失败，使用降级处理: {je}")
                return self._fallback_text_processing(extracted_text, scenario)
            
        except Exception as e:
            logger.error(f"❌ GPT生成失败: {e}")
            return self._fallback_text_processing(extracted_text, scenario)
    
    def _fallback_text_processing(self, text: str, scenario: str = "general") -> List[str]:
        """降级文本处理方案"""
        if not text.strip():
            # 没有文本时返回通用模板
            if scenario == "ecommerce":
                return [
                    "品质保证，值得信赖",
                    "用户好评，口碑产品", 
                    "限时优惠，不容错过"
                ]
            else:
                return [
                    "核心要点一：请根据内容填写",
                    "核心要点二：请根据内容填写",
                    "核心要点三：请根据内容填写"
                ]
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        # 简单的关键词提取
        points = []
        for i, line in enumerate(lines[:3]):
            if len(line) > 5:  # 过滤太短的行
                points.append(f"要点{i+1}: {line[:30]}...")
        
        # 如果提取的要点不够3个，补充通用要点
        while len(points) < 3:
            points.append(f"附加要点{len(points)+1}: 基于文本内容的补充说明")
        
        return points[:3]
    
    async def _create_enhanced_images(
        self, 
        image_urls: List[str], 
        selling_points: List[str]
    ) -> List[str]:
        """使用Pillow在图片上添加卖点文案"""
        
        enhanced_urls = []
        
        for i, image_url in enumerate(image_urls):
            if i < len(selling_points):
                try:
                    # 适配新的字符串格式卖点
                    selling_point_text = selling_points[i]
                    if isinstance(selling_point_text, dict):
                        # 兼容旧格式
                        selling_point_text = selling_point_text.get("title", str(selling_point_text))
                    
                    enhanced_url = await self._add_text_to_image(
                        image_url, 
                        str(selling_point_text)
                    )
                    enhanced_urls.append(enhanced_url)
                    logger.info(f"✅ 图片 {i+1} 文案添加完成")
                except Exception as e:
                    logger.error(f"❌ 图片 {i+1} 文案添加失败: {e}")
                    enhanced_urls.append(image_url)  # 降级使用原图
        
        return enhanced_urls
    
    async def _add_text_to_image(
        self, 
        image_url: str, 
        text: str
    ) -> str:
        """在图片上添加文字水印"""
        
        try:
            # 修复容器环境中的URL访问
            if image_url.startswith('http://localhost:8000'):
                image_url = image_url.replace('http://localhost:8000', 'http://django:8000')
            elif image_url.startswith('http://127.0.0.1:8000'):
                image_url = image_url.replace('http://127.0.0.1:8000', 'http://django:8000')
            elif image_url.startswith('http://127.0.0.1:80'):
                image_url = image_url.replace('http://127.0.0.1:80', 'http://django:8000')
            elif image_url.startswith('http://127.0.0.1'):
                image_url = image_url.replace('http://127.0.0.1', 'http://django:8000')
            
            logger.info(f"🖼️ 正在下载图片添加文案: {image_url}")
            
            # 下载原图
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            # 打开图片
            image = Image.open(BytesIO(response.content))
            
            # 确保是RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 创建绘图对象
            draw = ImageDraw.Draw(image)
            
            # 计算文字位置和字体大小
            img_width, img_height = image.size
            font_size = max(24, min(img_width // 20, 48))  # 动态字体大小
            
            # 尝试加载中文字体
            font = self._get_font(font_size)
            
            # 获取文字尺寸
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算文字位置（底部居中）
            x = (img_width - text_width) // 2
            y = img_height - text_height - 50  # 距离底部50像素
            
            # 添加半透明背景
            padding = 15
            bg_bbox = [
                x - padding, 
                y - padding, 
                x + text_width + padding, 
                y + text_height + padding
            ]
            
            # 创建半透明层
            overlay = Image.new('RGBA', image.size, (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)
            
            # 绘制圆角矩形背景
            overlay_draw.rounded_rectangle(
                bg_bbox, 
                radius=8, 
                fill=(0, 0, 0, 150)  # 半透明黑色
            )
            
            # 合成图片
            image = image.convert('RGBA')
            image = Image.alpha_composite(image, overlay)
            image = image.convert('RGB')
            
            # 添加白色文字
            draw = ImageDraw.Draw(image)
            draw.text((x, y), text, font=font, fill=(255, 255, 255))
            
            # 保存到临时文件并上传OSS
            with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
                image.save(tmp.name, "JPEG", quality=90, optimize=True)
                enhanced_url = upload_and_get_signed_url(
                    tmp.name, 
                    object_prefix="enhanced_imgs/"
                )
                
            # 清理临时文件
            try:
                os.unlink(tmp.name)
            except:
                pass
                
            return enhanced_url
            
        except Exception as e:
            logger.error(f"❌ 图片文字添加失败: {e}")
            raise
    
    def _get_font(self, size: int):
        """获取合适的字体"""
        # 常见的中文字体路径
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",           # macOS
            "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",  # Ubuntu
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # 通用
            "/Windows/Fonts/msyh.ttc",                      # Windows
        ]
        
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, size)
            except:
                continue
        
        # 降级到默认字体
        try:
            return ImageFont.load_default()
        except:
            # 最后的降级方案
            return ImageFont.load_default()

# 全局单例实例（延迟初始化）
_ocr_enhancer_instance = None

def get_ocr_enhancer() -> OCRTextEnhancer:
    """获取OCR增强器单例实例"""
    global _ocr_enhancer_instance
    if _ocr_enhancer_instance is None:
        _ocr_enhancer_instance = OCRTextEnhancer()
    return _ocr_enhancer_instance

# 兼容性别名
ocr_enhancer = get_ocr_enhancer() 