# app/tools/firstlast2video_volcengine.py

import asyncio
import os
import time
from typing import Any, Dict, Optional

try:
    from volcenginesdkarkruntime import Ark
except ImportError:
    print("请安装火山方舟SDK: pip install volcengine-python-sdk[ark]")
    raise

from app.config.settings import settings

class VolcengineFirstLast2VideoError(RuntimeError):
    """火山方舟首尾帧生成视频 API 错误"""
    pass

async def firstlast_to_video(
    prompt: str,
    first_frame_url: str,
    last_frame_url: str,
    *,
    resolution: str = "720p",
    duration: int = 5,
    model: str = "doubao-seedance-1-0-lite-i2v-250428",
    poll_interval: float = 10.0,     # 轮询间隔（秒）
    max_poll_time: float = 900.0,    # 轮询最长等待时间（秒，15分钟）
    max_retries: int = None          # 失败后最大重试次数
) -> Dict[str, Any]:
    """
    火山方舟首尾帧生成视频，使用官方SDK。
    
    参数:
        prompt: 文本提示词
        first_frame_url: 首帧图片的URL
        last_frame_url: 尾帧图片的URL
        resolution: 视频分辨率，支持: "720p", "1080p"
        duration: 视频时长（秒），支持: 5, 10
                          model: 模型名称，默认使用 doubao-seedance-1-0-lite-i2v-250428
        poll_interval: 轮询间隔（秒）
        max_poll_time: 轮询最长等待时间（秒）
        max_retries: 失败后最大重试次数
    
    返回:
        {
            "prompt": prompt,
            "task_id": "...",
            "task_status": "...",
            "video_url": "...",
            "created_at": "...",
            "updated_at": "...",
            "duration": duration,
            "resolution": resolution,
            "model": model
        }
    
    抛出 VolcengineFirstLast2VideoError on unrecoverable failure.
    """
    # 使用配置文件中的默认值
    if max_retries is None:
        max_retries = settings.volcengine_max_retries
    
    # 初始化Ark客户端
    client = Ark(api_key=settings.volcengine_api_key)
    
    # 构建提示词（包含参数）
    resolution_param = f"--rs {resolution}" if resolution != "720p" else f"--rs {resolution}"
    duration_param = f"--dur {duration}" if duration != 5 else f"--dur {duration}"
    watermark_param = "--wm false"  # 禁用水印
    
    full_prompt = f"{prompt} {watermark_param} {resolution_param} {duration_param}".strip()

    attempt = 0
    while attempt < max_retries:
        attempt += 1
        
        try:
            print(f"[尝试 {attempt}] 创建首尾帧生成视频任务...")
            print(f"提示词: {full_prompt}")
            print(f"首帧URL: {first_frame_url}")
            print(f"尾帧URL: {last_frame_url}")
            print(f"模型: {model}")
            
            # 1. 创建首尾帧生成视频任务
            create_result = client.content_generation.tasks.create(
                model=model,
                content=[
                    {
                        # 文本提示词与参数组合
                        "type": "text",
                        "text": full_prompt
                    },
                    {
                        # 首帧图片URL
                        "type": "image_url",
                        "image_url": {
                            "url": first_frame_url
                        },
                        "role": "first_frame"
                    },
                    {
                        # 尾帧图片URL
                        "type": "image_url",
                        "image_url": {
                            "url": last_frame_url
                        },
                        "role": "last_frame"
                    }
                ]
            )
            
            task_id = create_result.id
            print(f"[首尾帧生成视频任务 {task_id}] 创建成功")
            
            # 2. 轮询查询状态
            elapsed = 0.0
            while elapsed < max_poll_time:
                try:
                    # 查询任务状态
                    get_result = client.content_generation.tasks.get(task_id=task_id)
                    status = get_result.status
                    
                    print(f"[首尾帧生成视频任务 {task_id}] 状态: {status}, 已等待: {int(elapsed)}s")
                    
                    if status == "succeeded":
                        video_url = ""
                        if hasattr(get_result, 'content') and hasattr(get_result.content, 'video_url'):
                            video_url = get_result.content.video_url
                        
                        if not video_url:
                            raise VolcengineFirstLast2VideoError(f"[首尾帧生成视频任务 {task_id}] 成功但未返回 video_url")
                        
                        return {
                            "prompt": prompt,
                            "task_id": task_id,
                            "task_status": status,
                            "video_url": video_url,
                            "created_at": getattr(get_result, 'created_at', ''),
                            "updated_at": getattr(get_result, 'updated_at', ''),
                            "duration": duration,
                            "resolution": resolution,
                            "model": model
                        }
                    
                    elif status in {"failed", "cancelled"}:
                        error_msg = getattr(get_result, 'error', 'Unknown error')
                        print(f"[首尾帧生成视频任务 {task_id}] 状态 {status}，错误: {error_msg}，准备重试...")
                        break  # 跳出轮询，进入重试循环
                    
                    elif status in {"queued", "running"}:
                        # 继续等待
                        await asyncio.sleep(poll_interval)
                        elapsed += poll_interval
                    else:
                        print(f"[首尾帧生成视频任务 {task_id}] 未知状态: {status}，继续轮询...")
                        await asyncio.sleep(poll_interval)
                        elapsed += poll_interval
                        
                except Exception as e:
                    print(f"状态查询出错 ({e})，继续轮询...")
                    await asyncio.sleep(poll_interval)
                    elapsed += poll_interval
                    continue
            
            # 如果超过 max_poll_time 或状态为 failed/cancelled，下一轮重试
            print(f"[attempt {attempt}] 任务未在 {int(max_poll_time)}s 内完成，或状态失败，重试...")
            await asyncio.sleep(poll_interval)
            
        except Exception as e:
            if attempt >= max_retries:
                raise VolcengineFirstLast2VideoError(f"[attempt {attempt}] generation request failed: {e}")
            else:
                print(f"[attempt {attempt}] generation request failed, retrying... ({e})")
                await asyncio.sleep(poll_interval)
                continue

    # 超过最大重试次数仍未成功
    raise VolcengineFirstLast2VideoError(f"Maximum retry attempts exceeded ({max_retries}), first-last frame to video generation failed")


async def main():
    """测试函数"""
    try:
        print("开始测试火山方舟首尾帧生成视频...")
        
        # 测试图片URL（使用官方示例图片）
        first_frame_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_first_frame.png"
        last_frame_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_last_frame.png"
        test_prompt = "CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀。小鸟羽毛细腻，胸前有独特的花纹，背景是蓝天白云，阳光明媚。镜头跟随小鸟向上移动，展现出小鸟飞翔的姿态和天空的广阔。近景，仰视视角。"
        
        result = await firstlast_to_video(
            prompt=test_prompt,
            first_frame_url=first_frame_url,
            last_frame_url=last_frame_url,
            resolution="720p",
            duration=5
        )
        
        print("生成成功!")
        print(f"任务ID: {result['task_id']}")
        print(f"视频URL: {result['video_url']}")
        print(f"创建时间: {result['created_at']}")
        print(f"更新时间: {result['updated_at']}")
        print(f"模型: {result['model']}")
        print(f"原始提示词: {result['prompt']}")
        print(f"分辨率: {result['resolution']}")
        print(f"时长: {result['duration']}秒")
        
    except VolcengineFirstLast2VideoError as e:
        print(f"生成失败: {e}")
    except Exception as e:
        print(f"未知错误: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 