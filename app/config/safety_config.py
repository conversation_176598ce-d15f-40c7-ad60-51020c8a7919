"""
安全检查配置文件
用于定义关键词匹配规则和安全策略，避免硬编码
"""

class SafetyConfig:
    """安全检查配置类"""
    
    # 关键词匹配策略
    KEYWORD_MATCH_STRATEGY = "exact"  # exact: 精确匹配, loose: 宽松匹配, strict: 严格匹配
    
    # 文本分隔符定义
    TEXT_SEPARATORS = [
        ' ',     # 空格
        '\t',    # 制表符
        '\n',    # 换行
        '.',     # 句号
        '，',    # 中文逗号
        ',',     # 英文逗号
        '。',    # 中文句号
        ';',     # 分号
        '；',    # 中文分号
        ':',     # 冒号
        '：',    # 中文冒号
        '!',     # 感叹号
        '！',    # 中文感叹号
        '?',     # 问号
        '？',    # 中文问号
        '-',     # 连字符
        '_',     # 下划线
        '(',     # 左括号
        ')',     # 右括号
        '（',    # 中文左括号
        '）',    # 中文右括号
        '[',     # 左方括号
        ']',     # 右方括号
        '【',    # 中文左方括号
        '】',    # 中文右方括号
        '「',    # 中文左引号
        '」',    # 中文右引号
        '《',    # 中文左书名号
        '》',    # 中文右书名号
        '、',    # 中文顿号
    ]
    
    # 安全消息模板
    SAFETY_MESSAGES = {
        "political": "抱歉，您的请求涉及政治敏感内容，无法处理。请尝试其他创意内容。",
        "explicit": "抱歉，您的请求包含不当内容，无法处理。请尝试其他创意内容。",
        "violence": "抱歉，您的请求包含暴力内容，无法处理。请尝试其他创意内容。",
        "default": "抱歉，您的请求不符合内容安全政策，无法处理。请尝试其他创意内容。"
    }
    
    # 匹配严格程度配置
    MATCH_STRICTNESS = {
        "exact": {
            "require_word_boundary": True,
            "case_sensitive": False,
            "allow_partial_match": False,
            "min_match_ratio": 1.0
        },
        "loose": {
            "require_word_boundary": False,
            "case_sensitive": False,
            "allow_partial_match": True,
            "min_match_ratio": 0.8
        },
        "strict": {
            "require_word_boundary": True,
            "case_sensitive": True,
            "allow_partial_match": False,
            "min_match_ratio": 1.0
        }
    }
    
    @classmethod
    def get_separator_pattern(cls) -> str:
        """获取分隔符的正则表达式模式"""
        import re
        escaped_separators = [re.escape(sep) for sep in cls.TEXT_SEPARATORS]
        return '[' + ''.join(escaped_separators) + ']'
    
    @classmethod
    def get_current_strategy(cls) -> dict:
        """获取当前匹配策略的配置"""
        return cls.MATCH_STRICTNESS.get(cls.KEYWORD_MATCH_STRATEGY, cls.MATCH_STRICTNESS["exact"])
    
    @classmethod
    def get_safety_message(cls, category: str = "default") -> str:
        """获取指定类别的安全消息"""
        return cls.SAFETY_MESSAGES.get(category, cls.SAFETY_MESSAGES["default"]) 