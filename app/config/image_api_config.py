"""
图片API兼容性配置
定义不同AI服务对图片输入的格式要求
"""

from enum import Enum
from typing import Dict, Any

class ImageProcessType(Enum):
    """图片处理类型"""
    URL = "url"              # 直接使用URL
    BASE64 = "base64"        # 转换为Base64编码
    CLEAN_URL = "clean_url"  # 清理URL（移除查询参数）

class APIImageConfig:
    """API图片配置类"""
    
    # 各种API的图片处理策略配置
    API_CONFIGS = {
        # 阿里巴巴达摩院相关
        'seedance': {
            'process_type': ImageProcessType.CLEAN_URL,
            'supports_signed_url': False,
            'requires_public_access': True,
            'supported_formats': ['jpg', 'jpeg', 'png', 'webp'],
            'max_file_size_mb': 10,
            'description': 'Seedance API需要无查询参数的公共URL'
        },
        
        # 火山引擎/字节跳动
        'volcengine': {
            'process_type': ImageProcessType.BASE64,
            'supports_signed_url': True,  # 因为会下载转base64，所以支持签名URL
            'requires_public_access': False,
            'supported_formats': ['jpg', 'jpeg', 'png', 'webp'],
            'max_file_size_mb': 20,
            'description': '火山引擎使用Base64编码，兼容私有OSS桶'
        },
        
        # Google Veo3
        'veo3': {
            'process_type': ImageProcessType.BASE64,
            'supports_signed_url': True,  # 因为会下载转base64，所以支持签名URL
            'requires_public_access': False,
            'supported_formats': ['jpg', 'jpeg', 'png', 'webp', 'gif'],
            'max_file_size_mb': 32,
            'description': 'Veo3使用Base64编码，兼容性最好'
        },
        
        # 快手可灵
        'kling': {
            'process_type': ImageProcessType.CLEAN_URL,
            'supports_signed_url': False,
            'requires_public_access': True,
            'supported_formats': ['jpg', 'jpeg', 'png'],
            'max_file_size_mb': 10,
            'description': 'Kling需要无查询参数的公共URL'
        },
        
        # OpenAI (DALL-E, GPT-4V等)
        'openai': {
            'process_type': ImageProcessType.BASE64,
            'supports_signed_url': True,
            'requires_public_access': False,
            'supported_formats': ['jpg', 'jpeg', 'png', 'webp'],
            'max_file_size_mb': 20,
            'description': 'OpenAI支持Base64编码和公共URL'
        },
        
        # 通用/未知API的默认策略
        'default': {
            'process_type': ImageProcessType.CLEAN_URL,
            'supports_signed_url': False,
            'requires_public_access': True,
            'supported_formats': ['jpg', 'jpeg', 'png'],
            'max_file_size_mb': 10,
            'description': '默认使用URL清理策略'
        }
    }
    
    @classmethod
    def get_config(cls, api_type: str) -> Dict[str, Any]:
        """
        获取指定API的配置
        
        Args:
            api_type: API类型名称
            
        Returns:
            API配置字典
        """
        api_type = api_type.lower().strip()
        return cls.API_CONFIGS.get(api_type, cls.API_CONFIGS['default'])
    
    @classmethod
    def get_process_type(cls, api_type: str) -> ImageProcessType:
        """获取API的图片处理类型"""
        config = cls.get_config(api_type)
        return config['process_type']
    
    @classmethod
    def supports_signed_url(cls, api_type: str) -> bool:
        """检查API是否支持签名URL"""
        config = cls.get_config(api_type)
        return config['supports_signed_url']
    
    @classmethod
    def requires_public_access(cls, api_type: str) -> bool:
        """检查API是否需要公共访问权限"""
        config = cls.get_config(api_type)
        return config['requires_public_access']
    
    @classmethod
    def get_supported_formats(cls, api_type: str) -> list:
        """获取API支持的图片格式"""
        config = cls.get_config(api_type)
        return config['supported_formats']
    
    @classmethod
    def get_max_file_size(cls, api_type: str) -> int:
        """获取API支持的最大文件大小（MB）"""
        config = cls.get_config(api_type)
        return config['max_file_size_mb']
    
    @classmethod
    def list_all_apis(cls) -> Dict[str, str]:
        """列出所有支持的API及其描述"""
        return {
            api: config['description'] 
            for api, config in cls.API_CONFIGS.items()
            if api != 'default'
        }
    
    @classmethod
    def get_compatibility_matrix(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取兼容性矩阵，用于前端展示或调试
        
        Returns:
            兼容性信息字典
        """
        matrix = {}
        for api, config in cls.API_CONFIGS.items():
            if api == 'default':
                continue
                
            matrix[api] = {
                'strategy': config['process_type'].value,
                'signed_url_support': '✅' if config['supports_signed_url'] else '❌',
                'public_access_required': '✅' if config['requires_public_access'] else '❌',
                'formats': ', '.join(config['supported_formats']),
                'max_size': f"{config['max_file_size_mb']}MB",
                'description': config['description']
            }
        
        return matrix

# 便捷访问实例
image_api_config = APIImageConfig()

# 导出常用函数
def get_image_process_strategy(api_type: str) -> str:
    """
    获取图片处理策略的简化接口
    
    Args:
        api_type: API类型
        
    Returns:
        'url' | 'base64' | 'clean_url'
    """
    return image_api_config.get_process_type(api_type).value 