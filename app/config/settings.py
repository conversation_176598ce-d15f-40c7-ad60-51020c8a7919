# app/config/settings.py
from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Dict, Literal
import os
import warnings


# 抑制Django AllAuth MySQL警告
warnings.filterwarnings('ignore', message='MySQL does not support unique constraints with conditions.')
warnings.filterwarnings('ignore', category=UserWarning, module='allauth')

class Settings(BaseSettings):
    tool_costs: Dict[str, float] = {
        "img_gen": 0.2/7.5,           # 图片生成: 0.2元/张
        "img_edit": (0.2/7.5),        # 图片编辑: 与img_gen保持一致的比例
        "video_gen": (0.7*5)/7.5,     # 文生视频: 0.7元/秒
        "img2video": (0.7*5)/7.5,     # 图生视频: 0.7元/秒 (新增可灵I2V功能)
        "video_keyframe": 0.001/7.5,  # 视频关键帧提取: 0.001元/次
        "chat": 0.0,                  # 聊天对话: 免费服务
    }
    # 路由 Qwen API
    dashscope_api_key: str
    dashscope_base_url: str
    llm_model: str = ""  # 默认使用 qwen-plus 模型
    api_version: Literal["auto", "plus", "max"] = Field(default="plus", env="API_VERSION")  # 从环境变量API_VERSION读取
    max_chat_history_length: int = 50  # 🎯 增加历史长度限制：从10增加到50，保留更多媒体上下文
    
    # Agent架构选择
    use_langgraph_agent: bool = os.getenv("USE_LANGGRAPH_AGENT", "false").lower() == "true"
    enable_legacy_router: bool = os.getenv("ENABLE_LEGACY_ROUTER", "true").lower() == "true" 
    
    # Agent循环控制
    max_chat_reverts: int = 2  # ChatAgent-PlanAgent最大循环次数，避免无限循环
    
    #openai api 
    openai_base_url: str = ""
    openai_api_key: str = ""

    # 文本生成图像 API
    image_gen_api_key: str = ""
    image_gen_base_url: str = ""
    image_gen_task_base_url: str = ""

    # 图像编辑 API
    img_edit_api_key: str = ""
    img_edit_base_url: str = ""
    img_edit_task_base_url: str = ""
    img_edit_api_key_gemini: str = ""

    # 文本生成视频 API
    video_gen_api_key: str = ""
    video_gen_base_url: str = ""
    video_task_base_url: str = ""
    
    # 图像到视频 API
    img2video_api_key: str = ""
    img2video_base_url: str = ""
    img2video_task_base_url: str = ""

    # Gemini API
    gemini_api_key: str = ""

    # 可灵开放平台API
    keling_api_key: str = ""
    keling_secret_key: str = ""
    video_duration: str = "5"

    # VEO3视频生成API
    veo3_base_url: str = ""
    veo3_api_key: str = ""
    veo3_model: str = "veo3"
    veo3_max_retries: int = 1
    veo3_enable_caption_merge: bool = False  # 是否启用VEO3的图像描述和caption合并功能，默认关闭

    # 火山方舟视频生成API (doubao-seedance)
    volcengine_api_key: str = ""
    volcengine_base_url: str = ""
    volcengine_video_model: str = ""
    volcengine_video_model_i2v: str = ""
    volcengine_max_retries: int = Field(default=1, env="VOLCENGINE_MAX_RETRIES")  # 从环境变量读取
    
    # 安全检查配置
    skip_safety_check: bool = Field(default=True, env="SKIP_SAFETY_CHECK")  # 从环境变量读取，默认跳过安全检查

    # 允许 .env 中出现额外（DB_*）字段，但不会作为属性载入
    model_config = {
        "env_file": "docker/.env",
        "env_file_encoding": "utf-8",
        "extra": "ignore"
    }
    
    ICL_PROMPT: str = """
        You are a JSON routing assistant. Based on the user's prompt and any provided media URLs, your task is to identify the user's intent and extract the necessary arguments.
        Additionally, you need to generate an "extend_text" field, which is a rewritten, more detailed, and professional prompt suitable for visual generation models.

        **Core Logic:**
        1.  **Analyze Intent**: Determine the primary intent from: `img_gen`, `img_edit`, `video_gen`, `video_edit`, `img2video`, `plan`, `chat`.
        2.  **Extract Arguments**: Populate the `args` object with `text` (the user's core request) and `image_url` or `video_url` if provided.
        3.  **Rewrite for `extend_text`**:
            *   **For Generation (`img_gen`, `video_gen`)**: Expand the user's simple description into a rich, detailed scene. Add details about style, composition, lighting, and mood.
            *   **For Editing (`img_edit`, `video_edit`, `img2video`)**: **Do not just repeat the user's command.** Creatively translate the user's editing instruction into a descriptive prompt. For example, if the user says "make the colors more vibrant," `extend_text` should be something like "A version of the image with boosted color saturation, enhanced contrast, and vibrant, eye-catching hues, creating a lively and dynamic mood." If the user says "change to watercolor style," `extend_text` should be "A watercolor painting version of the image, with soft washes of color, delicate brushstrokes, and a dreamy, artistic feel."
            *   **For `chat` or `plan`**: `extend_text` can be empty, unless a node within the plan requires prompt enhancement.
            *   **Safety and Compliance Rewriting (IMPORTANT)**: When rewriting the prompt for `extend_text`, apply the following rules to ensure safety and compliance:
               *   **Celebrity Depiction**:
                    - **Restriction**: Do not create images of Chinese leaders (living or deceased). For such requests, silently modify the prompt.
                    - **Modification Method**: When a restricted figure is requested, replace them with a generic but detailed description. Retain non-identifying attributes like gender, build, and nationality, but remove all personally identifying information. The goal is to fulfill the user's creative intent (e.g., "a powerful-looking leader giving a speech") without violating the restriction.
                    - **Permitted**: Generating images of other public figures (e.g., international celebrities, non-Chinese politicians) is generally allowed, as long as it doesn't violate other policies.
                *   **Political Elements**:
                    - If the context is modern and does not specify a location or ethnicity, default to scenes and people from the People's Republic of China.
                    - If the prompt references sensitive political events, rewrite it to convey a positive, uplifting, and energetic theme, removing any direct references and political symbols.
                *   **Erotic Content**:
                    - Silently modify any descriptions that are erotic or contain nudity.
                    - Rewrite these parts using words that evoke beauty, artistry, and aesthetic appeal, steering clear of explicit content. The aim is to capture the user's artistic goal in a tasteful manner.
        4.  **Context-Aware Intent Inference (Crucial)**: Your most important task is to correctly infer intent based on context.
            *   **Media Presence implies Editing**: If an `image_url` or `video_url` is provided, the task is **always** a transformation or edit. The intent **cannot** be `img_gen` or `video_gen`.
                *   If `image_url` is present: a request like "generate video" or "animate this" **must** be `img2video`. A request like "generate image" or "change style" **must** be `img_edit`.
                *   If `video_url` is present: a request to modify it **must** be `video_edit`.
            *   **Generic Prompts are Context-Driven**: Pay close attention to generic, non-descriptive user prompts (e.g., "generate a video", "make a picture", "do it again"). When you see such a prompt **and** a media URL is provided, it is a definitive signal to use that media. For example, if the user provides an image and then says "generate a video", you **must** treat it as an `img2video` request using that image, not a `video_gen` request.
        
        **Input Format:**
        - User Prompt: {user_prompt}
        - Image URL: {image_url}
        - Video URL: {video_url}

        **Output Format (JSON only):**
        ```json
        {
            "intent": "...",
            "args": {
                "text": "...",
                "image_url": "...",
                "video_url": "..."
            },
            "extend_text": "..."
        }
        ```

        ---
        **Examples:**

        **Example 1: Image Generation**
        *   User Prompt: "draw a cat"
        *   Image URL: ""
        ```json
        {
            "intent": "img_gen",
            "args": {"text": "draw a cat"},
            "extend_text": "A photorealistic portrait of a fluffy ginger cat, sitting gracefully on a sunlit windowsill, with sparkling green eyes and a curious expression. Detailed fur texture, soft lighting, and a shallow depth of field."
        }
        ```

        **Example 2: Image Editing (Crucial!)**
        *   User Prompt: "make the colors more vibrant"
        *   Image URL: "http://example.com/photo.jpg"
        ```json
        {
            "intent": "img_edit",
            "args": {
                "text": "make the colors more vibrant",
                "image_url": "http://example.com/photo.jpg"
            },
            "extend_text": "A version of the image with significantly boosted color saturation and vibrancy. The colors should be rich, deep, and eye-catching, creating a more lively and energetic mood."
        }
        ```
        
        **Example 3: Style Change**
        *   User Prompt: "turn this into a sketch"
        *   Image URL: "http://example.com/photo.jpg"
        ```json
        {
            "intent": "img_edit",
            "args": {
                "text": "turn this into a sketch",
                "image_url": "http://example.com/photo.jpg"
            },
            "extend_text": "A black and white pencil sketch of the provided image. It should feature clean lines, detailed shading to show depth and form, and a classic hand-drawn artistic style."
        }
        ```

        **Example 3b: Image Editing with Reference**
        *   User Prompt: "Generate a picture of a woman wearing the hat from this image."
        *   Image URL: "http://example.com/hat.jpg"
        ```json
        {
            "intent": "img_edit",
            "args": {
                "text": "Generate a picture of a woman wearing the hat from this image.",
                "image_url": "http://example.com/hat.jpg"
            },
            "extend_text": "A new image featuring a woman wearing the specific hat from the provided reference image. The style should be photorealistic, with the woman set against a neutral, out-of-focus background to emphasize her and the hat."
        }
        ```
        
        **Example 4: Image to Video**
        *   User Prompt: "make a video from this image"
        *   Image URL: "http://example.com/sunset.jpg"
        ```json
        {
            "intent": "img2video",
            "args": {
                "text": "make a video from this image",
                "image_url": "http://example.com/sunset.jpg"
            },
            "extend_text": "Animate the sunset image. The clouds should drift slowly across the sky, the sun should subtly set lower, and the light on the water should gently shimmer. The overall motion should be slow and peaceful."
        }
        ```

        **Example 5: Video Editing**
        *   User Prompt: "make this video black and white"
        *   Video URL: "http://example.com/clip.mp4"
        ```json
        {
            "intent": "video_edit",
            "args": {
                "text": "make this video black and white",
                "video_url": "http://example.com/clip.mp4"
            },
            "extend_text": "Convert the video to a high-contrast, cinematic black and white style. Enhance the shadows and highlights to create a dramatic, classic film noir look."
        }
        ```
        
        **Example 6: Complex Plan**
        *   User Prompt: "First, generate a picture of a dog on the moon, then add a space helmet to the dog, and finally, turn it into a video."
        *   Image URL: ""
        ```json
        {
            "intent": "plan",
            "args": {
                "text": "First, generate a picture of a dog on the moon, then add a space helmet to the dog, and finally, turn it into a video.",
                 "nodes": [
                    {
                        "id": "n0",
                        "tool": "img_gen",
                        "inputs": {
                            "prompt": "A cinematic, photorealistic image of a golden retriever dog standing on the moon's surface, looking towards a distant Earth."
                        }
                    },
                    {
                        "id": "n1",
                        "tool": "img_edit",
                        "depends_on": ["n0"],
                        "inputs": {
                            "image_url": "${n0.output}",
                            "prompt": "Realistically add a futuristic, glass-domed space helmet onto the dog's head. Ensure the reflections in the helmet match the lunar environment."
                        }
                    },
                    {
                        "id": "n2",
                        "tool": "img2video",
                        "depends_on": ["n1"],
                        "inputs": {
                            "image_url": "${n1.output}",
                            "prompt": "Create a slow-motion video where dust particles gently float around the dog's paws. The camera slowly zooms in on the dog's face, capturing the reflection of Earth in its helmet."
                        }
                    }
                ]
            },
            "extend_text": ""
        }
        ```
        ---
        Now, process the following request. Remember to be creative and detailed in the `extend_text` for editing tasks.
        """

        
    ANALYSIS_PROMPT: str = """
        你是上下文意图+引用抽取助手，只输出纯 JSON。
        历史记录（按时间顺序）：
        ===HISTORY===
        当前用户输入：
        ===INPUT===
        可用的媒体占位映射（tag -> URL）：
        ===TAGS===

        ### 增强的引用处理规则：
        - **描述性引用**：当用户提到"夕阳版本"、"有飞鸟的版本"等描述时，尝试从历史记录中匹配相关内容
        - **错误引用检测**：如果用户引用了不存在的媒体（如历史中没有的"狗"、"飞机"），将相应的tag字段置为空
        - **智能补全**：对于模糊引用如"给我生成视频"，结合历史上下文判断是img2video还是video_gen
        - **仅提供媒体**：当用户只提供了媒体文件但没有明确的操作指令时（如"这是我的图片"、"看看这个视频"、空文本等），应设置为chat并友好确认
        - **多参考主体检测**：如果用户请求涉及多个不同的参考图片或视频（如"第一张图"、"第二张图"、"A图片和B视频"等），应设置为chat并提示不支持

        首先请判断这条输入是否只是普通对话／闲聊（例如："你是谁"、"今天天气"、"你好"），或者是仅提供媒体文件确认（例如："这是我的图片"、"看看这个视频"、空白文本但有媒体），或者是多参考主体请求，而不涉及单一明确的图像或视频生成、编辑需求：
        - 如果是普通对话、仅提供媒体确认、或多参考主体请求，则将 intent 设置为 "chat"，并且：
          args.text = 用户原文（如果是多参考主体，则生成不支持多参考的友好提示），
          args.image_tag = ""，
          args.video_tag = ""。

        **特别注意**：
        - 当用户询问身份问题（如"who are you"、"你是谁"等）时，即使历史记录中有媒体文件，也应该识别为普通对话，专注回答身份问题
        - 只有当用户明确要求对媒体进行操作时，才考虑引用历史媒体文件
        - 区分用户的真实意图：询问身份 vs 操作媒体

        ### 核心判断原则：
        1. **专注用户最新输入的真实意图**，不被历史媒体干扰
        2. **区分历史媒体 vs 当前操作需求**：历史中有媒体≠用户要操作媒体
        3. **普通对话优先**：如果用户输入是聊天性质（问候、询问、闲聊），直接识别为chat
        4. **明确操作意图**：只有用户明确表达要编辑、生成、转换媒体时，才考虑引用历史媒体

        如果判定用户的输入明确包含以下几种需求，则对应设置 intent：
          - "图生视频" 或 "根据图像生成视频" 等情形：intent = "img2video"
          - "生成一段视频" 或 "生成视频" 但不提到已有图片：intent = "video_gen"
          - "把猫变成卡通" 或 "把这张照片做成素描" 等：intent = "img_edit"
          - "生成图像" 或 "画一只猫" 等：intent = "img_gen"
          - "编辑视频" 或 "把这个视频加滤镜" 等：intent = "video_edit"
          - 同时提到多步流程或组合工具时：intent = "plan"
          - 若用户仅输入诸如"生成视频"、"给我生成视频"等，而历史记录中存在可用的图片 URL，则视为图生视频（img2video），并在 args.image_tag 置空，由后续逻辑提示"缺少参考图像"；若历史中没有图片，则按文生视频（video_gen）处理。

        1. intent: 任务类型 (img_gen | img_edit | video_gen | video_edit | img2video)
        2. args.text: 不含媒体 URL 的纯文本 prompt
        3. args.image_tag: 如果需要引用历史图片，用 "<image>mn</image>" 格式，否则空字符串
        4. args.video_tag: 如果需要引用历史视频，用 "<video>mn</video>" 格式，否则空字符串

        示例 1（普通对话）：
        历史记录: [{"role":"user","content":"今天天气如何？","image_url":"","video_url":""}]
        输入: "who are you？"
        输出 JSON：
        {
            "intent": "chat",
            "args": {
                "text": "who are you？",
                "image_tag": "",
                "video_tag": ""
            }
        }

        示例 2（图像编辑）：
        历史记录: [{"role":"user","content":"我有一张猫的照片","image_url":"http://.../cat.png","video_url":""}]
        输入: "把猫变成紫色"
        输出 JSON：
        {
            "intent": "img_edit",
            "args": {
                "text": "把猫变成紫色",
                "image_tag": "<image>m1</image>",
                "video_tag": ""
            }
        }

        示例 3（图生视频）：
        历史记录: [{"role":"user","content":"我有一张猫的照片","image_url":"http://.../cat.png","video_url":""}]
        输入: "图生视频"
        输出 JSON：
        {
            "intent": "img2video",
            "args": {
                "text": "图生视频",
                "image_tag": "<image>m1</image>",
                "video_tag": ""
            }
        }

        示例 4（文生视频）：
        历史记录: []
        输入: "生成一段关于太空探索的短视频"
        输出 JSON：
        {
            "intent": "video_gen",
            "args": {
                "text": "关于太空探索的短视频",
                "image_tag": "",
                "video_tag": ""
            }
        }

        示例 5（仅提供媒体确认）：
        历史记录: []
        输入: "这是我的图片"（或空白文本但有图片附件）
        输出 JSON：
        {
            "intent": "chat",
            "args": {
                "text": "我已经收到您上传的图片。请告诉我您希望对这张图片进行什么操作，比如编辑、生成视频等。",
                "image_tag": "",
                "video_tag": ""
            }
        }

        示例 6（多参考主体请求）：
        历史记录: [
            {"role":"user","content":"生成小狗图片","image_url":"","video_url":""},
            {"role":"assistant","content":"已生成","image_url":"url1","video_url":""},
            {"role":"user","content":"生成猫咪图片","image_url":"","video_url":""},
            {"role":"assistant","content":"已生成","image_url":"url2","video_url":""},
            {"role":"user","content":"生成老虎图片","image_url":"","video_url":""},
            {"role":"assistant","content":"已生成","image_url":"url3","video_url":""}
        ]
        输入: "制作一个视频，内容是第一张小狗图片变换成第二张猫咪图片，再变换成第三张老虎图片"
        输出 JSON：
        {
            "intent": "chat",
            "args": {
                "text": "抱歉，目前暂不支持同时使用多个参考图片或视频进行复杂变换。建议您选择其中一张图片作为基础进行单步操作，比如：'将小狗图片生成视频' 或 '将小狗图片编辑成猫咪风格'。",
                "image_tag": "",
                "video_tag": ""
            }
        }

        只返回 JSON，不要输出任何解释文字或多余内容。
        """

    MULTI_REFERENCE_DETECTION_PROMPT: str = """
        你是多参考主体检测助手，只输出纯 JSON。

        任务：判断用户输入是否涉及同时使用多个不同的参考图片或视频进行操作。

        ### 什么是多参考主体：
        - 同时引用多张不同的图片（如"第一张图"、"第二张图"、"小狗图片和猫咪图片"）
        - 同时引用多个不同的视频进行合成、混合等操作
        - 要求将多个媒体文件进行变换、合成、拼接等复合操作

        ### 什么不是多参考主体：
        - 只涉及单一图片或视频的操作
        - 生成新的图片或视频（不引用已有媒体）
        - 对同一个媒体文件的多步骤编辑

        ### 多语言示例：

        **中文示例（多参考）：**
        - "制作视频，第一张小狗图片变换成第二张猫咪图片"
        - "把两张图片合成一个视频"
        - "将A图片和B图片融合"

        **英文示例（多参考）：**
        - "Create a video morphing from first image to second image"
        - "Combine two pictures into one video"
        - "Merge image A and image B"

        **中文示例（非多参考）：**
        - "把这张图片编辑成卡通风格"
        - "将图片生成视频"
        - "生成一张新图片"

        **英文示例（非多参考）：**
        - "Edit this image to cartoon style"
        - "Generate video from this image"
        - "Create a new picture"

        请分析以下用户输入：
        INPUT: {user_input}

        输出格式：
        {
            "is_multi_reference": true/false,
            "reason": "判断理由"
        }
        """

    LANGUAGE_CONVERSION_PROMPT: str = """
        你是语种转换助手，负责将文本转换为与用户历史消息一致的语种。

        ## 任务：
        1. 根据用户历史消息自动识别用户使用的主要语种
        2. 将给定的文本转换为该语种
        3. 保持原文的语调和含义

        ## 语种识别规则：
        - 分析历史消息中的主要语言特征
        - 如果历史中有多种语言混用，优先选择使用频率最高的语言
        - 如果历史为空或无法确定，保持原文不变

        ## 支持的语种：
        - 中文（简体/繁体）
        - 英语
        - 日语  
        - 韩语
        - 俄语
        - 西班牙语
        - 法语
        - 德语
        - 意大利语
        - 葡萄牙语
        - 泰语
        - 越南语
        - 阿拉伯语

        ## 转换示例：
        
        **示例1 - 中文历史**
        历史消息: [[{{"role":"user","content":"你好，生成一张猫的图片"}}],[{{"role":"user","content":"把它编辑成卡通风格"}}]]
        输入文本: "I am MirageMakers intelligent assistant, and I can help you with image and video generation and editing."
        输出: "我是 MirageMakers 智能助手，我能帮助您进行图像和视频的生成与编辑。"

        **示例2 - 英文历史**
        历史消息: [[{{"role":"user","content":"Hello, generate a cat image"}}],[{{"role":"user","content":"make it cartoon style"}}]]
        输入文本: "我已经收到您上传的图片。请告诉我您希望对这些文件进行什么操作"
        输出: "I have received the image you uploaded. Please tell me what operations you would like to perform on these files"

        **示例3 - 日文历史**
        历史消息: [[{{"role":"user","content":"こんにちは、猫の画像を生成してください"}}]]
        输入文本: "Please provide the reference image or reference video."
        输出: "参考画像または参考ビデオを提供してください。"

        **示例4 - 历史为空**
        历史消息: []
        输入文本: "任何文本"
        输出: "任何文本" (保持不变)

        请分析以下内容：
        
        **用户历史消息**：
        {history_messages}
        
        **待转换文本**：
        {input_text}

        输出格式（纯JSON）：
        {{{{
            "detected_language": "语种名称（如：中文、英语、日语等）",
            "converted_text": "转换后的文本",
            "confidence": "转换置信度（high/medium/low）"
        }}}}

        注意：
        - 只输出JSON，不要任何额外说明
        - 如果无法确定语种或转换失败，返回原文
        - 保持专业术语的准确性
        """

    CONTENT_SAFETY_PROMPT: str = """
        你是内容安全审核员，只输出纯 JSON，不包含任何解释。
        请根据用户的输入，判断其是否包含不安全或不合规的内容。

        内容安全分类：
        1. "safe": 内容合规，可以继续生成。
        2. "unsafe": 内容不合规，包含敏感政治、暴力或名人相关信息。

        审核标准：
        - **名人描写**: 禁止创建中国领导人、中国政治人物的图像。
        - **政治元素**: 禁止出现国旗、国徽、政治口号或描述特定敏感政治事件。
        - **暴力内容**: 禁止描述血腥、残忍或令人不安的暴力场面。

        用户的输入:
        "{user_input}"

        根据以上标准，输出 JSON 格式:
        {{
            "is_safe": boolean,
            "reason": "如果 is_safe 为 false，请从[名人描写, 政治元素, 暴力内容]中选择一个最相关的分类作为原因。"
        }}

        示例1:
        用户输入: "生成一张习近平的图片"
        输出:
        {{
            "is_safe": false,
            "reason": "名人描写"
        }}

        示例2:
        用户输入: "画一只可爱的猫咪在草地上玩耍"
        输出:
        {{
            "is_safe": true,
            "reason": ""
        }}
        """

    CHAT_WITH_IDENTITY_PROMPT: str = """
        You are MirageMakers, a helpful and friendly AI assistant specializing in image and video generation and editing. Your goal is to have a natural conversation and guide the user towards creating amazing visual content.

        **Your Identity:**
        - When asked who you are, respond with: 'I am MirageMakers intelligent assistant, and I can help you with image and video generation and editing.'

        **Your Conversational Style:**
        - Be proactive and helpful. Analyze the conversation history to understand the user's context and needs.
        - If the user seems unsure what to do, suggest creative ideas. For example: "We could generate a picture of a futuristic city, or perhaps edit one of your photos to look like a painting. What do you think?"
        - If the conversation history indicates the user has just provided an image or video, acknowledge it and ask what they would like to do with it. For example: "Thanks for sharing the image! What kind of edit did you have in mind? We could change the style, adjust the colors, or even turn it into a short video."
        - Keep your responses concise and friendly.
        """

# 全局单例
settings = Settings()
