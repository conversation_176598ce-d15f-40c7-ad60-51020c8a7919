# miragemakers AI - Multimodal AI Content Generation Platform

> A modern, full-stack AI-powered platform for generating images, videos, and intelligent conversations with multimodal understanding capabilities.

## 🌟 Features
- **🎨 AI Image Generation**: Create high-quality images from text descriptions using DashScope API
- **🎬 AI Video Creation**: Generate video content from text or images
- **💬 Intelligent Chat**: Natural conversation with multimodal AI understanding
- **📱 Modern UI**: Responsive, ChatGPT-inspired interface built with Next.js
- **🔐 User Management**: Complete authentication system with social login
- **🌍 Internationalization**: English/Chinese language support
- **📊 Usage Analytics**: Track generations and user activity
- **⚡ Async Processing**: Redis + Celery for background task processing

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │     Django      │    │     Redis       │
│   Frontend      │◄──►│    Backend      │◄──►│    Cache        │
│   (Port 3000)   │    │   (Port 8000)   │    │   (Port 6379)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         │                       │              ┌─────────────────┐
         │                       │              │  Celery Worker  │
         │                       │              │  (Background)   │
         │                       │              └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Browser      │    │    Database     │    │  AI Services    │
│   Interface     │    │   (SQLite3306)  │    │  (DashScope)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

#### Backend (Django)
- **API Server**: RESTful APIs for all frontend interactions
- **User Management**: Authentication, authorization, and user profiles  
- **AI Router**: Intelligent routing to appropriate AI services via Celery tasks
- **Database**: SQLite for development, PostgreSQL for production
- **Social Auth**: Google, Facebook, Discord, Twitter integration
- **Async Processing**: Celery workers for AI generation tasks

#### Frontend (Next.js)
- **Responsive UI**: Modern, ChatGPT-inspired interface
- **Real-time Chat**: Interactive conversation with AI
- **Media Display**: Image and video generation results
- **User Dashboard**: Profile management and usage statistics
- **API Integration**: Seamless communication with Django backend

#### Infrastructure
- **Redis**: Caching and message broker for Celery
- **Celery**: Distributed task queue for AI processing
- **DashScope**: Alibaba Cloud's AI platform integration

## 🚀 Quick Start

### Prerequisites
- **Python 3.11+**
- **Node.js 18+** 
- **Redis 6.0+**
- **Git**

### 部署选项

#### 选项 A: 本地开发环境 (推荐开发)

### 1. Clone Repository
```bash
<NAME_EMAIL>:qingshu8822/visual_gen_agent.git
cd visual_gen_agent
```

### 2. Environment Setup
```bash
# Create and activate virtual environment
python3.11 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt
pip install -e .
```

### 3. Configuration
```bash
# Create environment file
cp .env.example .env
# Edit .env with your API keys and configuration
```

Required environment variables:
```env
DASHSCOPE_API_KEY=your_dashscope_api_key
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_secret
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
```

### 4. Database Setup
```bash
python manage.py migrate
python manage.py createsuperuser  # Optional: create admin user
```

### 5. Frontend Setup
```bash
cd frontend
npm install
cd ..
```

### 6. Start Services

#### Use quick utomated Start (Recommended)
./quick_start.sh
```

#### Option B: Manual Start (3 separate terminals)

**Terminal 1 - Celery Worker:**
```bash
cd /path/to/visual_gen_agent
source .venv/bin/activate
celery -A app.celery_app worker --loglevel=info --queues=default --concurrency=2
```

**Terminal 2 - Django Backend:**
```bash
cd /path/to/visual_gen_agent
source .venv/bin/activate
python manage.py runserver:8000
```

**Terminal 3 - Next.js Frontend:**
```bash
cd /path/to/visual_gen_agent/frontend
npm run dev
```

#### 选项 B: Docker 部署 (推荐生产)

### 1. 系统要求
- **Docker 20.10+**
- **Docker Compose 2.0+**
- **4GB+ RAM**

### 2. 快速启动
```bash
<NAME_EMAIL>:qingshu8822/visual_gen_agent.git
cd visual_gen_agent/docker

# 配置环境变量
cp env.example .env
# 编辑 .env 文件配置你的API密钥

# 一键启动所有服务
./start.sh
```

### 3. 访问应用
- **主应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **管理后台**: http://localhost:8000/admin

详细的Docker部署指南请查看 [docker/README.md](docker/README.md)

### 7. Access the Application
- **Main Application**: http://dev.miragemakers.ai:3000
- **Backend API**: http://127.0.0.1:8000
- **Admin Panel**: http://127.0.0.1:8000/admin

### 8. Test the System
```bash
# Run comprehensive system tests
python system_test.py

# Stop all services
./stop_server.sh
```

## 🛠️ Development

### Project Structure
```
visual_gen_agent/
├── app/                     # Celery application
│   ├── celery_app.py       # Celery configuration
│   ├── tasks.py            # Background tasks
│   └── models.py           # Task models (JobPlan, Node)
├── core/                   # Django app - main backend logic
│   ├── models.py           # Database models
│   ├── views.py            # API views
│   ├── auth_views.py       # Authentication views
│   └── adapters/           # AI service integrations
├── frontend/               # Next.js frontend application
│   ├── pages/              # Next.js pages
│   ├── components/         # React components
│   ├── contexts/           # React contexts (language, etc.)
│   └── styles/             # CSS and styling
├── generator/              # Django project settings
│   └── settings.py         # Main configuration
├── scripts/                # Development scripts
│   ├── start_server.sh     # Environment setup
│   ├── quick_start.sh      # Automated startup
│   ├── stop_server.sh      # Stop all services
│   └── system_test.py      # System health tests
└── requirements.txt        # Python dependencies
```

### Key Components

#### AI Processing Pipeline
1. **User Input** → Frontend (Next.js)
2. **API Request** → Django Backend
3. **Task Creation** → Celery Queue (Redis)
4. **AI Processing** → Celery Worker
5. **Result Storage** → Cache/Database
6. **Response** → Frontend Display

#### Backend APIs (`core/views.py`)
- **`POST /`**: Main chat/generation endpoint
- **`POST /api/auth/`**: Authentication endpoints
- **`GET /admin/`**: Django admin interface

#### Frontend Pages (`frontend/pages/`)
- **`/`**: Main chat interface
- **`/auth/login`**: User login
- **`/auth/register`**: User registration
- **`/auth/forgot-password`**: Password reset

### Development Commands

#### Backend Development
```bash
# Run Django development server
python manage.py runserver:8000

# Create database migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Django shell
python manage.py shell
```

#### Frontend Development
```bash
cd frontend

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

#### Celery Development
```bash
# Start Celery worker
celery -A app.celery_app worker --loglevel=info

# Monitor Celery tasks
celery -A app.celery_app flower

# Inspect active workers
celery -A app.celery_app inspect active
```

#### Redis Operations
```bash
# Check Redis connection
redis-cli ping

# Monitor Redis
redis-cli monitor

# View Redis keys
redis-cli keys "*"
```

## 🧪 Testing

### System Health Check
```bash
# Run all system tests
python system_test.py

# Test specific components
python -c "import redis; r = redis.Redis(); print('Redis:', r.ping())"
python -c "import django; django.setup(); print('Django: OK')"
```

### Test Coverage
- ✅ Module imports (Google AI, Pillow, Celery, Redis, Django)
- ✅ Redis connection and caching
- ✅ Django backend health
- ✅ Next.js frontend health  
- ✅ Celery worker connectivity
- ✅ API endpoints (chat, auth)
- ✅ Background task processing
- ✅ System resource monitoring

## 🚨 Troubleshooting

### Common Issues

#### Redis Connection Failed
```bash
# Start Redis service
brew services start redis  # macOS
sudo systemctl start redis  # Linux

# Check Redis status
redis-cli ping
```

#### Port Already in Use
```bash
# Find process using port
lsof -ti:8000  # Django
lsof -ti:3000  # Next.js
lsof -ti:6379  # Redis

# Kill process
kill $(lsof -ti:8000)
```

#### Celery Worker Not Responding
```bash
# Check Celery logs
tail -f logs/celery.log

# Restart Celery worker
pkill -f "celery.*worker"
celery -A app.celery_app worker --loglevel=info
```

#### Frontend Build Issues
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### Performance Optimization
- **Redis**: Configure memory limits and persistence
- **Celery**: Adjust worker concurrency based on CPU cores
- **Django**: Enable database connection pooling
- **Next.js**: Optimize bundle size and enable caching

## 📝 API Documentation

### Main Endpoints

#### Chat/Generation API
```http
POST /
Content-Type: multipart/form-data

prompt: "Generate a beautiful landscape"
mode: "generate"  # or "chat"
media: [file]     # optional
```

#### Authentication API
```http
POST /api/auth/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Response Formats

#### Chat Response
```json
{
  "chat_response": "AI response text",
  "logs": ["Processing step 1", "Processing step 2"]
}
```

#### Generation Response
```json
{
  "image_url": "https://example.com/generated-image.jpg",
  "logs": ["Analyzing prompt", "Generating image", "Upload complete"]
}
```

### Troubleshooting
Email function:
python ./tests/test_email_function.py
初始状态检查
- ✅ msmtp已正确安装：`/opt/homebrew/bin/msmtp`
- ✅ msmtp配置文件存在：`~/.msmtprc`
- ✅ Gmail SMTP配置正确：检查msmtp在.env中的配置是否正确
- ✅ Django邮件后端配置为：`core.msmtp_backend.MsmtpEmailBackend`
如果问题仍然存在，可以：
1. 检查msmtp日志：`tail -10 ~/.msmtp.log`
2. 运行邮件测试：`python test_email_now.py`
3. 查看Django日志中的邮件发送记录

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **DashScope API** for AI capabilities
- **Django** for robust backend framework
- **Next.js** for modern frontend development
- **Redis & Celery** for async processing
- **Tailwind CSS** for beautiful UI components

---

**Built with ❤️ by the miragemakers AI team** 