#!/bin/bash

# ========================================
# miragemakers AI 系统启动脚本
# ========================================

echo "🚀 启动 miragemakers AI 完整系统..."
echo "======================================"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查虚拟环境，如果不存在则创建
if [ ! -d ".venv" ]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv .venv
    echo "✅ 虚拟环境创建完成"
fi

# 检查虚拟环境是否激活
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  虚拟环境未激活，正在激活..."
    source .venv/bin/activate
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        echo "❌ 虚拟环境激活失败"
        exit 1
    fi
fi
echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"

# 检查Redis
echo ""
echo "🔄 检查Redis服务..."
if ! redis-cli ping >/dev/null 2>&1; then
    echo "⚠️  Redis未运行，正在启动..."
    if command -v brew >/dev/null 2>&1; then
        brew services start redis
        sleep 3
    else
        echo "❌ 请手动启动Redis服务: sudo systemctl start redis"
        exit 1
    fi
    if ! redis-cli ping >/dev/null 2>&1; then
        echo "❌ Redis启动失败"
        exit 1
    fi
fi
echo "✅ Redis服务正常"

# 检查MySQL
echo ""
echo "🗄️  检查MySQL服务..."
if ! mysqladmin ping >/dev/null 2>&1; then
    echo "⚠️  MySQL未运行，正在启动..."
    if command -v brew >/dev/null 2>&1; then
        brew services start mysql
        sleep 5
        if ! mysqladmin ping >/dev/null 2>&1; then
            echo "⚠️  正在尝试直接启动MySQL..."
            sudo mysqld_safe --user=mysql --datadir=/opt/homebrew/var/mysql >/dev/null 2>&1 &
            sleep 5
        fi
    else
        echo "⚠️  尝试启动MySQL服务..."
        sudo systemctl start mysql >/dev/null 2>&1 || sudo service mysql start >/dev/null 2>&1 || true
        sleep 3
    fi

    if ! mysqladmin ping >/dev/null 2>&1; then
        echo "❌ MySQL启动失败，请手动启动MySQL服务"
        echo "💡 macOS: brew services start mysql"
        echo "💡 Linux: sudo systemctl start mysql"
        exit 1
    fi
fi
echo "✅ MySQL服务正常"

# 检查依赖
echo ""
echo "📦 检查Python依赖..."
if ! python -c "import google.genai; from PIL import Image; import celery; import redis; import django" 2>/dev/null; then
    echo "❌ 缺少依赖，正在安装..."
    pip install -r requirements.txt
fi
echo "✅ 依赖检查完成"

# 检查前端依赖
echo ""
echo "📦 检查前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "❌ 前端依赖缺失，正在安装..."
    npm install
fi
cd ..
echo "✅ 前端依赖检查完成"

# 清理旧进程
echo ""
echo "🧹 清理旧进程..."
pkill -f "celery.*worker" 2>/dev/null || true
pkill -f "manage.py runserver" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
kill $(lsof -ti:8000) 2>/dev/null || true
kill $(lsof -ti:3000) 2>/dev/null || true
rm -f /tmp/celery.pid
echo "✅ 清理完成"

# 数据库迁移
echo ""
echo "📊 检查数据库..."
python manage.py migrate --noinput
echo "✅ 数据库检查完成"

echo ""
echo "🎉 准备工作完成！"
echo ""
echo "现在请按以下步骤在不同终端窗口中启动服务："
echo ""
echo "📝 第1步: 启动Celery Worker (新终端窗口)"
echo "   cd $(pwd)"
echo "   source .venv/bin/activate"
echo "   celery -A app.celery_app worker --loglevel=info --queues=default --concurrency=2"
echo ""
echo "📝 第2步: 启动Django Backend (新终端窗口)"
echo "   cd $(pwd)"
echo "   source .venv/bin/activate"
echo "   python manage.py runserver"
echo ""
echo "📝 第3步: 启动Next.js Frontend (新终端窗口)"
echo "   cd $(pwd)/frontend"
echo "   npm run dev -- --hostname 127.0.0.1 --port 3000"
echo ""
echo "🔧 或者使用快速启动命令:"
echo "   ./quick_start.sh  # 自动化版本"
echo ""
echo "💡 测试系统: python system_test.py"
echo "🛑 停止服务: ./stop_server.sh"
echo ""
echo "📄 查看服务日志:"
echo "   应用服务: tail /tmp/{celery,django,frontend}.log"
echo "   Redis: brew services list | grep redis 或 redis-cli info"
echo "   MySQL: brew services list | grep mysql 或 tail /opt/homebrew/var/mysql/*.err"