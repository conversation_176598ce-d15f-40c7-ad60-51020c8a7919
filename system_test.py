#!/usr/bin/env python3
"""
miragemakers AI 系统连接测试脚本
专注于服务连接、端口通讯和基础功能测试，不调用实际的AI API
"""

import os
import sys
import time
import redis
import requests
import subprocess
from pathlib import Path

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
sys.path.append(str(Path(__file__).parent))

import django  # noqa: E402
django.setup()

from django.core.cache import cache  # noqa: E402
from app.celery_app import app as celery_app  # noqa: E402

# ========================================
# 测试配置
# ========================================

BASE_URL = "http://127.0.0.1:8000"
FRONTEND_URL = "http://dev.miragemakers.ai:3000"  # 修改为正确的前端URL
REDIS_HOST = "127.0.0.1"
REDIS_PORT = 6379
TIMEOUT = 10


# ========================================
# 1. 模块导入测试
# ========================================

def test_module_imports():
    """测试所有必需模块的导入"""
    print("📦 测试模块导入...")
    
    modules = {
        "Google AI SDK": "google.genai",
        "Pillow": "PIL.Image", 
        "Celery": "celery",
        "Redis": "redis",
        "Django": "django",
        "Requests": "requests",
        "Django Redis": "django_redis",
    }
    
    results = {}
    for name, module_path in modules.items():
        try:
            __import__(module_path)
            print(f"  ✅ {name}")
            results[name] = True
        except ImportError as e:
            print(f"  ❌ {name}: {e}")
            results[name] = False
    
    return all(results.values()), results


# ========================================
# 2. 基础服务连接测试
# ========================================

def test_redis_connection():
    """测试Redis连接"""
    print("🔄 测试Redis连接...")
    
    try:
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=0)
        result = r.ping()
        print(f"  ✅ Redis连接成功: {result}")
        
        # 测试基本读写
        test_key = "system_test_redis"
        test_value = "connection_test"
        r.set(test_key, test_value, ex=60)
        retrieved = r.get(test_key)
        
        if retrieved and retrieved.decode() == test_value:
            print("  ✅ Redis读写测试成功")
            return True
        else:
            print("  ❌ Redis读写测试失败")
            return False
            
    except Exception as e:
        print(f"  ❌ Redis连接失败: {e}")
        return False


def test_django_cache():
    """测试Django缓存系统"""
    print("🗄️  测试Django缓存...")
    
    try:
        test_key = "system_test_cache"
        test_value = {"message": "Django cache test", "timestamp": time.time()}
        
        # 测试缓存写入
        cache.set(test_key, test_value, 60)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            print("  ✅ Django缓存读写正常")
            
            # 测试缓存删除
            cache.delete(test_key)
            deleted_value = cache.get(test_key)
            
            if deleted_value is None:
                print("  ✅ Django缓存删除正常")
                return True
            else:
                print("  ❌ Django缓存删除失败")
                return False
        else:
            print("  ❌ 缓存数据不匹配")
            return False
            
    except Exception as e:
        print(f"  ❌ Django缓存测试失败: {e}")
        return False


def test_celery_connection():
    """测试Celery连接"""
    print("⚙️  测试Celery连接...")
    
    try:
        # 测试Celery broker连接
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print("  ✅ Celery连接成功，活跃worker:")
            for worker, stat in stats.items():
                concurrency = stat.get('pool', {}).get('max-concurrency', 'N/A')
                processes = stat.get('pool', {}).get('processes', 'N/A')
                print(f"    - {worker}: {concurrency} 并发, {processes} 进程")
            return True
        else:
            print("  ⚠️  Celery broker连接成功，但没有活跃worker")
            return False
            
    except Exception as e:
        print(f"  ❌ Celery连接失败: {e}")
        return False


# ========================================
# 3. Web服务健康检查
# ========================================

def test_django_backend():
    """测试Django后端服务"""
    print("🔧 测试Django后端...")
    
    try:
        # 测试主页
        response = requests.get(f"{BASE_URL}/", timeout=TIMEOUT)
        print(f"  ✅ 主页响应: {response.status_code}")
        
        if response.status_code == 200:
            # 检查响应内容
            if "视觉生成工具" in response.text:
                print("  ✅ 页面内容正确")
            else:
                print("  ⚠️  页面内容可能异常")
        
        # 测试管理员页面
        try:
            admin_response = requests.get(f"{BASE_URL}/admin/", timeout=5)
            print(f"  ✅ 管理页面响应: {admin_response.status_code}")
        except Exception:
            print("  ⚠️  管理页面无法访问")
        
        return response.status_code in [200, 405]
        
    except requests.exceptions.ConnectTimeout:
        print("  ❌ 后端连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("  ❌ 后端连接被拒绝")
        return False
    except Exception as e:
        print(f"  ❌ 后端连接失败: {e}")
        return False


def test_nextjs_frontend():
    """测试Next.js前端服务"""
    print("🎨 测试Next.js前端...")
    
    try:
        response = requests.get(f"{FRONTEND_URL}/", timeout=TIMEOUT)
        print(f"  ✅ 前端响应: {response.status_code}")
        
        if response.status_code == 200:
            # 检查是否是Next.js应用
            text_lower = response.text.lower()
            if "next" in text_lower or "react" in text_lower:
                print("  ✅ Next.js应用正常")
            else:
                print("  ⚠️  前端内容可能异常")
        
        return response.status_code == 200
        
    except requests.exceptions.ConnectTimeout:
        print("  ❌ 前端连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("  ❌ 前端连接被拒绝")
        return False
    except Exception as e:
        print(f"  ❌ 前端连接失败: {e}")
        return False


# ========================================
# 4. API端点测试
# ========================================

def test_auth_endpoints():
    """测试认证相关API端点"""
    print("🔑 测试认证API端点...")
    
    success_count = 0
    
    # 测试后端忘记密码API
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/forgot-password/",
            json={"email": "<EMAIL>", "language": "en"},
            timeout=5
        )
        if response.status_code in [200, 400, 404]:  
            print(f"  ✅ 后端忘记密码API: {response.status_code}")
            success_count += 1
        else:
            print(f"  ⚠️  后端忘记密码API: {response.status_code}")
    except Exception as e:
        print(f"  ❌ 后端忘记密码API失败: {e}")
    
    # 测试前端忘记密码API
    try:
        response = requests.post(
            f"{FRONTEND_URL}/api/auth/forgot-password",
            json={"email": "<EMAIL>", "language": "en"},
            timeout=5
        )
        if response.status_code in [200, 400, 404]:
            print(f"  ✅ 前端忘记密码API: {response.status_code}")
            success_count += 1
        else:
            print(f"  ⚠️  前端忘记密码API: {response.status_code}")
    except Exception as e:
        print(f"  ❌ 前端忘记密码API失败: {e}")
    
    return success_count > 0


def test_main_api_endpoint():
    """测试主要API端点（不调用AI）"""
    print("💬 测试主要API端点...")
    
    try:
        # 测试简单的聊天请求（应该会返回错误或处理中状态）
        response = requests.post(
            f"{BASE_URL}/",
            data={
                "prompt": "系统连接测试",
                "mode": "chat"
            },
            timeout=15
        )
        
        print(f"  ✅ API端点响应: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if "chat_response" in result or "error" in result:
                    print("  ✅ API响应格式正确")
                    return True
                else:
                    print("  ⚠️  API响应格式异常")
                    return True  # 仍然认为连接成功
            except Exception:
                print("  ✅ API端点可访问（非JSON响应）")
                return True
        else:
            print(f"  ⚠️  API端点状态异常: {response.status_code}")
            return response.status_code in [400, 405, 500]
            
    except Exception as e:
        print(f"  ❌ API端点测试失败: {e}")
        return False


# ========================================
# 5. 系统资源和端口检查
# ========================================

def test_system_ports():
    """测试系统端口状态"""
    print("🖥️  检查系统端口...")
    
    ports = {
        "Redis": 6379,
        "Django": 8000,
        "Next.js": 3000
    }
    
    active_ports = 0
    
    for service, port in ports.items():
        try:
            result = subprocess.run(
                ["lsof", "-ti", f":{port}"],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                pid_list = ', '.join(pids)
                print(f"  ✅ {service} (端口 {port}): 运行中 (PID: {pid_list})")
                active_ports += 1
            else:
                print(f"  ❌ {service} (端口 {port}): 未运行")
        except Exception as e:
            print(f"  ❌ 检查 {service} 端口失败: {e}")
    
    return active_ports >= 2  # 至少需要Django和Redis运行


def test_redis_performance():
    """测试Redis性能"""
    print("📊 测试Redis性能...")
    
    try:
        r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=2)
        
        # 测试写入性能
        start_time = time.time()
        for i in range(100):
            r.set(f"perf_test_{i}", f"value_{i}")
        write_time = time.time() - start_time
        
        # 测试读取性能
        start_time = time.time()
        for i in range(100):
            r.get(f"perf_test_{i}")
        read_time = time.time() - start_time
        
        # 清理测试数据
        for i in range(100):
            r.delete(f"perf_test_{i}")
        
        print(f"  ✅ Redis写入100条: {write_time:.3f}s")
        print(f"  ✅ Redis读取100条: {read_time:.3f}s")
        
        # 检查Redis键数量
        key_count = len(r.keys('*'))
        print(f"  📊 Redis当前键数: {key_count}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Redis性能测试失败: {e}")
        return False


# ========================================
# 6. 数据库连接测试
# ========================================

def test_database_connection():
    """测试数据库连接"""
    print("🗃️  测试数据库连接...")
    
    try:
        from django.db import connection
        
        # 测试数据库连接
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        if result and result[0] == 1:
            print("  ✅ 数据库连接正常")
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_count = len(tables)
            print(f"  📊 数据库表数量: {table_count}")
            
            cursor.close()
            return True
        else:
            print("  ❌ 数据库查询失败")
            cursor.close()
            return False
            
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False


# ========================================
# 7. 主测试函数
# ========================================

def main():
    """主测试函数"""
    print("🚀 开始miragemakers AI系统连接测试")
    print("=" * 60)
    print("📝 注意: 此测试专注于服务连接，不会调用实际的AI API")
    print("=" * 60)
    
    # 定义所有测试
    tests = [
        ("模块导入", test_module_imports),
        ("Redis连接", test_redis_connection),
        ("Django缓存", test_django_cache),
        ("数据库连接", test_database_connection),
        ("Celery连接", test_celery_connection),
        ("Django后端", test_django_backend),
        ("Next.js前端", test_nextjs_frontend),
        ("认证API", test_auth_endpoints),
        ("主要API端点", test_main_api_endpoint),
        ("系统端口", test_system_ports),
        ("Redis性能", test_redis_performance),
    ]
    
    results = {}
    
    # 执行测试
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            if test_name == "模块导入":
                success, details = test_func()
                results[test_name] = success
            else:
                success = test_func()
                results[test_name] = success
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = failed = 0
    critical_tests = ["Redis连接", "Django后端", "Django缓存"]
    critical_failed = 0
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name:<15}: {status}")
        if success:
            passed += 1
        else:
            failed += 1
            if test_name in critical_tests:
                critical_failed += 1
    
    # 最终结果
    print("\n" + "=" * 60)
    print(f"📈 测试统计: {passed} 通过, {failed} 失败, 总计 {len(results)} 项")
    
    if failed == 0:
        print("🎉 所有测试通过！系统连接完全正常。")
        return 0
    elif critical_failed == 0:
        print("✅ 核心服务正常，部分功能可能需要检查。")
        return 1
    else:
        print("❌ 核心服务存在问题，请检查Redis、Django和缓存配置。")
        return 2


if __name__ == "__main__":
    sys.exit(main()) 