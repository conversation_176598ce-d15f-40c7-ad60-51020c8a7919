# miragemakers AI 系统使用指南

## 📋 概述

本文档提供了 miragemakers AI 系统的完整使用指南，包括启动、测试、监控和故障排除。

## 🚀 快速启动

### 1. 环境准备

确保已安装以下依赖：
- Python 3.11+
- Node.js 18+
- Redis 6.0+
- Git

### 2. 项目设置

```bash
# 克隆项目
git clone https://github.com/your-username/visual_gen_agent.git
cd visual_gen_agent

# 创建虚拟环境
python3.11 -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
pip install -e .

# 前端依赖
cd frontend && npm install && cd ..

# 数据库迁移
python manage.py migrate
```

### 3. 启动服务

#### 方式一：手动启动（推荐用于开发）

```bash
# 1. 准备环境
./start_server.sh

# 2. 启动Celery Worker (终端1)
source .venv/bin/activate
celery -A app.celery_app worker --loglevel=info --queues=default --concurrency=2

# 3. 启动Django Backend (终端2)
source .venv/bin/activate
python manage.py runserver

# 4. 启动Next.js Frontend (终端3)
cd frontend
npm run dev
```

#### 方式二：自动启动（快速测试）

```bash
# 激活虚拟环境
source .venv/bin/activate

# 自动启动所有服务
./quick_start.sh
```

### 4. 访问应用

- **前端应用**: http://dev.miragemakers.ai:3000
- **后端API**: http://127.0.0.1:8000
- **管理面板**: http://127.0.0.1:8000/admin

## 🧪 系统测试

### 运行完整测试

```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行系统连接测试
python system_test.py
```

### 测试覆盖范围

- ✅ **模块导入**: 检查所有必需的Python模块
- ✅ **Redis连接**: 测试Redis服务器连接和读写
- ✅ **Django缓存**: 验证Django缓存系统
- ✅ **数据库连接**: 检查SQLite数据库连接
- ✅ **Celery连接**: 测试Celery broker和worker
- ✅ **Web服务**: 检查Django和Next.js服务健康状态
- ✅ **API端点**: 测试主要API端点连通性
- ✅ **系统端口**: 监控服务端口状态
- ✅ **性能测试**: Redis读写性能基准测试

### 测试结果解读

```bash
📈 测试统计: X 通过, Y 失败, 总计 Z 项

# 结果状态：
🎉 所有测试通过！系统连接完全正常。        # 返回码: 0
✅ 核心服务正常，部分功能可能需要检查。      # 返回码: 1  
❌ 核心服务存在问题，请检查配置。          # 返回码: 2
```

## 🛠️ 开发工作流

### 日常开发

1. **启动开发环境**
   ```bash
   source .venv/bin/activate
   ./start_server.sh  # 查看启动指令
   ```

2. **运行测试**
   ```bash
   python system_test.py
   ```

3. **代码修改后重启**
   ```bash
   ./stop_server.sh    # 停止所有服务
   ./quick_start.sh    # 重新启动
   ```

### 后端开发

```bash
# Django管理命令
python manage.py makemigrations  # 创建迁移
python manage.py migrate         # 应用迁移
python manage.py createsuperuser # 创建管理员
python manage.py shell           # Django shell

# Celery管理
celery -A app.celery_app worker --loglevel=info    # 启动worker
celery -A app.celery_app inspect active            # 查看活跃任务
celery -A app.celery_app purge                     # 清空队列
```

### 前端开发

```bash
cd frontend

# 开发服务器
npm run dev         # 开发模式
npm run build       # 构建生产版本
npm start           # 生产模式

# 依赖管理
npm install         # 安装依赖
npm update          # 更新依赖
```

## 📊 监控和调试

### 服务状态检查

```bash
# 检查端口占用
lsof -ti:6379  # Redis
lsof -ti:8000  # Django
lsof -ti:3000  # Next.js

# 检查Redis
redis-cli ping
redis-cli info
redis-cli keys "*"

# 检查进程
ps aux | grep celery
ps aux | grep python
ps aux | grep node
```

### 日志查看

```bash
# Celery日志
tail -f /tmp/celery.log

# Django日志
python manage.py runserver --verbosity=2

# Redis日志
redis-cli monitor
```

### 性能监控

```bash
# Redis性能
redis-cli --latency
redis-cli --stat

# 系统资源
top
htop
iostat
```

## 🚨 故障排除

### 常见问题

#### 1. Redis连接失败

```bash
# 检查Redis状态
brew services list | grep redis

# 启动Redis
brew services start redis

# 重启Redis
brew services restart redis
```

#### 2. 端口被占用

```bash
# 查找占用进程
lsof -ti:8000

# 终止进程
kill $(lsof -ti:8000)

# 或使用停止脚本
./stop_server.sh
```

#### 3. Celery Worker无响应

```bash
# 检查Celery状态
celery -A app.celery_app inspect active

# 重启Celery
pkill -f "celery.*worker"
celery -A app.celery_app worker --loglevel=info
```

#### 4. 前端构建失败

```bash
cd frontend

# 清理并重新安装
rm -rf node_modules package-lock.json
npm install

# 检查Node版本
node --version  # 需要 18+
npm --version
```

#### 5. 数据库问题

```bash
# 重置数据库
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser
```

### 环境变量检查

确保 `.env` 文件包含必要配置：

```env
DASHSCOPE_API_KEY=your_api_key
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
EMAIL_HOST_USER=your_email
EMAIL_HOST_PASSWORD=your_password
```

## 📝 最佳实践

### 开发建议

1. **使用虚拟环境**: 始终在激活的虚拟环境中工作
2. **定期测试**: 每次修改后运行 `python system_test.py`
3. **监控日志**: 关注Celery和Django的日志输出
4. **清理资源**: 开发结束后使用 `./stop_server.sh` 停止服务

### 性能优化

1. **Redis配置**: 根据内存大小调整Redis配置
2. **Celery并发**: 根据CPU核心数调整worker并发数
3. **Django设置**: 生产环境启用数据库连接池
4. **前端优化**: 启用Next.js缓存和压缩

### 安全考虑

1. **API密钥**: 妥善保管API密钥，不要提交到版本控制
2. **数据库**: 生产环境使用PostgreSQL替代SQLite
3. **HTTPS**: 生产环境启用HTTPS
4. **防火墙**: 限制不必要的端口访问

## 🔧 脚本说明

### 启动脚本

- **`start_server.sh`**: 环境检查和手动启动指南
- **`quick_start.sh`**: 自动化启动所有服务
- **`stop_server.sh`**: 停止所有服务

### 测试脚本

- **`system_test.py`**: 完整的系统连接测试

### 使用示例

```bash
# 完整开发流程
source .venv/bin/activate    # 激活环境
./start_server.sh           # 查看启动指令
# 在3个终端中分别启动服务
python system_test.py       # 测试系统
./stop_server.sh           # 停止服务

# 快速测试流程
source .venv/bin/activate
./quick_start.sh           # 自动启动
# Ctrl+C 停止
```

## 📞 支持

如遇到问题，请：

1. 首先运行 `python system_test.py` 诊断问题
2. 检查相关日志文件
3. 参考本文档的故障排除部分
4. 联系开发团队

---

**miragemakers AI Team** - 构建智能的多模态AI平台 