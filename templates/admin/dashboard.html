{% load static %}
<!DOCTYPE html>
<html lang="zh-CN" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MirageMakers AI - Dashboard</title>

    <script src="{% static 'js/tailwindcss.min.js' %}"></script>
    <script src="{% static 'js/chart.min.js' %}"></script>
    <script src="{% static 'js/lucide.min.js' %}"></script>
    <style>
        @import url('{% static "css/inter-font.css" %}');

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
        }

        .bg-dark-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(51, 65, 85, 0.3);
        }

        .bg-dark-secondary {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
        }

        .gradient-purple {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 50%, #c084fc 100%);
        }

        .gradient-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
        }

        .gradient-green {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
        }

        .gradient-orange {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(139, 92, 246, 0.5);
        }

        .status-healthy {
            color: #10b981;
        }

        .status-warning {
            color: #f59e0b;
        }

        .status-error {
            color: #ef4444;
        }

        .message-item {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .modal-backdrop {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .message-item:hover {
            background: rgba(51, 65, 85, 0.3);
            border-color: rgba(139, 92, 246, 0.5);
        }

        .modal-backdrop {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(8px);
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .glow-effect {
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
        }

        .text-gradient {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(15, 23, 42, 0.3);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(139, 92, 246, 0.6);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(139, 92, 246, 0.8);
        }
    </style>
</head>

<body class="bg-slate-950 text-white min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-dark-card border-b border-slate-700/50 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="text-gradient text-xl font-bold">⚡ MirageMakers AI</div>
                    <div class="text-slate-400 text-sm">Dashboard</div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-slate-400 text-sm">
                        <span id="current-time"></span>
                    </div>
                    <button onclick="refreshData()"
                        class="p-2 text-slate-400 hover:text-violet-400 transition-colors rounded-lg hover:bg-slate-800/50">
                        <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto p-6 space-y-8">
        <!-- 页面标题和控制 -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Performance Overview</h1>
                <p class="text-slate-400">Real-time data and key performance indicators</p>
            </div>
            <div class="flex space-x-3">
                <select id="period-select"
                    class="bg-dark-card border border-slate-600 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-violet-500 focus:border-transparent">
                    <option value="day">按天</option>
                    <option value="week">按周</option>
                    <option value="month">按月</option>
                    <option value="year">按年</option>
                </select>
                <select id="days-select"
                    class="bg-dark-card border border-slate-600 text-white rounded-lg px-4 py-2 focus:ring-2 focus:ring-violet-500 focus:border-transparent">
                    <option value="7">最近7天</option>
                    <option value="30" selected>最近30天</option>
                    <option value="90">最近90天</option>
                    <option value="365">最近一年</option>
                </select>
            </div>
        </div>

        <!-- 核心指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <!-- 总注册用户数 -->
            <div class="bg-dark-card rounded-xl p-6 card-hover glow-effect">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">Total Users</p>
                        <p class="text-3xl font-bold text-white" id="total-users">-</p>
                        <p class="text-green-400 text-sm mt-1">
                            +<span id="new-users-count">-</span> 今日新增
                        </p>
                    </div>
                    <div class="gradient-purple rounded-full p-3">
                        <i data-lucide="users" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- 付费用户数 -->
            <div class="bg-dark-card rounded-xl p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">Paid Users</p>
                        <p class="text-3xl font-bold text-white" id="paid-users">-</p>
                        <p class="text-blue-400 text-sm mt-1">
                            <span id="conversion-rate">-</span>% 转化率
                        </p>
                    </div>
                    <div class="gradient-blue rounded-full p-3">
                        <i data-lucide="credit-card" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- 当前在线用户 -->
            <div class="bg-dark-card rounded-xl p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">Online Users</p>
                        <p class="text-3xl font-bold text-white" id="online-users">-</p>
                        <p class="text-green-400 text-sm mt-1">
                            <span id="active-users">-</span> 活跃用户
                        </p>
                    </div>
                    <div class="gradient-green rounded-full p-3">
                        <i data-lucide="activity" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- API调用总数 -->
            <div class="bg-dark-card rounded-xl p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">API Calls</p>
                        <p class="text-3xl font-bold text-white" id="total-api-calls">-</p>
                        <p class="text-orange-400 text-sm mt-1">
                            <span id="healthy-apis">-</span>/<span id="total-apis">-</span> 健康
                        </p>
                    </div>
                    <div class="gradient-orange rounded-full p-3">
                        <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
            </div>

            <!-- 生成内容 -->
            <div class="bg-dark-card rounded-xl p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm font-medium">Generations</p>
                        <p class="text-3xl font-bold text-white" id="total-generations">-</p>
                        <p class="text-violet-400 text-sm mt-1">
                            <span id="image-gen">-</span> 图 | <span id="video-gen">-</span> 视频
                        </p>
                    </div>
                    <div style="background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);"
                        class="rounded-full p-3">
                        <i data-lucide="image" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            <!-- 左侧：图表和统计区域 -->
            <div class="lg:col-span-8 space-y-8">
                <!-- 第一排：用户增长和API使用量 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 用户增长趋势 -->
                    <div class="bg-dark-card rounded-xl p-6 card-hover">
                        <h3 class="text-xl font-semibold text-white mb-6">User Growth Trend</h3>
                        <div class="h-64">
                            <canvas id="user-growth-chart"></canvas>
                        </div>
                    </div>

                    <!-- API使用量占比 -->
                    <div class="bg-dark-card rounded-xl p-6 card-hover">
                        <h3 class="text-xl font-semibold text-white mb-6">API Usage Distribution</h3>
                        <div class="h-64">
                            <canvas id="api-usage-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 第二排：活跃度趋势和在线时段分布 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 活跃度趋势 -->
                    <div class="bg-dark-card rounded-xl p-6 card-hover">
                        <h3 class="text-xl font-semibold text-white mb-6">Activity Trend</h3>
                        <div class="h-64">
                            <canvas id="activity-chart"></canvas>
                        </div>
                    </div>

                    <!-- 每日在线时段分布 -->
                    <div class="bg-dark-card rounded-xl p-6 card-hover">
                        <h3 class="text-xl font-semibold text-white mb-6">Daily Active Hours</h3>
                        <div class="h-64">
                            <canvas id="hourly-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 第三排：活跃用户排名 -->
                <div class="bg-dark-card rounded-xl p-6 card-hover">
                    <h3 class="text-xl font-semibold text-white mb-6">Top Active Users</h3>
                    <div id="top-users-list" class="space-y-3">
                        <!-- 活跃用户列表将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 右侧：API状态和系统监控 -->
            <div class="lg:col-span-4 space-y-8">
                <!-- API详细状态 -->
                <div class="bg-dark-card rounded-xl p-6 card-hover">
                    <h3 class="text-xl font-semibold text-white mb-6">API Status Details</h3>
                    <div id="api-status-details" class="space-y-4">
                        <!-- API详细状态将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 系统资源监控 -->
                <div class="bg-dark-card rounded-xl p-6 card-hover">
                    <h3 class="text-xl font-semibold text-white mb-6">System Resources</h3>
                    <div class="space-y-6">
                        <div>
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-slate-300">CPU Usage</span>
                                <span id="cpu-percent" class="text-white font-medium">-</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div id="cpu-bar"
                                    class="bg-gradient-to-r from-blue-500 to-violet-500 h-2 rounded-full transition-all duration-500"
                                    style="width: 0%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-slate-300">Memory Usage</span>
                                <span id="memory-percent" class="text-white font-medium">-</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div id="memory-bar"
                                    class="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-500"
                                    style="width: 0%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-slate-300">Disk Usage</span>
                                <span id="disk-percent" class="text-white font-medium">-</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div id="disk-bar"
                                    class="bg-gradient-to-r from-yellow-500 to-orange-500 h-2 rounded-full transition-all duration-500"
                                    style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户账号管理区域 -->
        <div class="bg-dark-card rounded-xl p-6 card-hover">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-white">User Account Management</h3>
                <div class="flex space-x-3">
                    <button onclick="showCreateUserModal()"
                        class="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                        <i data-lucide="user-plus" class="w-4 h-4"></i>
                        <span>Create User</span>
                    </button>
                    <button onclick="refreshUserList()"
                        class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>Refresh</span>
                    </button>
                </div>
            </div>

            <!-- 用户搜索和过滤 -->
            <div class="flex space-x-3 mb-4">
                <div class="flex-1 relative">
                    <input type="text" id="user-search" placeholder="Search by username or email..."
                        class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 pr-8 text-sm focus:ring-2 focus:ring-violet-500"
                        onkeypress="handleSearchKeyPress(event)"
                        oninput="handleSearchInput()">
                    <button onclick="clearSearch()" id="clear-search-btn"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors hidden">
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
                <select id="user-status-filter"
                    class="bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-violet-500"
                    onchange="searchUsers()">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <button onclick="searchUsers()" id="search-btn"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                    <i data-lucide="search" class="w-4 h-4"></i>
                    <span>Search</span>
                </button>
            </div>

            <!-- 搜索结果信息 -->
            <div id="search-results-info" class="mb-2">
                <!-- 搜索结果信息将通过JavaScript动态生成 -->
            </div>

            <div id="user-list" class="space-y-3 max-h-96 overflow-y-auto">
                <!-- 用户列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 内测申请管理区域 -->
        <div class="bg-dark-card rounded-xl p-6 card-hover">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-white">Beta Applications</h3>
                <div class="flex space-x-2">
                    <button onclick="showDirectInvitationModal()"
                        class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                        <i data-lucide="mail-plus" class="w-4 h-4"></i>
                        <span>Send Invitation</span>
                    </button>
                    <select id="beta-status-filter"
                        class="bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-violet-500">
                        <option value="">All Status</option>
                        <option value="PENDING">Pending</option>
                        <option value="APPROVED">Approved</option>
                        <option value="REJECTED">Rejected</option>
                        <option value="INVITED">Invited</option>
                    </select>
                    <select id="beta-source-filter"
                        class="bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-violet-500">
                        <option value="">All Sources</option>
                        <option value="SOCIAL_MEDIA">Social Media</option>
                        <option value="SEARCH_ENGINE">Search Engine</option>
                        <option value="FRIEND_REFERRAL">Friend Referral</option>
                        <option value="TECH_BLOG">Tech Blog</option>
                        <option value="ONLINE_COMMUNITY">Online Community</option>
                        <option value="ADVERTISING">Advertising</option>
                        <option value="ADMIN_INVITATION">Admin Invitation</option>
                        <option value="OTHER">Other</option>
                    </select>
                    <button onclick="loadBetaApplications()"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        Refresh
                    </button>
                </div>
            </div>

            <!-- 申请搜索和过滤 -->
            <div class="flex space-x-3 mb-4">
                <input type="text" id="beta-search" placeholder="Search by email, name, company..."
                    class="flex-1 bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-violet-500">
                <button onclick="searchBetaApplications()"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    Search
                </button>
            </div>

            <!-- 统计信息 -->
            <div id="beta-stats" class="grid grid-cols-5 gap-4 mb-6">
                <!-- 统计卡片将通过JavaScript动态生成 -->
            </div>

            <div id="beta-applications-list" class="space-y-3 max-h-96 overflow-y-auto">
                <!-- 申请列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 消息历史区域 -->
        <div class="bg-dark-card rounded-xl p-6 card-hover">
            <div class="flex justify-between items-center mb-6">
                <div class="flex items-center space-x-4">
                    <h3 class="text-xl font-semibold text-white">Recent Messages</h3>
                    <div class="flex items-center space-x-2">
                        <button onclick="toggleSelectAll()"
                            class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors flex items-center space-x-1">
                            <i data-lucide="check-square" class="w-3 h-3"></i>
                            <span id="select-all-text">Select All</span>
                        </button>
                        <span id="selected-count" class="text-slate-400 text-sm">0 selected</span>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="showFilterModal()"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                        <i data-lucide="filter" class="w-4 h-4"></i>
                        <span>Filter</span>
                    </button>
                    <button onclick="exportSelectedMessages()"
                        class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>Export</span>
                    </button>
                    <select id="message-type-filter"
                        class="bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-violet-500">
                        <option value="">All Types</option>
                        <option value="user">User</option>
                        <option value="assistant">Assistant</option>
                        <option value="system">System</option>
                    </select>
                    <select id="media-filter"
                        class="bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-violet-500">
                        <option value="">All Messages</option>
                        <option value="true">With Media</option>
                        <option value="false">Text Only</option>
                    </select>
                </div>
            </div>
            <div id="message-history-list" class="space-y-3 max-h-96 overflow-y-auto">
                <!-- 消息历史将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 消息详情弹窗 -->
    <div id="message-modal" class="fixed inset-0 modal-backdrop hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-dark-card rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <div class="flex justify-between items-center p-6 border-b border-slate-700">
                    <h3 class="text-xl font-semibold text-white">Message Details</h3>
                    <button onclick="closeMessageModal()" class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                <div id="message-modal-content" class="p-6 overflow-y-auto max-h-[70vh]">
                    <!-- 消息详情内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 创建用户弹窗 -->
    <div id="create-user-modal" class="fixed inset-0 modal-backdrop hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-dark-card rounded-xl max-w-md w-full">
                <div class="flex justify-between items-center p-6 border-b border-slate-700">
                    <h3 class="text-xl font-semibold text-white">Create New User</h3>
                    <button onclick="closeCreateUserModal()" class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="create-user-form" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Name</label>
                        <input type="text" id="new-user-name" required
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Email</label>
                        <input type="email" id="new-user-email" required
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Password</label>
                        <input type="password" id="new-user-password" required
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Initial Tokens</label>
                        <input type="number" id="new-user-tokens" value="0" min="0"
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                    </div>

                    <div class="flex space-x-3 pt-4">
                        <button type="submit"
                            class="flex-1 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
                            Create User
                        </button>
                        <button type="button" onclick="closeCreateUserModal()"
                            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 消息过滤弹窗 -->
    <div id="filter-modal" class="fixed inset-0 modal-backdrop hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-dark-card rounded-xl max-w-lg w-full">
                <div class="flex justify-between items-center p-6 border-b border-slate-700">
                    <h3 class="text-xl font-semibold text-white">Filter Messages</h3>
                    <button onclick="closeFilterModal()" class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <form id="filter-form" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Keyword</label>
                        <input type="text" id="filter-keyword" placeholder="Search in message content..."
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                    </div>

                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Start Date</label>
                            <input type="datetime-local" id="filter-start-date"
                                class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">End Date</label>
                            <input type="datetime-local" id="filter-end-date"
                                class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Message Type</label>
                        <select id="filter-message-type"
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                            <option value="">All Types</option>
                            <option value="user">User</option>
                            <option value="assistant">Assistant</option>
                            <option value="system">System</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">User Email</label>
                        <input type="email" id="filter-user-email" placeholder="Filter by user email..."
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Session ID</label>
                        <input type="text" id="filter-session-id" placeholder="Filter by session ID (UUID)..."
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500"
                            title="Search for messages within a specific conversation session">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Media Content</label>
                        <select id="filter-has-media"
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500">
                            <option value="">All Messages</option>
                            <option value="true">With Media</option>
                            <option value="false">Text Only</option>
                        </select>
                    </div>

                    <div class="flex space-x-3 pt-4">
                        <button type="submit"
                            class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                            Apply Filter
                        </button>
                        <button type="button" onclick="clearFilter()"
                            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                            Clear
                        </button>
                        <button type="button" onclick="closeFilterModal()"
                            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 内测申请详情弹窗 -->
    <div id="beta-application-modal" class="fixed inset-0 modal-backdrop hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-dark-card rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
                <div class="flex justify-between items-center p-6 border-b border-slate-700">
                    <h3 class="text-xl font-semibold text-white">Beta Application Details</h3>
                    <button onclick="closeBetaApplicationModal()"
                        class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                <div id="beta-application-modal-content" class="p-6 overflow-y-auto max-h-[70vh]">
                    <!-- 申请详情内容 -->
                </div>
                <div class="flex justify-between p-6 border-t border-slate-700">
                    <div class="flex space-x-3">
                        <button onclick="deleteBetaApplication()"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                            title="Delete invitation and user account (if created)">
                            Delete Invitation
                        </button>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="approveBetaApplication()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                            Approve
                        </button>
                        <button onclick="rejectBetaApplication()"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                            Reject
                        </button>
                        <button onclick="sendInvitation()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                            Send Invitation
                        </button>
                        <button onclick="closeBetaApplicationModal()"
                            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员备注弹窗 -->
    <div id="admin-notes-modal" class="fixed inset-0 modal-backdrop hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-dark-card rounded-xl max-w-md w-full">
                <div class="flex justify-between items-center p-6 border-b border-slate-700">
                    <h3 class="text-xl font-semibold text-white">Admin Notes</h3>
                    <button onclick="closeAdminNotesModal()" class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                <form id="admin-notes-form" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Notes</label>
                        <textarea id="admin-notes-text" rows="4"
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500"
                            placeholder="Add admin notes..."></textarea>
                    </div>
                    <div class="flex space-x-3 pt-4">
                        <button type="submit"
                            class="flex-1 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
                            Save Notes
                        </button>
                        <button type="button" onclick="closeAdminNotesModal()"
                            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 直接邀请弹窗 -->
    <div id="direct-invitation-modal" class="fixed inset-0 modal-backdrop hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-dark-card rounded-xl max-w-md w-full">
                <div class="flex justify-between items-center p-6 border-b border-slate-700">
                    <h3 class="text-xl font-semibold text-white">Send Direct Invitation</h3>
                    <button onclick="closeDirectInvitationModal()"
                        class="text-slate-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                <form id="direct-invitation-form" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Email Address</label>
                        <input type="email" id="direct-invitation-email" required
                            class="w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-violet-500"
                            placeholder="Enter email address...">
                    </div>
                    <div class="bg-slate-800/50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-slate-300 mb-2">Account Details</h4>
                        <ul class="text-sm text-slate-400 space-y-1">
                            <li>• Account will be created automatically</li>
                            <li>• Random password will be generated</li>
                            <li>• 8000 trial tokens (30 days validity)</li>
                            <li>• Email verified automatically</li>
                            <li>• Login credentials sent via email</li>
                        </ul>
                    </div>
                    <div class="flex space-x-3 pt-4">
                        <button type="submit"
                            class="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2">
                            <i data-lucide="mail" class="w-4 h-4"></i>
                            <span>Send Invitation</span>
                        </button>
                        <button type="button" onclick="closeDirectInvitationModal()"
                            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="fixed inset-0 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-dark-card rounded-xl p-6 flex items-center space-x-3">
            <div class="loading-spinner w-6 h-6 border-2 border-violet-500 border-t-transparent rounded-full"></div>
            <span class="text-white">Loading...</span>
        </div>
    </div>

    <script>
        // 初始化
        lucide.createIcons();

        // 全局变量
        let userGrowthChart = null;
        let activityChart = null;

        // 通用函数：构建带token的URL（支持Django admin session认证）
        function buildUrlWithToken(baseUrl) {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');

            if (token) {
                const separator = baseUrl.includes('?') ? '&' : '?';
                return `${baseUrl}${separator}token=${token}`;
            }
            // 对于Django admin session认证，直接返回原URL
            return baseUrl;
        }



        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // 绑定事件监听器
            document.getElementById('period-select').addEventListener('change', refreshData);
            document.getElementById('days-select').addEventListener('change', refreshData);
            document.getElementById('message-type-filter').addEventListener('change', loadMessageHistory);
            document.getElementById('media-filter').addEventListener('change', loadMessageHistory);

            // 监听勾选框变化来更新计数
            document.addEventListener('change', function (e) {
                if (e.target.classList.contains('message-checkbox')) {
                    updateSelectedCount();
                }
            });

            // 初始加载数据
            refreshData();
            loadMessageHistory();
            loadUserList();
        });

        // 全选/取消全选功能
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.message-checkbox');
            const selectAllText = document.getElementById('select-all-text');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });

            selectAllText.textContent = allChecked ? 'Select All' : 'Deselect All';
            updateSelectedCount();
        }

        // 更新选中数量显示
        function updateSelectedCount() {
            const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
            const selectedCount = document.getElementById('selected-count');
            const selectAllText = document.getElementById('select-all-text');
            const allCheckboxes = document.querySelectorAll('.message-checkbox');

            selectedCount.textContent = `${checkedBoxes.length} selected`;

            // 更新全选按钮文本
            if (checkedBoxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                selectAllText.textContent = 'Deselect All';
            } else {
                selectAllText.textContent = 'Select All';
            }
        }

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
        }

        // 显示加载指示器
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }

        // 隐藏加载指示器
        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        // 刷新数据
        async function refreshData() {
            showLoading();
            try {
                const period = document.getElementById('period-select').value;
                const days = document.getElementById('days-select').value;

                const response = await fetch(buildUrlWithToken(`/dashboard/api/?period=${period}&days=${days}`));
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                updateMetrics(data);
                updateCharts(data);
                updateSystemStatus(data);

            } catch (error) {
                console.error('加载数据失败:', error);
                alert('加载数据失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 更新核心指标
        function updateMetrics(data) {
            try {
                const userStats = data.user_stats || {};
                const chatStats = data.chat_stats || {};
                const apiStats = data.api_stats || {};

                document.getElementById('total-users').textContent = (userStats.total_users || 0).toLocaleString();
                document.getElementById('paid-users').textContent = (userStats.paid_users || 0).toLocaleString();
                document.getElementById('online-users').textContent = (userStats.online_users || 0).toLocaleString();
                document.getElementById('new-users-count').textContent = (userStats.new_users_count || 0).toLocaleString();
                document.getElementById('active-users').textContent = (userStats.active_users || 0).toLocaleString();
                document.getElementById('conversion-rate').textContent = userStats.conversion_rate || '0';

                document.getElementById('total-api-calls').textContent = (apiStats.total_api_calls || 0).toLocaleString();
                document.getElementById('healthy-apis').textContent = apiStats.healthy_apis || '0';
                document.getElementById('total-apis').textContent = apiStats.total_apis || '0';

                const totalGenerations = (chatStats.image_generations || 0) + (chatStats.video_generations || 0);
                document.getElementById('total-generations').textContent = totalGenerations.toLocaleString();
                document.getElementById('image-gen').textContent = (chatStats.image_generations || 0).toLocaleString();
                document.getElementById('video-gen').textContent = (chatStats.video_generations || 0).toLocaleString();
            } catch (error) {
                console.error('更新指标失败:', error);
                // 设置默认值避免显示错误
                document.getElementById('total-users').textContent = '0';
                document.getElementById('paid-users').textContent = '0';
                document.getElementById('online-users').textContent = '0';
                document.getElementById('new-users-count').textContent = '0';
                document.getElementById('active-users').textContent = '0';
                document.getElementById('conversion-rate').textContent = '0';
                document.getElementById('total-api-calls').textContent = '0';
                document.getElementById('healthy-apis').textContent = '0';
                document.getElementById('total-apis').textContent = '0';
                document.getElementById('total-generations').textContent = '0';
                document.getElementById('image-gen').textContent = '0';
                document.getElementById('video-gen').textContent = '0';
            }
        }

        // 更新图表
        function updateCharts(data) {
            try {
                const userStats = data.user_stats || {};
                const chatStats = data.chat_stats || {};
                const apiStats = data.api_stats || {};

                updateUserGrowthChart(userStats.growth_data || []);
                updateActivityChart(chatStats.activity_data || []);
                updateApiUsageChart(apiStats.api_usage_stats || {});
                updateHourlyChart(userStats.hourly_distribution || []);
                updateTopUsers(userStats.top_active_users || []);
            } catch (error) {
                console.error('更新图表失败:', error);
            }
        }

        // 更新用户增长图表
        function updateUserGrowthChart(growthData) {
            const ctx = document.getElementById('user-growth-chart').getContext('2d');

            if (userGrowthChart) {
                userGrowthChart.destroy();
            }

            const labels = growthData.map(item => new Date(item.date).toLocaleDateString('zh-CN'));
            const data = growthData.map(item => item.new_users);

            // 创建渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, 'rgba(139, 92, 246, 0.3)');
            gradient.addColorStop(1, 'rgba(139, 92, 246, 0.05)');

            userGrowthChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '新用户',
                        data: data,
                        borderColor: '#8b5cf6',
                        backgroundColor: gradient,
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#8b5cf6',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 3,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#8b5cf6',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        }
                    }
                }
            });
        }

        // 更新活跃度图表
        function updateActivityChart(activityData) {
            const ctx = document.getElementById('activity-chart').getContext('2d');

            if (activityChart) {
                activityChart.destroy();
            }

            const labels = activityData.map(item => new Date(item.date).toLocaleDateString('zh-CN'));
            const data = activityData.map(item => item.messages);

            // 创建渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, 'rgba(16, 185, 129, 0.3)');
            gradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)');

            activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '消息数',
                        data: data,
                        backgroundColor: gradient,
                        borderColor: '#10b981',
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#10b981',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 3,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#10b981',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        }
                    }
                }
            });
        }

        // 更新系统状态
        function updateSystemStatus(data) {
            const apiStats = data.api_stats;
            const systemStats = data.system_stats;

            updateApiStatus(apiStats.api_status);
            updateSystemResources(systemStats);
        }

        // 更新API状态
        function updateApiStatus(apiStatus) {
            const container = document.getElementById('api-status-details');
            container.innerHTML = '';

            Object.entries(apiStatus).forEach(([apiName, status]) => {
                const statusClass = status.status === 'healthy' ? 'status-healthy' :
                    status.status === 'warning' ? 'status-warning' : 'status-error';

                const statusIcon = status.status === 'healthy' ? 'check-circle' :
                    status.status === 'warning' ? 'alert-circle' : 'x-circle';

                const quotaPercentage = status.quota_percentage || 0;
                const quotaColorClass = quotaPercentage > 90 ? 'from-red-500 to-red-600' :
                    quotaPercentage > 70 ? 'from-yellow-500 to-orange-600' : 'from-green-500 to-emerald-600';

                const item = document.createElement('div');
                item.className = 'p-4 bg-slate-800/50 rounded-lg border border-slate-700 hover:border-violet-500/50 cursor-pointer transition-all duration-200';
                item.onclick = () => showApiDetails(apiName, status);

                item.innerHTML = `
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="${statusIcon}" class="w-4 h-4 ${statusClass}"></i>
                                <span class="text-sm font-medium text-white">${apiName}</span>
                            </div>
                            <span class="text-xs text-slate-400">${status.response_time}ms</span>
                        </div>
                        
                        <!-- 配额使用情况 -->
                        <div>
                            <div class="flex justify-between text-xs mb-1">
                                <span class="text-slate-400">Quota</span>
                                <span class="text-white">${status.quota_used}/${status.quota_total}</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-gradient-to-r ${quotaColorClass} h-2 rounded-full transition-all duration-500" 
                                     style="width: ${quotaPercentage}%"></div>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div>
                                <span class="text-slate-400">Success:</span>
                                <span class="text-green-400 ml-1">${status.success_rate}%</span>
                            </div>
                            <div>
                                <span class="text-slate-400">Calls:</span>
                                <span class="text-white ml-1">${status.calls_today}</span>
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(item);
            });

            lucide.createIcons();
        }

        // 更新系统资源
        function updateSystemResources(systemStats) {
            document.getElementById('cpu-percent').textContent = systemStats.cpu_percent + '%';
            document.getElementById('cpu-bar').style.width = systemStats.cpu_percent + '%';

            document.getElementById('memory-percent').textContent = systemStats.memory.percent + '%';
            document.getElementById('memory-bar').style.width = systemStats.memory.percent + '%';

            document.getElementById('disk-percent').textContent = systemStats.disk.percent + '%';
            document.getElementById('disk-bar').style.width = systemStats.disk.percent + '%';
        }

        // 加载消息历史
        async function loadMessageHistory() {
            try {
                const messageType = document.getElementById('message-type-filter').value;
                const hasMedia = document.getElementById('media-filter').value;

                const params = new URLSearchParams();
                if (messageType) params.append('type', messageType);
                if (hasMedia) params.append('has_media', hasMedia);
                params.append('limit', '30');

                const response = await fetch(buildUrlWithToken(`/dashboard/messages/?${params}`));
                const data = await response.json();

                const container = document.getElementById('message-history-list');
                container.innerHTML = '';

                data.messages.forEach(message => {
                    const item = document.createElement('div');
                    item.className = 'message-item p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:border-violet-500/50';

                    const typeColor = message.type === 'user' ? 'text-blue-400' :
                        message.type === 'assistant' ? 'text-green-400' : 'text-orange-400';

                    const mediaIcon = message.has_media ?
                        (message.media_type === 'image' ? 'image' : 'video') : '';

                    item.innerHTML = `
                        <div class="flex items-start space-x-3">
                            <input type="checkbox" class="message-checkbox mt-2" data-message-id="${message.id}">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center">
                                    <span class="text-xs font-medium text-white">
                                        ${message.user.name ? message.user.name.charAt(0).toUpperCase() : 'A'}
                                    </span>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0 message-content-area cursor-pointer">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm font-medium text-white">${message.user.name}</span>
                                        <span class="text-xs ${typeColor} font-medium">${message.type}</span>
                                        ${message.has_media ? `<i data-lucide="${mediaIcon}" class="w-4 h-4 text-violet-400"></i>` : ''}
                                    </div>
                                    <span class="text-xs text-slate-500">
                                        ${new Date(message.created_at).toLocaleString('zh-CN')}
                                    </span>
                                </div>
                                <p class="text-sm text-slate-300 mt-1 truncate">${message.content}</p>
                                <div class="text-xs text-slate-500 mt-1 space-y-1">
                                    <div>Session: ${message.session.title}</div>
                                    <div class="flex items-center space-x-2">
                                        <span>Session ID:</span>
                                        <span class="font-mono text-xs bg-slate-700 px-2 py-1 rounded cursor-pointer hover:bg-slate-600" 
                                              onclick="copyToClipboard('${message.session_id}')" 
                                              title="Click to copy session ID">
                                            ${message.session_id.substring(0, 8)}...
                                        </span>
                                        <button onclick="filterBySessionId('${message.session_id}')" 
                                                class="text-violet-400 hover:text-violet-300 text-xs underline"
                                                title="View all messages in this session">
                                            View Session
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 对消息内容区域添加点击事件，勾选框保持独立
                    const contentArea = item.querySelector('.message-content-area');
                    if (contentArea) {
                        contentArea.addEventListener('click', () => showMessageDetails(message));
                    }
                    container.appendChild(item);
                });

                lucide.createIcons();
                updateSelectedCount(); // 更新选择计数

            } catch (error) {
                console.error('加载消息历史失败:', error);
            }
        }

        // 显示消息详情
        async function showMessageDetails(message) {
            try {
                // 显示单条消息的详情，包含用户提示词和AI回复
                const modalContent = document.getElementById('message-modal-content');
                modalContent.innerHTML = `
                    <div class="space-y-6">
                        <!-- 对话详情 -->
                        <div class="bg-slate-800/50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-white mb-3">Conversation Details</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-slate-400">Message ID:</span>
                                    <span class="text-white ml-2">${message.id}</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">Type:</span>
                                    <span class="text-white ml-2">${message.type}</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">User:</span>
                                    <span class="text-white ml-2">${message.user.name} (${message.user.email})</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">Created:</span>
                                    <span class="text-white ml-2">${new Date(message.created_at).toLocaleString('zh-CN')}</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">Session:</span>
                                    <span class="text-white ml-2">${message.session.title}</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">Has Media:</span>
                                    <span class="text-white ml-2">${message.has_media ? 'Yes' : 'No'}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 用户提示词 -->
                        ${message.user_prompt ? `
                            <div>
                                <h4 class="text-lg font-semibold text-white mb-4">👤 User Prompt</h4>
                                <div class="bg-blue-900/30 rounded-lg p-4">
                                    <div class="text-white whitespace-pre-wrap">${message.user_prompt}</div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- AI回复内容 -->
                        <div>
                            <h4 class="text-lg font-semibold text-white mb-4">🤖 AI Response</h4>
                            <div class="bg-slate-800/30 rounded-lg p-4">
                                <div class="text-white whitespace-pre-wrap">${message.content_full || message.content}</div>
                                ${message.image_url ? `
                                    <div class="mt-4">
                                        <h5 class="text-sm font-medium text-slate-400 mb-2">Generated Image:</h5>
                                        <img src="${message.image_url}" alt="Generated Image" 
                                             class="max-w-full h-auto rounded-lg border border-slate-600 cursor-pointer"
                                             onclick="window.open('${message.image_url}', '_blank')">
                                    </div>
                                ` : ''}
                                ${message.video_url ? `
                                    <div class="mt-4">
                                        <h5 class="text-sm font-medium text-slate-400 mb-2">Generated Video:</h5>
                                        <video controls class="max-w-full h-auto rounded-lg border border-slate-600">
                                            <source src="${message.video_url}" type="video/mp4">
                                            Your browser does not support the video tag.
                                        </video>
                                    </div>
                                ` : ''}
                            </div>
                        </div>

                        <!-- 相关操作 -->
                        <div class="flex justify-end items-center">
                            <button onclick="closeMessageModal()" 
                                    class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                                Close
                            </button>
                        </div>
                    </div>
                `;

                document.getElementById('message-modal').classList.remove('hidden');

            } catch (error) {
                console.error('显示消息详情失败:', error);
                alert('显示消息详情失败: ' + error.message);
            }
        }

        // 下载媒体文件
        function downloadMedia(url, type) {
            const link = document.createElement('a');
            link.href = url;
            link.download = `mirage-makers-${type}-${Date.now()}.${type === 'image' ? 'jpg' : 'mp4'}`;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 查看完整会话（新增功能）
        async function viewFullSession(sessionId) {
            try {
                const response = await fetch(buildUrlWithToken(`/dashboard/sessions/${sessionId}/messages/`));
                const data = await response.json();

                const modalContent = document.getElementById('message-modal-content');
                modalContent.innerHTML = `
                    <div class="space-y-6">
                        <!-- 会话信息 -->
                        <div class="bg-slate-800/50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-white mb-3">Session Information</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-slate-400">Session ID:</span>
                                    <span class="text-white ml-2">${data.session.id}</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">Title:</span>
                                    <span class="text-white ml-2">${data.session.title}</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">User:</span>
                                    <span class="text-white ml-2">${data.session.user.name} (${data.session.user.email})</span>
                                </div>
                                <div>
                                    <span class="text-slate-400">Created:</span>
                                    <span class="text-white ml-2">${new Date(data.session.created_at).toLocaleString('zh-CN')}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 消息列表 -->
                        <div>
                            <h4 class="text-lg font-semibold text-white mb-4">Messages (${data.message_count})</h4>
                            <div class="space-y-4 max-h-96 overflow-y-auto">
                                ${data.messages.map(msg => {
                    const isUser = msg.type === 'user';
                    const bgColor = isUser ? 'bg-blue-900/30' : 'bg-slate-800/50';
                    const textAlign = isUser ? 'text-right' : 'text-left';

                    return `
                                        <div class="${textAlign}">
                                            <div class="inline-block max-w-[80%] ${bgColor} rounded-lg p-4">
                                                <div class="flex items-center justify-between mb-2">
                                                    <span class="text-xs font-medium ${isUser ? 'text-blue-400' : 'text-green-400'}">
                                                        ${msg.type.toUpperCase()}
                                                    </span>
                                                    <span class="text-xs text-slate-500">
                                                        ${new Date(msg.created_at).toLocaleString('zh-CN')}
                                                    </span>
                                                </div>
                                                <div class="text-white text-sm whitespace-pre-wrap">${msg.content}</div>
                                                ${msg.image_url ? `
                                                    <div class="mt-3">
                                                        <img src="${msg.image_url}" alt="Generated Image" 
                                                             class="max-w-full h-auto rounded-lg border border-slate-600 cursor-pointer"
                                                             onclick="window.open('${msg.image_url}', '_blank')">
                                                    </div>
                                                ` : ''}
                                                ${msg.video_url ? `
                                                    <div class="mt-3">
                                                        <video controls class="max-w-full h-auto rounded-lg border border-slate-600">
                                                            <source src="${msg.video_url}" type="video/mp4">
                                                            Your browser does not support the video tag.
                                                        </video>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    `;
                }).join('')}
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="flex justify-end">
                            <button onclick="closeMessageModal()" 
                                    class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors">
                                Close
                            </button>
                        </div>
                    </div>
                `;

            } catch (error) {
                console.error('加载完整会话失败:', error);
                alert('加载完整会话失败: ' + error.message);
            }
        }

        // 关闭消息详情弹窗
        function closeMessageModal() {
            document.getElementById('message-modal').classList.add('hidden');
        }

        // 点击背景关闭弹窗
        document.getElementById('message-modal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeMessageModal();
            }
        });

        // API使用量饼图
        let apiUsageChart = null;
        function updateApiUsageChart(apiUsageStats) {
            const ctx = document.getElementById('api-usage-chart').getContext('2d');

            if (apiUsageChart) {
                apiUsageChart.destroy();
            }

            const data = [
                apiUsageStats.text.calls,
                apiUsageStats.image.calls,
                apiUsageStats.video.calls
            ];

            apiUsageChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Text API', 'Image API', 'Video API'],
                    datasets: [{
                        label: 'API Calls',
                        data: data,
                        backgroundColor: [
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(6, 182, 212, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ],
                        borderColor: [
                            '#8b5cf6',
                            '#06b6d4',
                            '#10b981'
                        ],
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#e2e8f0',
                                font: {
                                    size: 14,
                                    weight: '500'
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    },
                    elements: {
                        bar: {
                            borderRadius: 8
                        }
                    }
                }
            });
        }

        // 每日活跃时段图
        let hourlyChart = null;
        function updateHourlyChart(hourlyData) {
            const ctx = document.getElementById('hourly-chart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            const labels = hourlyData.map(item => item.hour + ':00');
            const data = hourlyData.map(item => item.active_users);

            // 创建渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, 'rgba(245, 158, 11, 0.3)');
            gradient.addColorStop(1, 'rgba(245, 158, 11, 0.05)');

            hourlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '活跃用户数',
                        data: data,
                        borderColor: '#f59e0b',
                        backgroundColor: gradient,
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#f59e0b',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 3,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#f59e0b',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3,
                        pointStyle: 'circle'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#94a3b8',
                                maxTicksLimit: 12
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(51, 65, 85, 0.3)',
                                borderColor: 'rgba(51, 65, 85, 0.5)'
                            }
                        }
                    }
                }
            });
        }

        // 更新顶级用户列表
        function updateTopUsers(topUsers) {
            const container = document.getElementById('top-users-list');
            container.innerHTML = '';

            topUsers.forEach((user, index) => {
                const item = document.createElement('div');
                item.className = 'flex items-center justify-between p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:border-violet-500/50 cursor-pointer transition-all duration-200';
                item.onclick = () => showUserDetails(user);

                const rankColors = ['text-yellow-400', 'text-gray-300', 'text-orange-400', 'text-violet-400', 'text-blue-400'];
                const rankColor = rankColors[index] || 'text-slate-400';

                item.innerHTML = `
                    <div class="flex items-center space-x-4">
                        <div class="w-8 h-8 ${rankColor} font-bold text-lg flex items-center justify-center">
                            #${index + 1}
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center">
                                <span class="text-sm font-medium text-white">
                                    ${user.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                            <div>
                                <div class="text-white font-medium">${user.name}</div>
                                <div class="text-slate-400 text-sm">${user.email}</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-white font-semibold">${user.message_count}</div>
                        <div class="text-slate-400 text-sm">消息数</div>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // 显示用户详情
        function showUserDetails(user) {
            alert(`用户详情：\n姓名：${user.name}\n邮箱：${user.email}\n消息数：${user.message_count}\n最后活跃：${user.last_active || '未知'}`);
        }

        // 用户管理相关函数
        let selectedMessages = new Set();

        // 显示创建用户弹窗
        function showCreateUserModal() {
            document.getElementById('create-user-modal').classList.remove('hidden');
        }

        // 关闭创建用户弹窗
        function closeCreateUserModal() {
            document.getElementById('create-user-modal').classList.add('hidden');
            document.getElementById('create-user-form').reset();
        }

        // 创建用户表单提交
        document.getElementById('create-user-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = {
                action: 'create_user',
                name: document.getElementById('new-user-name').value,
                email: document.getElementById('new-user-email').value,
                password: document.getElementById('new-user-password').value,
                tokens: parseInt(document.getElementById('new-user-tokens').value) || 0
            };

            try {
                const response = await fetch(buildUrlWithToken('/dashboard/users/management/'), {
                    method: 'POST',
                    credentials: 'include',  // 确保发送cookies
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                alert(data.message);
                closeCreateUserModal();
                loadUserList();

            } catch (error) {
                console.error('创建用户失败:', error);
                alert('创建用户失败: ' + error.message);
            }
        });

        // 加载用户列表
        async function loadUserList() {
            try {
                const search = document.getElementById('user-search').value;
                const status = document.getElementById('user-status-filter').value;
                const searchResultsInfo = document.getElementById('search-results-info');

                console.log('🔍 搜索参数:', { search, status });

                const params = new URLSearchParams();
                if (search) params.append('search', search);
                if (status) params.append('status', status);
                params.append('page_size', '20');

                const url = buildUrlWithToken(`/dashboard/users/management/?${params}`);
                console.log('📡 请求URL:', url);
                console.log('📋 请求参数详细:', {
                    search: search,
                    status: status,
                    searchEncoded: encodeURIComponent(search),
                    fullParams: params.toString()
                });
                


                const response = await fetch(url, {
                    method: 'GET',
                    credentials: 'include',  // 确保发送cookies
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',  // 标识为AJAX请求
                    }
                });
                

                console.log('📋 响应状态:', response.status);
                console.log('📋 响应头:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ 服务器错误:', errorText);
                    // 如果是认证错误，显示token信息
                    if (response.status === 403) {
                        console.error('❌ 认证失败：请使用token URL访问dashboard');
                        alert('认证失败，请使用带token的URL访问dashboard');
                        return;
                    }
                    
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseText = await response.text();
                console.log('📄 原始响应:', responseText.substring(0, 500) + '...');
                
                const data = JSON.parse(responseText);
                console.log('📊 解析后数据:', {
                    userCount: data.users?.length,
                    totalCount: data.pagination?.total_count,
                    firstUser: data.users?.[0]?.email,
                    allUsers: data.users?.map(u => u.email)
                });

                const container = document.getElementById('user-list');
                container.innerHTML = '';

                // 显示搜索结果信息
                if (searchResultsInfo) {
                    const totalCount = data.pagination?.total_count || 0;
                    const isSearching = search.trim() !== '' || status !== '';
                    
                    if (isSearching) {
                        searchResultsInfo.innerHTML = `
                            <div class="text-slate-400 text-sm mb-3">
                                <i data-lucide="search" class="w-4 h-4 inline mr-1"></i>
                                找到 ${totalCount} 个用户
                                ${search.trim() ? `包含 "${search.trim()}"` : ''}
                                ${status ? `状态: ${status}` : ''}
                                <button onclick="clearSearch()" class="ml-2 text-violet-400 hover:text-violet-300 text-xs">
                                    <i data-lucide="x" class="w-3 h-3 inline mr-1"></i>清空搜索
                                </button>
                            </div>
                        `;
                    } else {
                        searchResultsInfo.innerHTML = `
                            <div class="text-slate-400 text-sm mb-3">
                                <i data-lucide="users" class="w-4 h-4 inline mr-1"></i>
                                显示所有用户 (共 ${totalCount} 个)
                            </div>
                        `;
                    }
                }

                // 检查是否有用户数据
                if (!data.users || data.users.length === 0) {
                    const isSearching = search.trim() !== '' || status !== '';
                    container.innerHTML = `
                        <div class="p-8 text-center bg-slate-800/30 rounded-lg border border-slate-700">
                            <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-slate-700/50 flex items-center justify-center">
                                <i data-lucide="${isSearching ? 'search-x' : 'users'}" class="w-8 h-8 text-slate-400"></i>
                            </div>
                            <p class="text-slate-300 text-lg font-medium mb-2">
                                ${isSearching ? '没有找到匹配的用户' : '暂无用户数据'}
                            </p>
                            <p class="text-slate-500 text-sm mb-4">
                                ${isSearching ? '尝试修改搜索条件或清空搜索' : '系统中还没有注册用户'}
                            </p>
                            ${isSearching ? `
                                <button onclick="clearSearch()" class="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
                                    <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-1"></i>
                                    清空搜索
                                </button>
                            ` : ''}
                        </div>
                    `;
                    lucide.createIcons();
                    return;
                }

                // 渲染用户列表
                data.users.forEach(user => {
                    const item = document.createElement('div');
                    item.className = 'p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:border-violet-500/50 transition-all duration-200';

                    const statusBadge = user.is_active
                        ? '<span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">Active</span>'
                        : '<span class="px-2 py-1 bg-red-600 text-white text-xs rounded-full">Inactive</span>';

                    // 高亮搜索关键词
                    const highlightText = (text, keyword) => {
                        if (!keyword || !text) return text;
                        const regex = new RegExp(`(${keyword})`, 'gi');
                        return text.replace(regex, '<mark class="bg-yellow-500/30 text-yellow-200">$1</mark>');
                    };

                    const searchKeyword = search.trim();
                    const highlightedName = highlightText(user.name, searchKeyword);
                    const highlightedEmail = highlightText(user.email, searchKeyword);

                    item.innerHTML = `
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">
                                        ${user.name.charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <div class="text-white font-medium">${highlightedName}</div>
                                    <div class="text-slate-400 text-sm">${highlightedEmail}</div>
                                    <div class="text-slate-500 text-xs">Tokens: ${user.tokens} | Messages: ${user.message_count}</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                ${statusBadge}
                                <button onclick="editUserTokens('${user.id}', '${user.name}', ${user.tokens})" 
                                    class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors">
                                    Edit Tokens
                                </button>
                                <button onclick="toggleUserStatus('${user.id}', '${user.name}', ${user.is_active})" 
                                    class="px-3 py-1 ${user.is_active ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'} text-white text-xs rounded transition-colors">
                                    ${user.is_active ? 'Disable' : 'Enable'}
                                </button>
                                <button onclick="deleteUser('${user.id}', '${user.name}', '${user.email}')" 
                                    class="px-3 py-1 bg-red-700 hover:bg-red-800 text-white text-xs rounded transition-colors">
                                    Delete
                                </button>
                            </div>
                        </div>
                    `;
                    container.appendChild(item);
                });

                lucide.createIcons();

            } catch (error) {
                console.error('❌ 加载用户列表失败:', error);

                // 显示错误信息给用户
                const container = document.getElementById('user-list');
                container.innerHTML = `
                    <div class="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                        <div class="flex items-center space-x-2 mb-2">
                            <i data-lucide="alert-circle" class="w-5 h-5 text-red-400"></i>
                            <p class="text-red-400 font-medium">加载用户列表失败</p>
                        </div>
                        <p class="text-red-300 text-sm mb-3">${error.message}</p>
                        <button onclick="loadUserList()" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                            <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-1"></i>
                            重试
                        </button>
                    </div>
                `;
                lucide.createIcons();
            }
        }

        // 刷新用户列表
        function refreshUserList() {
            loadUserList();
        }

        // 处理搜索框按键事件
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                searchUsers();
            }
        }

        // 处理搜索框输入变化
        function handleSearchInput() {
            const searchInput = document.getElementById('user-search');
            const clearBtn = document.getElementById('clear-search-btn');
            
            if (searchInput.value.trim()) {
                clearBtn.classList.remove('hidden');
            } else {
                clearBtn.classList.add('hidden');
            }
        }

        // 搜索用户
        function searchUsers() {
            console.log('🔍 搜索按钮被点击');
            const search = document.getElementById('user-search').value.trim();
            const status = document.getElementById('user-status-filter').value;
            
            console.log('🔍 搜索参数详细:', {
                search: search,
                status: status,
                searchLength: search.length,
                searchEncoded: encodeURIComponent(search)
            });
            
            if (!search && !status) {
                console.log('🔍 搜索条件为空，执行完整用户列表加载');
            } else {
                console.log('🔍 执行搜索 - 关键词:', search, '状态:', status);
            }

            // 显示搜索状态
            const searchBtn = document.getElementById('search-btn');
            const originalText = searchBtn.innerHTML;
            searchBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 animate-spin"></i><span>搜索中...</span>';
            searchBtn.disabled = true;

            // 执行搜索
            loadUserList().finally(() => {
                // 恢复按钮状态
                searchBtn.innerHTML = originalText;
                searchBtn.disabled = false;
                lucide.createIcons();
            });
        }

        // 清空搜索
        function clearSearch() {
            document.getElementById('user-search').value = '';
            document.getElementById('user-status-filter').value = '';
            loadUserList();
        }

        // 编辑用户tokens
        function editUserTokens(userId, userName, currentTokens) {
            const newTokens = prompt(`Edit tokens for ${userName}:`, currentTokens);
            if (newTokens !== null && !isNaN(newTokens)) {
                updateUserTokens(userId, parseInt(newTokens));
            }
        }

        // 更新用户tokens
        async function updateUserTokens(userId, tokens) {
            try {
                const response = await fetch(buildUrlWithToken('/dashboard/users/management/'), {
                    method: 'POST',
                    credentials: 'include',  // 确保发送cookies
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'update_tokens',
                        user_id: userId,
                        tokens: tokens
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                alert(data.message);
                loadUserList();

            } catch (error) {
                console.error('更新tokens失败:', error);
                alert('更新tokens失败: ' + error.message);
            }
        }

        // 切换用户状态
        async function toggleUserStatus(userId, userName, currentStatus) {
            const action = currentStatus ? 'disable' : 'enable';
            if (confirm(`Are you sure you want to ${action} user ${userName}?`)) {
                try {
                    const response = await fetch(buildUrlWithToken('/dashboard/users/management/'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'update_status',
                            user_id: userId,
                            is_active: !currentStatus
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('Server response:', errorText);
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    alert(data.message);
                    loadUserList();

                } catch (error) {
                    console.error('更新用户状态失败:', error);
                    alert('更新用户状态失败: ' + error.message);
                }
            }
        }

        // 删除用户
        async function deleteUser(userId, userName, userEmail) {
            const confirmMessage = `⚠️ 危险操作确认 ⚠️\n\n您即将删除用户：\n姓名：${userName}\n邮箱：${userEmail}\n\n此操作将：\n• 永久删除用户账户\n• 删除所有聊天记录和消息\n• 删除所有相关数据\n• 此操作不可撤销！\n\n请输入用户名 "${userName}" 来确认删除：`;

            const confirmation = prompt(confirmMessage);

            if (confirmation === userName) {
                try {
                    const response = await fetch(buildUrlWithToken('/dashboard/users/management/'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'delete_user',
                            user_id: userId,
                            confirm: true
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('Server response:', errorText);
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    alert(`✅ ${data.message}`);
                    loadUserList();

                } catch (error) {
                    console.error('删除用户失败:', error);
                    alert('删除用户失败: ' + error.message);
                }
            } else if (confirmation !== null) {
                alert('用户名不匹配，删除操作已取消。');
            }
        }

        // 消息过滤和导出相关函数

        // 显示过滤弹窗
        function showFilterModal() {
            document.getElementById('filter-modal').classList.remove('hidden');
        }

        // 关闭过滤弹窗
        function closeFilterModal() {
            document.getElementById('filter-modal').classList.add('hidden');
        }

        // 清除过滤条件
        function clearFilter() {
            document.getElementById('filter-form').reset();
            document.getElementById('filter-session-id').value = '';  // 明确清除session_id字段
            loadMessageHistory();
            closeFilterModal();
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function () {
                // 简单的提示效果
                const tooltip = document.createElement('div');
                tooltip.textContent = 'Copied!';
                tooltip.className = 'fixed top-4 right-4 bg-green-600 text-white px-3 py-2 rounded shadow-lg z-50';
                document.body.appendChild(tooltip);

                setTimeout(() => {
                    document.body.removeChild(tooltip);
                }, 2000);
            }).catch(function (err) {
                console.error('复制失败: ', err);
                // 降级方案：选择文本
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Session ID已复制到剪贴板');
            });
        }

        // 按Session ID过滤
        function filterBySessionId(sessionId) {
            // 设置session_id过滤条件
            document.getElementById('filter-session-id').value = sessionId;

            // 自动提交过滤表单
            const params = new URLSearchParams();
            params.append('session_id', sessionId);
            params.append('limit', '100');

            fetch(buildUrlWithToken(`/dashboard/messages/filter/?${params}`))
                .then(response => response.json())
                .then(data => {
                    displayFilteredMessages(data.messages);

                    // 显示提示信息
                    const tooltip = document.createElement('div');
                    tooltip.textContent = `Showing ${data.messages.length} messages from session ${sessionId.substring(0, 8)}...`;
                    tooltip.className = 'fixed top-4 right-4 bg-blue-600 text-white px-3 py-2 rounded shadow-lg z-50';
                    document.body.appendChild(tooltip);

                    setTimeout(() => {
                        document.body.removeChild(tooltip);
                    }, 3000);
                })
                .catch(error => {
                    console.error('按Session ID过滤失败:', error);
                    alert('按Session ID过滤失败: ' + error.message);
                });
        }

        // 过滤表单提交
        document.getElementById('filter-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            const keyword = document.getElementById('filter-keyword').value;
            const startDate = document.getElementById('filter-start-date').value;
            const endDate = document.getElementById('filter-end-date').value;
            const messageType = document.getElementById('filter-message-type').value;
            const userEmail = document.getElementById('filter-user-email').value;
            const sessionId = document.getElementById('filter-session-id').value;
            const hasMedia = document.getElementById('filter-has-media').value;

            const params = new URLSearchParams();
            if (keyword) params.append('keyword', keyword);
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            if (messageType) params.append('type', messageType);
            if (userEmail) params.append('user_email', userEmail);
            if (sessionId) params.append('session_id', sessionId);
            if (hasMedia) params.append('has_media', hasMedia);
            params.append('limit', '100');

            try {
                const response = await fetch(buildUrlWithToken(`/dashboard/messages/filter/?${params}`));
                const data = await response.json();

                displayFilteredMessages(data.messages);
                closeFilterModal();

            } catch (error) {
                console.error('过滤消息失败:', error);
                alert('过滤消息失败: ' + error.message);
            }
        });

        // 显示过滤后的消息
        function displayFilteredMessages(messages) {
            const container = document.getElementById('message-history-list');
            container.innerHTML = '';

            messages.forEach(message => {
                const item = document.createElement('div');
                item.className = 'message-item p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:border-violet-500/50 transition-all duration-200';

                const typeColor = message.type === 'user' ? 'text-blue-400' :
                    message.type === 'assistant' ? 'text-green-400' : 'text-orange-400';

                const mediaIcon = message.has_media ?
                    (message.media_type === 'image' ? 'image' : 'video') : '';

                item.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <input type="checkbox" class="message-checkbox mt-2" data-message-id="${message.id}">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center">
                                <span class="text-xs font-medium text-white">
                                    ${message.user.name ? message.user.name.charAt(0).toUpperCase() : 'A'}
                                </span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0 message-content cursor-pointer">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-white">${message.user.name}</span>
                                    <span class="text-xs ${typeColor} font-medium">${message.type}</span>
                                    ${message.has_media ? `<i data-lucide="${mediaIcon}" class="w-4 h-4 text-violet-400"></i>` : ''}
                                </div>
                                <span class="text-xs text-slate-500">
                                    ${new Date(message.created_at).toLocaleString('zh-CN')}
                                </span>
                            </div>
                            <p class="text-sm text-slate-300 mt-1">${message.content}</p>
                            <div class="text-xs text-slate-500 mt-1 space-y-1">
                                <div>Session: ${message.session.title}</div>
                                <div class="flex items-center space-x-2">
                                    <span>Session ID:</span>
                                    <span class="font-mono text-xs bg-slate-700 px-2 py-1 rounded cursor-pointer hover:bg-slate-600" 
                                          onclick="copyToClipboard('${message.session_id}')" 
                                          title="Click to copy session ID">
                                        ${message.session_id.substring(0, 8)}...
                                    </span>
                                    <button onclick="filterBySessionId('${message.session_id}')" 
                                            class="text-violet-400 hover:text-violet-300 text-xs underline"
                                            title="View all messages in this session">
                                        View Session
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 阻止整个item的点击事件冒泡，改为只对特定区域添加点击事件
                const messageContent = item.querySelector('.message-content');
                if (messageContent) {
                    messageContent.addEventListener('click', () => {
                        showMessageDetails(message);
                    });
                }

                container.appendChild(item);
            });

            lucide.createIcons();
            updateSelectedCount(); // 更新选择计数
        }

        // 导出选中的消息
        async function exportSelectedMessages() {
            const checkboxes = document.querySelectorAll('.message-checkbox:checked');
            const messageIds = Array.from(checkboxes).map(cb => cb.dataset.messageId);

            if (messageIds.length === 0) {
                alert('请先选择要导出的消息');
                return;
            }

            try {
                const response = await fetch(buildUrlWithToken('/dashboard/messages/export/'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        message_ids: messageIds
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 创建下载链接
                const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = data.filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                alert(`成功导出 ${data.total_count} 条消息`);

            } catch (error) {
                console.error('导出消息失败:', error);
                alert('导出消息失败: ' + error.message);
            }
        }

        // 内测申请管理相关函数
        let currentBetaApplication = null;

        // 加载内测申请列表
        async function loadBetaApplications() {
            try {
                const statusFilter = document.getElementById('beta-status-filter').value;
                const sourceFilter = document.getElementById('beta-source-filter').value;
                const search = document.getElementById('beta-search').value;

                const params = new URLSearchParams();
                if (statusFilter) params.append('status', statusFilter);
                if (sourceFilter) params.append('source', sourceFilter);
                if (search) params.append('search', search);

                const response = await fetch(buildUrlWithToken(`/dashboard/beta-applications/?${params}`));
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                displayBetaApplications(data.applications);
                displayBetaStats(data.stats);

            } catch (error) {
                console.error('加载内测申请失败:', error);
                alert('加载内测申请失败: ' + error.message);
            }
        }

        // 搜索内测申请
        function searchBetaApplications() {
            loadBetaApplications();
        }

        // 显示内测申请列表
        function displayBetaApplications(applications) {
            const container = document.getElementById('beta-applications-list');
            container.innerHTML = '';

            applications.forEach(app => {
                const item = document.createElement('div');
                item.className = 'p-4 bg-slate-800/30 rounded-lg border border-slate-700 hover:border-violet-500/50 transition-all duration-200 cursor-pointer';

                const statusColor = {
                    'PENDING': 'text-yellow-400',
                    'APPROVED': 'text-green-400',
                    'REJECTED': 'text-red-400',
                    'INVITED': 'text-blue-400'
                }[app.status] || 'text-gray-400';

                item.innerHTML = `
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <span class="text-white font-medium">${app.email}</span>
                                <span class="text-xs ${statusColor} font-medium px-2 py-1 bg-slate-700 rounded">
                                    ${app.status_display}
                                </span>
                                ${app.name ? `<span class="text-slate-400 text-sm">${app.name}</span>` : ''}
                            </div>
                            <div class="text-sm text-slate-300 mb-2">
                                <strong>Purpose:</strong> ${app.purpose.substring(0, 100)}${app.purpose.length > 100 ? '...' : ''}
                            </div>
                            <div class="flex items-center space-x-4 text-xs text-slate-500">
                                <span>Source: ${app.source_display}</span>
                                <span>Applied: ${new Date(app.created_at).toLocaleDateString()}</span>
                                ${app.reviewed_at ? `<span>Reviewed: ${new Date(app.reviewed_at).toLocaleDateString()}</span>` : ''}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            ${app.status === 'PENDING' ? `
                                <button onclick="quickApprove('${app.id}')" 
                                    class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs transition-colors">
                                    Approve
                                </button>
                                <button onclick="quickReject('${app.id}')" 
                                    class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs transition-colors">
                                    Reject
                                </button>
                            ` : ''}
                            ${app.status === 'APPROVED' ? `
                                <button onclick="quickSendInvitation('${app.id}')" 
                                    class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors">
                                    Send Invite
                                </button>
                            ` : ''}
                            <button onclick="deleteInvitation('${app.id}', '${app.email}')" 
                                class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs transition-colors"
                                title="Delete invitation and user account (if created)">
                                Delete
                            </button>
                        </div>
                    </div>
                `;

                item.addEventListener('click', (e) => {
                    // 如果点击的是按钮，不触发详情查看
                    if (e.target.tagName === 'BUTTON') return;
                    showBetaApplicationDetails(app);
                });

                container.appendChild(item);
            });
        }

        // 显示内测申请统计
        function displayBetaStats(stats) {
            const container = document.getElementById('beta-stats');
            container.innerHTML = '';

            const statItems = [
                { label: 'Total', value: stats.total, color: 'text-blue-400' },
                { label: 'Pending', value: stats.pending, color: 'text-yellow-400' },
                { label: 'Approved', value: stats.approved, color: 'text-green-400' },
                { label: 'Rejected', value: stats.rejected, color: 'text-red-400' },
                { label: 'Invited', value: stats.invited, color: 'text-purple-400' }
            ];

            statItems.forEach(stat => {
                const item = document.createElement('div');
                item.className = 'bg-slate-800/50 rounded-lg p-3 text-center';
                item.innerHTML = `
                    <div class="${stat.color} text-2xl font-bold">${stat.value}</div>
                    <div class="text-slate-400 text-sm">${stat.label}</div>
                `;
                container.appendChild(item);
            });
        }

        // 显示内测申请详情
        function showBetaApplicationDetails(application) {
            currentBetaApplication = application;
            const modal = document.getElementById('beta-application-modal');
            const content = document.getElementById('beta-application-modal-content');

            content.innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Email</label>
                            <div class="text-white">${application.email}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Status</label>
                            <span class="px-2 py-1 bg-slate-700 rounded text-sm ${getStatusColor(application.status)}">
                                ${application.status_display}
                            </span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Name</label>
                            <div class="text-white">${application.name || 'Not provided'}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Company</label>
                            <div class="text-white">${application.company || 'Not provided'}</div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Purpose</label>
                        <div class="text-white bg-slate-800/50 rounded p-3">${application.purpose}</div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Source</label>
                            <div class="text-white">${application.source_display}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Source Detail</label>
                            <div class="text-white">${application.source_detail || 'Not provided'}</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Applied At</label>
                            <div class="text-white">${new Date(application.created_at).toLocaleString()}</div>
                        </div>
                        ${application.reviewed_at ? `
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Reviewed At</label>
                            <div class="text-white">${new Date(application.reviewed_at).toLocaleString()}</div>
                        </div>
                        ` : ''}
                    </div>

                    ${application.reviewed_by ? `
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Reviewed By</label>
                        <div class="text-white">${application.reviewed_by}</div>
                    </div>
                    ` : ''}

                    ${application.status === 'INVITED' ? `
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Invitation Status</label>
                        <div class="text-green-400 bg-slate-800/50 rounded p-2">User account created and login credentials sent</div>
                    </div>
                    ` : ''}

                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Admin Notes</label>
                        <div class="text-white bg-slate-800/50 rounded p-3 min-h-[60px]">
                            ${application.admin_notes || 'No notes'}
                        </div>
                        <button onclick="editAdminNotes()" 
                            class="mt-2 px-3 py-1 bg-violet-600 hover:bg-violet-700 text-white rounded text-sm transition-colors">
                            Edit Notes
                        </button>
                    </div>
                </div>
            `;

            modal.classList.remove('hidden');
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'PENDING': 'text-yellow-400',
                'APPROVED': 'text-green-400',
                'REJECTED': 'text-red-400',
                'INVITED': 'text-blue-400'
            };
            return colors[status] || 'text-gray-400';
        }

        // 关闭内测申请详情弹窗
        function closeBetaApplicationModal() {
            document.getElementById('beta-application-modal').classList.add('hidden');
            currentBetaApplication = null;
        }

        // 快速批准
        async function quickApprove(applicationId) {
            await performBetaAction('approve', applicationId);
        }

        // 快速拒绝
        async function quickReject(applicationId) {
            await performBetaAction('reject', applicationId);
        }

        // 快速发送邀请
        async function quickSendInvitation(applicationId) {
            await performBetaAction('send_invitation', applicationId);
        }

        // 删除邀请
        async function deleteInvitation(applicationId, email) {
            if (!confirm(`Are you sure you want to delete the invitation for ${email}?\n\nThis will also delete the user account if it was already created.`)) {
                return;
            }
            await performBetaAction('delete_invitation', applicationId);
        }

        // 从详情弹窗删除申请
        async function deleteBetaApplication() {
            if (!currentBetaApplication) return;

            if (!confirm(`Are you sure you want to delete the invitation for ${currentBetaApplication.email}?\n\nThis will also delete the user account if it was already created.`)) {
                return;
            }

            await performBetaAction('delete_invitation', currentBetaApplication.id);
        }

        // 批准申请
        async function approveBetaApplication() {
            if (!currentBetaApplication) return;
            await performBetaAction('approve', currentBetaApplication.id);
        }

        // 拒绝申请
        async function rejectBetaApplication() {
            if (!currentBetaApplication) return;
            await performBetaAction('reject', currentBetaApplication.id);
        }

        // 发送邀请
        async function sendInvitation() {
            if (!currentBetaApplication) return;
            await performBetaAction('send_invitation', currentBetaApplication.id);
        }

        // 执行内测申请操作
        async function performBetaAction(action, applicationId, adminNotes = '') {
            try {
                const response = await fetch(buildUrlWithToken('/dashboard/beta-applications/'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action,
                        application_id: applicationId,
                        admin_notes: adminNotes
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success) {
                    // 删除操作只显示简单的成功消息
                    if (action === 'delete_invitation') {
                        alert(`✅ ${data.message}`);
                    }
                    // 其他操作（approve, reject, update_notes）只显示消息
                    else if (action === 'approve' || action === 'reject' || action === 'update_notes') {
                        alert(`✅ ${data.message}`);
                    }
                    // 发送邀请操作 - 不显示敏感信息
                    else if (action === 'send_invitation') {
                        if (data.email_sent) {
                            alert(`✅ ${data.message}\n\n邀请邮件已成功发送给用户。`);
                        } else {
                            alert(`⚠️ ${data.message}\n\n邮件发送失败，请联系用户并告知账户已创建。\n\n邮件错误: ${data.email_error || '未知错误'}`);
                        }
                    }
                } else {
                    alert(`❌ 操作失败: ${data.message || data.error}`);
                }

                loadBetaApplications();
                closeBetaApplicationModal();

            } catch (error) {
                console.error('操作失败:', error);
                alert('操作失败: ' + error.message);
            }
        }

        // 编辑管理员备注
        function editAdminNotes() {
            const modal = document.getElementById('admin-notes-modal');
            const textarea = document.getElementById('admin-notes-text');

            if (currentBetaApplication) {
                textarea.value = currentBetaApplication.admin_notes || '';
            }

            modal.classList.remove('hidden');
        }

        // 关闭管理员备注弹窗
        function closeAdminNotesModal() {
            document.getElementById('admin-notes-modal').classList.add('hidden');
        }

        // 保存管理员备注
        document.getElementById('admin-notes-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            if (!currentBetaApplication) return;

            const adminNotes = document.getElementById('admin-notes-text').value;

            try {
                const response = await fetch(buildUrlWithToken('/dashboard/beta-applications/'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'update_notes',
                        application_id: currentBetaApplication.id,
                        admin_notes: adminNotes
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                alert(data.message);

                // 更新当前申请的备注
                currentBetaApplication.admin_notes = adminNotes;

                // 重新显示详情
                showBetaApplicationDetails(currentBetaApplication);
                closeAdminNotesModal();

            } catch (error) {
                console.error('保存备注失败:', error);
                alert('保存备注失败: ' + error.message);
            }
        });

        // 在页面加载时也加载内测申请
        document.addEventListener('DOMContentLoaded', function () {
            // ... 现有的初始化代码 ...
            loadBetaApplications();
        });

        // 显示直接邀请弹窗
        function showDirectInvitationModal() {
            document.getElementById('direct-invitation-modal').classList.remove('hidden');
            document.getElementById('direct-invitation-email').value = '';
            document.getElementById('direct-invitation-email').focus();
        }

        // 关闭直接邀请弹窗
        function closeDirectInvitationModal() {
            document.getElementById('direct-invitation-modal').classList.add('hidden');
        }

        // 直接邀请表单提交
        document.getElementById('direct-invitation-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            const email = document.getElementById('direct-invitation-email').value.trim();

            if (!email) {
                alert('Please enter an email address');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address');
                return;
            }

            try {
                showLoading();

                const response = await fetch(buildUrlWithToken('/dashboard/send-invitation/'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                alert(`✅ 邀请发送成功！\n\n账户已创建并分配2300试用代币。\n登录凭据已通过邮件发送给用户。`);

                closeDirectInvitationModal();
                loadBetaApplications(); // 刷新列表显示新的邀请记录

            } catch (error) {
                console.error('Send invitation failed:', error);
                alert('Failed to send invitation: ' + error.message);
            } finally {
                hideLoading();
            }
        });


    </script>
</body>

</html>