<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MirageMakers AI - Django Backend Service</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0d1121 0%, #1a1f3a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00d9ff, #7b5dfa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #a0a9c0;
            margin-bottom: 20px;
        }

        .status {
            display: inline-block;
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid rgba(34, 197, 94, 0.3);
            font-weight: 500;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            border-color: rgba(123, 93, 250, 0.3);
            box-shadow: 0 12px 32px rgba(123, 93, 250, 0.1);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #00d9ff;
        }

        .card-content {
            color: #a0a9c0;
            line-height: 1.6;
        }

        .system-info {
            background: rgba(123, 93, 250, 0.1);
            border: 1px solid rgba(123, 93, 250, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .system-info dt {
            font-weight: 600;
            color: #7b5dfa;
            margin-top: 8px;
        }

        .system-info dd {
            margin-left: 16px;
            color: #a0a9c0;
            font-family: 'Courier New', monospace;
        }

        /* AI function test styles */
        .ai-test-form {
            background: rgba(0, 217, 255, 0.05);
            border: 1px solid rgba(0, 217, 255, 0.2);
            border-radius: 8px;
            padding: 20px;
            margin-top: 16px;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-group label {
            display: block;
            margin-bottom: 6px;
            color: #00d9ff;
            font-weight: 500;
        }

        .input-group input,
        .input-group select {
            width: 100%;
            padding: 10px 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #ffffff;
            font-size: 14px;
        }

        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: #00d9ff;
            box-shadow: 0 0 0 2px rgba(0, 217, 255, 0.2);
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
        }

        .file-input {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            display: inline-block;
            padding: 8px 16px;
            background: rgba(123, 93, 250, 0.1);
            border: 1px solid rgba(123, 93, 250, 0.3);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #7b5dfa;
            font-size: 14px;
        }

        .file-input-label:hover {
            background: rgba(123, 93, 250, 0.2);
            border-color: rgba(123, 93, 250, 0.5);
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #00d9ff, #7b5dfa);
            border: none;
            border-radius: 6px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 217, 255, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-area {
            margin-top: 20px;
            padding: 16px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            min-height: 120px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .loading {
            text-align: center;
            color: #00d9ff;
            font-style: italic;
        }

        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .success {
            color: #51cf66;
        }

        .result-media {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 10px;
        }

        .quick-links {
            text-align: center;
            margin-top: 40px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, #00d9ff, #7b5dfa);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .footer {
            text-align: center;
            margin-top: 60px;
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1 class="logo">MirageMakers AI</h1>
            <p class="subtitle">Django Backend Service Debug Panel</p>
            <span class="status">🟢 Service Running</span>
        </div>

        <div class="grid">
            <div class="card">
                <h2 class="card-title">🔧 System Information</h2>
                <div class="card-content">
                    <div class="system-info">
                        <dl>
                            <dt>Django Version:</dt>
                            <dd>{{ system_info.django_version }}</dd>

                            <dt>Python Version:</dt>
                            <dd>{{ system_info.python_version }}</dd>

                            <dt>Container Service:</dt>
                            <dd>{{ system_info.containers }}</dd>

                            <dt>Debug Mode:</dt>
                            <dd>{% if debug %}Enabled{% else %}Disabled{% endif %}</dd>

                            <dt>Allowed Hosts:</dt>
                            <dd>{{ allowed_hosts|join:", " }}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2 class="card-title">🤖 AI Function Test</h2>
                <div class="card-content">
                    <p>Here you can directly test AI chat and generation functions, supporting text dialogue, image
                        generation and video processing.</p>

                    <form id="ai-test-form" class="ai-test-form">
                        <div class="input-group">
                            <label for="prompt">Input Content:</label>
                            <input type="text" id="prompt" name="prompt"
                                placeholder="Please enter text content or description..." required>
                        </div>

                        <div class="input-group">
                            <label for="mode">Generation Mode:</label>
                            <select id="mode" name="mode">
                                <option value="chat">Smart Chat</option>
                                <option value="text_to_image">Text to Image</option>
                                <option value="text_to_video">Text to Video</option>
                                <option value="image_to_image">Image Transform</option>
                                <option value="image_to_video">Image to Video</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label>Media File (Optional):</label>
                            <div class="file-input-wrapper">
                                <input type="file" id="media" name="media" accept="image/*,video/*" class="file-input">
                                <label for="media" class="file-input-label">Choose File</label>
                                <span id="file-name" style="margin-left: 10px; color: #a0a9c0; font-size: 12px;"></span>
                            </div>
                        </div>

                        <button type="submit" class="submit-btn" id="submit-btn">
                            🚀 Start Test
                        </button>
                    </form>

                    <div id="result-area" class="result-area" style="display: none;">
                        <div id="result-content"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="quick-links">
            <a href="{{ frontend_url }}" class="btn btn-secondary" target="_blank">
                🌟 Frontend App
            </a>
            <a href="{{ api_urls.health }}" class="btn btn-secondary" target="_blank">
                ❤️ Health Check
            </a>
        </div>

        <div class="footer">
            <p>MirageMakers AI &copy; 2025 - Django Backend Service</p>
            <p>For technical issues, please contact the development team</p>
        </div>
    </div>

    <script>
        // Simple status check
        fetch('/health/')
            .then(response => response.json())
            .then(data => {
                console.log('Health check:', data);
            })
            .catch(error => {
                console.error('Health check failed:', error);
                document.querySelector('.status').innerHTML = '🔴 Service Error';
                document.querySelector('.status').style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                document.querySelector('.status').style.color = '#ef4444';
                document.querySelector('.status').style.borderColor = 'rgba(239, 68, 68, 0.3)';
            });

        // File selection handling
        document.getElementById('media').addEventListener('change', function (e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : '';
            document.getElementById('file-name').textContent = fileName;
        });

        // AI test form handling
        document.getElementById('ai-test-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData();
            const prompt = document.getElementById('prompt').value;
            const mode = document.getElementById('mode').value;
            const mediaFile = document.getElementById('media').files[0];

            if (!prompt.trim() && !mediaFile) {
                alert('Please enter text content or select a file');
                return;
            }

            formData.append('prompt', prompt);
            formData.append('mode', mode);
            formData.append('session_id', 'debug-test-' + Date.now());

            if (mediaFile) {
                formData.append('media', mediaFile);
            }

            // Show result area
            const resultArea = document.getElementById('result-area');
            const resultContent = document.getElementById('result-content');
            const submitBtn = document.getElementById('submit-btn');

            resultArea.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">🤖 AI is processing, please wait...</div>';
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    let content = '<div class="success">✅ Processing completed!</div>';

                    if (result.chat_response) {
                        content += `<p><strong>AI Reply:</strong> ${result.chat_response}</p>`;
                    }

                    if (result.image_url) {
                        content += `<p><strong>Generated Image:</strong></p>`;
                        content += `<img src="${result.image_url}" class="result-media" alt="Generated Image">`;
                    }

                    if (result.video_url) {
                        content += `<p><strong>Generated Video:</strong></p>`;
                        content += `<video src="${result.video_url}" class="result-media" controls>Your browser does not support video playback</video>`;
                    }

                    if (result.session_id) {
                        content += `<p style="font-size: 12px; color: #a0a9c0; margin-top: 10px;">Session ID: ${result.session_id}</p>`;
                    }

                    resultContent.innerHTML = content;
                } else {
                    throw new Error(result.error || 'Processing failed');
                }

            } catch (error) {
                console.error('AI test failed:', error);
                resultContent.innerHTML = `<div class="error">❌ Test failed: ${error.message}</div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 Start Test';
            }
        });
    </script>
</body>

</html>