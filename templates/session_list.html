<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Session List</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- TailwindCSS CDN -->
  {% load static %}
  <script src="{% static 'js/tailwindcss.min.js' %}"></script>
</head>

<body class="bg-gray-50 p-4">

  <div class="max-w-2xl mx-auto bg-white p-6 rounded shadow">
    <h2 class="text-2xl mb-4">Session List</h2>

    <!-- Back to home button -->
    <a href="{% url 'index' %}" class="text-blue-600 hover:underline mb-4 inline-block">
      ← Back to Home
    </a>

    {% if sessions %}
    <ul class="space-y-2">
      {% for session in sessions %}
      <li class="border p-4 rounded hover:bg-gray-100">
        <a href="{% url 'session_detail' session.id %}" class="font-medium text-lg text-gray-800 hover:underline">
          {{ session.title }}
        </a>
        <div class="text-sm text-gray-500">
          Created at: {{ session.created_at }}
        </div>
      </li>
      {% endfor %}
    </ul>
    {% else %}
    <p class="text-gray-600">
      No sessions yet,
      <a href="{% url 'index' %}" class="text-blue-600 hover:underline">start now</a>
    </p>
    {% endif %}

  </div>

</body>

</html>