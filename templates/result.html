{# templates/result.html #}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Generation Result</title>
</head>

<body>
  {# Chat mode response #}
  {% if chat_response %}
  <input type="hidden" id="result-media" value="">
  <div id="chat-response" class="p-2 bg-gray-100 rounded">
    {{ chat_response }}
  </div>

  {# Error message #}
  {% elif error %}
  <input type="hidden" id="result-media" value="">
  <span id="error-text" class="text-red-500">{{ error }}</span>

  {# Generation tool mode #}
  {% else %}
  {# Hidden field: frontend script reads final URL #}
  <input type="hidden" id="result-media" value="{{ video_url|default:image_url }}">

  {# Thinking process logs #}
  {% if logs %}
  <div id="thinking-logs">
    {% for line in logs %}
    <div class="italic text-gray-600 text-sm mb-1">{{ line }}</div>
    {% endfor %}
  </div>
  {% endif %}

  {# Media display #}
  {% if video_url %}
  <video controls class="max-w-full rounded">
    <source src="{{ video_url }}" type="video/mp4">
    Your browser does not support video playback.
  </video>
  {% elif image_url %}
  <img src="{{ image_url }}" alt="Generated Image" class="max-w-full rounded">
  {% else %}
  <p>No media links returned.</p>
  {% endif %}
  {% endif %}
</body>

</html>