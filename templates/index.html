{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Visual Generation Tool</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="{% static 'js/tailwindcss.min.js' %}"></script>
</head>

<body class="bg-gray-50 p-4">
  <div class="max-w-2xl mx-auto bg-white p-6 rounded shadow">
    <h2 class="text-2xl mb-4">Visual Generation Tool</h2>

    <div id="chat-box">
      {% for msg in history_messages %}
      {% if msg.role == 'user' %}
      <div class="text-right text-blue-600 my-2 p-2 rounded">
        {{ msg.content }}
      </div>
      {% else %}
      <div class="text-left text-gray-800 my-2 p-2 rounded">
        {{ msg.content }}
      </div>
      {% endif %}
      {% endfor %}
    </div>

    <form id="chat-form" method="post" enctype="multipart/form-data" class="flex space-x-2">
      {% csrf_token %}
      <input id="prompt-input" name="prompt" type="text" placeholder="Enter text" class="flex-1 border p-2 rounded" />
      <input id="media-input" name="media" type="file" accept="image/*,video/*" class="border p-2 rounded" />
      <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Send</button>
    </form>
  </div>

  <script>
    const chatBox = document.getElementById('chat-box');
    const form = document.getElementById('chat-form');
    const promptInput = document.getElementById('prompt-input');
    const mediaInput = document.getElementById('media-input');
    const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;

    function appendMessage(sender, html) {
      const msg = document.createElement('div');
      msg.className = sender === 'user'
        ? 'text-right text-blue-600 my-2'
        : 'text-left text-gray-800 my-2';
      msg.innerHTML = html;
      chatBox.appendChild(msg);
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    form.onsubmit = async function (e) {
      e.preventDefault();
      const prompt = promptInput.value.trim();
      if (!prompt && mediaInput.files.length === 0) return;

      // Display user message
      let userHtml = '';
      if (prompt) userHtml += `<div>${prompt}</div>`;
      if (mediaInput.files[0]) {
        const file = mediaInput.files[0];
        const url = URL.createObjectURL(file);
        userHtml += file.type.startsWith('video/')
          ? `<video src="${url}" controls class="max-w-xs mt-2"></video>`
          : `<img src="${url}" class="max-w-xs mt-2">`;
      }
      appendMessage('user', userHtml);

      appendMessage('bot', '🤖 Thinking...');

      const fd = new FormData(form);
      const res = await fetch('', {
        method: 'POST',
        headers: { 'X-CSRFToken': csrfToken },
        body: fd,
        credentials: 'same-origin'
      });

      const text = await res.text();
      const tmp = document.createElement('div');
      tmp.innerHTML = text;

      // Remove thinking prompt
      chatBox.lastChild.remove();

      // Check for errors
      const errorEl = tmp.querySelector('#error-text');
      const chatRespEl = tmp.querySelector('#chat-response');
      const resultEl = tmp.querySelector('#result-media');

      if (errorEl) {
        appendMessage('bot', `<span class="text-red-500">Error: ${errorEl.textContent}</span>`);
      } else if (chatRespEl) {
        appendMessage('bot', chatRespEl.innerHTML);
      } else if (resultEl) {
        // Thinking logs
        const logs = tmp.querySelectorAll('#thinking-logs div');
        logs.forEach(el => appendMessage('bot', `<div class="italic text-gray-500">${el.textContent}</div>`));

        const url = resultEl.value;
        // Media preview
        if (url.includes('.mp4')) {
          appendMessage('bot', `<video src="${url}" controls class="max-w-xs"></video>`);
        } else {
          appendMessage('bot', `<img src="${url}" class="max-w-xs">`);
        }
        // URL text
        appendMessage('bot', `<div class="text-xs text-gray-500 break-words mt-1">Resource URL:<br>${url}</div>`);
      } else {
        appendMessage('bot', 'Server returned unknown content');
      }

      // Clear input
      promptInput.value = '';
      mediaInput.value = '';
    };
  </script>
</body>

</html>