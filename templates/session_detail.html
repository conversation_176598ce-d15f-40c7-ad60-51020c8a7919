{# session_detail.html #}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Session Details</title>
  {% load static %}
  <script src="{% static 'js/tailwindcss.min.js' %}"></script>
</head>

<body class="bg-gray-50 p-4">
  <div class="max-w-2xl mx-auto bg-white p-6 rounded shadow">
    <h2 class="text-2xl mb-4">Session {{ session.id }}</h2>
    <div class="space-y-4">
      {% for msg in messages %}
      <div class="p-3 rounded {{ msg.role|yesno:'bg-blue-100,bg-gray-100' }}">
        <div class="text-sm text-gray-500 capitalize">{{ msg.role }}</div>
        <div class="mt-1">{{ msg.content }}</div>
        <div class="text-xs text-gray-400 mt-1">{{ msg.created_at }}</div>
      </div>
      {% empty %}
      <p class="text-gray-500">No messages in this session yet.</p>
      {% endfor %}
    </div>
    <a href="{% url 'session_list' %}" class="inline-block mt-6 text-blue-600 hover:underline">
      ← Back to Session List
    </a>
  </div>
</body>

</html>