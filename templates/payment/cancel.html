<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - MirageMakers AI</title>
    {% load static %}
    <script src="{% static 'js/tailwindcss.min.js' %}"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f0a1d 0%, #241b3a 100%);
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
        <!-- Cancel icon -->
        <div class="w-20 h-20 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z">
                </path>
            </svg>
        </div>

        <!-- Title -->
        <h1 class="text-2xl font-bold text-white mb-4">{{ title }}</h1>

        <!-- Message -->
        <p class="text-gray-300 mb-8">{{ message }}</p>

        <!-- Buttons -->
        <div class="space-y-4">
            <a href="/profile"
                class="block w-full py-3 px-6 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-700 transition-all font-medium">
                Choose Plan Again
            </a>
            <a href="/"
                class="block w-full py-3 px-6 border border-white/30 text-white rounded-lg hover:bg-white/10 transition-all">
                Back to Home
            </a>
        </div>
    </div>

    <!-- Auto redirect script -->
    <script>
        // Auto redirect to home page after 10 seconds
        setTimeout(function () {
            window.location.href = '/';
        }, 10000);
    </script>
</body>

</html>