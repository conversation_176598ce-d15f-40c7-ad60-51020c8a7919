#!/usr/bin/env python3
"""
将用户消息导出文件转换为eval格式的脚本

将messages_export JSON文件按session_id分组，转换为eval/run_agent.py可以处理的格式
"""

import os
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
from collections import defaultdict


def convert_message_format(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    将原始消息格式转换为eval格式
    
    原始格式:
    {
        "message_id": "...",
        "session_id": "...", 
        "user": {...},
        "message_type": "user|assistant",
        "content": "...",
        "user_prompt": "...",
        "image_url": "...",
        "video_url": "...",
        "has_media": bool,
        "created_at": "...",
        "updated_at": "..."
    }
    
    转换为eval格式:
    {
        "role": "user|assistant",
        "content": "...",
        "image_url": "...",
        "video_url": "...",
        "timestamp": "...",
        "message_id": "...",
        "media": [...]  # 如果有媒体
    }
    """
    converted = {
        "role": message["message_type"],
        "content": message["content"],
        "image_url": message.get("image_url", ""),
        "video_url": message.get("video_url", ""),
        "timestamp": message["created_at"],
        "message_id": message["message_id"]
    }
    
    # 处理媒体文件
    media = []
    
    # 如果有图片URL
    if message.get("image_url"):
        media.append({
            "type": "image",
            "url": message["image_url"],
            "source": "history" if message["message_type"] == "assistant" else "upload",
            "message_id": message["message_id"],
            "description": "Generated image" if message["message_type"] == "assistant" else "User uploaded image"
        })
    
    # 如果有视频URL
    if message.get("video_url"):
        media.append({
            "type": "video", 
            "url": message["video_url"],
            "source": "history" if message["message_type"] == "assistant" else "upload",
            "message_id": message["message_id"],
            "description": "Generated video" if message["message_type"] == "assistant" else "User uploaded video"
        })
    
    if media:
        converted["media"] = media
    
    return converted


def group_messages_by_session(messages: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """按session_id分组消息"""
    sessions = defaultdict(list)
    
    for message in messages:
        session_id = message["session_id"]
        converted_message = convert_message_format(message)
        sessions[session_id].append(converted_message)
    
    return dict(sessions)


def get_session_title(messages: List[Dict[str, Any]]) -> str:
    """获取session的标题，使用第一条用户消息的内容前50个字符"""
    for message in messages:
        if message["role"] == "user" and message["content"].strip():
            content = message["content"].strip()
            # 移除特殊字符，只保留前50个字符
            title = content[:50].replace("\n", " ").replace("\r", " ")
            if len(content) > 50:
                title += "..."
            return title
    return "New Chat"


def save_session_to_file(session_id: str, messages: List[Dict[str, Any]], output_dir: Path):
    """将单个session保存为JSON文件"""
    # 按时间戳排序消息
    messages.sort(key=lambda x: x["timestamp"])
    
    # 生成文件名
    session_title = get_session_title(messages)
    # 清理文件名中的特殊字符
    safe_title = "".join(c for c in session_title if c.isalnum() or c in (' ', '-', '_')).strip()
    safe_title = safe_title.replace(' ', '_')
    
    filename = f"{session_id}_{safe_title}.json"
    filepath = output_dir / filename
    
    # 保存JSON文件
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(messages, f, ensure_ascii=False, indent=4)
    
    return filepath


def create_summary_file(sessions: Dict[str, List[Dict[str, Any]]], output_dir: Path):
    """创建汇总文件"""
    summary = {
        "total_sessions": len(sessions),
        "created_at": datetime.now().isoformat(),
        "sessions": []
    }
    
    for session_id, messages in sessions.items():
        session_info = {
            "session_id": session_id,
            "title": get_session_title(messages),
            "message_count": len(messages),
            "user_messages": len([m for m in messages if m["role"] == "user"]),
            "assistant_messages": len([m for m in messages if m["role"] == "assistant"]),
            "has_media": any(m.get("media") for m in messages),
            "first_message_time": min(m["timestamp"] for m in messages),
            "last_message_time": max(m["timestamp"] for m in messages)
        }
        summary["sessions"].append(session_info)
    
    # 按消息数量排序
    summary["sessions"].sort(key=lambda x: x["message_count"], reverse=True)
    
    summary_file = output_dir / "sessions_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    return summary_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="将用户消息导出文件转换为eval格式",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--input-file",
        type=str,
        default="data/user_query/messages_export_20250713_085118.json",
        help="输入的消息导出JSON文件路径"
    )
    parser.add_argument(
        "--output-dir", 
        type=str,
        default="data/session_json/plan/real",
        help="输出目录路径"
    )
    parser.add_argument(
        "--min-messages",
        type=int,
        default=2,
        help="每个session的最小消息数量（过滤太短的对话）"
    )
    
    args = parser.parse_args()
    
    # 检查输入文件
    input_file = Path(args.input_file)
    if not input_file.exists():
        print(f"❌ 输入文件不存在: {input_file}")
        return
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📥 读取消息文件: {input_file}")
    
    # 读取输入文件
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        print(f"✅ 成功读取 {len(messages)} 条消息")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 按session分组
    print("🔄 按session_id分组消息...")
    sessions = group_messages_by_session(messages)
    print(f"✅ 找到 {len(sessions)} 个session")
    
    # 过滤短对话
    filtered_sessions = {
        session_id: msgs for session_id, msgs in sessions.items() 
        if len(msgs) >= args.min_messages
    }
    
    if len(filtered_sessions) < len(sessions):
        print(f"🔍 过滤掉 {len(sessions) - len(filtered_sessions)} 个短对话（少于{args.min_messages}条消息）")
    
    print(f"📊 处理 {len(filtered_sessions)} 个有效session")
    
    # 保存每个session
    saved_files = []
    for session_id, messages in filtered_sessions.items():
        try:
            filepath = save_session_to_file(session_id, messages, output_dir)
            saved_files.append(filepath)
            print(f"💾 保存session: {filepath.name} ({len(messages)} 条消息)")
        except Exception as e:
            print(f"❌ 保存session {session_id} 失败: {e}")
    
    # 创建汇总文件
    try:
        summary_file = create_summary_file(filtered_sessions, output_dir)
        print(f"📋 创建汇总文件: {summary_file}")
    except Exception as e:
        print(f"❌ 创建汇总文件失败: {e}")
    
    print(f"\n🎉 转换完成!")
    print(f"📁 输出目录: {output_dir}")
    print(f"📄 保存了 {len(saved_files)} 个session文件")
    print(f"📊 汇总文件: sessions_summary.json")
    
    # 显示统计信息
    total_messages = sum(len(msgs) for msgs in filtered_sessions.values())
    user_messages = sum(len([m for m in msgs if m["role"] == "user"]) for msgs in filtered_sessions.values())
    assistant_messages = sum(len([m for m in msgs if m["role"] == "assistant"]) for msgs in filtered_sessions.values())
    media_sessions = sum(1 for msgs in filtered_sessions.values() if any(m.get("media") for m in msgs))
    
    print(f"\n📈 统计信息:")
    print(f"  - 总消息数: {total_messages}")
    print(f"  - 用户消息: {user_messages}")
    print(f"  - 助手消息: {assistant_messages}")
    print(f"  - 包含媒体的session: {media_sessions}")


if __name__ == "__main__":
    main() 