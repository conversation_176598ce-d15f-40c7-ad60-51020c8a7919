#!/usr/bin/env python3
"""
并发Agent测试脚本

基于新版本Graph接口改进，支持媒体引用测试，与 tasks.py 中的调用方式保持一致。
可以设置并发数量，同时处理多个测试用例。
"""

import os
import sys
import json
import asyncio
import argparse
import dataclasses
from pathlib import Path
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any
import time

# 调整路径以从项目根目录导入模块
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from app.config.settings import settings
from app.core.graph import VisualGenerationGraph

class DataclassJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，用于处理dataclass和Enum对象"""
    def default(self, o):
        if dataclasses.is_dataclass(o):
            return dataclasses.asdict(o)
        if isinstance(o, Enum):
            return o.value
        return super().default(o)

def setup_arg_parser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="并发运行聊天会话测试并保存结果。",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--input-dir",
        type=str,
        default="data/session_json/plan",
        help="包含输入聊天会话JSON文件的目录。"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default=f"data/output/{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        help="用于保存测试结果的目录。"
    )
    parser.add_argument(
        "--file",
        type=str,
        default=None,
        help="指定要测试的单个JSON文件名（位于输入目录中）。"
    )
    parser.add_argument(
        "--skip-execute",
        action="store_true",
        help="如果设置，将跳过工具执行阶段，只进行到计划生成。"
    )
    parser.add_argument(
        "--skip-safety-check",
        action="store_true",
        help="如果设置，将跳过安全检查阶段，直接进入ChatAgent。"
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=3,
        help="并发执行的最大数量。"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=900,
        help="单个测试用例的超时时间（秒）。"
    )
    parser.add_argument(
        "--retry-failed",
        action="store_true",
        help="是否重试失败的测试用例。"
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=2,
        help="失败测试用例的最大重试次数。"
    )
    return parser.parse_args()

class TestResult:
    """测试结果数据类"""
    def __init__(self, file_name: str, api_version: str):
        self.file_name = file_name
        self.api_version = api_version
        self.start_time = None
        self.end_time = None
        self.duration = None
        self.success = False
        self.result = None
        self.error = None
        self.retry_count = 0

    def to_dict(self) -> Dict[str, Any]:
        return {
            "source_file": self.file_name,
            "tested_api_version": self.api_version,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.duration,
            "success": self.success,
            "retry_count": self.retry_count,
            "error": self.error,
            "result": self.result
        }

async def run_single_test(
    conversation: List[Dict], 
    api_version: str, 
    skip_execute: bool,
    skip_safety_check: bool = False,
    timeout: int = 900
) -> Dict[str, Any]:
    """
    运行单个测试用例（带超时控制）
    与 tasks.py 中的 execute_langchain_workflow 调用方式保持一致
    """
    # 每次测试都创建一个新的图实例以避免状态污染
    # 启用并发模式以优化并发环境下的性能
    graph = VisualGenerationGraph(concurrent_mode=True, skip_safety_check=skip_safety_check)
    
    # 默认跳过token检查（用于离线测试）
    graph.skip_token_check = True
    
    # 临时设置当前测试的API版本
    original_api_version = settings.api_version
    settings.api_version = api_version
    
    try:
        print(f"   🔍 [测试] 调用参数:")
        print(f"       - conversation: {len(conversation)} 条消息")
        print(f"       - skip_tool_execution: {skip_execute}")
        print(f"       - skip_safety_check: {skip_safety_check}")
        print(f"       - skip_token_check: True (默认)")
        
        # 检查conversation格式，输出媒体统计
        media_count = 0
        for msg in conversation:
            if msg.get("media"):
                media_count += len(msg["media"])
        if media_count > 0:
            print(f"       - 检测到 {media_count} 个媒体文件")
        
        # 使用与 tasks.py 一致的调用方式
        result_summary = await asyncio.wait_for(
            graph.arun_with_conversation(
                conversation=conversation,
                skip_tool_execution=skip_execute,
                skip_safety_check=skip_safety_check,
                user_data=None  # 离线测试模式不需要用户数据
            ),
            timeout=timeout
        )
        
        print(f"   ✅ [测试] Graph执行完成:")
        print(f"       - success: {result_summary.get('success', False)}")
        print(f"       - response_type: {result_summary.get('response_type', 'N/A')}")
        if result_summary.get('generated_results'):
            print(f"       - generated_results: {len(result_summary['generated_results'])} 个")
        if result_summary.get('final_response'):
            print(f"       - final_response: {len(result_summary['final_response'])} 字符")
        
        return result_summary
        
    finally:
        # 恢复原始设置
        settings.api_version = original_api_version

async def run_test_with_retry(
    test_case: Dict[str, Any],
    skip_execute: bool,
    skip_safety_check: bool,
    timeout: int,
    max_retries: int,
    retry_failed: bool
) -> TestResult:
    """运行测试用例，支持重试机制"""
    file_path = test_case["file_path"]
    conversation = test_case["conversation"]
    api_version = test_case["api_version"]
    
    result = TestResult(file_path.name, api_version)
    result.start_time = datetime.now()
    
    # 验证conversation格式
    if not isinstance(conversation, list):
        result.error = f"Invalid conversation format: expected list, got {type(conversation)}"
        result.end_time = datetime.now()
        result.duration = 0
        return result
    
    # 统计conversation信息
    media_stats = {"total_media": 0, "images": 0, "videos": 0, "has_new_format": False}
    for msg in conversation:
        if msg.get("media") and isinstance(msg["media"], list):
            media_stats["has_new_format"] = True
            media_stats["total_media"] += len(msg["media"])
            for media in msg["media"]:
                if media.get("type") == "image":
                    media_stats["images"] += 1
                elif media.get("type") == "video":
                    media_stats["videos"] += 1
    
    print(f"📝 [测试] {file_path.name}:")
    print(f"   📊 对话: {len(conversation)} 条消息")
    print(f"   🎬 媒体: {media_stats['total_media']} 个 (图片: {media_stats['images']}, 视频: {media_stats['videos']})")
    print(f"   📋 格式: {'新版本' if media_stats['has_new_format'] else '旧版本'}")
    print(f"   🔧 配置: skip_execute={skip_execute}")
    
    for attempt in range(max_retries + 1):
        try:
            print(f"🚀 [{datetime.now().strftime('%H:%M:%S')}] 开始测试: {file_path.name} (API: {api_version}) - 尝试 {attempt + 1}/{max_retries + 1}")
            
            test_result = await run_single_test(
                conversation=conversation,
                api_version=api_version,
                skip_execute=skip_execute,
                skip_safety_check=skip_safety_check,
                timeout=timeout
            )
            
            result.success = True
            result.result = test_result
            result.retry_count = attempt
            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] 测试成功: {file_path.name}")
            break
            
        except asyncio.TimeoutError:
            error_msg = f"测试超时 ({timeout}秒)"
            
            # 检查是否有部分成功的结果
            # 如果在超时前已经有一些成功的结果，我们可以考虑这是部分成功
            # 这里我们可以通过检查日志或其他方式来判断
            # 目前先标记为超时，但保留结果
            result.error = error_msg
            result.success = False  # 保持当前逻辑，但可以考虑改进
            print(f"⏰ [{datetime.now().strftime('%H:%M:%S')}] 测试超时: {file_path.name} - {error_msg}")
            print(f"   💡 提示: 如果看到工具执行成功的日志，说明功能正常，只是总结阶段超时")
        
        except Exception as e:
            error_msg = f"测试失败: {str(e)}"
            result.error = error_msg
            print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 测试失败: {file_path.name} - {error_msg}")
            import traceback
            print(f"   详细错误: {traceback.format_exc()}")
        
        # 如果不是最后一次尝试，且启用了重试
        if attempt < max_retries and retry_failed:
            wait_time = 2 ** attempt  # 指数退避
            print(f"🔄 [{datetime.now().strftime('%H:%M:%S')}] {wait_time}秒后重试: {file_path.name}")
            await asyncio.sleep(wait_time)
        elif not retry_failed:
            break
    
    result.end_time = datetime.now()
    result.duration = (result.end_time - result.start_time).total_seconds()
    
    return result

async def run_concurrent_tests(
    test_cases: List[Dict[str, Any]], 
    concurrency: int,
    skip_execute: bool,
    skip_safety_check: bool,
    timeout: int,
    max_retries: int,
    retry_failed: bool
) -> List[TestResult]:
    """并发运行多个测试用例"""
    semaphore = asyncio.Semaphore(concurrency)
    
    async def run_with_semaphore(test_case):
        async with semaphore:
            return await run_test_with_retry(
                test_case=test_case,
                skip_execute=skip_execute,
                skip_safety_check=skip_safety_check, # Pass skip_safety_check from test_case
                timeout=timeout,
                max_retries=max_retries,
                retry_failed=retry_failed
            )
    
    print(f"🚀 开始并发测试，并发数: {concurrency}, 总任务数: {len(test_cases)}")
    start_time = datetime.now()
    
    # 创建所有任务
    tasks = [run_with_semaphore(test_case) for test_case in test_cases]
    
    # 并发执行所有任务
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    end_time = datetime.now()
    total_duration = (end_time - start_time).total_seconds()
    
    print(f"🎉 所有测试完成！总耗时: {total_duration:.2f}秒")
    
    # 处理异常结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            error_result = TestResult(test_cases[i]["file_path"].name, test_cases[i]["api_version"])
            error_result.error = f"任务执行异常: {str(result)}"
            error_result.start_time = start_time
            error_result.end_time = end_time
            error_result.duration = total_duration
            processed_results.append(error_result)
        else:
            processed_results.append(result)
    
    return processed_results

def save_results(results: List[TestResult], output_path: Path) -> None:
    """保存测试结果到文件"""
    for result in results:
        # 单个结果文件
        output_filename = f"{Path(result.file_name).stem}_{result.api_version}.json"
        output_filepath = output_path / output_filename
        
        try:
            with open(output_filepath, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, ensure_ascii=False, indent=2, cls=DataclassJSONEncoder)
            print(f"💾 结果已保存: {output_filepath}")
        except Exception as e:
            print(f"❌ 保存失败 {output_filename}: {e}")
    
    # 汇总报告
    summary_filepath = output_path / "test_summary.json"
    summary_data = {
        "total_tests": len(results),
        "successful_tests": sum(1 for r in results if r.success),
        "failed_tests": sum(1 for r in results if not r.success),
        "total_duration": sum(r.duration or 0 for r in results),
        "average_duration": sum(r.duration or 0 for r in results) / len(results) if results else 0,
        "timestamp": datetime.now().isoformat(),
        "results": [r.to_dict() for r in results]
    }
    
    try:
        with open(summary_filepath, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2, cls=DataclassJSONEncoder)
        print(f"📊 汇总报告已保存: {summary_filepath}")
    except Exception as e:
        print(f"❌ 保存汇总报告失败: {e}")

def print_statistics(results: List[TestResult]) -> None:
    """打印测试统计信息"""
    total = len(results)
    successful = sum(1 for r in results if r.success)
    failed = total - successful
    
    total_duration = sum(r.duration or 0 for r in results)
    avg_duration = total_duration / total if total > 0 else 0
    
    print(f"\n📊 测试统计:")
    print(f"   总测试数: {total}")
    print(f"   成功: {successful} ({successful/total*100:.1f}%)")
    print(f"   失败: {failed} ({failed/total*100:.1f}%)")
    print(f"   总耗时: {total_duration:.2f}秒")
    print(f"   平均耗时: {avg_duration:.2f}秒")
    
    if failed > 0:
        print(f"\n❌ 失败的测试:")
        for result in results:
            if not result.success:
                print(f"   - {result.file_name}: {result.error}")

def validate_conversation_format(conversation: List[Dict]) -> Dict[str, Any]:
    """验证并分析conversation格式"""
    stats = {
        "total_messages": len(conversation),
        "user_messages": 0,
        "assistant_messages": 0,
        "total_media": 0,
        "images": 0,
        "videos": 0,
        "has_new_format": False,
        "has_legacy_format": False,
        "has_message_ids": False,
        "has_timestamps": False,
        "format_issues": []
    }
    
    for i, msg in enumerate(conversation):
        if not isinstance(msg, dict):
            stats["format_issues"].append(f"消息 {i}: 不是字典格式")
            continue
            
        role = msg.get("role")
        if role == "user":
            stats["user_messages"] += 1
        elif role == "assistant":
            stats["assistant_messages"] += 1
        
        # 检查新格式媒体字段
        if msg.get("media") and isinstance(msg["media"], list):
            stats["has_new_format"] = True
            stats["total_media"] += len(msg["media"])
            for media in msg["media"]:
                if media.get("type") == "image":
                    stats["images"] += 1
                elif media.get("type") == "video":
                    stats["videos"] += 1
        
        # 检查旧格式媒体字段
        if msg.get("image_url") or msg.get("video_url"):
            stats["has_legacy_format"] = True
            if msg.get("image_url"):
                stats["images"] += 1
                stats["total_media"] += 1
            if msg.get("video_url"):
                stats["videos"] += 1
                stats["total_media"] += 1
        
        # 检查其他字段
        if msg.get("message_id"):
            stats["has_message_ids"] = True
        if msg.get("timestamp"):
            stats["has_timestamps"] = True
    
    return stats

async def main():
    """主执行函数"""
    args = setup_arg_parser()

    input_path = Path(args.input_dir)
    output_path = Path(args.output_dir)

    print(f"🔧 并发Agent测试脚本 - 新版本兼容")
    print(f"▶️  输入目录: {input_path.resolve()}")
    print(f"▶️  输出目录: {output_path.resolve()}")
    print(f"⚡ 并发数: {args.concurrency}")
    print(f"⏱️  超时时间: {args.timeout}秒")
    print(f"🔧 跳过工具执行: {'是' if args.skip_execute else '否'}")
    print(f"🔧 跳过安全检查: {'是' if args.skip_safety_check else '否'}")
    print(f"🔄 重试失败: {'是' if args.retry_failed else '否'}")
    if args.retry_failed:
        print(f"🔢 最大重试次数: {args.max_retries}")

    # 创建输出目录
    try:
        output_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 输出目录创建成功")
    except Exception as e:
        print(f"❌ 无法创建输出目录: {e}")
        return

    # 收集测试文件
    if args.file:
        file_path = input_path / args.file
        if not file_path.is_file():
            print(f"❌ 指定的测试文件不存在: {file_path}")
            return
        test_files = [file_path]
        print(f"🎯 指定了单个测试文件: {args.file}")
    else:
        test_files = sorted(list(input_path.glob("*.json")))
        if not test_files:
            print(f"⚠️ 在目录 {input_path} 中未找到测试文件。")
            return
        print(f"📁 找到了 {len(test_files)} 个测试文件")

    # 准备测试用例并验证格式
    test_cases = []
    api_version = settings.api_version
    
    print(f"\n🔍 验证数据格式:")
    for file_path in test_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                conversation = json.load(f)
            
            # 验证conversation格式
            stats = validate_conversation_format(conversation)
            print(f"\n📄 {file_path.name}:")
            print(f"   💬 消息: {stats['total_messages']} 条 (用户: {stats['user_messages']}, 助手: {stats['assistant_messages']})")
            print(f"   🎬 媒体: {stats['total_media']} 个 (图片: {stats['images']}, 视频: {stats['videos']})")
            print(f"   📋 格式: {'新版本' if stats['has_new_format'] else ''}{'旧版本' if stats['has_legacy_format'] else ''}{'未知' if not stats['has_new_format'] and not stats['has_legacy_format'] else ''}")
            print(f"   🆔 字段: {'✓ message_id' if stats['has_message_ids'] else '✗ message_id'} {'✓ timestamp' if stats['has_timestamps'] else '✗ timestamp'}")
            
            if stats['format_issues']:
                print(f"   ⚠️ 格式问题: {stats['format_issues']}")
            
            test_cases.append({
                "file_path": file_path,
                "conversation": conversation,
                "api_version": api_version,
                "skip_safety_check": args.skip_safety_check, # Add skip_safety_check to test_case
                "stats": stats
            })
            
        except json.JSONDecodeError as e:
            print(f"⚠️ 跳过无效JSON文件 {file_path.name}: {e}")
        except Exception as e:
            print(f"⚠️ 跳过文件 {file_path.name}: {e}")

    if not test_cases:
        print(f"❌ 没有有效的测试用例")
        return

    print(f"\n🎯 准备执行 {len(test_cases)} 个测试用例")

    # 并发执行测试
    results = await run_concurrent_tests(
        test_cases=test_cases,
        concurrency=args.concurrency,
        skip_execute=args.skip_execute,
        skip_safety_check=args.skip_safety_check,
        timeout=args.timeout,
        max_retries=args.max_retries,
        retry_failed=args.retry_failed
    )

    # 保存结果
    save_results(results, output_path)
    
    # 打印统计信息
    print_statistics(results)

if __name__ == "__main__":
    asyncio.run(main())