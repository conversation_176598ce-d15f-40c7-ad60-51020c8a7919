.env
.env.example
.venv/
db.sqlite3
.DS_Store

media/generated/
.pytest_cache/
.idea/
agent_backend.egg-info/
node_modules/

# Next.js
.next/
frontend/.next/
out/
build/
frontend/out/
frontend/build/

# 其他缓存文件
__pycache__/
*.py[cod]
*$py.class
.cache/
.env
.env.example
docker/.env
docker/.env.backup
docker/env.example
logs/
frontend/package-lock.json

# Nginx
nginx/nginx.conf
docker/nginx/nginx.conf

# Backup files
*.backup.*
**/*.backup.*
docker/database/*
docker/logs/*
db_backup.json
