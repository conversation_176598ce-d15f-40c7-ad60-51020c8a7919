from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
     path('admin/', admin.site.urls),
     path('', include('core.urls')),
     
     # API endpoints
     path('api/', include('api.urls')),
     
     # Django Allauth URLs (社交登录)
     path('accounts/', include('allauth.urls')),
     
     # 社交登录回调
     path('api/auth/social-success/', include('core.social_auth_urls')),
 ]

# 开发环境下，静态和媒体都由 Django 提供
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
