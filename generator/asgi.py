# generator/asgi.py
"""
ASGI配置，支持HTTP和WebSocket
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')

# 初始化Django ASGI应用（必须在导入路由之前）
django_asgi_app = get_asgi_application()

# 注意：这个导入必须在get_asgi_application()之后，以避免应用启动时的导入循环
# 我们将在应用配置中动态导入路由
try:
    from core.routing import websocket_urlpatterns
    from core.websocket_middleware import get_websocket_auth_middleware_stack
except ImportError:
    # 如果路由文件还不存在，使用空列表和默认中间件
    websocket_urlpatterns = []
    def get_websocket_auth_middleware_stack():
        return AuthMiddlewareStack

# ASGI应用配置
application = ProtocolTypeRouter({
    # Django的HTTP处理（包括管理界面、API等）
    "http": django_asgi_app,
    
    # WebSocket处理 - 使用自定义Token认证中间件
    "websocket": AllowedHostsOriginValidator(
        get_websocket_auth_middleware_stack()(
            URLRouter(
                websocket_urlpatterns
            )
        )
    ),
}) 