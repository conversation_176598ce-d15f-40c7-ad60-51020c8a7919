import os
from pathlib import Path
from dotenv import load_dotenv
import socket

load_dotenv()  # 加载 .env

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-d^jjs$_5*(^-$2=53j^rai07@vv36#=^))9@%^8=v=$9p3*+br"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*', 'dev.MirageMakers.ai', 'localhost', '127.0.0.1']

# CSRF信任的源 - 动态配置
CSRF_TRUSTED_ORIGINS = [
    'http://127.0.0.1:3000',
    'http://127.0.0.1:8000', 
    'https://127.0.0.1:3000',
    'https://127.0.0.1:8000',
    'https://127.0.0.1',
    'http://127.0.0.1',
    'http://localhost:3000',
    'http://localhost:8000',
    'https://localhost:3000', 
    'https://localhost:8000',
    'https://localhost',
    'http://localhost',
    'https://miragemakers.ai',
    'http://miragemakers.ai',
    'https://www.miragemakers.ai',
    'http://www.miragemakers.ai',
]

# CORS配置
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "https://miragemakers.ai",
    "http://miragemakers.ai",
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'stripe-signature',
]

# URL Configuration
APPEND_SLASH = False

# Custom User Model
AUTH_USER_MODEL = 'core.User'

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",
    "corsheaders",
    "rest_framework",
    "rest_framework.authtoken",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    "allauth.socialaccount.providers.facebook",
    "allauth.socialaccount.providers.discord",
    "allauth.socialaccount.providers.twitter",
    # WebSocket 实时推送支持
    "channels",
    "core",
    "app",
]

MIDDLEWARE = [
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "core.CustomCSRFMiddleware",  # 使用自定义CSRF中间件
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    "core.security_middleware.SecurityMiddleware",  # 增强的安全中间件 - 支持CSP和Permissions Policy
]

ROOT_URLCONF = "generator.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [str(BASE_DIR / "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "generator.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# Docker环境检测
USE_DOCKER = os.getenv('USE_DOCKER', 'false').lower() == 'true'

if USE_DOCKER:
    # Docker环境使用MySQL
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.getenv('MYSQL_DATABASE', 'MirageMakers_db'),
            'USER': os.getenv('MYSQL_USER', 'MirageMakers'),
            'PASSWORD': os.getenv('MYSQL_PASSWORD', 'MirageMakers_password'),
            'HOST': os.getenv('MYSQL_HOST', 'mysql'),
            'PORT': os.getenv('MYSQL_PORT', '3306'),
            'OPTIONS': {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            },
        }
    }
    
    # Docker环境邮件配置 - 根据环境变量决定使用Gmail SMTP还是Postfix
    EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '')
    EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
    
    # 检查是否配置了真实的Gmail SMTP
    is_real_email_config = (
        EMAIL_HOST_USER and 
        EMAIL_HOST_PASSWORD and 
        '@' in EMAIL_HOST_USER and
        not EMAIL_HOST_USER.startswith('demo@') and
        'your-gmail' not in EMAIL_HOST_USER
    )
    
    if is_real_email_config:
        # 使用SMTP直接发送（支持Gmail和企业邮箱）
        EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
        EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
        EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
        
        # 根据端口自动设置SSL/TLS，优先级高于环境变量
        if EMAIL_PORT == 465:
            # 465端口必须使用SSL，不能使用TLS
            EMAIL_USE_SSL = True
            EMAIL_USE_TLS = False
        elif EMAIL_PORT == 587:
            # 587端口必须使用TLS，不能使用SSL
            EMAIL_USE_SSL = False
            EMAIL_USE_TLS = True
        else:
            # 其他端口使用环境变量配置，但确保不会同时设置两个
            env_tls = os.getenv('EMAIL_USE_TLS', 'True').lower() == 'true'
            env_ssl = os.getenv('EMAIL_USE_SSL', 'False').lower() == 'true'
            if env_ssl:
                EMAIL_USE_SSL = True
                EMAIL_USE_TLS = False
            else:
                EMAIL_USE_SSL = False
                EMAIL_USE_TLS = env_tls
        
        DEFAULT_FROM_EMAIL = f'MirageMakers AI <{EMAIL_HOST_USER}>'
        
        # 强制确保SSL/TLS设置正确（防止被环境变量覆盖）
        if EMAIL_PORT == 465:
            EMAIL_USE_SSL = True
            EMAIL_USE_TLS = False
        elif EMAIL_PORT == 587:
            EMAIL_USE_SSL = False
            EMAIL_USE_TLS = True
    else:
        # 回退到使用Postfix
        EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
        EMAIL_HOST = os.getenv('POSTFIX_HOST', 'postfix')
        EMAIL_PORT = 25  # 使用内部端口25，不需要认证
        EMAIL_USE_TLS = False  # Postfix容器内部通信不需要TLS
        EMAIL_USE_SSL = False  # 不使用SSL
        EMAIL_HOST_USER = ''  # 容器内部通信不需要认证
        EMAIL_HOST_PASSWORD = ''  # 容器内部通信不需要认证
        DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', 'MirageMakers AI <<EMAIL>>')
    
    SERVER_EMAIL = DEFAULT_FROM_EMAIL
    
else:
    # 本地环境使用MySQL数据库
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.getenv('DB_NAME', 'mvp'),  # 添加默认值
            'USER': os.getenv('DB_USER', 'root'),
            'PASSWORD': os.getenv('DB_PASSWORD', ''),  # 默认空密码
            'HOST': os.getenv('DB_HOST', '127.0.0.1'),
            'PORT': os.getenv('DB_PORT', '3306'),
            'OPTIONS': {
                'charset': 'utf8mb4',  # 确保字符集一致
                'sql_mode': 'STRICT_TRANS_TABLES',  # Django 推荐模式
            }
        }
    }

    # 本地环境邮件配置 - 支持msmtp后端
    EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '')
    EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')

    # 检查是否配置了msmtp
    USE_MSMTP = os.getenv('USE_MSMTP', 'True').lower() == 'true'
    MSMTP_PATH = os.getenv('MSMTP_PATH', '/opt/homebrew/bin/msmtp')
    MSMTP_ACCOUNT = os.getenv('MSMTP_ACCOUNT', 'default')

    if USE_MSMTP and os.path.exists(MSMTP_PATH):
        # 使用msmtp后端
        EMAIL_BACKEND = 'core.msmtp_backend.MsmtpEmailBackend'
        # msmtp的发件人邮箱从msmtprc配置中读取，这里设置为你的Gmail
        DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', 'MirageMakers AI <<EMAIL>>')
        
        # msmtp后端配置
        EMAIL_MSMTP_PATH = MSMTP_PATH
        EMAIL_MSMTP_ACCOUNT = MSMTP_ACCOUNT
    else:
        # 判断是否为真实的邮件配置（非演示模式）
        is_real_email_config = (
            EMAIL_HOST_USER and 
            EMAIL_HOST_PASSWORD and 
            '@' in EMAIL_HOST_USER and
            not EMAIL_HOST_USER.startswith('demo@') and
            'your-gmail' not in EMAIL_HOST_USER
        )

        if is_real_email_config:
            EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
        else:
            EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

        # SMTP 配置（仅在不使用msmtp时有效）
        EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
        EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
        EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'True').lower() == 'true'
        EMAIL_USE_SSL = os.getenv('EMAIL_USE_SSL', 'False').lower() == 'true'
        DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', 'MirageMakers AI <<EMAIL>>')

    # 如果没有配置发件人邮箱，使用默认值
    if not DEFAULT_FROM_EMAIL or 'demo@' in DEFAULT_FROM_EMAIL:
        DEFAULT_FROM_EMAIL = 'MirageMakers AI <<EMAIL>>'


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Singapore"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / 'staticfiles'

# 静态文件收集目录
STATICFILES_DIRS = [
    BASE_DIR / "static",
] if (BASE_DIR / "static").exists() else []

# 静态文件查找器
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# CORS settings
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000', 
    'http://localhost:8000',
    'http://127.0.0.1:8000',
    'https://localhost:3000',
    'https://127.0.0.1:8000',
    'https://localhost',
    'http://localhost',
]

# 动态添加当前主机的配置
try:
    # 获取当前主机IP
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    if local_ip != '127.0.0.1':
        CORS_ALLOWED_ORIGINS.extend([
            f'http://{local_ip}:3000',
            f'http://{local_ip}:8000', 
            f'https://{local_ip}:3000',
            f'https://{local_ip}:8000',
            f'https://{local_ip}',
            f'http://{local_ip}',
        ])
        ALLOWED_HOSTS.append(local_ip)
        # 同时添加到CSRF信任源
        CSRF_TRUSTED_ORIGINS.extend([
            f'http://{local_ip}:3000',
            f'http://{local_ip}:8000',
            f'https://{local_ip}:3000',
            f'https://{local_ip}:8000',
            f'https://{local_ip}',
            f'http://{local_ip}',
        ])
except:
    pass

# 从环境变量获取域名配置
PRIMARY_DOMAIN = os.getenv('PRIMARY_DOMAIN', '')
API_DOMAIN = os.getenv('API_DOMAIN', '')
FRONTEND_DOMAIN = os.getenv('FRONTEND_DOMAIN', '')

if PRIMARY_DOMAIN:
    ALLOWED_HOSTS.append(PRIMARY_DOMAIN)
    CORS_ALLOWED_ORIGINS.extend([
        f'https://{PRIMARY_DOMAIN}',
        f'http://{PRIMARY_DOMAIN}',
    ])
    CSRF_TRUSTED_ORIGINS.extend([
        f'https://{PRIMARY_DOMAIN}',
        f'http://{PRIMARY_DOMAIN}',
    ])

if API_DOMAIN:
    ALLOWED_HOSTS.append(API_DOMAIN)
    CSRF_TRUSTED_ORIGINS.extend([
        f'https://{API_DOMAIN}',
        f'http://{API_DOMAIN}',
    ])

if FRONTEND_DOMAIN:
    ALLOWED_HOSTS.append(FRONTEND_DOMAIN)
    CORS_ALLOWED_ORIGINS.extend([
        f'https://{FRONTEND_DOMAIN}',
        f'http://{FRONTEND_DOMAIN}',
    ])
    CSRF_TRUSTED_ORIGINS.extend([
        f'https://{FRONTEND_DOMAIN}',
        f'http://{FRONTEND_DOMAIN}',
    ])

# CORS additional settings
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'cache-control',
    'pragma',
]

# Django REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',  # 移除Session认证以避免CSRF问题
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',  # 保持AllowAny权限
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
}

# 前端URL配置
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://127.0.0.1:3000')

# Django Allauth 配置
SITE_ID = 1

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]

# Allauth 设置 - 使用新的配置格式
ACCOUNT_LOGIN_METHODS = {'email'}  # 替代 ACCOUNT_AUTHENTICATION_METHOD
ACCOUNT_SIGNUP_FIELDS = ['email*', 'password1*', 'password2*']  # 替代 ACCOUNT_EMAIL_REQUIRED 和 ACCOUNT_USERNAME_REQUIRED
ACCOUNT_EMAIL_VERIFICATION = 'none'  # 我们使用自定义邮箱验证
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USER_MODEL_USERNAME_FIELD = None
ACCOUNT_USER_MODEL_EMAIL_FIELD = "email"

# 社交登录重定向配置 - 支持多域名
LOGIN_REDIRECT_URL = '/api/auth/social-success/'
LOGOUT_REDIRECT_URL = '/api/auth/social-logout/'

# 开发环境特殊配置
SOCIALACCOUNT_LOGIN_ON_GET = True
SOCIAL_AUTH_REDIRECT_IS_HTTPS = False  # 允许HTTP
SOCIAL_AUTH_SANITIZE_REDIRECTS = True

# 社交登录提供商配置
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        },
        'OAUTH_PKCE_ENABLED': True,
    },
    'facebook': {
        'METHOD': 'oauth2',
        'SDK_URL': '//connect.facebook.net/{locale}/sdk.js',
        'SCOPE': ['email', 'public_profile'],
        'AUTH_PARAMS': {'auth_type': 'reauthenticate'},
        'INIT_PARAMS': {'cookie': True},
        'FIELDS': [
            'id',
            'first_name',
            'last_name', 
            'middle_name',
            'name',
            'name_format',
            'picture',
            'short_name',
            'email',
        ],
        'EXCHANGE_TOKEN': True,
        'LOCALE_FUNC': 'path.to.callable',
        'VERIFIED_EMAIL': False,
        'VERSION': 'v13.0',
    },
    'discord': {
        'SCOPE': ['identify', 'email'],
    },
    'twitter': {
        'SCOPE': ['tweet.read', 'users.read'],
    }
}

# 社交登录客户端配置 (从环境变量读取)
SOCIALACCOUNT_PROVIDERS.update({
    'google': {
        **SOCIALACCOUNT_PROVIDERS['google'],
        'APP': {
            'client_id': os.getenv('GOOGLE_CLIENT_ID', ''),
            'secret': os.getenv('GOOGLE_CLIENT_SECRET', ''),
        }
    },
    'facebook': {
        **SOCIALACCOUNT_PROVIDERS['facebook'],
        'APP': {
            'client_id': os.getenv('FACEBOOK_CLIENT_ID', ''),
            'secret': os.getenv('FACEBOOK_CLIENT_SECRET', ''),
        }
    },
    'discord': {
        **SOCIALACCOUNT_PROVIDERS['discord'],
        'APP': {
            'client_id': os.getenv('DISCORD_CLIENT_ID', ''),
            'secret': os.getenv('DISCORD_CLIENT_SECRET', ''),
        }
    },
    'twitter': {
        **SOCIALACCOUNT_PROVIDERS['twitter'],
        'APP': {
            'client_id': os.getenv('TWITTER_CLIENT_ID', ''),
            'secret': os.getenv('TWITTER_CLIENT_SECRET', ''),
        }
    }
})

# 本地 MEDIA 配置，用于存储用户上传和生成文件
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# CSRF settings
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_HTTPONLY = False  # 允许 JavaScript 访问 CSRF cookie
CSRF_USE_SESSIONS = False
CSRF_COOKIE_NAME = 'csrftoken'

# CSRF cookie security - 检测是否使用HTTPS
PROTOCOL = os.getenv('PROTOCOL', 'http')
if PROTOCOL == 'https':
    CSRF_COOKIE_SECURE = True
    CSRF_TRUSTED_ORIGINS = [origin.replace('http://', 'https://') for origin in CSRF_TRUSTED_ORIGINS]
else:
    CSRF_COOKIE_SECURE = False

# 确保CSRF信任源包含miragemakers.ai的HTTPS版本
if 'https://miragemakers.ai' not in CSRF_TRUSTED_ORIGINS:
    CSRF_TRUSTED_ORIGINS.append('https://miragemakers.ai')
if 'http://miragemakers.ai' not in CSRF_TRUSTED_ORIGINS:
    CSRF_TRUSTED_ORIGINS.append('http://miragemakers.ai')

# CSRF豁免的URL模式 - API端点不需要CSRF检查
CSRF_EXEMPT_URLS = [
    r'^/api/auth/.*',  # 所有认证API
    r'^/api/.*',       # 所有API端点
    r'^/health/.*',    # 健康检查端点
]

# Redis for Cache & Celery Broker & Backend - 融合main分支配置
REDIS_HOST = os.getenv("REDIS_HOST", "127.0.0.1")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")

CELERY_BROKER_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"
CELERY_RESULT_BACKEND = f"redis://{REDIS_HOST}:{REDIS_PORT}/1"
CELERY_TASK_ALWAYS_EAGER = False  # 使用真实的Celery异步执行
CELERY_TASK_DEFAULT_QUEUE = "default"

# 缓存配置 - 使用Redis缓存
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{REDIS_HOST}:{REDIS_PORT}/2",
        "OPTIONS": {"CLIENT_CLASS": "django_redis.client.DefaultClient"},
    }
}

# WebSocket 配置 - Django Channels
ASGI_APPLICATION = "generator.asgi.application"

# Channel Layer 配置 - 使用 Redis 作为消息传递后端
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [(REDIS_HOST, int(REDIS_PORT))],
            "prefix": "miragemakers_websocket:",  # 添加前缀避免与其他Redis键冲突
        },
    },
}

# Logging 配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
    },
    'loggers': {
        'core.email_utils': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# 禁用Django的默认CSP设置
SECURE_CONTENT_TYPE_NOSNIFF = False  # 禁用内容类型嗅探保护
SECURE_BROWSER_XSS_FILTER = False    # 禁用XSS过滤器

# Cookie配置
SESSION_COOKIE_SAMESITE = 'None'
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SAMESITE = 'None'
CSRF_COOKIE_SECURE = True

# 允许在第三方上下文中使用cookies
SESSION_COOKIE_HTTPONLY = False
CSRF_COOKIE_HTTPONLY = False

# PayPal 配置 (沙盒环境)
PAYPAL_CLIENT_ID = os.getenv('PAYPAL_CLIENT_ID', '')
PAYPAL_CLIENT_SECRET = os.getenv('PAYPAL_CLIENT_SECRET', '')
PAYPAL_WEBHOOK_ID = os.getenv('PAYPAL_WEBHOOK_ID', '')
PAYPAL_MODE = os.getenv('PAYPAL_MODE', 'sandbox')  # sandbox 或 production

# Stripe 配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY = os.getenv('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', '')
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY', '')
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET', '')
STRIPE_SUCCESS_URL = os.getenv('STRIPE_SUCCESS_URL', 'https://miragemakers.ai/payment/success')
STRIPE_CANCEL_URL = os.getenv('STRIPE_CANCEL_URL', 'https://miragemakers.ai/payment/cancel')

# Stripe Price IDs (需要在Stripe Dashboard中创建)
STRIPE_TRIAL_PRICE_ID = os.getenv('STRIPE_TRIAL_PRICE_ID', '')  # 一次性$6.99
STRIPE_BASIC_PRICE_ID = os.getenv('STRIPE_BASIC_PRICE_ID', '')  # 月付$19.99
STRIPE_PREMIUM_PRICE_ID = os.getenv('STRIPE_PREMIUM_PRICE_ID', '')  # 月付$59.99
STRIPE_ANNUAL_PRICE_ID = os.getenv('STRIPE_ANNUAL_PRICE_ID', '')  # 年付$199.99

# 站点URL配置
SITE_URL = os.getenv('SITE_URL', 'https://miragemakers.ai')

# 会员系统配置
MEMBERSHIP_SETTINGS = {
    # 代币提醒阈值
    'LOW_BALANCE_THRESHOLD': 1000,
    
    # 过期提醒天数
    'EXPIRY_REMINDER_DAYS': [7, 3, 1],
    
    # 默认代币过期天数
    'DEFAULT_TOKEN_EXPIRY_DAYS': 30,
    
    # 免费服务列表
    'FREE_SERVICES': ['video_keyframe'],
    
    # 代币消耗配置已移至 core.services.token_service.TokenService.TOKEN_COSTS
    # 避免重复配置，统一使用 TokenService 中的定义
}

# 添加会员系统相关的包到requirements中
PAYMENT_BACKENDS = {
    'paypal': 'core.services.paypal_service.PayPalService',
    # 后续可添加其他支付网关
    # 'stripe': 'core.services.stripe_service.StripeService',
    # 'alipay_hk': 'core.services.alipay_service.AlipayService',
}

# Content Security Policy 配置 - 支持PayPal SDK
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True

# 自定义CSP中间件配置 - 支持PayPal和Stripe
CSP_DEFAULT_SRC = ["'self'", "https:", "data:", "blob:"]
CSP_SCRIPT_SRC = [
    "'self'", 
    "'unsafe-inline'", 
    "'unsafe-eval'",
    # Stripe 支持
    "https://js.stripe.com",
    "https://*.stripe.com",
    "https://*.stripe.network",
    # CDN 支持
    "https://cdn.tailwindcss.com",
    "https://cdn.jsdelivr.net",
    "https://unpkg.com",
    "https://cdnjs.cloudflare.com"
]
CSP_STYLE_SRC = [
    "'self'", 
    "'unsafe-inline'",
    "https://fonts.googleapis.com",
    # Stripe 支持
    "https://js.stripe.com",
    "https://*.stripe.com",
    # CDN 支持
    "https://cdn.tailwindcss.com",
    "https://cdn.jsdelivr.net",
    "https://unpkg.com"
]
CSP_FONT_SRC = [
    "'self'", 
    "data:",
    "https://fonts.gstatic.com",
    "https://fonts.googleapis.com",
    # Stripe 字体支持
    "https://*.stripe.com",
    "https://*.stripe.network"
]
CSP_IMG_SRC = [
    "'self'", 
    "https:", 
    "data:", 
    "blob:",
    # Stripe 支持
    "https://*.stripe.com",
    "https://*.stripe.network"
]
CSP_CONNECT_SRC = [
    "'self'", 
    "https:", 
    "wss:",
    # Stripe 支持
    "https://api.stripe.com",
    "https://*.stripe.com",
    "https://*.stripe.network",
    "https://r.stripe.com",
    "https://m.stripe.com",
    "https://q.stripe.com"
]
CSP_FRAME_SRC = [
    "'self'",
    # Stripe 支持
    "https://js.stripe.com",
    "https://*.stripe.com",
    "https://hooks.stripe.com"
]
CSP_OBJECT_SRC = ["'none'"]
CSP_BASE_URI = ["'self'"]

# Permissions Policy 配置 - 控制浏览器功能访问权限
PERMISSIONS_POLICY = {
    'accelerometer': [],
    'ambient-light-sensor': [],
    'autoplay': ['self'],
    'battery': [],
    'camera': [],
    'cross-origin-isolated': [],
    'display-capture': [],
    'document-domain': [],
    'encrypted-media': [],
    'execution-while-not-rendered': [],
    'execution-while-out-of-viewport': [],
    'fullscreen': ['self'],
    'geolocation': [],
    'gyroscope': [],
    'keyboard-map': [],
    'magnetometer': [],
    'microphone': [],
    'midi': [],
    'navigation-override': [],
    'payment': ['self', 'https://*.stripe.com'],
    'picture-in-picture': [],
    'publickey-credentials-get': [],
    'screen-wake-lock': [],
    'sync-xhr': [],
    'usb': [],
    'web-share': [],
    'xr-spatial-tracking': [],
    # 禁用 private-state-token 相关功能以避免违规警告
    'private-state-token-redemption': [],
    'private-state-token-issuance': [],
}
