from django.urls import path
from .views import stripe_payment, membership
from .views.chat_reference import (
    get_session_media_history,
    test_reference_resolution,
    send_message_with_references,
    agent_route
)
# 导入core.views中的健康检查函数和任务状态函数
from core.views import health_check, task_status
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
# ai_service.py功能已迁移到core/views.py

# 包装函数：将查询参数转换为路径参数
@require_http_methods(["GET"])
def api_task_status(request):
    """API任务状态查询 - 包装函数，支持查询参数形式"""
    task_id = request.GET.get('task_id')
    if not task_id:
        return JsonResponse({'error': 'task_id parameter is required'}, status=400)

    # 调用原始的task_status函数
    return task_status(request, task_id)

# 新增图像增强相关导入
from api.views.image_enhance import (
    analyze_images,
    compress_images,
    check_task_status,
    enhance_prompt
)
from api.views.prompt_optimizer import optimize_prompt, get_optimization_types

# 新增电商流相关导入
from api.views.ecommerce_stream import (
    start_ecommerce_stream,
    process_stream_step,
    get_stream_status,
    trigger_ecommerce_flow
)

# 新增文件上传相关导入
from api.views.file_upload import upload_file, upload_multiple_files

urlpatterns = [
    # Health check endpoint
    path('health/', health_check, name='api-health-check'),

    # Task status endpoint for frontend polling
    path('task-status/', api_task_status, name='api-task-status'),

    # User profile and usage endpoints
    path('user/profile/', membership.get_user_profile, name='user-profile'),
    path('user/usage/', membership.get_user_usage, name='user-usage'),
    path('user/orders/', membership.get_user_orders, name='user-orders'),

    # Membership endpoints
    path('membership/plans/', membership.get_membership_plans, name='membership-plans'),
    path('user/membership/', membership.get_user_membership, name='user-membership'),

    # Stripe payment endpoints only
    path('stripe/create-payment-intent/', stripe_payment.create_payment_intent, name='stripe-create-payment-intent'),
    path('stripe/confirm-payment/', stripe_payment.confirm_payment, name='stripe-confirm-payment'),
    path('stripe/create-checkout-session/', stripe_payment.create_checkout_session, name='stripe-create-checkout-session'),
    path('stripe/checkout-success/', stripe_payment.checkout_success, name='stripe-checkout-success'),
    path('stripe/create-billing-portal/', stripe_payment.create_billing_portal, name='stripe-create-billing-portal'),
    path('stripe/cancel-subscription/', stripe_payment.cancel_subscription, name='stripe-cancel-subscription'),
    path('stripe/subscription-status/', stripe_payment.subscription_status, name='stripe-subscription-status'),
    path('stripe/payment-history/', stripe_payment.payment_history, name='stripe-payment-history'),
    path('stripe/webhook/', stripe_payment.stripe_webhook, name='stripe-webhook'),

    # 多媒体引用功能API
    path('chat/sessions/<uuid:session_id>/media-history/', get_session_media_history, name='get_session_media_history'),
    path('chat/test-reference-resolution/', test_reference_resolution, name='test_reference_resolution'),
    path('chat/send-with-references/', send_message_with_references, name='send_message_with_references'),

    # Agent路由API (集成LangGraph)
    path('agent/route/', agent_route, name='agent_route'),

    # AI服务功能已迁移到 /api/generate/ (core/views.py)

    # 图像增强和OCR相关API
    path('image/analyze/', analyze_images, name='analyze_images'),
    path('image/compress/', compress_images, name='compress_images'),
    path('task/status/<str:task_id>/', check_task_status, name='check_task_status'),
    path('prompt/enhance/', enhance_prompt, name='enhance_prompt'),

    # 提示词优化API
    path('prompt/optimize/', optimize_prompt, name='optimize_prompt'),
    path('prompt/types/', get_optimization_types, name='get_optimization_types'),

    # 电商流Stream API
    path('ecommerce/stream/start/', start_ecommerce_stream, name='start_ecommerce_stream'),
    path('ecommerce/stream/<str:stream_id>/step/', process_stream_step, name='process_stream_step'),
    path('ecommerce/stream/<str:stream_id>/status/', get_stream_status, name='get_stream_status'),
    path('ecommerce/trigger/', trigger_ecommerce_flow, name='trigger_ecommerce_flow'),

    # 文件上传API
    path('file/upload/', upload_file, name='upload_file'),
    path('file/upload/multiple/', upload_multiple_files, name='upload_multiple_files'),
] 