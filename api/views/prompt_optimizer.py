"""
提示词优化API视图
"""
import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from app.core.llm_client import LLMClient

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def optimize_prompt(request):
    """
    AI提示词优化端点
    """
    try:
        data = json.loads(request.body)
        original_prompt = data.get('prompt', '').strip()
        optimization_type = data.get('optimization_type', 'creative')
        count = min(data.get('count', 3), 6)  # 最多6个版本

        if not original_prompt:
            return JsonResponse({
                'error': '请输入要优化的提示词'
            }, status=400)

        # 根据优化类型选择不同的优化策略
        optimization_strategies = {
            'creative': {
                'name': '创意增强',
                'prompt': f"""作为一个专业的创意写作专家，请帮我优化以下提示词，让它更具创意性和想象力：

原提示词："{original_prompt}"

请生成{count}个优化版本，每个版本都应该：
1. 保持原意的基础上增加创意元素
2. 使用更生动、具体的描述词
3. 添加适当的氛围和情感表达
4. 使语言更加丰富多彩

请以JSON格式返回，格式如下：
{{"optimized_prompts": ["优化版本1", "优化版本2", "优化版本3"]}}"""
            },
            'detailed': {
                'name': '细节丰富',
                'prompt': f"""作为一个专业的视觉描述专家，请帮我优化以下提示词，让它更加详细和精确：

原提示词："{original_prompt}"

请生成{count}个优化版本，每个版本都应该：
1. 添加更多具体的视觉细节
2. 明确光线、色彩、构图等要素
3. 增加材质、纹理的描述
4. 提供更精确的场景设定

请以JSON格式返回，格式如下：
{{"optimized_prompts": ["优化版本1", "优化版本2", "优化版本3"]}}"""
            },
            'artistic': {
                'name': '艺术风格',
                'prompt': f"""作为一个艺术指导专家，请帮我优化以下提示词，让它更具艺术感和美学价值：

原提示词："{original_prompt}"

请生成{count}个优化版本，每个版本都应该：
1. 融入特定的艺术风格或流派
2. 添加艺术技法和表现手法
3. 增强美学表达和情感深度
4. 使用专业的艺术术语

请以JSON格式返回，格式如下：
{{"optimized_prompts": ["优化版本1", "优化版本2", "优化版本3"]}}"""
            },
            'commercial': {
                'name': '商业应用',
                'prompt': f"""作为一个商业设计专家，请帮我优化以下提示词，让它更适合商业应用：

原提示词："{original_prompt}"

请生成{count}个优化版本，每个版本都应该：
1. 符合商业审美和市场需求
2. 考虑品牌形象和目标受众
3. 强调视觉冲击力和传播效果
4. 适合商业用途和营销推广

请以JSON格式返回，格式如下：
{{"optimized_prompts": ["优化版本1", "优化版本2", "优化版本3"]}}"""
            }
        }

        strategy = optimization_strategies.get(optimization_type, optimization_strategies['creative'])
        
        # 使用LLM客户端进行优化
        llm_client = LLMClient()
        
        logger.info(f"🎨 [PromptOptimize] 开始优化提示词 - 类型: {strategy['name']}, 原文: {original_prompt[:50]}...")
        
        response = llm_client.send_request(
            messages=[{
                "role": "user", 
                "content": strategy['prompt']
            }],
            stream=False,
            model="gpt-4",
            temperature=0.8,
            max_tokens=1000
        )

        if response and response.get('choices') and len(response['choices']) > 0:
            content = response['choices'][0]['message']['content'].strip()
            
            # 尝试解析JSON响应
            try:
                import re
                # 查找JSON块
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    result_json = json.loads(json_match.group())
                    optimized_prompts = result_json.get('optimized_prompts', [])
                else:
                    # 如果没有找到JSON，尝试按行分割
                    lines = [line.strip() for line in content.split('\n') if line.strip()]
                    optimized_prompts = [line for line in lines if len(line) > 10][:count]
                
                if not optimized_prompts:
                    # 如果仍然为空，使用原提示词的变体
                    optimized_prompts = [original_prompt + "，高质量，精美细节，专业制作"]
                
                logger.info(f"✅ [PromptOptimize] 优化完成 - 生成了{len(optimized_prompts)}个版本")
                
                return JsonResponse({
                    'status': 'success',
                    'optimized_prompts': optimized_prompts[:count],
                    'original_prompt': original_prompt,
                    'optimization_type': strategy['name']
                })

            except Exception as parse_error:
                logger.error(f"❌ [PromptOptimize] JSON解析失败: {parse_error}")
                # 降级处理：简单优化
                fallback_prompts = [
                    f"{original_prompt}，高质量，精美细节",
                    f"{original_prompt}，专业制作，视觉震撼",
                    f"{original_prompt}，艺术感强，色彩丰富"
                ]
                
                return JsonResponse({
                    'status': 'success',
                    'optimized_prompts': fallback_prompts[:count],
                    'original_prompt': original_prompt,
                    'optimization_type': strategy['name']
                })
        else:
            logger.error(f"❌ [PromptOptimize] LLM响应为空")
            return JsonResponse({
                'error': 'AI服务暂时不可用，请稍后重试'
            }, status=503)

    except json.JSONDecodeError:
        return JsonResponse({
            'error': '请求格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"❌ [PromptOptimize] 优化失败: {str(e)}")
        return JsonResponse({
            'error': f'优化失败: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
@login_required
def get_optimization_types(request):
    """
    获取可用的优化类型
    """
    return JsonResponse({
        'optimization_types': [
            {'id': 'creative', 'name': '创意增强', 'description': '增加创意元素和想象力'},
            {'id': 'detailed', 'name': '细节丰富', 'description': '添加具体的视觉细节'},
            {'id': 'artistic', 'name': '艺术风格', 'description': '融入艺术感和美学价值'},
            {'id': 'commercial', 'name': '商业应用', 'description': '适合商业用途和营销推广'}
        ]
    }) 