"""
多媒体引用相关的API视图
集成LangGraph Agent系统和引用服务
"""
import json
import asyncio
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.conf import settings

from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from core.models import ChatSession, ChatMessage, User
from django.db import models

logger = logging.getLogger(__name__)

@api_view(['GET'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
def get_session_media_history(request, session_id):
    """获取会话媒体历史"""
    try:
        # 使用DRF认证，用户已经过验证
        logger.info(f"Authenticated user: {request.user.email} requesting session {session_id}")
        
        # 简化逻辑：只处理已认证用户的会话
        try:
            session = ChatSession.objects.get(id=session_id, user=request.user)
            logger.info(f"Found session {session_id} for user {request.user.email}")
        except ChatSession.DoesNotExist:
            logger.warning(f"ChatSession {session_id} not found for user {request.user.email}")
            return Response({
                'success': True,
                'media_messages': [],
                'total_count': 0,
                'session_id': str(session_id),
                'message': 'Session not found or access denied'
            }, status=status.HTTP_200_OK)
        
        # 简化版本：直接从ChatMessage获取媒体
        media_messages = []
        messages = session.messages.filter(
            models.Q(image_url__isnull=False) | models.Q(video_url__isnull=False)
        ).order_by('created_at')
        
        for index, msg in enumerate(messages):
            if msg.image_url:
                media_messages.append({
                    'id': str(msg.id),
                    'reference_id': f'image_{index + 1}',
                    'type': 'image',
                    'url': msg.image_url,
                    'display_name': f'Image {index + 1}',
                    'content': msg.content[:100] if msg.content else '',
                    'created_at': msg.created_at.isoformat()
                })
            if msg.video_url:
                media_messages.append({
                    'id': str(msg.id),
                    'reference_id': f'video_{index + 1}',
                    'type': 'video', 
                    'url': msg.video_url,
                    'display_name': f'Video {index + 1}',
                    'content': msg.content[:100] if msg.content else '',
                    'created_at': msg.created_at.isoformat()
                })
        
        return Response({
            'success': True,
            'media_messages': media_messages,
            'total_count': len(media_messages),
            'session_id': str(session_id)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error fetching media history for session {session_id}: {e}")
        return Response({
            'success': False,
            'error': 'Failed to load media history',
            'media_messages': [],
            'total_count': 0,
            'session_id': str(session_id)
        }, status=status.HTTP_200_OK)  # 返回200而不是500，避免前端错误

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def test_reference_resolution(request):
    """测试引用解析功能"""
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')
        content = data.get('content')
        
        if not session_id or not content:
            return JsonResponse({'error': 'Missing session_id or content'}, status=400)
        
        session = get_object_or_404(ChatSession, id=session_id, user=request.user)
        
        # 简化版本：基础引用解析
        import re
        references = re.findall(r'@(image_\d+|video_\d+|latest_image|latest_video)', content)
        processed_content = content
        referenced_messages = []
        
        # 模拟解析结果
        reference_context = {
            'found_references': references,
            'total_count': len(references)
        }
        
        return JsonResponse({
            'success': True,
            'original_content': content,
            'processed_content': processed_content,
            'referenced_messages': [],
            'reference_context': reference_context,
            'reference_count': len(references)
        })
    except Exception as e:
        logger.error(f"Error resolving references: {e}")
        return JsonResponse({'error': str(e)}, status=400)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def send_message_with_references(request):
    """发送带引用的消息到Agent系统 - 临时回退到原API"""
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')
        utterance = data.get('utterance', '')
        mode = data.get('mode', 'auto')
        
        if not session_id or not utterance:
            return JsonResponse({'error': 'Missing session_id or utterance'}, status=400)
        
        session = get_object_or_404(ChatSession, id=session_id, user=request.user)
        
        # 临时方案：回退到原有的generate API
        import requests
        from django.conf import settings
        
        try:
            # 构建原有的API请求
            api_url = f"{request.scheme}://{request.get_host()}/api/generate/"
            
            # 准备数据
            files = {}
            form_data = {
                'prompt': utterance,
                'mode': mode,
                'session_id': str(session_id)
            }
            
            # 获取CSRF token
            csrf_token = request.META.get('CSRF_COOKIE')
            if not csrf_token:
                from django.middleware.csrf import get_token
                csrf_token = get_token(request)
            
            headers = {
                'X-CSRFToken': csrf_token,
                'X-Requested-With': 'XMLHttpRequest',
            }
            
            # 添加认证
            if request.user.is_authenticated:
                token = request.session.get('token')  # 或者从其他地方获取token
                if token:
                    headers['Authorization'] = f'Token {token}'
            
            # 发送请求到原有API
            import requests
            response = requests.post(
                api_url, 
                data=form_data,
                files=files,
                headers=headers,
                cookies=request.COOKIES,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return JsonResponse({
                    'success': True,
                    'response': result,
                    'mode': mode,
                    'references_processed': 0,
                    'session_id': str(session_id),
                    'fallback': True
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': f'Generate API returned {response.status_code}',
                    'mode': mode
                }, status=response.status_code)
                
        except requests.RequestException as req_error:
            logger.error(f"Request to generate API failed: {req_error}")
            return JsonResponse({
                'success': False,
                'error': f'Failed to connect to generate API: {str(req_error)}',
                'mode': mode
            }, status=500)
        
    except Exception as e:
        logger.error(f"Error in send_message_with_references: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def agent_route(request):
    """Agent路由接口 - 临时禁用"""
    return JsonResponse({
        'error': 'Agent route is temporarily disabled during migration',
        'status': 'migration_in_progress'
    }, status=503) 