"""
图像增强API端点
支持OCR文字提取、文案增强、批量图片处理
"""

import logging
from typing import List
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from celery.result import AsyncResult
from app.tasks import process_image_ocr_enhance, batch_image_compress

logger = logging.getLogger(__name__)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
@csrf_exempt
def analyze_images(request):
    """
    批量图片分析和文案提取API
    支持最多10张图片的OCR和AI文案生成
    """
    try:
        data = request.data if hasattr(request, 'data') else request.POST
        
        # 获取参数
        image_urls = data.get('image_urls', [])
        scenario = data.get('scenario', 'auto')  # auto 或 ecommerce
        session_id = data.get('session_id')  # 可选的会话ID，用于WebSocket通知
        
        # 参数验证
        if not image_urls:
            return JsonResponse({
                'error': '请提供图片URL列表'
            }, status=400)
        
        if not isinstance(image_urls, list):
            return JsonResponse({
                'error': 'image_urls必须是数组格式'
            }, status=400)
        
        if len(image_urls) > 10:
            return JsonResponse({
                'error': '最多支持10张图片同时分析'
            }, status=400)
        
        if scenario not in ['auto', 'ecommerce']:
            return JsonResponse({
                'error': 'scenario参数必须是auto或ecommerce'
            }, status=400)
        
        # 提交到Celery队列异步处理
        task = process_image_ocr_enhance.delay(
            image_urls=image_urls,
            scenario=scenario,
            session_id=session_id
        )
        
        logger.info(f"🚀 [API] 提交OCR任务: {task.id}, 图片数量: {len(image_urls)}")
        
        return JsonResponse({
            'success': True,
            'task_id': task.id,
            'message': f'已提交{len(image_urls)}张图片的分析任务',
            'estimated_time': len(image_urls) * 5  # 预估每张图片5秒
        })
        
    except Exception as e:
        logger.error(f"❌ [API] 图片分析提交失败: {str(e)}")
        return JsonResponse({
            'error': f'提交失败: {str(e)}'
        }, status=500)

@api_view(['GET'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
def check_task_status(request, task_id):
    """
    检查OCR任务状态
    """
    try:
        task_result = AsyncResult(task_id)
        
        if task_result.state == 'PENDING':
            response = {
                'status': 'PENDING',
                'message': '任务排队中...'
            }
        elif task_result.state == 'PROGRESS':
            response = {
                'status': 'PROGRESS',
                'message': '正在处理中...',
                'progress': task_result.info.get('progress', 0)
            }
        elif task_result.state == 'SUCCESS':
            response = {
                'status': 'SUCCESS',
                'result': task_result.result
            }
        else:
            # FAILURE
            response = {
                'status': 'FAILURE',
                'error': str(task_result.info)
            }
        
        return JsonResponse(response)
        
    except Exception as e:
        logger.error(f"❌ [API] 任务状态查询失败: {str(e)}")
        return JsonResponse({
            'error': f'查询失败: {str(e)}'
        }, status=500)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
@csrf_exempt
def compress_images(request):
    """
    批量图片压缩API
    用于多图上传后的格式统一和大小优化
    """
    try:
        data = request.data if hasattr(request, 'data') else request.POST
        
        # 获取参数
        image_urls = data.get('image_urls', [])
        max_width = int(data.get('max_width', 1920))
        quality = float(data.get('quality', 0.85))
        
        # 参数验证
        if not image_urls:
            return JsonResponse({
                'error': '请提供图片URL列表'
            }, status=400)
        
        if len(image_urls) > 10:
            return JsonResponse({
                'error': '最多支持10张图片同时压缩'
            }, status=400)
        
        if max_width < 512 or max_width > 4096:
            return JsonResponse({
                'error': 'max_width必须在512-4096之间'
            }, status=400)
        
        if quality < 0.1 or quality > 1.0:
            return JsonResponse({
                'error': 'quality必须在0.1-1.0之间'
            }, status=400)
        
        # 提交到Celery队列
        task = batch_image_compress.delay(
            image_urls=image_urls,
            max_width=max_width,
            quality=quality
        )
        
        logger.info(f"🗜️ [API] 提交压缩任务: {task.id}, 图片数量: {len(image_urls)}")
        
        return JsonResponse({
            'success': True,
            'task_id': task.id,
            'message': f'已提交{len(image_urls)}张图片的压缩任务'
        })
        
    except Exception as e:
        logger.error(f"❌ [API] 图片压缩提交失败: {str(e)}")
        return JsonResponse({
            'error': f'提交失败: {str(e)}'
        }, status=500)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
@csrf_exempt
def enhance_prompt(request):
    """
    提示词增强API
    使用GPT优化用户输入的提示词
    """
    try:
        data = request.data if hasattr(request, 'data') else request.POST
        
        # 获取参数
        original_prompt = data.get('prompt', '').strip()
        context_type = data.get('type', 'general')  # general, ecommerce, creative
        
        # 参数验证
        if not original_prompt:
            return JsonResponse({
                'error': '请提供需要增强的提示词'
            }, status=400)
        
        if len(original_prompt) > 1000:
            return JsonResponse({
                'error': '提示词长度不能超过1000字符'
            }, status=400)
        
        if context_type not in ['general', 'ecommerce', 'creative']:
            return JsonResponse({
                'error': 'type参数必须是general、ecommerce或creative'
            }, status=400)
        
        # 简单的提示词增强逻辑（可以后续扩展为AI增强）
        enhanced_prompt = _enhance_prompt_simple(original_prompt, context_type)
        
        logger.info(f"✨ [API] 提示词增强完成: {original_prompt[:50]}...")
        
        return JsonResponse({
            'success': True,
            'original_prompt': original_prompt,
            'enhanced_prompt': enhanced_prompt,
            'context_type': context_type
        })
        
    except Exception as e:
        logger.error(f"❌ [API] 提示词增强失败: {str(e)}")
        return JsonResponse({
            'error': f'增强失败: {str(e)}'
        }, status=500)

def _enhance_prompt_simple(prompt: str, context_type: str) -> str:
    """
    简单的提示词增强逻辑
    后续可以集成AI模型进行更智能的增强
    """
    
    # 基础质量词汇
    quality_terms = [
        "高质量", "精美", "专业", "清晰", "细致"
    ]
    
    # 根据类型添加特定词汇
    if context_type == "ecommerce":
        style_terms = [
            "商品摄影", "产品展示", "电商风格", "营销海报", "品牌形象"
        ]
    elif context_type == "creative":
        style_terms = [
            "艺术创作", "创意设计", "视觉冲击", "独特风格", "想象力"
        ]
    else:
        style_terms = [
            "现代设计", "简约风格", "美观大方"
        ]
    
    # 简单的增强逻辑
    enhanced = prompt
    
    # 如果原提示词较短，添加质量描述
    if len(prompt) < 50:
        enhanced += f"，{quality_terms[0]}，{style_terms[0]}"
    
    # 添加技术参数
    if "分辨率" not in enhanced and "高清" not in enhanced:
        enhanced += "，高清分辨率"
    
    return enhanced 