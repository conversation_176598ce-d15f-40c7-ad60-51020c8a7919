"""
电商流Stream API视图
"""
import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from app.agents.ecommerce_stream_agent import ecommerce_stream_agent, EcommerceStreamState

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def start_ecommerce_stream(request):
    """
    启动电商流Stream
    """
    try:
        data = json.loads(request.body)
        uploaded_images = data.get('uploaded_images', [])
        
        if not uploaded_images:
            return JsonResponse({
                'error': '请上传至少一张图片'
            }, status=400)

        user_id = str(request.user.id)
        
        # 启动电商流
        result = ecommerce_stream_agent.start_stream(user_id, uploaded_images)
        
        if 'error' in result:
            return JsonResponse(result, status=500)
            
        return JsonResponse({
            'status': 'success',
            'stream_id': ecommerce_stream_agent.current_state.stream_id,
            'result': result
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': '请求格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"❌ [EcomStream] 启动失败: {str(e)}")
        return JsonResponse({
            'error': f'启动电商流失败: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
@login_required
def process_stream_step(request, stream_id):
    """
    处理Stream步骤
    """
    try:
        data = json.loads(request.body)
        step_name = data.get('step')
        user_input = data.get('user_input', {})
        
        # 加载流程状态
        state = EcommerceStreamState.load(stream_id)
        if not state:
            return JsonResponse({
                'error': '流程不存在或已过期'
            }, status=404)
            
        # 验证用户权限
        if state.user_id != str(request.user.id):
            return JsonResponse({
                'error': '无权限访问此流程'
            }, status=403)
            
        # 设置当前状态并处理步骤
        ecommerce_stream_agent.current_state = state
        result = ecommerce_stream_agent.process_step(step_name, user_input)
        
        return JsonResponse({
            'status': 'success',
            'stream_id': stream_id,
            'result': result
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': '请求格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"❌ [EcomStream] 步骤处理失败: {str(e)}")
        return JsonResponse({
            'error': f'步骤处理失败: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
@login_required
def get_stream_status(request, stream_id):
    """
    获取Stream状态
    """
    try:
        state = EcommerceStreamState.load(stream_id)
        if not state:
            return JsonResponse({
                'error': '流程不存在或已过期'
            }, status=404)
            
        # 验证用户权限
        if state.user_id != str(request.user.id):
            return JsonResponse({
                'error': '无权限访问此流程'
            }, status=403)
            
        result = ecommerce_stream_agent.get_stream_status(stream_id)
        
        return JsonResponse({
            'status': 'success',
            'result': result
        })

    except Exception as e:
        logger.error(f"❌ [EcomStream] 获取状态失败: {str(e)}")
        return JsonResponse({
            'error': f'获取状态失败: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def trigger_ecommerce_flow(request):
    """
    触发电商流程的简化入口
    当用户输入"根据上传的图片提取卖点文案"时自动调用
    """
    try:
        data = json.loads(request.body)
        message = data.get('message', '').strip()
        uploaded_images = data.get('uploaded_images', [])
        
        logger.info(f"🔍 [EcomTrigger] 接收到请求: message='{message}', images={len(uploaded_images)}")
        
        # 检查是否是电商流程触发词
        trigger_keywords = [
            "提取卖点文案", 
            "提取卖点", 
            "产品文案", 
            "商品文案",
            "卖点提取"
        ]
        
        is_ecommerce_trigger = any(keyword in message for keyword in trigger_keywords)
        logger.info(f"🔍 [EcomTrigger] 关键词检测结果: {is_ecommerce_trigger}")
        
        if not is_ecommerce_trigger:
            return JsonResponse({
                'is_ecommerce_flow': False,
                'message': '这不是电商流程触发指令'
            })
            
        if not uploaded_images:
            return JsonResponse({
                'error': '电商流程需要上传图片'
            }, status=400)
            
        # 获取用户信息 - 兼容性处理
        if hasattr(request, 'user') and request.user.is_authenticated:
            user_id = str(request.user.id)
        else:
            # 临时使用默认用户ID用于测试
            user_id = "1"
            logger.warning("🚨 [EcomTrigger] 使用默认用户ID进行测试")
            
        logger.info(f"🔍 [EcomTrigger] 用户ID: {user_id}")
        
        # 自动启动电商流
        result = ecommerce_stream_agent.start_stream(user_id, uploaded_images)
        
        logger.info(f"✅ [EcomTrigger] 电商流启动结果: {result}")
        
        return JsonResponse({
            'is_ecommerce_flow': True,
            'stream_id': ecommerce_stream_agent.current_state.stream_id,
            'result': result
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': '请求格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"❌ [EcomFlow] 触发失败: {str(e)}")
        return JsonResponse({
            'error': f'电商流程触发失败: {str(e)}'
        }, status=500) 