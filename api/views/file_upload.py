"""
文件上传API视图
支持图片和视频文件的快速上传
"""
import os
import uuid
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
def upload_file(request):
    """
    快速文件上传端点
    接受图片和视频文件，返回可访问的URL
    """
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'error': '没有找到上传的文件'
            }, status=400)
        
        uploaded_file = request.FILES['file']
        
        # 检查文件类型
        allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/mov']
        if uploaded_file.content_type not in allowed_types:
            return JsonResponse({
                'error': f'不支持的文件类型: {uploaded_file.content_type}'
            }, status=400)
        
        # 检查文件大小 (最大50MB)
        max_size = 50 * 1024 * 1024  # 50MB
        if uploaded_file.size > max_size:
            return JsonResponse({
                'error': '文件大小不能超过50MB'
            }, status=400)
        
        # 生成唯一文件名
        file_extension = os.path.splitext(uploaded_file.name)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        
        # 根据类型决定存储路径
        if uploaded_file.content_type.startswith('image/'):
            file_path = f"uploads/images/{unique_filename}"
        else:
            file_path = f"uploads/videos/{unique_filename}"
        
        # 保存文件
        saved_path = default_storage.save(file_path, ContentFile(uploaded_file.read()))
        
        # 生成访问URL
        file_url = default_storage.url(saved_path)
        
        # 如果使用相对路径，转换为完整URL
        if file_url.startswith('/'):
            file_url = f"{request.scheme}://{request.get_host()}{file_url}"
        
        logger.info(f"📎 [FileUpload] 文件上传成功: {saved_path}")
        
        return JsonResponse({
            'success': True,
            'url': file_url,
            'filename': uploaded_file.name,
            'size': uploaded_file.size,
            'content_type': uploaded_file.content_type,
            'path': saved_path
        })
        
    except Exception as e:
        logger.error(f"❌ [FileUpload] 上传失败: {str(e)}")
        return JsonResponse({
            'error': f'上传失败: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def upload_multiple_files(request):
    """
    批量文件上传端点
    支持一次上传多个文件
    """
    try:
        uploaded_files = request.FILES.getlist('files')
        
        if not uploaded_files:
            return JsonResponse({
                'error': '没有找到上传的文件'
            }, status=400)
        
        if len(uploaded_files) > 10:
            return JsonResponse({
                'error': '一次最多只能上传10个文件'
            }, status=400)
        
        results = []
        for uploaded_file in uploaded_files:
            try:
                # 检查文件类型
                allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/mov']
                if uploaded_file.content_type not in allowed_types:
                    results.append({
                        'filename': uploaded_file.name,
                        'success': False,
                        'error': f'不支持的文件类型: {uploaded_file.content_type}'
                    })
                    continue
                
                # 检查文件大小
                max_size = 50 * 1024 * 1024  # 50MB
                if uploaded_file.size > max_size:
                    results.append({
                        'filename': uploaded_file.name,
                        'success': False,
                        'error': '文件大小不能超过50MB'
                    })
                    continue
                
                # 生成唯一文件名
                file_extension = os.path.splitext(uploaded_file.name)[1]
                unique_filename = f"{uuid.uuid4()}{file_extension}"
                
                # 根据类型决定存储路径
                if uploaded_file.content_type.startswith('image/'):
                    file_path = f"uploads/images/{unique_filename}"
                else:
                    file_path = f"uploads/videos/{unique_filename}"
                
                # 保存文件
                saved_path = default_storage.save(file_path, ContentFile(uploaded_file.read()))
                
                # 生成访问URL
                file_url = default_storage.url(saved_path)
                
                # 如果使用相对路径，转换为完整URL
                if file_url.startswith('/'):
                    file_url = f"{request.scheme}://{request.get_host()}{file_url}"
                
                results.append({
                    'filename': uploaded_file.name,
                    'success': True,
                    'url': file_url,
                    'size': uploaded_file.size,
                    'content_type': uploaded_file.content_type,
                    'path': saved_path
                })
                
            except Exception as e:
                results.append({
                    'filename': uploaded_file.name,
                    'success': False,
                    'error': str(e)
                })
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        
        logger.info(f"📎 [FileUpload] 批量上传完成: {success_count}/{len(uploaded_files)} 成功")
        
        return JsonResponse({
            'success': True,
            'results': results,
            'total_uploaded': len(uploaded_files),
            'success_count': success_count,
            'failure_count': len(uploaded_files) - success_count
        })
        
    except Exception as e:
        logger.error(f"❌ [FileUpload] 批量上传失败: {str(e)}")
        return JsonResponse({
            'error': f'批量上传失败: {str(e)}'
        }, status=500) 