import stripe
import json
import logging
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db import transaction

from core.models import User, PaymentOrder, StripeSubscription, TokenConsumption
from core.services.stripe_service import StripeService
from core.services.token_service import TokenService
from core.services.email_service import EmailService

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_checkout_session(request):
    """Create Stripe Checkout session"""
    try:
        data = request.data
        plan_type = data.get('plan_type')
        
        if not plan_type:
            return Response({
                'success': False,
                'error': 'Missing plan_type'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate plan type
        valid_plans = ['TRIAL', 'BASIC', 'PREMIUM', 'ANNUAL']
        if plan_type not in valid_plans:
            return Response({
                'success': False,
                'error': f'Invalid plan type. Must be one of: {valid_plans}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        stripe_service = StripeService()
        result = stripe_service.create_checkout_session(request.user, plan_type)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in create_checkout_session: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def checkout_success(request):
    """Handle Checkout success callback"""
    try:
        session_id = request.GET.get('session_id')
        
        if not session_id:
            return Response({
                'success': False,
                'error': 'Missing session_id'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        stripe_service = StripeService()
        result = stripe_service.handle_successful_payment(session_id)
        
        return Response(result, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in checkout_success: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_billing_portal(request):
    """Create customer billing portal session"""
    try:
        stripe_service = StripeService()
        result = stripe_service.create_billing_portal_session(request.user)
        
        return Response(result, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in create_billing_portal: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cancel_subscription(request):
    """Cancel subscription"""
    try:
        data = request.data
        subscription_id = data.get('subscription_id')
        
        if not subscription_id:
            return Response({
                'success': False,
                'error': 'Missing subscription_id'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        stripe_service = StripeService()
        result = stripe_service.cancel_subscription(request.user, subscription_id)
        
        return Response(result, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in cancel_subscription: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def subscription_status(request):
    """Get user subscription status"""
    try:
        subscriptions = StripeSubscription.objects.filter(
            user=request.user,
            status__in=['active', 'trialing', 'past_due']
        ).order_by('-created_at')
        
        subscription_data = []
        for sub in subscriptions:
            subscription_data.append({
                'id': sub.stripe_subscription_id,
                'plan_type': sub.plan_type,
                'status': sub.status,
                'amount': float(sub.amount),
                'currency': sub.currency,
                'current_period_start': sub.current_period_start.isoformat(),
                'current_period_end': sub.current_period_end.isoformat(),
                'trial_start': sub.trial_start.isoformat() if sub.trial_start else None,
                'trial_end': sub.trial_end.isoformat() if sub.trial_end else None,
                'canceled_at': sub.canceled_at.isoformat() if sub.canceled_at else None,
                'is_active': sub.is_active,
                'is_trial': sub.is_trial,
            })
        
        return Response({
            'success': True,
            'subscriptions': subscription_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in subscription_status: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_history(request):
    """Get user payment history"""
    try:
        # Get payment orders (all orders for this user are Stripe orders)
        orders = PaymentOrder.objects.filter(
            user=request.user
        ).order_by('-created_at')
        
        order_data = []
        for order in orders:
            order_data.append({
                'id': order.id,
                'plan_type': order.plan_type,
                'amount': float(order.amount),
                'tokens_to_add': order.tokens_to_add,
                'payment_type': order.payment_type,
                'status': order.status,
                'stripe_session_id': order.stripe_session_id,
                'stripe_payment_intent_id': order.stripe_payment_intent_id,
                'stripe_subscription_id': order.stripe_subscription_id,
                'created_at': order.created_at.isoformat(),
                'completed_at': order.completed_at.isoformat() if order.completed_at else None,
            })
        
        # Get token consumption records
        consumptions = TokenConsumption.objects.filter(
            user=request.user
        ).order_by('-created_at')[:50]  # Last 50 records
        
        consumption_data = []
        for consumption in consumptions:
            # Calculate cost based on tokens used (approximate)
            cost_usd = consumption.tokens_used * 0.001  # Rough estimate: $0.001 per token
            
            consumption_data.append({
                'id': str(consumption.id),
                'service_type': consumption.service_type,
                'tokens_used': consumption.tokens_used,
                'prompt': consumption.prompt[:100] + '...' if len(consumption.prompt) > 100 else consumption.prompt,
                'result_url': consumption.result_url,
                'created_at': consumption.created_at.isoformat(),
                'cost_usd': round(cost_usd, 3),
            })
        
        return Response({
            'success': True,
            'orders': order_data,
            'consumptions': consumption_data,
            'current_tokens': request.user.tokens,
            'current_plan': request.user.current_plan,
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in payment_history: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@csrf_exempt
@require_http_methods(["POST"])
def stripe_webhook(request):
    """Handle Stripe webhooks"""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError as e:
        logger.error(f"Invalid payload: {e}")
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid signature: {e}")
        return HttpResponse(status=400)

    # Handle the event
    try:
        if event['type'] == 'checkout.session.completed':
            handle_checkout_session_completed(event['data']['object'])
        elif event['type'] == 'invoice.payment_succeeded':
            handle_invoice_payment_succeeded(event['data']['object'])
        elif event['type'] == 'invoice.payment_failed':
            handle_invoice_payment_failed(event['data']['object'])
        elif event['type'] == 'customer.subscription.created':
            handle_subscription_created(event['data']['object'])
        elif event['type'] == 'customer.subscription.updated':
            handle_subscription_updated(event['data']['object'])
        elif event['type'] == 'customer.subscription.deleted':
            handle_subscription_deleted(event['data']['object'])
        else:
            logger.info(f"Unhandled event type: {event['type']}")

        return HttpResponse(status=200)
    except Exception as e:
        logger.error(f"Error handling webhook: {e}")
        return HttpResponse(status=500)

def handle_checkout_session_completed(session):
    """Handle completed checkout session"""
    try:
        stripe_service = StripeService()
        result = stripe_service.handle_successful_payment(session['id'])
        logger.info(f"Processed checkout session: {session['id']}")
    except Exception as e:
        logger.error(f"Error processing checkout session {session['id']}: {e}")

def handle_invoice_payment_succeeded(invoice):
    """Handle successful invoice payment"""
    try:
        subscription_id = invoice.get('subscription')
        if subscription_id:
            # Update subscription and add tokens
            subscription = StripeSubscription.objects.filter(
            stripe_subscription_id=subscription_id
            ).first()
            
            if subscription:
                # Create a temporary payment order for token addition
                from core.models import PaymentOrder, MembershipPlan
                from decimal import Decimal
                
                try:
                    plan = MembershipPlan.objects.get(plan_type=subscription.plan_type)
                    temp_order = PaymentOrder(
                        user=subscription.user,
                        plan_type=subscription.plan_type,
                        amount=subscription.amount,
                        tokens_to_add=plan.tokens,
                        payment_type='SUBSCRIPTION',
                        stripe_customer_id=subscription.stripe_customer_id,
                        stripe_subscription_id=subscription_id,
                        status='COMPLETED'
                    )
                    temp_order.save()
                    
                    # Add tokens for subscription renewal
                    success, message = TokenService.add_tokens_from_payment(subscription.user, temp_order)
                    if success:
                        logger.info(f"Added tokens for subscription renewal: {subscription_id}")
                    else:
                        logger.error(f"Failed to add tokens for subscription renewal: {message}")
                except Exception as e:
                    logger.error(f"Error creating temp order for subscription renewal: {e}")
    except Exception as e:
        logger.error(f"Error handling invoice payment: {e}")

def handle_invoice_payment_failed(invoice):
    """Handle failed invoice payment"""
    try:
        subscription_id = invoice.get('subscription')
        if subscription_id:
            logger.warning(f"Invoice payment failed for subscription: {subscription_id}")
            # Could implement notification logic here
    except Exception as e:
        logger.error(f"Error handling failed invoice: {e}")

def handle_subscription_created(subscription):
    """Handle subscription creation"""
    try:
        logger.info(f"Subscription created: {subscription['id']}")
    except Exception as e:
        logger.error(f"Error handling subscription creation: {e}")

def handle_subscription_updated(subscription):
    """Handle subscription update"""
    try:
        logger.info(f"Subscription updated: {subscription['id']}")
    except Exception as e:
        logger.error(f"Error handling subscription update: {e}")

def handle_subscription_deleted(subscription):
    """Handle subscription deletion"""
    try:
        subscription_id = subscription['id']
        
        # Update local subscription record
        local_subscription = StripeSubscription.objects.filter(
            stripe_subscription_id=subscription_id
        ).first()
        
        if local_subscription:
            local_subscription.status = 'canceled'
            local_subscription.canceled_at = timezone.now()
            local_subscription.save()
            
        logger.info(f"Subscription deleted: {subscription_id}")
    except Exception as e:
        logger.error(f"Error handling subscription deletion: {e}")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_payment_intent(request):
    """Create Stripe Payment Intent for local payment form"""
    try:
        data = request.data
        plan_type = data.get('plan_type')
        
        if not plan_type:
            return Response({
                'success': False,
                'error': 'Missing plan_type'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate plan type
        valid_plans = ['TRIAL', 'BASIC', 'PREMIUM', 'ANNUAL']
        if plan_type not in valid_plans:
            return Response({
                'success': False,
                'error': f'Invalid plan type. Must be one of: {valid_plans}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        stripe_service = StripeService()
        result = stripe_service.create_payment_intent(request.user, plan_type)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.error(f"Error in create_payment_intent: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def confirm_payment(request):
    """Confirm payment and add tokens to user account"""
    try:
        data = request.data
        payment_intent_id = data.get('payment_intent_id')
        
        if not payment_intent_id:
            return Response({
                'success': False,
                'error': 'Missing payment_intent_id'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        stripe_service = StripeService()
        result = stripe_service.confirm_payment_intent(request.user, payment_intent_id)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in confirm_payment: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 