from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from core.models import MembershipPlan, PaymentOrder, TokenConsumption
from django.utils import timezone
from datetime import timedelta
import logging
import pytz

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([AllowAny])
def get_membership_plans(request):
    """Get all available membership plans"""
    try:
        plans = MembershipPlan.objects.filter(is_active=True).order_by('price_usd')
        
        plan_data = []
        for plan in plans:
            plan_data.append({
                'id': plan.id,
                'plan_type': plan.plan_type,
                'name': plan.name,
                'price_usd': float(plan.price_usd),
                'tokens': plan.tokens,
                'duration_days': plan.duration_days,
                'description': plan.description,
                'is_active': plan.is_active,
            })
        
        return Response({
            'success': True,
            'plans': plan_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting membership plans: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_membership(request):
    """Get user membership information and usage statistics"""
    try:
        user = request.user
        
        # Get recent payment orders
        recent_orders = PaymentOrder.objects.filter(
            user=user,
            status='COMPLETED'
        ).order_by('-created_at')[:10]
        
        order_data = []
        for order in recent_orders:
            order_data.append({
                'id': order.id,
                'plan_type': order.plan_type,
                'amount': float(order.amount),
                'tokens_added': order.tokens_to_add,
                'payment_type': order.payment_type,
                'created_at': order.created_at.isoformat(),
                'completed_at': order.completed_at.isoformat() if order.completed_at else None,
            })
        
        # Calculate monthly spend (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        monthly_orders = PaymentOrder.objects.filter(
            user=user,
            status='COMPLETED',
            completed_at__gte=thirty_days_ago
        )
        monthly_spend = sum(float(order.amount) for order in monthly_orders)
        
        # Get token usage statistics (last 30 days)
        monthly_consumptions = TokenConsumption.objects.filter(
            user=user,
            created_at__gte=thirty_days_ago
        )
        monthly_tokens_used = sum(consumption.tokens_used for consumption in monthly_consumptions)
        
        # Get usage by service type
        usage_by_service = {}
        for consumption in monthly_consumptions:
            service = consumption.service_type
            if service not in usage_by_service:
                usage_by_service[service] = {
                    'tokens_used': 0,
                    'count': 0
                }
            usage_by_service[service]['tokens_used'] += consumption.tokens_used
            usage_by_service[service]['count'] += 1
        
        # Get detailed usage records for Usage Event Records (last 50 records)
        recent_consumptions = TokenConsumption.objects.filter(
            user=user
        ).order_by('-created_at')[:50]
        
        usage_records = []
        for consumption in recent_consumptions:
            # Map service types to display names
            service_display_map = {
                'image_gen': 'Text to Image',
                'img_edit': 'Image Editing', 
                'video_gen': 'Text to Video',
                'img2video': 'Image to Video',
                'video_keyframe': 'Video Keyframe Extraction',
                'chat_api': 'Chat API'
            }
            
            # 转换为新加坡时间 (UTC+8)
            singapore_tz = pytz.timezone('Asia/Singapore')
            singapore_time = consumption.created_at.astimezone(singapore_tz)
            
            usage_records.append({
                'id': str(consumption.id),
                'date': singapore_time.strftime('%m/%d/%Y, %I:%M:%S %p'),
                'service': service_display_map.get(consumption.service_type, consumption.service_type),
                'service_type': consumption.service_type,
                'tokens': consumption.tokens_used,
                'status': 'Completed',
                'prompt': consumption.prompt[:100] + '...' if len(consumption.prompt) > 100 else consumption.prompt,
                'result_url': consumption.result_url,
                'created_at': singapore_time.isoformat()
            })
        
        # Calculate 7-day daily usage for trend chart
        daily_usage = []
        for i in range(7):
            day_start = (timezone.now() - timedelta(days=i)).replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            day_consumptions = TokenConsumption.objects.filter(
                user=user,
                created_at__gte=day_start,
                created_at__lt=day_end
            )
            
            day_tokens = sum(consumption.tokens_used for consumption in day_consumptions)
            day_requests = day_consumptions.count()
            
            daily_usage.append({
                'date': day_start.strftime('%m-%d'),
                'tokens_used': day_tokens,
                'requests_count': day_requests
            })
        
        # Reverse to get chronological order (oldest to newest)
        daily_usage.reverse()
        
        return Response({
            'success': True,
            'user_info': {
                'email': user.email,
                'name': user.name,
                'current_plan': user.current_plan,
                'tokens': user.tokens,
                'tokens_expires_at': user.tokens_expires_at.isoformat() if user.tokens_expires_at else None,
                'total_generations': user.total_generations,
                'created_at': user.created_at.isoformat(),
            },
            'recent_orders': order_data,
            'monthly_stats': {
                'spend_usd': round(monthly_spend, 2),
                'tokens_used': monthly_tokens_used,
                'usage_by_service': usage_by_service,
            },
            'usage_records': usage_records,
            'daily_usage': daily_usage
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting user membership: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_profile(request):
    """Get user profile information"""
    try:
        user = request.user
        
        return Response({
            'success': True,
            'user': {
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'username': user.username,
                'tokens': user.tokens,
                'current_plan': user.current_plan or 'Free Plan',
                'tokens_expires_at': user.tokens_expires_at.isoformat() if user.tokens_expires_at else None,
                'total_generations': user.total_generations,
                'is_email_verified': user.is_email_verified,
                'created_at': user.created_at.isoformat(),
                'last_login_at': user.last_login_at.isoformat() if user.last_login_at else None
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_usage(request):
    """Get user usage statistics and records"""
    try:
        user = request.user
        days = int(request.GET.get('days', 30))  # Default to 30 days
        
        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Get token consumptions in the specified period
        consumptions = TokenConsumption.objects.filter(
            user=user,
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # Calculate total usage
        total_tokens_used = sum(consumption.tokens_used for consumption in consumptions)
        total_requests = consumptions.count()
        
        # Get usage by service type
        usage_by_service = {}
        service_display_map = {
            'image_gen': 'Text to Image',
            'img_edit': 'Image Editing', 
            'video_gen': 'Text to Video',
            'img2video': 'Image to Video',
            'video_keyframe': 'Video Keyframe Extraction',
            'chat_api': 'Chat API'
        }
        
        for consumption in consumptions:
            service = consumption.service_type
            if service not in usage_by_service:
                usage_by_service[service] = {
                    'name': service_display_map.get(service, service),
                    'tokens_used': 0,
                    'count': 0
                }
            usage_by_service[service]['tokens_used'] += consumption.tokens_used
            usage_by_service[service]['count'] += 1
        
        # Calculate daily usage for the last 7 days
        daily_usage = []
        for i in range(7):
            day_start = (end_date - timedelta(days=i)).replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            day_consumptions = consumptions.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            )
            
            day_tokens = sum(consumption.tokens_used for consumption in day_consumptions)
            day_requests = day_consumptions.count()
            
            daily_usage.append({
                'date': day_start.strftime('%m-%d'),
                'tokens_used': day_tokens,
                'requests_count': day_requests
            })
        
        # Reverse to get chronological order
        daily_usage.reverse()
        
        # Get recent usage records
        recent_consumptions = TokenConsumption.objects.filter(
            user=user
        ).order_by('-created_at')[:50]
        
        usage_records = []
        singapore_tz = pytz.timezone('Asia/Singapore')
        
        for consumption in recent_consumptions:
            singapore_time = consumption.created_at.astimezone(singapore_tz)
            
            usage_records.append({
                'id': str(consumption.id),
                'date': singapore_time.strftime('%m/%d/%Y, %I:%M:%S %p'),
                'service': service_display_map.get(consumption.service_type, consumption.service_type),
                'service_type': consumption.service_type,
                'tokens': consumption.tokens_used,
                'status': 'Completed',
                'prompt': consumption.prompt[:100] + '...' if len(consumption.prompt) > 100 else consumption.prompt,
                'result_url': consumption.result_url,
                'created_at': singapore_time.isoformat()
            })
        
        return Response({
            'success': True,
            'period_days': days,
            'total_tokens_used': total_tokens_used,
            'total_requests': total_requests,
            'usage_by_service': usage_by_service,
            'daily_usage': daily_usage,
            'usage_records': usage_records,
            'current_tokens': user.tokens,
            'low_balance_warning': user.tokens < 100
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting user usage: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_orders(request):
    """Get user payment orders and billing history"""
    try:
        user = request.user
        
        # Get payment orders
        orders = PaymentOrder.objects.filter(user=user).order_by('-created_at')
        
        order_data = []
        for order in orders:
            order_data.append({
                'id': order.id,
                'plan_type': order.plan_type,
                'amount': float(order.amount),
                'tokens_to_add': order.tokens_to_add,
                'payment_type': order.payment_type,
                'status': order.status,
                'stripe_session_id': order.stripe_session_id,
                'stripe_payment_intent_id': order.stripe_payment_intent_id,
                'stripe_subscription_id': order.stripe_subscription_id,
                'created_at': order.created_at.isoformat(),
                'completed_at': order.completed_at.isoformat() if order.completed_at else None,
            })
        
        # Calculate monthly spend (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        monthly_orders = orders.filter(
            status='COMPLETED',
            completed_at__gte=thirty_days_ago
        )
        monthly_spend = sum(float(order.amount) for order in monthly_orders)
        
        return Response({
            'success': True,
            'orders': order_data,
            'monthly_spend': round(monthly_spend, 2),
            'current_plan': user.current_plan,
            'current_tokens': user.tokens,
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting user orders: {e}")
        return Response({
            'success': False,
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)