# Agent架构升级PRD文档

## 1. 项目概述

### 1.1 项目背景
当前系统使用传统的路由器+执行器分离架构（`app/router.py`），存在以下问题：
- 路由分析和执行逻辑分离，增加了系统复杂度
- 状态管理分散，难以追踪整个处理流程
- 错误处理和重试逻辑不够统一
- 缺乏Agent间的协调机制

### 1.2 升级目标
将系统升级为基于LangGraph的现代化Agent架构（`app/core/graph.py`），实现：
- 统一的流程编排和状态管理
- 清晰的Agent职责分工（Safety → Chat → Plan → Execute）
- 更好的错误处理和流程控制
- 简化的异步任务调用逻辑

### 1.3 影响范围
- **核心模块**：`core/views.py` Web API接口
- **任务系统**：Celery异步任务调用方式
- **测试系统**：相关测试用例更新
- **兼容性**：保持前端API接口不变

## 2. 当前架构分析

### 2.1 旧版架构（QwenRouter）
```
用户请求 → views.py → QwenRouter.route() → JobPlan → QwenRouter.execute() → 结果
         ↑                    ↓
    同步调用              Celery异步调用
```

**核心组件**：
- `QwenRouter.route()`：负责意图识别、安全审查、计划生成
- `QwenRouter.execute()`：负责工具调用和结果生成
- 分离的调用方式：同步route + 异步execute

**存在问题**：
1. 路由和执行分离，状态传递复杂
2. 错误处理分散在两个阶段
3. 无法利用LangGraph的流程编排能力
4. Agent间缺乏协调机制

### 2.2 新版架构（VisualGenerationGraph）
```
用户请求 → views.py → VisualGenerationGraph.run_with_conversation() → 结果
                              ↑
                        统一的Celery调用
```

**核心组件**：
- `SafetyAgent`：内容安全审查
- `ChatAgent`：意图分析和对话处理
- `PlanAgent`：执行计划生成  
- `ExecuteNode`：工具执行
- `VisualGenerationGraph`：统一流程编排

**优势**：
1. 统一的状态管理和流程控制
2. 清晰的Agent职责分工
3. 内置的错误处理和重试机制
4. 更好的可观测性和调试能力

## 3. 升级方案设计

### 3.1 核心变更

#### 3.1.1 Web API层变更
**文件**：`core/views.py`

**变更内容**：
- 移除 `QwenRouter.route()` 和 `QwenRouter.execute()` 的分离调用
- 统一使用 `VisualGenerationGraph.run_with_conversation()` 
- 简化异步任务调用逻辑

**调用流程变更**：
```python
# 旧版本 - 分离式调用
router = QwenRouter(history=history)
plan = await router.route(route_request)          # 同步路由
if has_long_running_task:
    task = execute_jobplan.delay(plan_id)         # 部分异步执行
else:
    result = await router.execute(plan)           # 部分同步执行

# 新版本 - 统一异步调用
conversation = build_conversation_from_session(chat_session, prompt, attachments)
task = execute_graph_workflow.delay(
    conversation_data=conversation,
    user_data={'user_id': user.id, 'session_id': str(chat_session.id)}
)  # 所有请求统一异步处理
```

#### 3.1.2 任务系统变更
**文件**：`app/tasks.py`

**变更内容**：
- 新增 `execute_graph_workflow.delay()` 任务
- 保留 `execute_jobplan.delay()` 用于兼容性
- 统一的错误处理和结果保存逻辑

#### 3.1.3 状态管理变更
**优势**：
- 使用LangGraph的`AgentState`统一管理状态
- 自动的状态传递和更新
- 内置的检查点和回滚机制

### 3.2 接口兼容性

#### 3.2.1 前端API保持不变
- 请求格式：`POST /api/generate/`
- 响应格式：保持现有JSON结构
- 异步任务：继续使用task_id轮询机制

#### 3.2.2 数据库模型无变更
- `ChatSession` 和 `ChatMessage` 模型不变
- 代币消费逻辑保持不变
- 消息引用关系保持不变

### 3.3 升级策略

#### 3.3.1 渐进式升级
1. **阶段1**：新增Graph调用路径，保留旧路径
2. **阶段2**：切换为Graph作为主要路径
3. **阶段3**：移除旧的Router代码

#### 3.3.2 回滚方案
- 通过配置开关控制使用新旧架构
- 保留旧代码直到新架构完全稳定
- 数据库结构无变更，便于快速回滚

## 4. 开发计划

### 4.1 分步实施策略

#### 4.1.1 第一阶段：同步版本实现（确保接口正确性）

**目标**：先实现同步调用版本，验证Graph接口调用正确性

**核心改动**：
1. **修改** `core/views.py`，添加同步Graph调用
2. **新增** 辅助函数确保数据格式正确
3. **保持** 原有异步任务不变，确保兼容性

**同步版本代码示例**：
```python
# core/views.py - 同步调用实现
def handle_graph_request_sync(chat_session, prompt, attachments):
    """同步版本的Graph调用"""
    try:
        # 构建对话历史
        conversation = build_conversation_from_session(chat_session, prompt, attachments)
        
        # 直接调用Graph（同步）
        graph = VisualGenerationGraph()
        result = graph.run_with_conversation(
            conversation=conversation, 
            skip_tool_execution=False
        )
        
        # 保存结果到数据库
        save_graph_result_to_db(result, {
            'user_id': chat_session.user.id, 
            'session_id': str(chat_session.id)
        })
        
        return JsonResponse({
            'success': True,
            'data': result,
            'message': 'Task completed successfully'
        })
        
    except Exception as e:
        logger.error(f"Graph execution failed: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
```

#### 4.1.2 第二阶段：异步任务实现

**目标**：在同步版本验证无误后，实现Celery异步任务版本

**新增模块**：
**文件**：`app/tasks.py`（如果不存在则创建）
```python
from app.core.graph import VisualGenerationGraph

@celery_app.task(bind=True)
def execute_graph_workflow(self, conversation_data, skip_execution=False, user_data=None):
    """执行LangGraph工作流 - 统一异步任务"""
    try:
        # 初始化Graph实例
        graph = VisualGenerationGraph()
        
        # 执行工作流
        result = graph.run_with_conversation(
            conversation=conversation_data, 
            skip_tool_execution=skip_execution
        )
        
        # 保存结果到数据库
        if user_data:
            save_graph_result_to_db(result, user_data)
        
        return result
    except Exception as e:
        logger.error(f"Graph workflow execution failed: {e}")
        raise
```

### 4.2 核心修改模块

#### 4.2.1 第一阶段修改（同步版本）
**文件**：`core/views.py`（第337行附近的Router调用逻辑）

**当前代码**：
```python
router = QwenRouter(history=history)
plan = await router.route(route_request)  # 同步路由
task = execute_jobplan.delay(plan_id)     # 异步执行
```

**第一阶段修改为（同步Graph调用）**：
```python
# 添加配置开关控制
if settings.USE_LANGGRAPH_AGENT:
    # 新架构：同步Graph调用
    return handle_graph_request_sync(chat_session, prompt, attachments)
else:
    # 旧架构：保持现有逻辑
    router = QwenRouter(history=history)
    plan = await router.route(route_request)
    task = execute_jobplan.delay(plan_id)
    # ... 现有逻辑
```

#### 4.2.2 第二阶段修改（异步版本）
**在第一阶段验证无误后，修改为统一异步调用**：
```python
# 构建对话历史格式（参考test_chat_data.py）
conversation = build_conversation_from_session(chat_session, prompt, attachments)

# 统一异步调用（移除has_long_running_task判断）
task = execute_graph_workflow.delay(
    conversation_data=conversation,
    skip_execution=False,
    user_data={'user_id': user.id, 'session_id': str(chat_session.id)}
)

# 返回统一的task_id供前端轮询
return JsonResponse({
    'success': True,
    'task_id': task.id,
    'message': 'Task queued for processing'
})
```

### 4.3 辅助函数新增
**文件**：`core/views.py`
**新增函数**：
```python
def build_conversation_from_session(chat_session, current_prompt, attachments):
    """
    从ChatSession构建LangGraph所需的conversation格式
    参考test_chat_data.py的数据格式
    """
    conversation = []
    
    # 添加历史消息
    recent_messages = chat_session.messages.order_by('-created_at')[:10]
    for msg in reversed(recent_messages):
        conversation.append({
            "role": msg.type,  # "user" or "assistant"
            "content": msg.content,
            "image_url": msg.image_url or "",
            "video_url": msg.video_url or ""
        })
    
    # 添加当前用户消息
    current_msg = {
        "role": "user",
        "content": current_prompt,
        "image_url": "",
        "video_url": ""
    }
    
    # 处理附件
    for att in attachments:
        if att.type == "image":
            current_msg["image_url"] = att.url
        elif att.type == "video":
            current_msg["video_url"] = att.url
    
    conversation.append(current_msg)
    return conversation

def save_graph_result_to_db(graph_result, user_data):
    """保存Graph执行结果到数据库"""
    # 提取结果中的媒体URL
    final_results = graph_result.get("generated_results", [])
    
    # 构建响应内容
    response_content = "Task completed successfully"
    image_url = None
    video_url = None
    
    for result in final_results:
        if result.success:
            if result.result_path.endswith(('.jpg', '.png', '.jpeg')):
                image_url = result.result_path
                response_content = "Image generated successfully"
            elif result.result_path.endswith(('.mp4', '.mov')):
                video_url = result.result_path
                response_content = "Video generated successfully"
    
    # 保存到数据库的逻辑...
```

### 4.4 配置开关模块
**文件**：`app/config/settings.py`
**新增配置**：
```python
# Agent架构选择
USE_LANGGRAPH_AGENT = os.getenv("USE_LANGGRAPH_AGENT", "true").lower() == "true"
ENABLE_LEGACY_ROUTER = os.getenv("ENABLE_LEGACY_ROUTER", "false").lower() == "true"
```

### 4.5 具体开发步骤

#### 4.5.1 第一阶段开发步骤（同步版本）
1. **修改** `core/views.py`  
   - 添加 `build_conversation_from_session` 函数
   - 添加 `save_graph_result_to_db` 函数
   - 添加 `handle_graph_request_sync` 函数
   - 添加配置开关判断逻辑

2. **配置环境变量**
   ```bash
   export USE_LANGGRAPH_AGENT=true
   ```

3. **测试同步调用**
   - 使用curl脚本测试各种功能
   - 确保Graph接口调用正确
   - 验证数据格式和结果保存

#### 4.5.2 第二阶段开发步骤（异步版本）
1. **创建** `app/tasks.py`（如不存在）
   - 实现 `execute_graph_workflow` 任务
   - 处理异常和错误恢复
   - 集成数据库保存逻辑

2. **修改** `core/views.py`
   - 将同步调用改为异步任务调用
   - 统一响应格式返回task_id
   - 移除has_long_running_task判断逻辑

3. **启动Celery Worker**
   ```bash
   celery -A generator worker --loglevel=info
   ```

### 4.6 测试脚本设计

#### 4.6.1 Curl测试脚本

**创建测试脚本**：`scripts/test_graph_api.sh`

```bash
#!/bin/bash

# 测试配置
BASE_URL="http://localhost:8000"
API_ENDPOINT="/api/generate/"

# 获取CSRF Token和Session Cookie
echo "🔄 Getting CSRF token..."
CSRF_RESPONSE=$(curl -s -c cookies.txt -b cookies.txt "${BASE_URL}/")
CSRF_TOKEN=$(echo "$CSRF_RESPONSE" | grep -o 'csrfToken.*' | cut -d'"' -f3)

if [ -z "$CSRF_TOKEN" ]; then
    echo "❌ Failed to get CSRF token"
    exit 1
fi

echo "✅ CSRF Token: $CSRF_TOKEN"

# 测试用例1：聊天对话
echo "🧪 测试用例1: 聊天对话"
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: $CSRF_TOKEN" \
  -b cookies.txt \
  -d '{
    "prompt": "你好，你是谁？",
    "attachments": []
  }' \
  "${BASE_URL}${API_ENDPOINT}" \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n" \
  -o response1.json

echo "📄 Response saved to response1.json"
cat response1.json | jq '.'

# 测试用例2：图像生成
echo -e "\n🧪 测试用例2: 图像生成"
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: $CSRF_TOKEN" \
  -b cookies.txt \
  -d '{
    "prompt": "生成一个黑色的包的图像",
    "attachments": []
  }' \
  "${BASE_URL}${API_ENDPOINT}" \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n" \
  -o response2.json

echo "📄 Response saved to response2.json"
cat response2.json | jq '.'

# 测试用例3：图生视频（需要图像URL）
echo -e "\n🧪 测试用例3: 图生视频"
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: $CSRF_TOKEN" \
  -b cookies.txt \
  -d '{
    "prompt": "Generate a video from the provided image",
    "attachments": [
      {
        "type": "image",
        "url": "https://example.com/sample-image.jpg"
      }
    ]
  }' \
  "${BASE_URL}${API_ENDPOINT}" \
  -w "\nStatus: %{http_code}\nTime: %{time_total}s\n" \
  -o response3.json

echo "📄 Response saved to response3.json"
cat response3.json | jq '.'

# 清理临时文件
echo -e "\n🧹 Cleaning up..."
rm -f cookies.txt
echo "✅ Test completed!"
```

#### 4.6.2 测试验证标准

**第一阶段（同步版本）验证**：
- ✅ HTTP状态码：200
- ✅ 响应格式：`{'success': True, 'data': {...}, 'message': '...'}`
- ✅ 聊天任务响应时间：< 10秒
- ✅ 图像生成任务响应时间：< 30秒

**第二阶段（异步版本）验证**：
- ✅ HTTP状态码：200  
- ✅ 响应格式：`{'success': True, 'task_id': '...', 'message': '...'}`
- ✅ Task ID格式：UUID格式
- ✅ 任务状态查询：可通过`/api/task_status/<task_id>/`查询

#### 4.6.3 错误场景测试

**测试无效输入**：
```bash
# 测试空prompt
curl -X POST -H "Content-Type: application/json" \
  -d '{"prompt": "", "attachments": []}' \
  "${BASE_URL}${API_ENDPOINT}"

# 测试无效附件URL
curl -X POST -H "Content-Type: application/json" \
  -d '{"prompt": "test", "attachments": [{"type": "image", "url": "invalid-url"}]}' \
  "${BASE_URL}${API_ENDPOINT}"
```

**预期结果**：
- HTTP状态码：400或500
- 响应格式：`{'success': False, 'error': '...'}`

## 5. 风险控制与验收

### 5.1 分阶段风险控制

#### 5.1.1 第一阶段风险控制（同步版本）
- **配置开关回退**：`export USE_LANGGRAPH_AGENT=false`
- **直接结果观察**：同步调用便于实时调试
- **功能验证**：curl脚本完整测试所有功能

#### 5.1.2 第二阶段风险控制（异步版本）
- **渐进升级**：基于已验证的同步版本
- **任务监控**：Celery任务状态实时监控
- **快速回滚**：保留同步版本作为备选方案

### 5.2 验收标准

#### 5.2.1 第一阶段验收（同步版本）
- ✅ curl测试脚本所有用例HTTP 200
- ✅ 响应格式：`{'success': True, 'data': {...}}`
- ✅ Graph调用无异常，结果正确
- ✅ 数据库保存格式与旧版一致

#### 5.2.2 第二阶段验收（异步版本）
- ✅ 任务ID正确返回，格式为UUID
- ✅ 响应格式：`{'success': True, 'task_id': '...'}`
- ✅ Celery任务执行成功，无异常
- ✅ 任务状态查询API正常工作

---

**文档版本**：v1.0  
**创建日期**：2025-06-19  
**聚焦范围**：方案实现和代码实现  
**审核人**：技术负责人 