# 图片API兼容性解决方案

## 📖 概述

本系统提供了一个通用的图片处理适配器，可以自动根据不同AI服务的要求处理图片格式，解决各种API对图片URL格式的兼容性问题。

## 🔧 核心组件

### 1. 通用图片适配器 (`app/adapters/image_adapter.py`)
- **ImageAdapter**: 提供URL清理、Base64转换等基础功能
- **APIImageProcessor**: 针对不同API的图片处理器
- **process_image_for_api()**: 统一的图片处理入口函数

### 2. API配置管理 (`app/config/image_api_config.py`)
- 集中管理各种API的图片格式要求
- 提供兼容性矩阵和配置查询接口

## 🎯 支持的API及其策略

| API服务 | 处理策略 | 签名URL支持 | 公共访问需求 | 支持格式 | 描述 |
|---------|----------|-------------|--------------|----------|------|
| **Seedance** | 清理URL | ❌ | ✅ | jpg, jpeg, png, webp | 需要无查询参数的公共URL |
| **Volcengine** | 清理URL | ❌ | ✅ | jpg, jpeg, png, webp | 火山引擎需要无查询参数的公共URL |
| **Veo3** | Base64编码 | ✅ | ❌ | jpg, jpeg, png, webp, gif | 使用Base64编码，兼容性最好 |
| **Kling** | 清理URL | ❌ | ✅ | jpg, jpeg, png | 需要无查询参数的公共URL |
| **OpenAI** | Base64编码 | ✅ | ❌ | jpg, jpeg, png, webp | 支持Base64编码和公共URL |

## 💡 使用方法

### 基础使用

```python
from app.adapters.image_adapter import process_image_for_api

# 为Seedance API处理图片
result = process_image_for_api(
    image_url="https://oss-bucket.com/image.jpg?OSSAccessKeyId=xxx&Expires=xxx", 
    api_type="seedance"
)

if result['success']:
    clean_url = result['data']  # 清理后的URL
    print(f"处理后的URL: {clean_url}")
else:
    print(f"处理失败: {result['error']}")

# 为Veo3 API处理图片
result = process_image_for_api(
    image_url="https://oss-bucket.com/image.jpg?OSSAccessKeyId=xxx&Expires=xxx",
    api_type="veo3"
)

if result['success']:
    base64_data = result['data']       # Base64编码数据
    mime_type = result['mime_type']    # MIME类型
    print(f"Base64长度: {len(base64_data)}, 类型: {mime_type}")
```

### 在API工具中使用

```python
# 在img2video工具中的使用示例
async def image_to_video(prompt: str, img_url: str, api_type: str = "seedance"):
    # 使用通用图片适配器
    image_result = process_image_for_api(img_url, api_type)
    
    if not image_result['success']:
        raise APIError(f"图片处理失败: {image_result['error']}")
    
    # 根据不同API使用处理后的数据
    if image_result['type'] == 'url':
        # 使用清理后的URL
        processed_url = image_result['data']
        # 发送API请求...
        
    elif image_result['type'] == 'base64':
        # 使用Base64数据
        base64_data = image_result['data']
        mime_type = image_result['mime_type']
        # 构建data URL或直接使用base64数据...
```

### 配置查询

```python
from app.config.image_api_config import APIImageConfig

# 查询API配置
config = APIImageConfig.get_config('veo3')
print(f"Veo3处理类型: {config['process_type']}")
print(f"支持签名URL: {config['supports_signed_url']}")

# 获取兼容性矩阵
matrix = APIImageConfig.get_compatibility_matrix()
for api, info in matrix.items():
    print(f"{api}: {info['strategy']} - {info['description']}")
```

## 🔄 处理流程

### URL清理模式 (Seedance, Volcengine, Kling)
```
原始URL: https://bucket.oss.com/image.jpg?OSSAccessKeyId=xxx&Expires=xxx&Signature=xxx
    ↓
清理处理: 移除所有查询参数
    ↓
结果URL: https://bucket.oss.com/image.jpg
```

### Base64编码模式 (Veo3, OpenAI)
```
原始URL: https://bucket.oss.com/image.jpg?OSSAccessKeyId=xxx&Expires=xxx
    ↓
下载图片: 通过HTTP请求获取图片数据
    ↓
格式转换: 使用OpenCV确保JPEG格式
    ↓
Base64编码: 转换为Base64字符串
    ↓
结果: data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...
```

## ⚙️ 配置管理

### 添加新的API支持

在 `app/config/image_api_config.py` 中添加新的API配置：

```python
API_CONFIGS = {
    # 添加新的API
    'new_api': {
        'process_type': ImageProcessType.BASE64,  # 或 CLEAN_URL
        'supports_signed_url': True,
        'requires_public_access': False,
        'supported_formats': ['jpg', 'jpeg', 'png'],
        'max_file_size_mb': 15,
        'description': '新API的描述'
    },
    # ... 其他现有配置
}
```

### 修改处理策略

如果某个API的要求发生变化，只需要修改对应的配置即可，无需改动具体的处理逻辑。

## 🚨 常见问题解决

### 1. OSS签名URL兼容性问题
**问题**: API返回 `InvalidParameter.UnsupportedImageFormat`
**解决**: 使用URL清理模式，移除签名参数

### 2. 图片格式不支持
**问题**: 某些API只支持特定格式
**解决**: 在Base64模式下自动转换为JPEG格式

### 3. 文件大小限制
**问题**: 图片文件过大导致API调用失败
**解决**: 在配置中设置合适的 `max_file_size_mb`，并在处理时进行验证

### 4. 网络超时问题
**问题**: 下载图片时网络超时
**解决**: 适配器内置30秒超时和重试机制

## 🎪 最佳实践

### 1. 选择合适的处理策略
- **URL清理**: 适用于图片已经公开可访问的场景
- **Base64编码**: 适用于需要高兼容性或图片有访问限制的场景

### 2. 错误处理
```python
try:
    result = process_image_for_api(img_url, api_type)
    if not result['success']:
        # 记录错误并提供用户友好的错误信息
        logger.error(f"图片处理失败: {result['error']}")
        return {"error": "图片格式不兼容，请上传JPG或PNG格式的图片"}
except Exception as e:
    # 处理意外错误
    logger.exception("图片处理过程中发生未知错误")
    return {"error": "图片处理失败，请稍后重试"}
```

### 3. 性能优化
- Base64编码会消耗更多内存和带宽，适用于小文件
- URL清理性能最佳，适用于大文件和高频调用
- 根据实际场景选择合适的策略

## 🔮 扩展性

这个设计支持：
- ✅ 新增API类型
- ✅ 修改处理策略  
- ✅ 自定义验证规则
- ✅ 添加新的处理模式
- ✅ 集成其他云存储服务

通过统一的适配器接口，可以轻松支持更多AI服务，无需修改现有的业务逻辑。 