FROM nginx:1.25-alpine

# 创建SSL目录
RUN mkdir -p /etc/nginx/ssl

# 复制nginx配置文件
COPY nginx.conf /etc/nginx/nginx.conf

# 复制SSL证书文件
COPY miragemakers.ai.pem /etc/nginx/ssl/miragemakers.ai.pem
COPY miragemakers.ai.key /etc/nginx/ssl/miragemakers.ai.key
COPY dhparam.pem /etc/nginx/ssl/dhparam.pem

# 设置证书文件权限
RUN chmod 644 /etc/nginx/ssl/miragemakers.ai.pem && \
    chmod 600 /etc/nginx/ssl/miragemakers.ai.key && \
    chmod 644 /etc/nginx/ssl/dhparam.pem

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"] 