FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 安装基础构建和图形依赖项
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    pkg-config \
    default-libmysqlclient-dev \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglvnd0 \
    libglx0 \
    libegl1 \
    libgomp1 \
    # PaddleOCR和PyMuPDF额外依赖
    libgeos-dev \
    libproj-dev \
    libgdal-dev \
    python3-dev \
    libffi-dev \
    libjpeg-dev \
    zlib1g-dev \
    swig \
    libfreetype6-dev \
    libopenjp2-7-dev \
    liblcms2-dev \
    libwebp-dev \
    libtiff5-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    && rm -rf /var/lib/apt/lists/*

    # 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目代码
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p staticfiles media logs && \
    chown -R appuser:appuser /app

# 设置权限
RUN chmod +x manage.py

# 健康检查脚本
RUN echo '#!/bin/bash\ncurl -f http://localhost:8000/health/ || exit 1' > /healthcheck.sh \
    && chmod +x /healthcheck.sh

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /healthcheck.sh

# 暴露端口
EXPOSE 8000

# 默认命令
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"] 