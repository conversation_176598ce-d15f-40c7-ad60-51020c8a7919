#!/bin/bash

# ==========================================
# MirageMakers AI 开发环境启动脚本 v3.0
# 专注于开发环境：热重载、调试、快速启动
#
# 优化特性 (January 2025 v3.0):
# - 移除数据库备份和OSS相关功能
# - 专注开发体验：快速启动、热重载、调试
# - 简化配置，提高开发效率
# - 优化错误处理和日志输出
# ==========================================

# 脚本版本
SCRIPT_VERSION="3.0.0"

# 严格模式
set -euo pipefail

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 配置变量
MODE=""
COMPOSE_CMD=""

# 加载环境变量
load_env_variables() {
    # 尝试加载.env文件
    if [[ -f ".env" ]]; then
        source .env
        log_success ".env 文件加载成功"
    else
        log_warn ".env 文件不存在，使用开发环境默认配置"

        # 创建默认的.env文件供开发使用
        cat > .env << 'EOF'
# MirageMakers AI 开发环境配置

# 管理员账户配置
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# 开发账户配置
DEV_EMAIL=<EMAIL>
DEV_USERNAME=developer
DEV_PASSWORD=dev123456

# 测试账户配置
TEST_EMAIL=<EMAIL>
TEST_USERNAME=tester
TEST_PASSWORD=test123456

# 基础配置
DEBUG=True
SECRET_KEY=dev-secret-key-for-development-only
ALLOWED_HOSTS=localhost,127.0.0.1,**************

# 数据库配置
MYSQL_DATABASE=miragemakers_dev
MYSQL_USER=root
MYSQL_PASSWORD=rootpassword
MYSQL_ROOT_PASSWORD=rootpassword

# Redis配置
REDIS_URL=redis://redis:6379/0

# 邮件配置（开发环境）
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
DEFAULT_FROM_EMAIL=MirageMakers AI <<EMAIL>>

# 域名配置（开发环境）
PRIMARY_DOMAIN=**************
API_DOMAIN=**************
FRONTEND_DOMAIN=**************
PROTOCOL=https

# CORS配置
CORS_ALLOWED_ORIGINS=https://**************,http://localhost:3000
CSRF_TRUSTED_ORIGINS=https://**************,http://localhost:3000

# 静态文件配置
STATIC_URL=/static/
MEDIA_URL=/media/

# API配置
OPENAI_API_KEY=your_openai_api_key
DASHSCOPE_API_KEY=your_dashscope_api_key

# 开发环境标识
ENVIRONMENT=development
NODE_ENV=development
EOF
        source .env
        log_success "创建并加载默认.env配置文件"
    fi

    # 设置默认值（如果.env中没有定义）
    export ADMIN_EMAIL="${ADMIN_EMAIL:-<EMAIL>}"
    export ADMIN_USERNAME="${ADMIN_USERNAME:-admin}"
    export ADMIN_PASSWORD="${ADMIN_PASSWORD:-admin123}"
    export DEV_EMAIL="${DEV_EMAIL:-<EMAIL>}"
    export DEV_USERNAME="${DEV_USERNAME:-developer}"
    export DEV_PASSWORD="${DEV_PASSWORD:-dev123456}"
    export TEST_EMAIL="${TEST_EMAIL:-<EMAIL>}"
    export TEST_USERNAME="${TEST_USERNAME:-tester}"
    export TEST_PASSWORD="${TEST_PASSWORD:-test123456}"

    # 确保配置已设置
    log_info "开发环境账户配置:"
    log_info "  管理员: $ADMIN_EMAIL / $ADMIN_PASSWORD"
    log_info "  开发用户: $DEV_EMAIL / $DEV_PASSWORD"
    log_info "  测试用户: $TEST_EMAIL / $TEST_PASSWORD"
}

# 获取本机IP
get_local_ip() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
    else
        LOCAL_IP=$(hostname -I | awk '{print $1}')
    fi

    if [[ -z "$LOCAL_IP" ]]; then
        LOCAL_IP="127.0.0.1"
        log_warn "无法获取本机IP，使用默认地址: $LOCAL_IP"
    fi

    log_info "开发环境IP: $LOCAL_IP"
}

# 显示帮助信息
show_help() {
    cat << EOF
MirageMakers AI 开发环境启动脚本 v${SCRIPT_VERSION}

用法: $0 [模式] [选项]

启动模式:
  --init          初始化开发环境 - 完全重置，适用于首次部署
  --start         启动开发环境（默认）- 正常启动服务
  --restart       重启开发环境 - 保持数据，重启服务

选项:
  --help, -h      显示此帮助信息

开发环境特性:
  ✨ 代码热重载（前端和后端自动重载）
  🐛 调试模式（详细日志，开放调试端口5678）
  🔄 快速启动（优化的容器启动顺序）
  📁 源码挂载（实时反映代码变更）
  🛠️ 开发工具（无资源限制，开发依赖）
  🚫 无备份功能（专注开发，不干扰OSS）
  📡 WebSocket实时通信（替代轮询，延迟<300ms）

示例:
  $0 --init           # 初始化开发环境
  $0 --start          # 启动开发环境
  $0 --restart        # 重启开发环境

开发环境访问:
  前端: http://localhost:3000/ (热重载)
  后端: http://localhost:8000/ (Django开发服务器)
  WebSocket: ws://localhost:8001/ws/ (实时通信)
  调试: localhost:5678 (Django debug端口)
  MySQL: localhost:3306
  Redis: localhost:6379

注意事项:
  - 开发环境不包含数据备份功能
  - 数据持久化通过Docker volumes实现
  - 重要数据请手动备份或使用生产环境
  - WebSocket连接问题请检查Daphne服务状态
EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --init)
                MODE="init"
                shift
                ;;
            --start)
                MODE="start"
                shift
                ;;
            --restart)
                MODE="restart"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 默认启动模式
    if [[ -z "$MODE" ]]; then
        MODE="start"
    fi
}

# 检查开发环境
check_dev_environment() {
    log_step "检查开发环境..."

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi

    # 设置Docker Compose命令
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose -f docker-compose.dev.yml"
    else
        COMPOSE_CMD="docker-compose -f docker-compose.dev.yml"
    fi

    # 检查必要文件
    local required_files=(
        "docker-compose.dev.yml"
        "frontend/Dockerfile.dev"
        "../core/models.py"
    )

    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "必要文件不存在: $file"
            exit 1
        fi
    done

    # 创建必要目录
    mkdir -p logs ../outputs

    log_success "开发环境检查通过"
}

# 优化开发环境性能
optimize_dev_performance() {
    log_step "优化开发环境性能..."

    # 清理前端开发缓存
    if [[ -d "../frontend/.next" ]]; then
        log_info "清理前端开发缓存..."
        rm -rf ../frontend/.next 2>/dev/null || {
            log_warn "无法删除前端缓存，将在容器内处理"
        }
    fi

    # 清理Docker缓存（仅初始化模式）
    if [[ "$MODE" == "init" ]]; then
        log_info "清理开发环境Docker缓存..."
        docker system prune -f --volumes 2>/dev/null || true
    fi

    log_success "开发环境性能优化完成"
}

# 构建和启动开发服务
build_and_start_dev_services() {
    log_step "构建和启动开发服务..."

    # 根据模式选择不同策略
    case "$MODE" in
        "init")
            log_info "初始化模式：检查是否需要重建..."

            # 检查是否有数据需要保护
            if docker volume ls | grep -q "mysql_dev_data"; then
                log_warn "检测到现有数据库数据！"
                echo "🤔 您确定要重新初始化吗？这将删除所有现有数据（聊天记录、用户数据等）"
                echo "   如果只是想重启服务，请使用: ./start-dev.sh --restart"
                echo "   如果只是想更新代码，请使用: ./start-dev.sh --start"
                echo ""
                read -p "💥 确认删除所有数据并重新初始化？(输入 'DELETE_ALL_DATA' 确认): " confirm

                if [[ "$confirm" != "DELETE_ALL_DATA" ]]; then
                    log_info "初始化已取消，改为重启模式..."
                    MODE="restart"
                    $COMPOSE_CMD down 2>/dev/null || true
                    $COMPOSE_CMD build --no-cache
                else
                    log_warn "⚠️  用户确认删除数据，执行完整重建..."
                    $COMPOSE_CMD down -v 2>/dev/null || true
                    $COMPOSE_CMD build --no-cache
                fi
            else
                log_info "首次初始化，执行完整构建..."
                $COMPOSE_CMD down -v 2>/dev/null || true
                $COMPOSE_CMD build --no-cache
            fi
            ;;
        "restart")
            log_info "重启模式：保持数据重启..."
            $COMPOSE_CMD down 2>/dev/null || true
            ;;
        "start")
            log_info "启动模式：检查并启动..."
            ;;
    esac

    # 启动服务
    log_info "启动开发服务..."
    $COMPOSE_CMD up -d

    log_success "开发服务启动完成"
}

# 等待开发服务就绪
wait_for_dev_services() {
    log_step "等待开发服务就绪..."

    # 等待MySQL
    local mysql_wait=0
    local mysql_max_wait=30
    log_info "等待MySQL服务..."
    while [[ $mysql_wait -lt $mysql_max_wait ]]; do
        if docker exec MirageMakers-mysql-dev mysqladmin ping -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" >/dev/null 2>&1; then
            log_success "MySQL开发服务就绪"
            break
        fi
        echo -n "."
        sleep 2
        mysql_wait=$((mysql_wait + 2))
    done

    if [[ $mysql_wait -ge $mysql_max_wait ]]; then
        log_error "MySQL开发服务启动超时"
        return 1
    fi

    # 等待Redis
    local redis_wait=0
    local redis_max_wait=30
    log_info "等待Redis服务..."
    while [[ $redis_wait -lt $redis_max_wait ]]; do
        if docker exec MirageMakers-redis-dev redis-cli ping >/dev/null 2>&1; then
            log_success "Redis开发服务就绪"
            break
        fi
        echo -n "."
        sleep 2
        redis_wait=$((redis_wait + 2))
    done

    if [[ $redis_wait -ge $redis_max_wait ]]; then
        log_error "Redis开发服务启动超时"
        return 1
    fi

    # 等待Django
    local django_wait=0
    local django_max_wait=60
    log_info "等待Django开发服务..."
    while [[ $django_wait -lt $django_max_wait ]]; do
        if docker exec MirageMakers-django-dev python manage.py check --database default >/dev/null 2>&1; then
            log_success "Django开发服务就绪"
            break
        fi
        echo -n "."
        sleep 3
        django_wait=$((django_wait + 3))
    done

    if [[ $django_wait -ge $django_max_wait ]]; then
        log_error "Django开发服务启动超时"
        return 1
    fi

    # 等待Daphne WebSocket服务
    local daphne_wait=0
    local daphne_max_wait=60
    log_info "等待Daphne WebSocket服务..."
    while [[ $daphne_wait -lt $daphne_max_wait ]]; do
        if docker ps --filter "name=MirageMakers-daphne-dev" --filter "status=running" | grep -q "Up"; then
            # 检查WebSocket端口是否监听
            if docker exec MirageMakers-daphne-dev netstat -tlnp 2>/dev/null | grep -q ":8001" 2>/dev/null || \
               curl -f --max-time 5 http://localhost:8001/health/ >/dev/null 2>&1; then
                log_success "Daphne WebSocket服务就绪"
                break
            fi
        fi
        echo -n "."
        sleep 3
        daphne_wait=$((daphne_wait + 3))
    done

    if [[ $daphne_wait -ge $daphne_max_wait ]]; then
        log_warn "Daphne WebSocket服务启动超时，但不影响基础功能"
    fi

    # 等待Frontend (更宽松的检查)
    local frontend_wait=0
    local frontend_max_wait=120
    log_info "等待Frontend服务..."
    while [[ $frontend_wait -lt $frontend_max_wait ]]; do
        # 检查容器是否健康运行
        if docker ps --filter "name=MirageMakers-frontend-dev" --filter "status=running" | grep -q "Up"; then
            # 检查端口是否可访问
            if curl -f --max-time 30 http://localhost:3000/ >/dev/null 2>&1; then
                log_success "Frontend开发服务就绪"
                break
            fi
        fi

        # 如果容器在重启，给出提示
        if docker ps --filter "name=MirageMakers-frontend-dev" | grep -q "Restarting"; then
            log_warn "Frontend容器重启中，继续等待..."
        fi

        echo -n "."
        sleep 3
        frontend_wait=$((frontend_wait + 3))
    done

    if [[ $frontend_wait -ge $frontend_max_wait ]]; then
        log_warn "Frontend服务启动超时，检查容器状态..."
        docker logs MirageMakers-frontend-dev --tail 20
        log_error "可能需要手动重启frontend服务"
        return 1
    fi

    log_success "所有开发服务就绪"
}

# 初始化开发数据库
initialize_dev_database() {
    log_step "初始化开发数据库..."

    # 检查数据库表数量
    local table_count=$(docker exec MirageMakers-django-dev python manage.py shell -c "
from django.db import connection
cursor = connection.cursor()
cursor.execute('SHOW TABLES')
print(len(cursor.fetchall()))
" 2>/dev/null | tail -1)

    if [[ "$table_count" -lt 10 ]]; then
        log_info "执行完整数据库迁移..."
        docker exec MirageMakers-django-dev python manage.py migrate
    else
        log_info "数据库已存在，执行增量迁移..."
        docker exec MirageMakers-django-dev python manage.py migrate 2>/dev/null || {
            log_warn "增量迁移失败，执行完整迁移..."
            docker exec MirageMakers-django-dev python manage.py migrate
        }
    fi

    # 创建开发环境初始数据
    log_info "创建开发环境初始数据..."
    docker exec MirageMakers-django-dev python manage.py shell -c "
from core.models import User
from rest_framework.authtoken.models import Token

# 创建开发用户（从环境变量读取配置）
dev_users = [
    {'email': '${DEV_EMAIL}', 'username': '${DEV_USERNAME}', 'password': '${DEV_PASSWORD}'},
    {'email': '${TEST_EMAIL}', 'username': '${TEST_USERNAME}', 'password': '${TEST_PASSWORD}'},
]

for user_data in dev_users:
    user, created = User.objects.get_or_create(
        email=user_data['email'],
        defaults={
            'username': user_data['username'],
            'name': user_data['username'].title(),
            'is_staff': True,
            'is_active': True,
            'is_email_verified': True,
            'tokens': 1000,  # 给开发用户一些token
            'current_plan': 'Free Plan'
        }
    )
    if created:
        user.set_password(user_data['password'])
        user.save()
        print(f'✓ 创建开发用户: {user.email}')
    else:
        # 确保密码是最新的
        user.set_password(user_data['password'])
        user.save()
        print(f'✓ 开发用户已存在，密码已更新: {user.email}')

    # 确保每个用户都有token
    token, _ = Token.objects.get_or_create(user=user)
    print(f'  Token: {token.key[:8]}...')

# 创建管理员用户
admin_email = '${ADMIN_EMAIL}'
admin_username = '${ADMIN_USERNAME}'
admin_password = '${ADMIN_PASSWORD}'

admin_user, created = User.objects.get_or_create(
    email=admin_email,
    defaults={
        'username': admin_username,
        'is_staff': True,
        'is_superuser': True,
        'is_active': True,
        'is_email_verified': True,
        'tokens': 0,
        'current_plan': 'Premium Plan',
        'invitation_type': 'ADMIN_INVITATION'
    }
)

if created:
    admin_user.set_password(admin_password)
    admin_user.save()
    print(f'✓ 创建管理员用户: {admin_email}')
else:
    # 确保密码是最新的（防止logout后登录不了）
    admin_user.set_password(admin_password)
    admin_user.save()
    print(f'✓ 管理员用户已存在，密码已更新: {admin_email}')

# 确保admin用户有token
token, _ = Token.objects.get_or_create(user=admin_user)
print(f'✓ 管理员API Token: {token.key[:8]}...')

# 输出用户统计
total_users = User.objects.count()
total_tokens = Token.objects.count()
print(f'✓ 开发环境用户: {total_users} 个用户，{total_tokens} 个Token')
"

    log_success "开发数据库初始化完成"
}

# 更新开发数据库
update_dev_database() {
    log_step "更新开发数据库..."

    # 执行迁移
    log_info "执行数据库迁移..."
    docker exec MirageMakers-django-dev python manage.py migrate

    # 收集静态文件（开发环境也需要）
    log_info "收集静态文件..."
    docker exec MirageMakers-django-dev python manage.py collectstatic --noinput --clear >/dev/null 2>&1

    # 确保WebSocket相关配置正确
    log_info "验证WebSocket配置..."
    docker exec MirageMakers-django-dev python manage.py check --settings=generator.settings >/dev/null 2>&1 || {
        log_warn "Django配置检查有警告，但不影响基本功能"
    }

    log_success "开发数据库更新完成"
}

# 检查和修复服务状态
check_and_fix_services() {
    log_step "检查并修复服务状态..."

    # 检查前端容器状态
    local frontend_status=$(docker ps --filter "name=MirageMakers-frontend-dev" --format "{{.Status}}" 2>/dev/null || echo "not found")
    if [[ "$frontend_status" =~ "Restarting" ]]; then
        log_warn "检测到frontend容器重启循环，尝试修复..."

        # 停止并重新启动frontend
        docker stop MirageMakers-frontend-dev >/dev/null 2>&1 || true
        sleep 5
        docker start MirageMakers-frontend-dev >/dev/null 2>&1 || true

        # 等待一段时间
        sleep 10

        # 再次检查
        if docker ps --filter "name=MirageMakers-frontend-dev" | grep -q "Restarting"; then
            log_error "Frontend修复失败，需要重新构建"

            # 强制重新构建frontend
            log_info "重新构建frontend容器..."
            $COMPOSE_CMD stop frontend
            $COMPOSE_CMD rm -f frontend
            $COMPOSE_CMD build --no-cache frontend
            $COMPOSE_CMD up -d frontend

            # 再次等待
            sleep 30
        fi
    fi

    log_success "服务状态检查完成"
}

# 验证开发环境
verify_dev_deployment() {
    log_step "验证开发环境..."

    # 检查服务状态
    log_info "检查开发服务状态..."
    $COMPOSE_CMD ps

    # 检查数据库连接
    if docker exec MirageMakers-django-dev python manage.py check --database default >/dev/null 2>&1; then
        log_success "开发数据库连接正常"
    else
        log_error "开发数据库连接失败"
        return 1
    fi

    # 检查前端访问
    local frontend_url="http://localhost:3000"
    if curl -s "$frontend_url" >/dev/null 2>&1; then
        log_success "开发前端服务正常"
    else
        log_warn "开发前端服务可能需要时间启动"
    fi

    log_success "开发环境验证完成"
}

# 显示开发环境信息
show_dev_info() {
    log_step "开发环境启动完成！"
    echo ""
    echo "=== MirageMakers AI 开发环境 ==="
    echo "启动模式: $MODE"
    echo "脚本版本: $SCRIPT_VERSION"
    echo "启动时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo "=== 开发环境访问地址 ==="
    echo "前端开发服务器: http://localhost:3000/"
    echo "后端API: http://localhost:8000/"
    echo "Django Admin: http://localhost:8000/admin/"
    echo "WebSocket连接: ws://localhost:8001/ws/"
    echo "健康检查: http://localhost:8000/health/"
    echo ""
    echo "=== 开发数据库 ==="
    echo "MySQL: localhost:3306"
    echo "Redis: localhost:6379"
    echo ""
    echo "=== 实时通信 ==="
    echo "WebSocket服务: ✅ Daphne (端口8001)"
    echo "实时推送: ✅ 任务进度、完成通知"
    echo "连接状态: 可在前端界面查看"
    echo ""
    echo "=== 调试信息 ==="
    echo "Django调试端口: localhost:5678"
    echo "热重载: ✅ 前端和后端都支持"
    echo "代码挂载: ../frontend, ../ (后端)"
    echo ""
    echo "=== 开发账户 ==="
    echo "管理员: $ADMIN_EMAIL / $ADMIN_PASSWORD"
    echo "开发用户: $DEV_EMAIL / $DEV_PASSWORD"
    echo "测试用户: $TEST_EMAIL / $TEST_PASSWORD"
    echo ""
    echo "=== 开发服务管理 ==="
    echo "状态检查: ./manage_services.sh --dev status"
    echo "健康检查: ./manage_services.sh --dev health"
    echo "查看日志: ./manage_services.sh --dev logs [服务名]"
    echo "停止服务: ./stop-dev.sh"
    echo ""
    echo "💡 开发提示："
    echo "  - 修改代码会自动重载，无需重启"
    echo "  - WebSocket连接问题请检查Daphne服务状态"
    echo "  - 使用VSCode可连接Django调试端口5678"
    echo "  - 开发环境不包含备份功能，重要数据请手动备份"
    echo ""
}

# 初始化环境变量
load_env_variables

# 主函数
main() {
    echo "=== MirageMakers AI 开发环境启动脚本 v${SCRIPT_VERSION} ==="
    echo ""

    # 解析参数
    parse_arguments "$@"

    log_info "启动模式: $MODE"
    echo ""

    # 执行主要流程
    check_dev_environment
    get_local_ip
    optimize_dev_performance
    build_and_start_dev_services
    check_and_fix_services
    wait_for_dev_services

    # 根据模式执行不同操作
    case "$MODE" in
        "init")
            initialize_dev_database
            ;;
        "restart"|"start")
            update_dev_database
            ;;
    esac

    verify_dev_deployment
    show_dev_info

    log_success "开发环境启动成功！开始愉快地开发吧 🚀"
}

# 错误处理
trap 'log_error "开发环境启动失败，请检查错误信息"' ERR

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi