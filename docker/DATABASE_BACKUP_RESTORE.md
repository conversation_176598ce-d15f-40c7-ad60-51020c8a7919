# MirageMakers AI 数据库备份与恢复指南

## 📋 概述

本系统提供了完整的MySQL数据库备份和恢复解决方案，支持：
- 🗜️ 自动压缩备份（gzip格式）
- 📁 本地存储 + OSS云备份
- 🔄 一键恢复功能
- 📊 备份文件管理
- 🛡️ 数据完整性验证

## 🔧 备份功能

### 自动备份
```bash
# 手动执行备份
./manage_services.sh backup

# 查看定时任务（每晚21:30自动备份）
crontab -l
```

### 备份特性
- **压缩存储**: 使用gzip压缩，节省70-80%存储空间
- **完整备份**: 包含所有数据库、表结构、存储过程、触发器、用户权限
- **事务一致性**: 使用`--single-transaction`确保数据一致性
- **自动清理**: 本地保留最近7个备份文件
- **云端备份**: 自动上传到阿里云OSS（如果配置）

### 备份文件位置
```
docker/
├── database/
│   └── mysql_data_backup/          # 本地备份目录
│       ├── mysql_backup_20250117_143022_a1b2c3d4.sql.gz
│       ├── mysql_backup_20250116_210030_a1b2c3d4.sql.gz
│       └── ...
└── logs/
    └── mysql_backup.log            # 备份日志
```

### 备份文件命名规则
```
mysql_backup_YYYYMMDD_HHMMSS_CONTAINERID.sql.gz
```
- `YYYYMMDD_HHMMSS`: 备份时间戳
- `CONTAINERID`: MySQL容器ID（前8位）
- `.sql.gz`: 压缩的SQL文件

## 🔄 恢复功能

### 交互式恢复
```bash
./manage_services.sh restore
```

### 恢复流程
1. **选择备份文件**: 显示所有可用备份，按时间排序
2. **确认操作**: 输入`YES`确认（防止误操作）
3. **停止服务**: 自动停止Django和Celery服务
4. **验证文件**: 检查备份文件完整性
5. **恢复数据**: 解压并导入数据库
6. **重启服务**: 重新启动相关服务
7. **验证结果**: 检查数据库连接状态

### 安全保护
- ⚠️ **数据覆盖警告**: 明确提示将覆盖现有数据
- 🔒 **二次确认**: 需要输入`YES`才能继续
- 🛡️ **完整性检查**: 恢复前验证备份文件
- 🔄 **自动回滚**: 失败时自动重启服务

## 📊 管理命令

### 查看备份状态
```bash
# 查看备份目录
ls -la docker/database/mysql_data_backup/

# 查看备份日志
tail -f docker/logs/mysql_backup.log

# 检查备份文件大小
du -h docker/database/mysql_data_backup/
```

### 手动备份操作
```bash
# 进入docker目录
cd docker/

# 直接执行备份脚本
./oss_backup.sh

# 查看最新备份
ls -t database/mysql_data_backup/ | head -5
```

## 🛠️ 高级操作

### 手动恢复（命令行）
```bash
# 1. 停止相关服务
docker-compose stop django celery

# 2. 选择备份文件
BACKUP_FILE="database/mysql_data_backup/mysql_backup_20250117_143022_a1b2c3d4.sql.gz"

# 3. 验证文件完整性
gzip -t "$BACKUP_FILE"

# 4. 执行恢复
gunzip < "$BACKUP_FILE" | docker exec -i MirageMakers-mysql mysql -u root -p"$MYSQL_ROOT_PASSWORD"

# 5. 重启服务
docker-compose start django celery
```

### 从OSS恢复备份
```bash
# 1. 下载OSS备份到本地
ossutil cp "oss://your-bucket/backups/mysql_backup_20250117_143022_a1b2c3d4.sql.gz" database/mysql_data_backup/

# 2. 使用manage_services.sh恢复
./manage_services.sh restore
```

### 备份到其他位置
```bash
# 复制备份到外部存储
cp database/mysql_data_backup/mysql_backup_*.sql.gz /external/backup/location/

# 创建备份的备份
tar -czf mysql_backups_$(date +%Y%m%d).tar.gz database/mysql_data_backup/
```

## ⚡ 快速参考

### 常用命令
```bash
./manage_services.sh backup     # 立即备份
./manage_services.sh restore    # 恢复数据库
./manage_services.sh health     # 检查系统健康
./manage_services.sh status     # 查看服务状态
```

### 备份配置
在`.env`文件中配置：
```bash
# OSS配置（可选）
OSS_BUCKET_NAME=your-bucket-name
OSS_ACCESS_KEY_ID=your-access-key
OSS_ACCESS_KEY_SECRET=your-secret-key
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com

# 本地备份保留数量
MAX_LOCAL_BACKUPS=7
```

## 🚨 注意事项

1. **数据安全**: 恢复操作会完全覆盖现有数据，请确保选择正确的备份文件
2. **服务中断**: 恢复过程中会短暂停止Django和Celery服务
3. **磁盘空间**: 确保有足够的磁盘空间存储备份文件
4. **权限问题**: 确保Docker有权限访问备份目录
5. **版本兼容**: 备份和恢复应在相同或兼容的MySQL版本间进行

## 🔍 故障排除

### 备份失败
```bash
# 检查MySQL容器状态
docker ps | grep mysql

# 查看MySQL日志
docker logs MirageMakers-mysql

# 检查磁盘空间
df -h

# 查看备份日志
tail -20 logs/mysql_backup.log
```

### 恢复失败
```bash
# 检查备份文件完整性
gzip -t database/mysql_data_backup/backup_file.sql.gz

# 手动测试MySQL连接
docker exec -it MirageMakers-mysql mysql -u root -p

# 重启MySQL服务
docker-compose restart mysql
```

### 权限问题
```bash
# 检查文件权限
ls -la database/mysql_data_backup/

# 修复权限
chmod 644 database/mysql_data_backup/*.sql.gz
```

---

**📞 技术支持**: 如遇问题，请检查日志文件或联系系统管理员。 