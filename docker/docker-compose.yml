# 生产环境 Docker Compose 配置
services:
  # Nginx反向代理 - 生产环境
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: MirageMakers-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - django
    networks:
      - MirageMakers_network

    deploy:
      resources:
        limits:
          cpus: '0.3'      # 降低到0.3核
          memory: 256M     # 降低到256M
        reservations:
          cpus: '0.1'
          memory: 128M
    healthcheck:
      test: [ "CMD", "nginx", "-t" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: MirageMakers-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=128M
      --max-connections=50

    deploy:
      resources:
        limits:
          cpus: '0.5'      # 降低到0.5核
          memory: 512M     # 降低到512M
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      timeout: 20s
      retries: 10
      interval: 10s
    networks:
      - MirageMakers_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: MirageMakers-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf

    deploy:
      resources:
        limits:
          cpus: '0.2'      # 降低到0.2核
          memory: 128M     # 降低到128M
        reservations:
          cpus: '0.1'
          memory: 64M
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - MirageMakers_network

  # Django Backend - 生产环境
  django:
    build:
      context: ..
      dockerfile: docker/django/Dockerfile
    container_name: MirageMakers-django
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DJANGO_SETTINGS_MODULE=generator.settings
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - USE_DOCKER=true
      - DEBUG=false
      - PRODUCTION=true
      - DJANGO_LOG_LEVEL=INFO
      - ALLOWED_HOSTS=MirageMakers.ai,localhost,127.0.0.1
      - EMAIL_USE_SSL=True
    ports:
      - "8000:8000"
    volumes:
      - ../:/app
      - django_static:/app/staticfiles
      - django_media:/app/media
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - MirageMakers_network

    deploy:
      resources:
        limits:
          cpus: '0.6'      # 降低到0.6核
          memory: 1G       # 保持1G内存
        reservations:
          cpus: '0.3'
          memory: 512M
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:8000/health/ || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: python manage.py runserver 0.0.0.0:8000 --noreload

  # Daphne ASGI Server - WebSocket支持 (生产环境)
  daphne:
    build:
      context: ..
      dockerfile: docker/django/Dockerfile
    container_name: MirageMakers-daphne
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DJANGO_SETTINGS_MODULE=generator.settings
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - USE_DOCKER=true
      - DEBUG=false
      - PRODUCTION=true
      - DJANGO_LOG_LEVEL=INFO
      - ALLOWED_HOSTS=MirageMakers.ai,localhost,127.0.0.1
      - EMAIL_USE_SSL=True
    ports:
      - "8001:8001"  # WebSocket服务端口
    volumes:
      - ../:/app
      - django_static:/app/staticfiles
      - django_media:/app/media
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - MirageMakers_network
    deploy:
      resources:
        limits:
          cpus: '0.4'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: [ "CMD-SHELL", "curl -f --max-time 30 http://localhost:8001/ || exit 1" ]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 90s
    # 使用Daphne运行ASGI应用，支持WebSocket
    command: daphne -b 0.0.0.0 -p 8001 generator.asgi:application

  # Celery Worker
  celery:
    build:
      context: ..
      dockerfile: docker/django/Dockerfile
    container_name: MirageMakers-celery
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DJANGO_SETTINGS_MODULE=generator.settings
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - USE_DOCKER=true
    volumes:
      - ../:/app
      - django_media:/app/media
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      django:
        condition: service_started
    networks:
      - MirageMakers_network

    deploy:
      resources:
        limits:
          cpus: '0.4'      # 降低到0.4核
          memory: 1G       # 保持1G内存
        reservations:
          cpus: '0.2'
          memory: 512M
    healthcheck:
      test: [ "CMD-SHELL", "celery -A app.celery_app inspect ping || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    command: celery -A app.celery_app worker --loglevel=info --queues=default --concurrency=1

  # Next.js Frontend - 生产模式优化
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/frontend/Dockerfile
      target: runner
      args:
        - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}
    container_name: MirageMakers-frontend
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - DOCKER_ENV=true
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - PORT=${FRONTEND_PORT:-3000}
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      - NEXT_TELEMETRY_DISABLED=1
    ports:
      - "3000:3000"
    depends_on:
      - django
    networks:
      - MirageMakers_network

    deploy:
      resources:
        limits:
          cpus: '0.5'      # 分配0.5核，这是重点优化的服务
          memory: 512M     # 降低到512M
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mysql_data:
    driver: local
    name: mysql_data
  redis_data:
    driver: local
    name: redis_data
  django_static:
    driver: local
    name: django_static
  django_media:
    driver: local
    name: django_media

networks:
  MirageMakers_network:
    driver: bridge

# 总CPU分配：nginx(0.3) + mysql(0.5) + redis(0.2) + django(0.6) + daphne(0.4) + celery(0.4) + frontend(0.5) = 2.9核
# 实际使用会根据reservations动态调整，总预留：1.1核，剩余1.8核浮动分配
