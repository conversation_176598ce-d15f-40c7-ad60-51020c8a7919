# 创建选项与 LangGraph 架构集成规范

## 📋 概述

本文档基于现有的 **LangGraph 多 Agent 架构**，描述如何在 `VisualGenerationGraph` 工作流中集成创建选项功能。

## 🏗️ LangGraph 架构图

### 实际系统架构

```mermaid
graph TB
    subgraph "🎨 前端 Frontend"
        A[FloatingMessageBar.jsx<br/>创建选项UI]
        B[chatApi.submitGenerate<br/>API调用]
    end
    
    subgraph "🚪 Django 入口"
        C[/api/generate/<br/>views.py::index]
        D[execute_langchain_workflow<br/>Celery Task]
    end
    
    subgraph "🧠 LangGraph 工作流"
        E[VisualGenerationGraph<br/>主控制器]
        F[SafetyAgent<br/>安全检查]
        G[ChatAgent<br/>意图分析]
        H[PromptEnhanceAgent<br/>提示优化]
        I[PlanAgent<br/>执行计划]
        J[TokenCheckNode<br/>代币验证]
        K[ExecuteNode<br/>工具执行]
    end
    
    subgraph "🔧 生成工具层"
        L[img_gen<br/>gpt-image-1]
        M[video_gen<br/>seedance/veo3]
        N[img2video<br/>图生视频]
        O[img_edit<br/>图像编辑]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    K --> M
    K --> N
    K --> O
    
    style E fill:#e8f5e8
    style G fill:#e1f5fe
    style I fill:#fff3e0
    style K fill:#fce4ec
```

### 核心组件分析

| 组件 | 文件位置 | 功能 | 创建选项集成点 |
|------|----------|------|----------------|
| `VisualGenerationGraph` | `app/core/graph.py` | 主工作流控制器 | 状态初始化时传入选项 |
| `ChatAgent` | `app/agents/chat_agent.py` | 意图分析和对话管理 | 解析创建选项意图 |
| `PlanAgent` | `app/agents/plan_agent.py` | 生成执行计划 | 根据选项选择合适工具 |
| `ExecuteNode` | `app/core/graph.py::_execute_node` | 工具执行节点 | 传递选项参数给工具 |

## 🔄 LangGraph 数据流集成

### 1. 状态管理集成

**文件**: `app/core/state.py`

```python
# 🆕 扩展 AgentState 以支持创建选项
class AgentState(TypedDict):
    # 现有字段...
    user_input: str
    chat_history: List[Dict[str, str]]
    enhanced_chat_history: List[Dict[str, Any]]
    
    # 🆕 新增创建选项字段
    creation_options: Optional[Dict[str, Any]]  # 前端创建选项
    resolved_model: Optional[str]               # 解析后的实际模型
    generation_parameters: Optional[Dict[str, Any]]  # 转换后的生成参数
```

### 2. Graph 初始化集成

**文件**: `app/core/graph.py::create_initial_state`

```python
def create_initial_state(conversation: List[Dict], skip_tool_execution: bool = False, 
                        skip_safety_check: bool = False, creation_options: Dict = None) -> AgentState:
    """根据对话历史创建初始状态 - 支持创建选项"""
    
    # 现有代码...
    latest_user_message = next((msg for msg in reversed(conversation) if msg["role"] == "user"), None)
    
    # 🆕 处理创建选项
    processed_options = None
    if creation_options:
        processed_options = CreationOptionsProcessor.process_options(creation_options)
    
    return {
        # 现有字段...
        "user_input": latest_user_message.get("content", ""),
        "chat_history": conversation,
        "enhanced_chat_history": enhanced_chat_history,
        
        # 🆕 新增字段
        "creation_options": creation_options,
        "resolved_model": processed_options.get('target_model') if processed_options else None,
        "generation_parameters": processed_options if processed_options else {},
        
        # 其他字段...
        "current_plan": None,
        "execution_results": [],
        "tools_executed": []
    }
```

### 3. ChatAgent 意图分析增强

**文件**: `app/agents/chat_agent.py`

```python
def analyze_user_intent(self, state: AgentState) -> Dict[str, Any]:
    """分析用户意图 - 支持创建选项"""
    
    user_input = state.get("user_input", "")
    creation_options = state.get("creation_options")
    
    # 🆕 如果有创建选项，增强意图分析
    if creation_options:
        enhanced_analysis = self._analyze_with_creation_options(user_input, creation_options)
        return enhanced_analysis
    
    # 原有的意图分析逻辑
    return self._standard_intent_analysis(user_input)

def _analyze_with_creation_options(self, user_input: str, creation_options: Dict) -> Dict[str, Any]:
    """基于创建选项的增强意图分析"""
    
    model_type = creation_options.get('model', 'auto')
    content_type = creation_options.get('type', 'image')
    
    # 根据创建选项推断任务类型
    if content_type == 'image':
        task_type = GenerationTaskType.TEXT_TO_IMAGE
    elif content_type == 'video':
        if model_type == 'auto':
            # Auto 模型不支持视频，需要提示用户
            return {
                "intent_clear": False,
                "task_type": None,
                "user_intent": "模型选择冲突",
                "requirements": {},
                "missing_info": ["Auto模型仅支持图片生成，请选择Pro或Max模型进行视频生成"],
                "next_question": "Auto模型仅支持图片生成，请选择Pro（Seedance）或Max（VEO3）模型来生成视频。",
                "confidence": 1.0
            }
        else:
            task_type = GenerationTaskType.TEXT_TO_VIDEO
    
    # 构建增强的需求信息
    requirements = {
        "content_description": user_input,
        "aspect_ratio": creation_options.get('aspect_ratio', '16:9'),
        "resolution": creation_options.get('resolution', '1080p'),
        "model_preference": model_type
    }
    
    if content_type == 'video':
        requirements["duration"] = int(creation_options.get('duration', '5s').replace('s', ''))
    
    return {
        "intent_clear": True,
        "task_type": task_type,
        "user_intent": f"使用{model_type}模型生成{content_type}",
        "requirements": requirements,
        "missing_info": [],
        "next_question": None,
        "confidence": 0.95
    }
```

### 4. PlanAgent 计划生成增强

**文件**: `app/agents/plan_agent.py`

```python
def generate_execution_plan(self, state: AgentState) -> ExecutionPlan:
    """生成执行计划 - 支持创建选项"""
    
    task_type = state.get("current_task_type")
    creation_options = state.get("creation_options")
    resolved_model = state.get("resolved_model")
    generation_parameters = state.get("generation_parameters", {})
    
    # 🆕 根据创建选项选择工具和参数
    if creation_options:
        return self._generate_plan_with_options(task_type, creation_options, generation_parameters)
    
    # 原有的计划生成逻辑
    return self._standard_plan_generation(task_type, state)

def _generate_plan_with_options(self, task_type, creation_options, generation_parameters):
    """基于创建选项生成执行计划"""
    
    model_type = creation_options.get('model')
    content_type = creation_options.get('type')
    
    # 根据模型类型选择工具
    if content_type == 'image':
        tool_name = "img_gen"
        actual_model = MODEL_MAPPING[model_type]['image_model']
    elif content_type == 'video':
        tool_name = "video_gen"
        actual_model = MODEL_MAPPING[model_type]['video_model']
    
    # 构建工具参数
    tool_params = {
        "prompt": state.get("user_input"),
        "model": actual_model,
        **generation_parameters
    }
    
    # 创建执行计划
    tool_call = ToolCall(
        tool_name=tool_name,
        parameters=tool_params,
        description=f"使用{model_type}模型生成{content_type}",
        order=1
    )
    
    return ExecutionPlan(
        task_type=task_type,
        tool_calls=[tool_call],
        estimated_time=self._estimate_time(tool_name, tool_params),
        description=f"使用指定的创建选项生成{content_type}"
    )
```

### 5. 工具执行节点增强

**文件**: `app/core/graph.py::_execute_node`

```python
def _execute_node(self, state: AgentState) -> AgentState:
    """执行节点 - 支持创建选项参数传递"""
    
    current_plan = state.get("current_plan")
    creation_options = state.get("creation_options")
    
    if not current_plan:
        return {**state, "execution_error": "没有可执行的计划"}
    
    execution_results = []
    
    for tool_call in current_plan.tool_calls:
        try:
            # 🆕 如果有创建选项，增强工具参数
            enhanced_params = tool_call.parameters.copy()
            
            if creation_options:
                enhanced_params.update({
                    'creation_options': creation_options,
                    'aspect_ratio': creation_options.get('aspect_ratio'),
                    'resolution': creation_options.get('resolution'),
                    'duration': creation_options.get('duration')
                })
            
            # 执行工具
            result = execute_tool(tool_call.tool_name, enhanced_params)
            execution_results.append(result)
            
        except Exception as e:
            logger.error(f"工具执行失败: {e}")
            execution_results.append(GenerationResult(
                tool_name=tool_call.tool_name,
                result_path="",
                success=False,
                error_message=str(e)
            ))
    
    return {
        **state,
        "execution_results": execution_results,
        "tools_executed": [call.tool_name for call in current_plan.tool_calls]
    }
```

## 🔧 模型映射配置

### 创建选项处理服务

**文件**: `app/core/creation_options_service.py` (新建)

```python
from typing import Dict, Any
import json
import os

class CreationOptionsProcessor:
    """创建选项处理器 - 基于现有配置模板"""
    
    @classmethod
    def load_config(cls):
        """加载创建选项配置模板"""
        config_path = "app/config/prompt_templates/creation_options_template.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @classmethod
    def validate_options(cls, options: Dict[str, Any]) -> bool:
        """验证创建选项"""
        config = cls.load_config()
        
        model = options.get('model')
        content_type = options.get('type')
        
        if model not in config['models']:
            raise ValueError(f"不支持的模型: {model}")
        
        model_config = config['models'][model]
        
        # 检查模型是否支持指定的内容类型
        if not model_config['supports'].get(content_type, False):
            supported_types = [k for k, v in model_config['supports'].items() if v]
            raise ValueError(f"模型 {model} 不支持 {content_type}，支持的类型: {supported_types}")
        
        # 检查约束条件
        constraints = model_config['constraints']
        
        aspect_ratio = options.get('aspect_ratio')
        if aspect_ratio not in constraints['aspect_ratios']:
            raise ValueError(f"模型 {model} 不支持比例 {aspect_ratio}")
        
        resolution = options.get('resolution')
        if resolution not in constraints['resolutions']:
            raise ValueError(f"模型 {model} 不支持分辨率 {resolution}")
        
        return True
    
    @classmethod
    def process_options(cls, options: Dict[str, Any]) -> Dict[str, Any]:
        """处理创建选项，转换为工具参数"""
        config = cls.load_config()
        
        # 验证选项
        cls.validate_options(options)
        
        model = options['model']
        content_type = options['type']
        model_config = config['models'][model]
        
        # 获取实际模型名称
        if content_type == 'image':
            actual_model = model_config['backend_mapping']['image_model']
        else:
            actual_model = model_config['backend_mapping']['video_model']
        
        # 转换分辨率和比例
        aspect_ratio = options['aspect_ratio']
        resolution = options['resolution']
        
        aspect_mapping = config['aspect_ratio_mapping'][aspect_ratio]
        resolution_mapping = config['resolution_mapping'][resolution]
        
        # 构建参数
        result = {
            'target_model': actual_model,
            'content_type': content_type,
            'width': aspect_mapping['width'],
            'height': aspect_mapping['height'],
            'aspect_ratio': aspect_ratio,
            'resolution': resolution,
            'quality': 'high' if resolution in ['1080p', '4K'] else 'standard'
        }
        
        # 视频特有参数
        if content_type == 'video':
            duration = options.get('duration', '5s')
            duration_mapping = config['duration_mapping'][duration]
            result.update({
                'duration': duration_mapping['seconds'],
                'fps': 30 if resolution != '4K' else 24
            })
        
        return result
```

## 📊 完整集成流程

### 数据流序列图

```mermaid
sequenceDiagram
    participant F as Frontend
    participant D as Django API
    participant C as Celery
    participant G as VisualGenerationGraph
    participant CA as ChatAgent
    participant PA as PlanAgent
    participant T as Tools
    
    F->>D: POST /api/generate/ + creation_options
    D->>C: execute_langchain_workflow.delay()
    C->>G: arun_with_conversation(creation_options)
    G->>G: create_initial_state(creation_options)
    G->>CA: analyze_user_intent(state + options)
    CA->>CA: _analyze_with_creation_options()
    CA->>G: 返回增强的意图分析
    G->>PA: generate_execution_plan(state)
    PA->>PA: _generate_plan_with_options()
    PA->>G: 返回包含模型选择的计划
    G->>G: _execute_node(state)
    G->>T: execute_tool(enhanced_params)
    T->>G: 返回生成结果
    G->>C: 完整的执行结果
    C->>D: 任务完成
    D->>F: 返回结果
```

## 🎯 具体集成步骤

### 步骤 1: 扩展 Django 入口

**文件**: `core/views.py::_async_index`

```python
async def _async_index(request):
    """异步处理 - 支持创建选项"""
    try:
        # 解析请求数据
        if request.content_type.startswith('multipart/form-data'):
            form_data = request.POST
            creation_options = json.loads(form_data.get('creation_options', '{}'))
        else:
            data = json.loads(request.body)
            creation_options = data.get('creation_options', {})
        
        # 🆕 验证创建选项
        if creation_options:
            CreationOptionsProcessor.validate_options(creation_options)
        
        # 构建任务数据
        task_data = {
            "conversation": conversation,
            "user_data": {"user": request.user} if request.user.is_authenticated else None,
            "creation_options": creation_options  # 🆕 传递创建选项
        }
        
        # 提交到 Celery
        task = execute_langchain_workflow.delay(task_data)
        
        return JsonResponse({
            'success': True,
            'task_id': task.id,
            'status': 'processing'
        })
        
    except ValueError as e:
        return JsonResponse({'error': str(e)}, status=400)
```

### 步骤 2: 更新 Celery 任务

**文件**: `app/tasks.py::execute_langchain_workflow`

```python
@app.task(name="execute_langchain_workflow")
def execute_langchain_workflow(task_data: dict):
    """执行 LangGraph 工作流 - 支持创建选项"""
    
    conversation = task_data.get("conversation", [])
    user_data = task_data.get("user_data")
    creation_options = task_data.get("creation_options")  # 🆕 获取创建选项
    
    def run_graph_async():
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 创建 Graph 实例
            graph = VisualGenerationGraph(skip_safety_check=settings.skip_safety_check)
            
            # 🆕 运行带创建选项的工作流
            result = loop.run_until_complete(
                graph.arun_with_conversation(
                    conversation=conversation,
                    skip_tool_execution=False,
                    skip_safety_check=settings.skip_safety_check,
                    user_data=user_data,
                    creation_options=creation_options  # 🆕 传递创建选项
                )
            )
            
            return result
            
        finally:
            loop.close()
    
    return run_graph_async()
```

### 步骤 3: 更新 Graph 主方法

**文件**: `app/core/graph.py::arun_with_conversation`

```python
async def arun_with_conversation(self, conversation: List[Dict], skip_tool_execution: bool = False, 
                                skip_safety_check: bool = False, user_data: Dict = None,
                                creation_options: Dict = None) -> Dict[str, Any]:
    """运行工作流 - 支持创建选项"""
    
    # 🆕 创建包含创建选项的初始状态
    initial_state = create_initial_state(
        conversation, 
        skip_tool_execution, 
        skip_safety_check,
        creation_options=creation_options  # 🆕 传递创建选项
    )
    
    # 添加用户数据
    if user_data and user_data.get("user"):
        initial_state["user"] = user_data["user"]
    
    # 异步运行图
    final_state = await self.graph.ainvoke(initial_state)
    
    # 返回结果摘要
    result = self._create_result_summary(final_state)
    result["state"] = final_state
    return result
```

## 🧪 测试验证

### 端到端测试

```bash
# 测试图片生成（Auto模型）
curl -X POST http://localhost:8000/api/generate/ \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "生成一个草原图片",
    "session_id": "test_session",
    "creation_options": {
      "type": "image",
      "model": "auto",
      "aspect_ratio": "16:9",
      "resolution": "1080p"
    }
  }'

# 测试视频生成（Pro模型）  
curl -X POST http://localhost:8000/api/generate/ \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "生成海浪视频",
    "session_id": "test_session",
    "creation_options": {
      "type": "video", 
      "model": "pro",
      "aspect_ratio": "16:9",
      "resolution": "1080p",
      "duration": "5s"
    }
  }'

# 测试错误场景（Auto模型 + 视频）
curl -X POST http://localhost:8000/api/generate/ \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "生成视频",
    "creation_options": {
      "type": "video",
      "model": "auto"
    }
  }'
```

## 📁 文件清单

### 需要修改的文件

1. **`app/core/state.py`** - 扩展 AgentState
2. **`app/core/graph.py`** - 更新 create_initial_state 和执行节点
3. **`app/agents/chat_agent.py`** - 增强意图分析
4. **`app/agents/plan_agent.py`** - 增强计划生成
5. **`core/views.py`** - 更新 Django 入口
6. **`app/tasks.py`** - 更新 Celery 任务

### 需要新建的文件

1. **`app/core/creation_options_service.py`** - 创建选项处理服务

### 现有配置文件

1. **`app/config/prompt_templates/creation_options_template.json`** - 已存在，包含完整的模型配置

---

**版本**: v3.0 (2024-01) - 基于真实 LangGraph 架构
**架构**: LangGraph + 多Agent系统 + 创建选项集成 