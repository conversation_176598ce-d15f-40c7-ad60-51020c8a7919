#!/bin/bash

# 设置OSS自动备份定时任务脚本

# 严格模式
set -euo pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OSS_BACKUP_SCRIPT="$SCRIPT_DIR/oss_backup.sh"

# 显示帮助信息
show_help() {
    cat << EOF
OSS自动备份定时任务设置脚本

用法: $0 [选项]

选项:
  --time HH:MM    设置备份时间（默认21:00）
  --install       安装定时任务
  --uninstall     卸载定时任务
  --status        查看定时任务状态
  --test          测试OSS备份脚本
  --help, -h      显示此帮助信息

示例:
  $0 --install                # 安装默认定时任务（每天21:00）
  $0 --install --time 02:30   # 安装定时任务（每天02:30）
  $0 --status                 # 查看当前定时任务
  $0 --uninstall              # 卸载定时任务
  $0 --test                   # 测试备份脚本

注意:
  - 需要root权限或当前用户有crontab权限
  - 确保OSS配置文件已正确设置
  - 备份日志保存在 ./logs/ 目录
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查crontab
    if ! command -v crontab &> /dev/null; then
        log_error "crontab未安装，请先安装cron服务"
        return 1
    fi
    
    # 检查OSS备份脚本
    if [[ ! -f "$OSS_BACKUP_SCRIPT" ]]; then
        log_error "OSS备份脚本不存在: $OSS_BACKUP_SCRIPT"
        return 1
    fi
    
    # 检查脚本执行权限
    if [[ ! -x "$OSS_BACKUP_SCRIPT" ]]; then
        log_warn "OSS备份脚本没有执行权限，正在设置..."
        chmod +x "$OSS_BACKUP_SCRIPT"
    fi
    
    # 检查Python3和oss2包
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        return 1
    fi
    
    if ! python3 -c "import oss2" 2>/dev/null; then
        log_warn "oss2包未安装，尝试安装..."
        if pip3 install oss2 >/dev/null 2>&1; then
            log_success "oss2包安装成功"
        else
            log_error "oss2包安装失败"
            return 1
        fi
    fi
    
    log_success "依赖检查通过"
}

# 验证时间格式
validate_time() {
    local time="$1"
    
    if [[ ! "$time" =~ ^([0-1][0-9]|2[0-3]):([0-5][0-9])$ ]]; then
        log_error "时间格式错误，请使用HH:MM格式（如：21:00）"
        return 1
    fi
    
    return 0
}

# 安装定时任务
install_crontab() {
    local backup_time="$1"
    
    log_info "安装OSS自动备份定时任务..."
    
    # 验证时间格式
    if ! validate_time "$backup_time"; then
        return 1
    fi
    
    # 解析时间
    local hour=$(echo "$backup_time" | cut -d: -f1)
    local minute=$(echo "$backup_time" | cut -d: -f2)
    
    # 生成crontab条目
    local cron_entry="$minute $hour * * * cd $SCRIPT_DIR && ./oss_backup.sh >/dev/null 2>&1"
    local cron_comment="# MirageMakers AI OSS Auto Backup"
    
    # 检查是否已存在相同的定时任务
    if crontab -l 2>/dev/null | grep -q "oss_backup.sh"; then
        log_warn "OSS备份定时任务已存在，将更新..."
        # 删除现有任务
        crontab -l 2>/dev/null | grep -v "oss_backup.sh" | grep -v "MirageMakers AI OSS Auto Backup" | crontab -
    fi
    
    # 添加新的定时任务
    (crontab -l 2>/dev/null; echo "$cron_comment"; echo "$cron_entry") | crontab -
    
    log_success "定时任务安装成功"
    log_info "备份时间: 每天 $backup_time"
    log_info "备份脚本: $OSS_BACKUP_SCRIPT"
    log_info "日志目录: $SCRIPT_DIR/logs/"
    
    # 验证安装
    log_info "验证定时任务..."
    if crontab -l | grep -q "oss_backup.sh"; then
        log_success "定时任务验证通过"
    else
        log_error "定时任务验证失败"
        return 1
    fi
}

# 卸载定时任务
uninstall_crontab() {
    log_info "卸载OSS自动备份定时任务..."
    
    # 检查是否存在定时任务
    if ! crontab -l 2>/dev/null | grep -q "oss_backup.sh"; then
        log_warn "未找到OSS备份定时任务"
        return 0
    fi
    
    # 删除定时任务
    crontab -l 2>/dev/null | grep -v "oss_backup.sh" | grep -v "MirageMakers AI OSS Auto Backup" | crontab -
    
    log_success "定时任务卸载成功"
}

# 查看定时任务状态
show_crontab_status() {
    log_info "检查OSS自动备份定时任务状态..."
    
    # 检查cron服务状态
    if systemctl is-active --quiet cron 2>/dev/null || systemctl is-active --quiet crond 2>/dev/null; then
        log_success "Cron服务运行正常"
    else
        log_warn "Cron服务可能未运行"
    fi
    
    # 显示相关的定时任务
    echo ""
    echo "=== 当前用户的定时任务 ==="
    if crontab -l 2>/dev/null | grep -E "(oss_backup|MirageMakers)"; then
        crontab -l 2>/dev/null | grep -E "(oss_backup|MirageMakers)" | while IFS= read -r line; do
            if [[ "$line" == \#* ]]; then
                echo -e "${BLUE}$line${NC}"
            else
                echo -e "${GREEN}$line${NC}"
                
                # 解析时间
                local minute=$(echo "$line" | awk '{print $1}')
                local hour=$(echo "$line" | awk '{print $2}')
                echo -e "${YELLOW}  → 执行时间: 每天 ${hour}:${minute}${NC}"
            fi
        done
    else
        echo "未找到OSS备份定时任务"
    fi
    
    # 显示最近的备份日志
    echo ""
    echo "=== 最近的备份日志 ==="
    local log_files=($(find "$SCRIPT_DIR/logs" -name "oss_backup_*.log" -type f 2>/dev/null | sort -r | head -3))
    
    if [[ ${#log_files[@]} -gt 0 ]]; then
        for log_file in "${log_files[@]}"; do
            local log_date=$(basename "$log_file" .log | sed 's/oss_backup_//')
            local log_size=$(du -h "$log_file" | cut -f1)
            echo "  $(basename "$log_file") (${log_size})"
            
            # 显示最后几行
            if [[ -f "$log_file" ]]; then
                echo "    最后状态: $(tail -1 "$log_file" 2>/dev/null || echo "无法读取")"
            fi
        done
    else
        echo "  未找到备份日志文件"
    fi
    
    # 检查OSS配置
    echo ""
    echo "=== OSS配置状态 ==="
    local oss_config="../oss_config.json"
    if [[ -f "$oss_config" ]]; then
        log_success "OSS配置文件存在"
        
        # 验证配置格式
        if python3 -c "import json; json.load(open('$oss_config'))" 2>/dev/null; then
            log_success "OSS配置文件格式正确"
            
            # 显示配置信息（隐藏敏感信息）
            local bucket=$(python3 -c "import json; config=json.load(open('$oss_config')); print(config.get('bucket', 'N/A'))" 2>/dev/null)
            local endpoint=$(python3 -c "import json; config=json.load(open('$oss_config')); print(config.get('endpoint', 'N/A'))" 2>/dev/null)
            echo "    存储桶: $bucket"
            echo "    端点: $endpoint"
        else
            log_error "OSS配置文件格式错误"
        fi
    else
        log_error "OSS配置文件不存在: $oss_config"
    fi
}

# 测试OSS备份脚本
test_oss_backup() {
    log_info "测试OSS备份脚本..."
    
    # 检查依赖
    if ! check_dependencies; then
        return 1
    fi
    
    # 运行试运行模式
    log_info "执行试运行测试..."
    if cd "$SCRIPT_DIR" && ./oss_backup.sh --dry-run; then
        log_success "OSS备份脚本测试通过"
        
        echo ""
        log_info "如果要执行实际备份测试，请运行:"
        echo "  cd $SCRIPT_DIR && ./oss_backup.sh --keep-local"
        
    else
        log_error "OSS备份脚本测试失败"
        return 1
    fi
}

# 主函数
main() {
    local action=""
    local backup_time="21:00"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --install)
                action="install"
                shift
                ;;
            --uninstall)
                action="uninstall"
                shift
                ;;
            --status)
                action="status"
                shift
                ;;
            --test)
                action="test"
                shift
                ;;
            --time)
                backup_time="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 默认操作
    if [[ -z "$action" ]]; then
        action="status"
    fi
    
    echo "=== OSS自动备份定时任务管理 ==="
    echo ""
    
    # 执行操作
    case "$action" in
        install)
            check_dependencies || exit 1
            install_crontab "$backup_time" || exit 1
            ;;
        uninstall)
            uninstall_crontab || exit 1
            ;;
        status)
            show_crontab_status
            ;;
        test)
            test_oss_backup || exit 1
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    log_success "操作完成"
}

# 错误处理
trap 'log_error "脚本执行失败"' ERR

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
