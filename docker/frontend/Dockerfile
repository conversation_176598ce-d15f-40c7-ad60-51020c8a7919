# 生产环境 Dockerfile - 多阶段构建，优化镜像大小和构建速度
FROM node:20-alpine AS deps
# 安装必要的系统依赖
RUN apk add --no-cache libc6-compat

WORKDIR /app

# 复制package文件
COPY package.json ./

# 优化npm配置
RUN npm config set registry https://registry.npmmirror.com
RUN npm config set fund false
RUN npm config set audit-level moderate

# 完全在容器内重新生成lock文件和安装依赖
RUN npm install --only=production --ignore-scripts --no-package-lock
RUN npm install --only=production --ignore-scripts
RUN npm cache clean --force

# 构建阶段
FROM node:20-alpine AS builder
WORKDIR /app

# 接收构建时参数
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ARG NEXT_PUBLIC_API_URL

# 复制生成的依赖和lock文件
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/package-lock.json ./package-lock.json
COPY . .

# 安装所有依赖（包括devDependencies）
RUN npm install --ignore-scripts

# 构建应用 - 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
RUN npm run build

# 生产运行阶段
FROM node:20-alpine AS runner
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache wget curl

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制构建结果
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 设置文件权限
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露端口
EXPOSE 3000

# 环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# 启动应用
CMD ["node", "server.js"]