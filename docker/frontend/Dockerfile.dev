FROM node:20-alpine

# 安装系统依赖
RUN apk add --no-cache libc6-compat curl

WORKDIR /app

# 首先复制package文件
COPY package*.json ./

# 设置npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖（包括devDependencies）
RUN npm install

# 复制项目源码文件
COPY . .

# 接收构建时参数
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ARG NEXT_PUBLIC_API_URL

# 设置开发环境变量
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL

# 暴露端口
EXPOSE 3000

# 开发环境健康检查（更宽松的配置）
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=5 \
    CMD curl -f --max-time 30 http://localhost:3000/ || exit 1

# 开发环境启动命令（支持热重载和调试）
CMD ["npm", "run", "dev"] 