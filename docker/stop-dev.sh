#!/bin/bash

# ==========================================
# MirageMakers AI 开发环境停止脚本 v2.0
# 安全停止开发环境服务
# ==========================================

# 脚本版本
SCRIPT_VERSION="2.0.0"

# 严格模式
set -euo pipefail

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 配置变量
CLEAN_DATA=false
COMPOSE_CMD=""

# 显示帮助信息
show_help() {
    cat << EOF
MirageMakers AI 开发环境停止脚本 v${SCRIPT_VERSION}

用法: $0 [选项]

选项:
  --clean         清理所有数据（包括数据库和卷）
  --help, -h      显示此帮助信息

默认行为:
  - 安全停止所有开发容器
  - 保留数据库数据和配置
  - 保留Docker卷和网络

示例:
  $0              # 停止开发服务，保留数据
  $0 --clean      # 停止服务并清理所有数据

注意:
  --clean 选项将删除所有开发环境数据，谨慎使用！
EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                CLEAN_DATA=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Docker Compose
check_docker_compose() {
    if ! docker compose version &> /dev/null && ! docker-compose version &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    # 设置Docker Compose命令
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose -f docker-compose.dev.yml"
    else
        COMPOSE_CMD="docker-compose -f docker-compose.dev.yml"
    fi
    
    # 检查配置文件
    if [[ ! -f "docker-compose.dev.yml" ]]; then
        log_error "开发环境配置文件不存在: docker-compose.dev.yml"
        exit 1
    fi
}

# 停止开发服务
stop_dev_services() {
    log_step "停止开发环境服务..."
    
    # 检查是否有运行的容器
    if ! $COMPOSE_CMD ps --services --filter "status=running" | grep -q .; then
        log_warn "没有运行的开发环境容器"
        return 0
    fi
    
    # 显示当前运行的服务
    log_info "当前运行的开发服务:"
    $COMPOSE_CMD ps
    echo ""
    
    if [[ "$CLEAN_DATA" == "true" ]]; then
        log_warn "⚠️  --clean 模式：将删除所有开发环境数据！"
        echo -n "确认继续？输入 'YES' 继续，其他任意键取消: "
        read -r confirm
        
        if [[ "$confirm" != "YES" ]]; then
            log_info "操作已取消"
            exit 0
        fi
        
        log_step "停止服务并清理所有数据..."
        $COMPOSE_CMD down --volumes --remove-orphans
        
        # 清理开发环境特定的镜像
        log_info "清理开发环境Docker镜像..."
        docker images --filter "reference=*dev*" --format "table {{.Repository}}:{{.Tag}}" | grep -v REPOSITORY | while read image; do
            if [[ -n "$image" ]]; then
                docker rmi "$image" 2>/dev/null || log_warn "无法删除镜像: $image"
            fi
        done
        
        # 清理未使用的资源
        log_info "清理未使用的Docker资源..."
        docker system prune -f
        
        log_success "开发环境完全清理完成"
    else
        log_step "安全停止开发服务（保留数据）..."
        $COMPOSE_CMD down
        log_success "开发环境服务已停止，数据已保留"
    fi
}

# 验证停止状态
verify_stop() {
    log_step "验证停止状态..."
    
    # 检查是否还有开发容器运行
    local running_containers=$(docker ps --filter "name=MirageMakers-.*-dev" --format "{{.Names}}" | wc -l)
    
    if [[ $running_containers -eq 0 ]]; then
        log_success "所有开发环境容器已停止"
    else
        log_warn "仍有 $running_containers 个开发容器在运行:"
        docker ps --filter "name=MirageMakers-.*-dev" --format "table {{.Names}}\t{{.Status}}"
    fi
    
    # 检查端口占用
    local ports_in_use=""
    if netstat -tuln 2>/dev/null | grep -q ":3000 " || ss -tuln 2>/dev/null | grep -q ":3000 "; then
        ports_in_use="$ports_in_use 3000"
    fi
    if netstat -tuln 2>/dev/null | grep -q ":8000 " || ss -tuln 2>/dev/null | grep -q ":8000 "; then
        ports_in_use="$ports_in_use 8000"
    fi
    if netstat -tuln 2>/dev/null | grep -q ":5679 " || ss -tuln 2>/dev/null | grep -q ":5679 "; then
        ports_in_use="$ports_in_use 5679"
    fi
    
    if [[ -n "$ports_in_use" ]]; then
        log_warn "以下开发端口仍在使用:$ports_in_use"
        log_info "可能有其他进程占用这些端口"
    else
        log_success "开发环境端口已释放"
    fi
}

# 显示停止信息
show_stop_info() {
    echo ""
    echo "=== MirageMakers AI 开发环境停止完成 ==="
    echo "停止时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "脚本版本: $SCRIPT_VERSION"
    
    if [[ "$CLEAN_DATA" == "true" ]]; then
        echo "停止模式: 完全清理"
        echo ""
        echo "已清理内容:"
        echo "  ✓ 所有开发容器"
        echo "  ✓ 所有开发数据卷"
        echo "  ✓ 开发环境镜像"
        echo "  ✓ 未使用的Docker资源"
        echo ""
        echo "下次启动请使用: ./start-dev.sh --init"
    else
        echo "停止模式: 安全停止（保留数据）"
        echo ""
        echo "已保留内容:"
        echo "  ✓ 数据库数据"
        echo "  ✓ Redis数据"
        echo "  ✓ 用户上传文件"
        echo "  ✓ 日志文件"
        echo ""
        echo "重新启动请使用: ./start-dev.sh"
    fi
    
    echo ""
    echo "=== 其他操作 ==="
    echo "重新启动开发环境: ./start-dev.sh"
    echo "查看生产环境: ./manage_services.sh --prod status"
    echo "完全重新初始化: ./start-dev.sh --init"
    echo ""
}

# 主函数
main() {
    echo "=== MirageMakers AI 开发环境停止脚本 v${SCRIPT_VERSION} ==="
    echo ""
    
    # 解析参数
    parse_arguments "$@"
    
    # 检查环境
    check_docker_compose
    
    # 执行停止
    stop_dev_services
    verify_stop
    show_stop_info
    
    log_success "开发环境停止完成！"
}

# 错误处理
trap 'log_error "停止脚本执行失败，请检查错误信息"' ERR

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 