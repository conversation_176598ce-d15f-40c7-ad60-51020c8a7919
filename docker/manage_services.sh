#!/bin/bash

# ==========================================
# MirageMakers AI 容器服务管理脚本 v3.0
# 支持生产和开发环境的统一管理
# 
# 系统架构：
# - 6个Docker容器：nginx, mysql, redis, django, celery, frontend
# - 混合架构：同步处理(图像/聊天) + 异步处理(视频)
# - 健康检查和自动重启机制
# - 完整的API测试和诊断功能
# 
# 优化特性 (January 2025 v3.0):
# - 统一支持生产和开发环境管理
# - 移除开发环境的备份功能
# - 优化环境检测和服务管理
# - 简化命令行参数和用户体验
# ==========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 全局变量
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 环境配置
ENVIRONMENT="prod"  # 默认生产环境
COMPOSE_FILE="docker-compose.yml"
CONTAINER_PREFIX="MirageMakers"

# 加载环境变量
load_env_variables() {
    if [[ -f ".env" ]]; then
        log_info "加载环境变量配置..."
        set -a
        source .env
        set +a
        log_success "环境变量加载完成"
    else
        log_error ".env 文件不存在，请先创建配置文件"
        exit 1
    fi
}

# 获取本机IP
get_local_ip() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
    else
        LOCAL_IP=$(hostname -I | awk '{print $1}')
    fi
    
    if [[ -z "$LOCAL_IP" ]]; then
        LOCAL_IP="127.0.0.1"
    fi
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 测试结果记录函数（优化版）
record_test() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    case "$result" in
        "PASS")
            echo -e "  ✅ $test_name: ${GREEN}通过${NC} - $details"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            ;;
        "FAIL")
            echo -e "  ❌ $test_name: ${RED}失败${NC} - $details"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            ;;
        "WARN")
            echo -e "  ⚠️  $test_name: ${YELLOW}警告${NC} - $details"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            ;;
        "INFO")
            echo -e "  ℹ️  $test_name: ${CYAN}信息${NC} - $details"
            # INFO级别不计入失败，但也不计入通过
            ;;
        *)
            echo -e "  ⚠️  $test_name: ${YELLOW}未知${NC} - $details"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            ;;
    esac
}

# 服务列表
get_services() {
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        echo "nginx mysql redis django daphne celery frontend"
    else
        echo "nginx mysql redis django daphne celery frontend"
    fi
}

# 获取容器名称
get_container_name() {
    local service="$1"
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        echo "${CONTAINER_PREFIX}-${service}-dev"
    else
        echo "${CONTAINER_PREFIX}-${service}"
    fi
}

# 获取Docker Compose命令
get_compose_cmd() {
    local cmd=""
    if docker compose version &> /dev/null; then
        cmd="docker compose"
    else
        cmd="docker-compose"
    fi
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        echo "$cmd -f docker-compose.dev.yml"
    else
        echo "$cmd -f docker-compose.yml"
    fi
}

# 显示帮助信息
show_help() {
    echo ""
    echo "MirageMakers AI 容器服务管理脚本 v3.0"
    echo ""
    echo "用法: $0 [环境选项] <命令> [服务名]"
    echo ""
    echo "环境选项:"
    echo "  --dev        - 管理开发环境 (docker-compose.dev.yml)"
    echo "  --prod       - 管理生产环境 (docker-compose.yml) [默认]"
    echo ""
    echo "命令:"
    echo "  status       - 查看所有服务状态"
    echo "  start        - 启动所有服务或指定服务"
    echo "  stop         - 停止所有服务或指定服务"
    echo "  restart      - 重启所有服务或指定服务"
    echo "  logs         - 查看服务日志"
    echo "  health       - 执行智能健康检查和诊断"
    echo "  security     - 执行安全配置测试"
    echo "  diagnose     - 深度诊断和故障排查"
    echo "  test-auth    - 测试认证功能（注册、登录、验证）"
    echo "  test-api     - 测试所有API端点"
    echo "  test-all     - 执行所有测试（健康、安全、认证、API）"
    echo "  clean        - 清理所有容器和卷"
    echo "  fix          - 修复CSRF/CORS和前后端对接问题"
    echo "  rebuild      - 重新构建指定服务或所有服务"
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        echo "  backup       - 执行数据库备份（仅生产环境）"
        echo "  restore      - 恢复数据库数据（仅生产环境）"
    fi
    echo ""
    echo "服务名 (可选):"
    echo "  nginx        - Nginx 反向代理/SSL终端"
    echo "  mysql        - MySQL 数据库"
    echo "  redis        - Redis 缓存"
    echo "  django       - Django 后端"
    echo "  celery       - Celery 任务队列"
    echo "  frontend     - Next.js 前端"
    echo ""
    echo "示例:"
    echo "  # 生产环境操作"
    echo "  $0 status"
    echo "  $0 health"
    echo "  $0 restart django"
    echo "  $0 rebuild django"
    echo "  $0 backup"
    echo ""
    echo "  # 开发环境操作"
    echo "  $0 --dev status"
    echo "  $0 --dev health"
    echo "  $0 --dev logs frontend"
    echo "  $0 --dev restart django"
    echo ""
    echo "  # 测试功能"
    echo "  $0 test-auth"
    echo "  $0 --dev test-all"
    echo ""
    echo "环境差异："
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        echo "  - 开发环境：无备份功能，专注开发调试"
        echo "  - 容器名称：xxx-dev"
        echo "  - 配置文件：docker-compose.dev.yml"
    else
        echo "  - 生产环境：包含备份和恢复功能"
        echo "  - 容器名称：xxx"
        echo "  - 配置文件：docker-compose.yml"
    fi
    echo ""
}

# 检查服务状态
check_status() {
    local compose_cmd=$(get_compose_cmd)
    log_step "检查 $ENVIRONMENT 环境容器状态..."
    
    # 抑制Docker Compose警告信息，只显示容器状态
    $compose_cmd ps 2>/dev/null
    echo ""
    
    # 检查健康状态
    local healthy_count=0
    local total_count=0
    local services=($(get_services))
    
    for service in "${services[@]}"; do
        total_count=$((total_count + 1))
        local container_name=$(get_container_name "$service")
        
        # 查找实际运行的容器（支持模糊匹配）
        local actual_container=$(docker ps --filter "name=$container_name" --format "{{.Names}}" | head -1)
        
        if [[ -n "$actual_container" ]]; then
            # 获取容器状态和健康状态
            local container_status=$(docker ps --filter "name=$container_name" --format "{{.Status}}" | head -1)
            local health_status=$(docker inspect --format='{{if .State.Health}}{{.State.Health.Status}}{{else}}no-health-check{{end}}' "$actual_container" 2>/dev/null)
            
            case "$health_status" in
                "healthy")
                    log_success "$service: 健康运行 ($health_status)"
                    healthy_count=$((healthy_count + 1))
                    ;;
                "unhealthy")
                    log_warn "$service: 运行但不健康 ($health_status)"
                    ;;
                "starting")
                    log_info "$service: 启动中 ($health_status)"
                    ;;
                "no-health-check")
                    # 检查容器是否正常运行（基于状态）
                    if [[ "$container_status" == *"Up"* ]]; then
                        log_info "$service: 运行中（无健康检查）"
                        healthy_count=$((healthy_count + 1))
                    else
                        log_warn "$service: 状态异常 ($container_status)"
                    fi
                    ;;
                *)
                    log_warn "$service: 未知状态 ($health_status)"
                    ;;
            esac
        else
            log_error "$service: 未运行"
        fi
    done
    
    echo ""
    echo "=== $ENVIRONMENT 环境状态总结 ==="
    echo "健康服务: $healthy_count/$total_count"
    if [[ $healthy_count -eq $total_count ]]; then
        log_success "所有服务运行正常"
    else
        log_warn "部分服务存在问题，建议执行健康检查: $0 --$ENVIRONMENT health"
    fi
}

# 启动服务
start_services() {
    local service="$1"
    local compose_cmd=$(get_compose_cmd)
    
    if [[ -n "$service" ]]; then
        log_step "启动 $ENVIRONMENT 环境服务: $service"
        $compose_cmd start "$service" 2>/dev/null
    else
        log_step "启动所有 $ENVIRONMENT 环境服务..."
        $compose_cmd up -d 2>/dev/null
    fi
    
    log_success "$ENVIRONMENT 环境服务启动完成"
}

# 停止服务
stop_services() {
    local service="$1"
    local compose_cmd=$(get_compose_cmd)
    
    if [[ -n "$service" ]]; then
        log_step "停止 $ENVIRONMENT 环境服务: $service"
        $compose_cmd stop "$service" 2>/dev/null
    else
        log_step "停止所有 $ENVIRONMENT 环境服务..."
        $compose_cmd down 2>/dev/null
    fi
    
    log_success "$ENVIRONMENT 环境服务停止完成"
}

# 重启服务
restart_services() {
    local service="$1"
    local compose_cmd=$(get_compose_cmd)
    
    if [[ -n "$service" ]]; then
        log_step "重启 $ENVIRONMENT 环境服务: $service"
        $compose_cmd restart "$service" 2>/dev/null
    else
        log_step "重启所有 $ENVIRONMENT 环境服务..."
        $compose_cmd restart 2>/dev/null
    fi
    
    log_success "$ENVIRONMENT 环境服务重启完成"
}

# 重新构建服务
rebuild_services() {
    local service="$1"
    local compose_cmd=$(get_compose_cmd)
    
    if [[ -n "$service" ]]; then
        log_step "重新构建 $ENVIRONMENT 环境服务: $service"
        
        # 停止指定服务
        log_info "停止服务: $service"
        $compose_cmd stop "$service" 2>/dev/null
        
        # 重新构建镜像（强制不使用缓存）
        log_info "重新构建镜像: $service (不使用缓存)"
        $compose_cmd build --no-cache "$service"
        
        # 启动服务
        log_info "启动重构后的服务: $service"
        $compose_cmd up -d "$service" 2>/dev/null
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 10
        
        # 检查服务状态
        local container_name=$(get_container_name "$service")
        local actual_container=$(docker ps --filter "name=$container_name" --format "{{.Names}}" | head -1)
        
        if [[ -n "$actual_container" ]]; then
            local container_status=$(docker ps --filter "name=$container_name" --format "{{.Status}}" | head -1)
            if [[ "$container_status" == *"Up"* ]]; then
                log_success "服务 $service 重构并启动成功"
            else
                log_warn "服务 $service 重构完成但状态异常: $container_status"
                log_info "建议查看日志: $0 logs $service"
            fi
        else
            log_error "服务 $service 重构后未能成功启动"
            log_info "建议查看日志: $0 logs $service"
            return 1
        fi
        
    else
        log_step "重新构建所有 $ENVIRONMENT 环境服务..."
        
        # 停止所有服务
        log_info "停止所有服务"
        $compose_cmd down 2>/dev/null
        
        # 重新构建所有镜像
        log_info "重新构建所有镜像 (不使用缓存)"
        $compose_cmd build --no-cache
        
        # 启动所有服务
        log_info "启动所有重构后的服务"
        $compose_cmd up -d 2>/dev/null
        
        # 等待服务启动
        log_info "等待所有服务启动..."
        sleep 30
        
        log_success "所有服务重构并启动完成"
        log_info "建议执行健康检查: $0 health"
    fi
}

# 查看日志
view_logs() {
    local service="$1"
    local compose_cmd=$(get_compose_cmd)
    
    if [[ -n "$service" ]]; then
        log_step "查看 $ENVIRONMENT 环境服务日志: $service"
        $compose_cmd logs -f --tail=50 "$service" 2>/dev/null
    else
        log_step "查看 $ENVIRONMENT 环境所有服务日志..."
        $compose_cmd logs --tail=50 -f 2>/dev/null
    fi
}

# 智能健康检查和诊断
intelligent_health_check() {
    log_step "🏥 执行智能健康检查和诊断..."
    get_local_ip
    
    # 根据环境选择合适的访问URL
    local target_url
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        # 开发环境使用localhost进行健康检查
        target_url="http://localhost"
    else
        target_url="${PROTOCOL}://${PRIMARY_DOMAIN}"
    fi
    local issues_found=false
    local suggestions=()
    
    echo ""
    log_step "🔍 1. 检查容器运行状态..."
    
    # 检查容器状态 - 修复名称匹配问题
    for service in "${SERVICES[@]}"; do
        local container_pattern
        if [[ "$ENVIRONMENT" == "dev" ]]; then
            container_pattern="MirageMakers-$service-dev"
        else
            container_pattern="MirageMakers-$service"
        fi
        
        # 使用模糊匹配找到容器，处理名称前缀问题
        local container_id=$(docker ps --filter "name=$container_pattern" --filter "status=running" --format "{{.ID}}" | head -1)
        
        if [[ -n "$container_id" ]]; then
            local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_id" 2>/dev/null || echo "no-health-check")
            
            if [[ "$health_status" == "healthy" ]]; then
                record_test "$service 容器状态" "PASS" "健康运行"
            elif [[ "$health_status" == "unhealthy" ]]; then
                record_test "$service 容器状态" "FAIL" "运行但不健康"
                issues_found=true
                suggestions+=("检查 $service 容器日志: $COMPOSE_CMD logs $service")
            else
                record_test "$service 容器状态" "PASS" "运行中"
            fi
        else
            record_test "$service 容器状态" "FAIL" "容器未运行"
            issues_found=true
            suggestions+=("启动 $service 服务: $COMPOSE_CMD up -d $service")
        fi
    done
    
    echo ""
    log_step "🔍 2. 检查网络连接和端口..."
    
    # 检查端口监听
    local ports=("80:nginx HTTP" "443:nginx HTTPS" "${FRONTEND_PORT}:frontend" "${API_PORT}:django" "8001:daphne WebSocket" "${MYSQL_PORT}:mysql" "${REDIS_PORT}:redis")
    for port_info in "${ports[@]}"; do
        local port=$(echo "$port_info" | cut -d: -f1)
        local service_name=$(echo "$port_info" | cut -d: -f2)
        
        # 修复macOS兼容性：使用lsof和实际连接测试
        local port_listening=false
        
        # 方法1：使用lsof检查端口监听（macOS兼容）
        if lsof -i ":$port" -sTCP:LISTEN >/dev/null 2>&1; then
            port_listening=true
        # 方法2：使用netstat（兼容不同系统格式）
        elif netstat -an 2>/dev/null | grep -E "(:|\.)${port}[[:space:]].*LISTEN" >/dev/null; then
            port_listening=true
        # 方法3：直接测试连接（最可靠的方法）
        elif timeout 2 bash -c "</dev/tcp/localhost/$port" >/dev/null 2>&1; then
            port_listening=true
        fi
        
        if [[ "$port_listening" == "true" ]]; then
            record_test "$service_name 端口监听" "PASS" "端口 $port 正在监听"
        else
            record_test "$service_name 端口监听" "FAIL" "端口 $port 未监听"
            issues_found=true
            if [[ "$port" == "80" ]] || [[ "$port" == "443" ]]; then
                suggestions+=("检查 nginx 容器是否运行: docker-compose ps nginx")
                suggestions+=("检查 nginx 配置: docker exec MirageMakers-nginx nginx -t")
            fi
        fi
    done
    
    echo ""
    log_step "🔍 3. 检查 HTTPS 访问 (主要问题排查)..."
    
    # 检查HTTPS主页访问 - 只获取状态码和基本信息，避免HTML内容输出
    local https_response=$(curl -k -s -o /dev/null -w "%{http_code}|%{content_type}|%{size_download}" --connect-timeout 10 --max-time 20 "$target_url/" 2>/dev/null || echo "000||0")
    local http_code=$(echo "$https_response" | cut -d'|' -f1)
    local content_type=$(echo "$https_response" | cut -d'|' -f2)
    local content_length=$(echo "$https_response" | cut -d'|' -f3)
    
    if [[ "$http_code" == "200" ]]; then
        # 检查内容长度而不是实际内容
        if [[ $content_length -lt 100 ]]; then
            record_test "HTTPS 主页访问" "FAIL" "页面内容过少 ($content_length 字节) - 可能是白页问题"
            issues_found=true
            suggestions+=("页面显示白页，可能原因:")
            suggestions+=("1. 前端服务未正常启动: docker-compose logs frontend")
            suggestions+=("2. nginx 配置错误: docker exec MirageMakers-nginx nginx -t")
            suggestions+=("3. 前端构建失败: docker exec MirageMakers-frontend npm run build")
            suggestions+=("4. API 连接问题: 检查环境变量配置")
        elif [[ "$content_type" == *"text/html"* ]] && [[ $content_length -gt 1000 ]]; then
            record_test "HTTPS 主页访问" "PASS" "页面正常加载 ($content_length 字节)"
        else
            record_test "HTTPS 主页访问" "WARN" "页面可访问但内容可能异常 ($content_length 字节, $content_type)"
            suggestions+=("检查页面内容类型是否符合预期")
        fi
    elif [[ "$http_code" == "502" ]]; then
        record_test "HTTPS 主页访问" "FAIL" "502 Bad Gateway - 后端服务问题"
        issues_found=true
        suggestions+=("502 错误解决方案:")
        suggestions+=("1. 检查前端容器状态: docker-compose ps frontend")
        suggestions+=("2. 检查前端日志: docker-compose logs frontend")
        suggestions+=("3. 重启前端服务: docker-compose restart frontend")
    elif [[ "$http_code" == "000" ]]; then
        record_test "HTTPS 主页访问" "FAIL" "连接超时或连接被拒绝"
        issues_found=true
        suggestions+=("连接问题解决方案:")
        suggestions+=("1. 检查 nginx 容器状态: docker-compose ps nginx")
        suggestions+=("2. 检查防火墙设置: sudo ufw status")
        suggestions+=("3. 检查 SSL 证书: openssl s_client -connect ${LOCAL_IP}:443 -servername ${LOCAL_IP}")
    else
        record_test "HTTPS 主页访问" "FAIL" "HTTP $http_code 错误，检查 nginx 配置和日志"
        issues_found=true
    fi
    
    echo ""
    log_step "🔍 4. 检查关键 API 端点..."
    
    # 4.1 基础健康检查端点
    local health_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url/health/" 2>/dev/null || echo "000")
    if [[ "$health_response" == "200" ]]; then
        if curl -k -s --connect-timeout 5 "$target_url/health/" 2>/dev/null | grep -q "healthy" 2>/dev/null; then
            record_test "系统健康检查API" "PASS" "Django API 正常响应"
        else
            record_test "系统健康检查API" "WARN" "API 可访问但响应内容异常"
            issues_found=true
            suggestions+=("Django API 响应异常，检查数据库连接和配置")
        fi
    else
        record_test "系统健康检查API" "FAIL" "Django API 异常 (HTTP $health_response)"
        issues_found=true
        suggestions+=("Django API 问题：检查容器状态和日志")
    fi
    
    # 4.2 认证相关API检查（优化版）
    # GET方法测试的端点
    local get_auth_endpoints=(
        "api/auth/social-success/providers/:社交登录配置"
        "api/user/profile/:用户资料"
    )
    
    for endpoint_info in "${get_auth_endpoints[@]}"; do
        local endpoint=$(echo "$endpoint_info" | cut -d: -f1)
        local desc=$(echo "$endpoint_info" | cut -d: -f2)
        local auth_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url/$endpoint" 2>/dev/null || echo "000")
        case "$auth_response" in
            "200"|"401"|"403")
                record_test "$desc" "PASS" "API可访问 (HTTP $auth_response)"
                ;;
            "404")
                record_test "$desc" "INFO" "端点不存在，可能未实现 (HTTP 404)"
                ;;
            "500"|"502"|"503")
                record_test "$desc" "FAIL" "服务器错误 (HTTP $auth_response)"
                issues_found=true
                suggestions+=("认证API错误：检查Django容器和数据库连接")
                ;;
            "000")
                record_test "$desc" "FAIL" "连接失败"
                issues_found=true
                ;;
            *)
                record_test "$desc" "WARN" "异常响应 (HTTP $auth_response)"
                ;;
        esac
    done
    
    # POST方法测试的端点（使用POST方法）
    local logout_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 -X POST "$target_url/api/auth/logout/" 2>/dev/null || echo "000")
    case "$logout_response" in
        "200")
            record_test "用户注销API" "PASS" "注销成功 (HTTP $logout_response)"
            ;;
        "400"|"401"|"403")
            record_test "用户注销API" "PASS" "需要认证，API保护正常 (HTTP $logout_response)"
            ;;
        "404")
            record_test "用户注销API" "INFO" "注销端点不存在，可能未实现"
            ;;
        "405")
            record_test "用户注销API" "INFO" "方法不允许，API存在但需要特定参数"
            ;;
        "500"|"502"|"503")
            record_test "用户注销API" "FAIL" "服务器错误 (HTTP $logout_response)"
            issues_found=true
            suggestions+=("注销API错误：检查Django认证配置和日志")
            ;;
        "000")
            record_test "用户注销API" "FAIL" "连接失败"
            issues_found=true
            ;;
        *)
            record_test "用户注销API" "WARN" "异常响应 (HTTP $logout_response)"
            ;;
    esac
    
    # 4.3 邮件系统API检查（多路径测试）
    local email_endpoints=(
        "api/auth/password/reset/:密码重置邮件"
        "api/auth/password/reset/:密码重置邮件V2"
        "api/auth/verify-email/:邮箱验证"
    )
    
    local email_test_found=false
    for endpoint_info in "${email_endpoints[@]}"; do
        local endpoint=$(echo "$endpoint_info" | cut -d: -f1)
        local desc=$(echo "$endpoint_info" | cut -d: -f2)
        
        # 尝试不同的API路径
        local email_test_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 -X POST "$target_url/$endpoint" -H "Content-Type: application/json" -d '{"email":"<EMAIL>"}' 2>/dev/null || echo "000")
        
        case "$email_test_response" in
            "200"|"400"|"429")
                record_test "$desc" "PASS" "邮件API可访问 (HTTP $email_test_response)"
                email_test_found=true
                break
                ;;
            "404")
                # 404是正常的，继续尝试下一个路径
                continue
                ;;
            "500"|"502"|"503")
                record_test "$desc" "FAIL" "邮件服务异常 (HTTP $email_test_response)"
                issues_found=true
                suggestions+=("邮件系统问题：检查SMTP配置和Django邮件设置")
                break
                ;;
            "000")
                record_test "$desc" "FAIL" "邮件API连接失败"
                issues_found=true
                break
                ;;
        esac
    done
    
    # 如果所有邮件API都返回404，标记为信息级别
    if [[ "$email_test_found" == false ]]; then
        record_test "邮件系统API" "INFO" "邮件API端点未实现或路径变更"
    fi
    
    # 4.4 文件上传/媒体API检查
    local media_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url/media/" 2>/dev/null || echo "000")
    case "$media_response" in
        "200"|"403"|"404")
            record_test "媒体文件API" "PASS" "媒体服务可访问 (HTTP $media_response)"
            ;;
        "500"|"502"|"503")
            record_test "媒体文件API" "FAIL" "媒体服务异常 (HTTP $media_response)"
            issues_found=true
            suggestions+=("媒体文件问题：检查文件存储配置和权限")
            ;;
        "000")
            record_test "媒体文件API" "FAIL" "媒体API连接失败"
            issues_found=true
            ;;
        *)
            record_test "媒体文件API" "INFO" "媒体API响应 (HTTP $media_response)"
            ;;
    esac
    
    # 4.5 Django Admin检查
    local admin_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url/admin/" 2>/dev/null || echo "000")
    case "$admin_response" in
        "200"|"302")
            record_test "Django Admin" "PASS" "管理界面可访问 (HTTP $admin_response)"
            ;;
        "500"|"502"|"503")
            record_test "Django Admin" "FAIL" "管理界面异常 (HTTP $admin_response)"
            issues_found=true
            suggestions+=("Django Admin问题：检查静态文件和数据库连接")
            ;;
        "000")
            record_test "Django Admin" "FAIL" "管理界面连接失败"
            issues_found=true
            ;;
        *)
            record_test "Django Admin" "WARN" "管理界面异常响应 (HTTP $admin_response)"
            ;;
    esac
    
    # 检查数据库连接 - 修复容器名称和命令
    local mysql_container
    local redis_container
    local django_container
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        mysql_container="MirageMakers-mysql-dev"
        redis_container="MirageMakers-redis-dev"
        django_container="MirageMakers-django-dev"
    else
        mysql_container="MirageMakers-mysql"
        redis_container="MirageMakers-redis"
        django_container="MirageMakers-django"
    fi
    
    # 动态查找实际的容器名称（处理前缀问题）
    local actual_django_container=$(docker ps --filter "name=$django_container" --format "{{.Names}}" | head -1)
    local actual_redis_container=$(docker ps --filter "name=$redis_container" --format "{{.Names}}" | head -1)
    
    # 通过Django检查数据库连接（更准确的方法）
    if [[ -n "$actual_django_container" ]] && docker exec "$actual_django_container" python manage.py check --database default >/dev/null 2>&1; then
        record_test "数据库连接" "PASS" "Django数据库连接正常"
    else
        record_test "数据库连接" "FAIL" "Django数据库连接异常"
        issues_found=true
        suggestions+=("数据库问题解决方案:")
        suggestions+=("1. 检查 MySQL 容器日志: $COMPOSE_CMD logs mysql")
        suggestions+=("2. 重启 MySQL 服务: $COMPOSE_CMD restart mysql")
        suggestions+=("3. 检查数据库配置: 验证环境变量")
    fi
    
    # 检查 Redis 连接
    if [[ -n "$actual_redis_container" ]] && docker exec "$actual_redis_container" redis-cli ping 2>/dev/null | grep -q "PONG"; then
        record_test "Redis 缓存" "PASS" "Redis 连接正常"
    else
        record_test "Redis 缓存" "FAIL" "Redis 连接异常"
        issues_found=true
        suggestions+=("Redis 问题解决方案:")
        suggestions+=("1. 检查 Redis 容器: $COMPOSE_CMD logs redis")
        suggestions+=("2. 重启 Redis 服务: $COMPOSE_CMD restart redis")
    fi
    
    echo ""
    log_step "🔍 5. 检查前端核心功能..."
    
    # 5.1 前端主页检查
    local frontend_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$target_url/" 2>/dev/null || echo "000")
    if [[ "$frontend_response" == "200" ]]; then
        # 检查页面内容长度，确保不是空白页
        local content_length=$(curl -k -s -w "%{size_download}" -o /dev/null --connect-timeout 10 "$target_url/" 2>/dev/null || echo "0")
        if [[ $content_length -gt 1000 ]]; then
            record_test "前端主页" "PASS" "页面正常加载 ($content_length 字节)"
        else
            record_test "前端主页" "FAIL" "页面内容过少，可能是白页 ($content_length 字节)"
            issues_found=true
            suggestions+=("前端白页问题：检查前端容器状态和构建是否成功")
        fi
    else
        record_test "前端主页" "FAIL" "页面无法访问 (HTTP $frontend_response)"
        issues_found=true
        suggestions+=("前端页面问题：检查nginx配置和前端容器状态")
    fi
    
    # 5.2 前端静态资源检查（优化版）
    local static_endpoints=("_next/static/" "favicon.ico" "api/")
    for endpoint in "${static_endpoints[@]}"; do
        local static_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url/$endpoint" 2>/dev/null || echo "000")
        case "$static_response" in
            "200"|"304")
                record_test "静态资源: $endpoint" "PASS" "资源可访问 (HTTP $static_response)"
                ;;
            "301"|"302"|"308")
                # 重定向是正常的，特别是对于Next.js
                record_test "静态资源: $endpoint" "PASS" "资源重定向 (HTTP $static_response) - 正常"
                ;;
            "404")
                if [[ "$endpoint" == "_next/static/" ]]; then
                    record_test "静态资源: $endpoint" "INFO" "Next.js静态资源不存在，开发模式正常"
                else
                    record_test "静态资源: $endpoint" "INFO" "资源不存在，可能未配置 (HTTP 404)"
                fi
                ;;
            "500"|"502"|"503")
                record_test "静态资源: $endpoint" "FAIL" "服务器错误 (HTTP $static_response)"
                issues_found=true
                ;;
            "000")
                record_test "静态资源: $endpoint" "FAIL" "连接失败"
                issues_found=true
                ;;
            *)
                record_test "静态资源: $endpoint" "INFO" "响应 (HTTP $static_response)"
                ;;
        esac
    done
    
    echo ""
    log_step "🔍 6. 检查前端-后端API连接..."
    
    # 6.1 API代理检查
    local api_proxy_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url/api/" 2>/dev/null || echo "000")
    case "$api_proxy_response" in
        "200"|"404"|"405")
            record_test "API代理路由" "PASS" "API路由可达 (HTTP $api_proxy_response)"
            ;;
        "502"|"503"|"504")
            record_test "API代理路由" "FAIL" "API代理错误 (HTTP $api_proxy_response)"
            issues_found=true
            suggestions+=("API代理问题：检查nginx配置和Django容器连接")
            ;;
        "000")
            record_test "API代理路由" "FAIL" "API代理连接失败"
            issues_found=true
            ;;
        *)
            record_test "API代理路由" "WARN" "API代理异常响应 (HTTP $api_proxy_response)"
            ;;
    esac
    
    # 6.2 CORS检查
    local cors_response=$(curl -k -s -o /dev/null -w "%{http_code}" -H "Origin: $target_url" -H "Access-Control-Request-Method: GET" -X OPTIONS "$target_url/api/" 2>/dev/null || echo "000")
    if [[ "$cors_response" == "200" ]] || [[ "$cors_response" == "204" ]]; then
        record_test "CORS配置" "PASS" "跨域配置正常 (HTTP $cors_response)"
    elif [[ "$cors_response" == "404" ]] || [[ "$cors_response" == "405" ]]; then
        record_test "CORS配置" "INFO" "OPTIONS方法响应 (HTTP $cors_response)"
    else
        record_test "CORS配置" "WARN" "跨域配置可能有问题 (HTTP $cors_response)"
        suggestions+=("CORS配置问题：检查Django CORS设置和nginx配置")
    fi
    
    # 生成诊断报告
    echo ""
    echo "=========================================="
    log_info "🏥 智能诊断报告"
    echo "=========================================="
    echo -e "🎯 测试目标: $target_url"
    echo -e "📊 总测试数: $TOTAL_TESTS"
    echo -e "✅ 通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "❌ 失败: ${RED}$FAILED_TESTS${NC}"
    
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        local pass_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
        echo -e "📈 健康度: $pass_rate%"
        
        if [[ $pass_rate -ge 90 ]]; then
            echo -e "\n${GREEN}🎉 系统状态: 优秀${NC}"
        elif [[ $pass_rate -ge 70 ]]; then
            echo -e "\n${YELLOW}⚠️  系统状态: 良好 (有小问题)${NC}"
        else
            echo -e "\n${RED}🚨 系统状态: 需要修复${NC}"
        fi
    fi
    
    # 显示修复建议
    if [[ ${#suggestions[@]} -gt 0 ]]; then
        echo -e "\n${CYAN}🛠️  问题修复建议:${NC}"
        local i=1
        for suggestion in "${suggestions[@]}"; do
            echo -e "  $i. $suggestion"
            ((i++))
        done
        
        echo -e "\n${YELLOW}💡 快速修复命令:${NC}"
        echo "  🔄 重启所有服务: $0 restart"
        echo "  📋 查看详细日志: $0 logs"
        echo "  🧪 安全测试: $0 security"
    else
        echo -e "\n${GREEN}✨ 所有服务运行正常！${NC}"
    fi
}

# 安全配置测试
security_test() {
    log_step "🔒 执行安全配置测试..."
    get_local_ip
    
    # 根据环境选择合适的访问URL
    local target_url
    local target_host
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        target_url="http://localhost"
        target_host="localhost"
    else
        target_url="${PROTOCOL}://${PRIMARY_DOMAIN}"
        target_host="$PRIMARY_DOMAIN"
    fi
    
    # 重置测试计数器
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    echo ""
    log_step "🔍 1. SSL/TLS 安全性测试..."
    
    # SSL连接测试
    if timeout 10 openssl s_client -connect $target_host:443 -servername $target_host </dev/null >/dev/null 2>&1; then
        ssl_info=$(echo | timeout 5 openssl s_client -connect $target_host:443 -servername $target_host 2>/dev/null)
        
        # 检查TLS版本
        tls_version=$(echo "$ssl_info" | grep "Protocol" | head -1 | sed 's/.*Protocol[[:space:]]*:[[:space:]]*//' | tr -d '\r\n')
        if [[ "$tls_version" == "TLSv1.3" ]] || [[ "$tls_version" == "TLSv1.2" ]]; then
            record_test "TLS协议版本" "PASS" "使用安全版本: $tls_version"
        elif [[ -z "$tls_version" ]]; then
            record_test "TLS协议版本" "WARN" "无法获取TLS版本信息"
        else
            record_test "TLS协议版本" "FAIL" "不安全版本: $tls_version"
        fi
        
        # 检查密码套件
        cipher=$(echo "$ssl_info" | grep "Cipher" | head -1 | sed 's/.*Cipher[[:space:]]*:[[:space:]]*//' | tr -d '\r\n')
        if [[ "$cipher" == *"GCM"* ]] || [[ "$cipher" == *"CHACHA20"* ]] || [[ "$cipher" == *"AES"* ]]; then
            record_test "密码套件强度" "PASS" "使用强密码套件: $cipher"
        else
            record_test "密码套件强度" "WARN" "密码套件: $cipher"
        fi
    else
        record_test "SSL连接" "FAIL" "无法建立SSL连接"
    fi
    
    echo ""
    log_step "🔍 2. HTTP 安全头测试..."
    
    # 获取安全头 - 只获取响应头，不获取页面内容
    headers_response=$(curl -k -I -s -m 10 $target_url/ 2>/dev/null || echo "CONNECTION_FAILED")
    
    if [[ "$headers_response" != "CONNECTION_FAILED" ]] && [[ -n "$headers_response" ]]; then
        # 检查关键安全头
        security_headers=(
            "strict-transport-security:HSTS:CRITICAL"
            "x-frame-options:点击劫持防护:IMPORTANT"
            "x-content-type-options:MIME嗅探防护:IMPORTANT"
            "content-security-policy:内容安全策略:OPTIONAL"
            "referrer-policy:引用策略:OPTIONAL"
        )
        
        for header_check in "${security_headers[@]}"; do
            header_name=$(echo "$header_check" | cut -d: -f1)
            header_desc=$(echo "$header_check" | cut -d: -f2)
            header_level=$(echo "$header_check" | cut -d: -f3)
            
            if echo "$headers_response" | grep -i "^$header_name:" >/dev/null 2>&1; then
                header_value=$(echo "$headers_response" | grep -i "^$header_name:" | head -1 | cut -d: -f2- | tr -d '\r' | sed 's/^ *//')
                record_test "$header_desc" "PASS" "已设置: $(echo "$header_value" | cut -c1-50)..."
            else
                case "$header_level" in
                    "CRITICAL")
                record_test "$header_desc" "FAIL" "未设置 $header_name 头"
                        ;;
                    "IMPORTANT")
                        record_test "$header_desc" "WARN" "建议设置 $header_name 头"
                        ;;
                    "OPTIONAL")
                        record_test "$header_desc" "INFO" "可选: 未设置 $header_name 头"
                        ;;
                esac
            fi
        done
    else
        record_test "安全头检查" "FAIL" "无法连接到目标服务器或无响应"
    fi
    
    echo ""
    log_step "🔍 3. HTTP 方法安全测试..."
    
    # 测试不安全的HTTP方法 - 调整为更合理的期望
    dangerous_methods=("TRACE")  # 只有TRACE是真正危险的
    api_methods=("PUT" "DELETE" "PATCH")  # 这些对于API来说是正常的
    
    for method in "${dangerous_methods[@]}"; do
        response_code=$(curl -k -X "$method" -s -o /dev/null -w "%{http_code}" -m 5 "$target_url/" 2>/dev/null || echo "000")
        
        if [[ "$response_code" == "405" ]]; then
            record_test "$method 方法禁用" "PASS" "正确返回 HTTP 405"
        elif [[ "$response_code" == "000" ]]; then
            record_test "$method 方法禁用" "WARN" "连接超时或失败"
        else
            record_test "$method 方法禁用" "FAIL" "返回 HTTP $response_code (应为405)"
        fi
    done
    
    # API方法测试 - 标记为警告级别而不是失败
    for method in "${api_methods[@]}"; do
        response_code=$(curl -k -X "$method" -s -o /dev/null -w "%{http_code}" -m 5 "$target_url/" 2>/dev/null || echo "000")
        
        if [[ "$response_code" == "405" ]]; then
            record_test "$method 方法控制" "PASS" "正确返回 HTTP 405"
        elif [[ "$response_code" == "000" ]]; then
            record_test "$method 方法控制" "WARN" "连接超时或失败"
        else
            # 对于API应用，这些方法返回200/404等是正常的
            record_test "$method 方法控制" "INFO" "返回 HTTP $response_code (API应用中可接受)"
        fi
    done
    
    echo ""
    log_step "🔍 4. HTTPS 重定向测试..."
    
    # 测试HTTP到HTTPS重定向
    http_url="http://$target_host/"
    redirect_response=$(curl -s -o /dev/null -w "%{http_code}" -m 5 "$http_url" 2>/dev/null || echo "000")
    
    if [[ "$redirect_response" == "301" ]] || [[ "$redirect_response" == "302" ]]; then
        redirect_location=$(curl -s -I -m 5 "$http_url" 2>/dev/null | grep -i "^location:" | head -1)
        if [[ "$redirect_location" == *"https://"* ]]; then
            record_test "HTTPS强制重定向" "PASS" "HTTP正确重定向到HTTPS (HTTP $redirect_response)"
        else
            record_test "HTTPS强制重定向" "WARN" "重定向但非HTTPS: $redirect_location"
        fi
    elif [[ "$redirect_response" == "000" ]]; then
        record_test "HTTPS强制重定向" "WARN" "HTTP端口无响应 (可能已完全禁用)"
    else
        record_test "HTTPS强制重定向" "FAIL" "HTTP未重定向 (HTTP $redirect_response)"
    fi
    
    echo ""
    log_step "🔍 5. 敏感文件访问测试..."
    
    # 测试敏感文件访问
    critical_files=(".git/")  # 真正敏感的文件
    other_files=(".env" ".htaccess" "backup.sql" "config.ini")  # 一般敏感文件
    
    for file in "${critical_files[@]}"; do
        response_code=$(curl -k -s -o /dev/null -w "%{http_code}" -m 5 "$target_url/$file" 2>/dev/null || echo "000")
        
        if [[ "$response_code" == "403" ]] || [[ "$response_code" == "404" ]]; then
            record_test "关键文件保护 ($file)" "PASS" "访问被阻止 (HTTP $response_code)"
        elif [[ "$response_code" == "000" ]]; then
            record_test "关键文件保护 ($file)" "WARN" "连接超时"
        else
            record_test "关键文件保护 ($file)" "FAIL" "可能可访问 (HTTP $response_code)"
        fi
    done
    
    for file in "${other_files[@]}"; do
        response_code=$(curl -k -s -o /dev/null -w "%{http_code}" -m 5 "$target_url/$file" 2>/dev/null || echo "000")
        
        if [[ "$response_code" == "403" ]] || [[ "$response_code" == "404" ]]; then
            record_test "敏感文件保护 ($file)" "PASS" "访问被阻止 (HTTP $response_code)"
        elif [[ "$response_code" == "000" ]]; then
            record_test "敏感文件保护 ($file)" "WARN" "连接超时"
        else
            record_test "敏感文件保护 ($file)" "INFO" "建议检查文件保护 (HTTP $response_code)"
        fi
    done
    
    # 生成安全测试报告
    echo ""
    echo "=========================================="
    log_info "🔒 安全测试报告"
    echo "=========================================="
    echo -e "🎯 测试目标: $target_url"
    echo -e "📊 总测试数: $TOTAL_TESTS"
    echo -e "✅ 通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "❌ 失败: ${RED}$FAILED_TESTS${NC}"
    
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        local pass_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
        echo -e "📈 安全评分: $pass_rate%"
        
        if [[ $pass_rate -ge 85 ]]; then
            echo -e "\n${GREEN}🏆 安全等级: 优秀${NC}"
            echo -e "${GREEN}✨ HSTS已正确配置，核心安全措施到位${NC}"
        elif [[ $pass_rate -ge 70 ]]; then
            echo -e "\n${YELLOW}🥈 安全等级: 良好${NC}"
            echo -e "${YELLOW}💡 大部分安全措施已配置，少数项目可优化${NC}"
        elif [[ $pass_rate -ge 60 ]]; then
            echo -e "\n${YELLOW}🥉 安全等级: 可接受${NC}"
            echo -e "${YELLOW}⚠️  基本安全措施已到位，建议进一步加强${NC}"
        else
            echo -e "\n${RED}⚠️  安全等级: 需要改进${NC}"
            echo -e "${RED}🚨 发现重要安全问题，建议立即修复${NC}"
        fi
    fi
}

# 深度诊断
deep_diagnose() {
    log_step "🔬 执行深度诊断..."
    
    echo ""
    log_step "📊 系统资源使用情况..."
    
    # Docker系统信息
    echo "Docker 系统信息:"
    docker system df
    
    echo ""
    echo "容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
    
    echo ""
    log_step "📋 容器详细状态..."
    
    for service in "${SERVICES[@]}"; do
        local container_name
        if [[ "$ENVIRONMENT" == "dev" ]]; then
            container_name="MirageMakers-$service-dev"
        else
            container_name="MirageMakers-$service"
        fi
        echo ""
        echo "=== $service 容器诊断 ($ENVIRONMENT 环境) ==="
        
        if docker ps -a --filter "name=$container_name" | grep -q "$container_name"; then
            echo "状态: $(docker inspect --format='{{.State.Status}}' "$container_name")"
            echo "健康: $(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "无健康检查")"
            echo "重启次数: $(docker inspect --format='{{.RestartCount}}' "$container_name")"
            echo "最后启动: $(docker inspect --format='{{.State.StartedAt}}' "$container_name")"
            
            # 显示最近的日志
            echo "最近日志:"
            docker logs --tail=5 "$container_name" 2>&1 | sed 's/^/  /'
        else
            echo "容器不存在"
        fi
    done
    
    echo ""
    log_step "🌐 网络诊断..."
    
    # 检查Docker网络
    echo "Docker 网络:"
    docker network ls
    
    echo ""
    echo "端口监听状态:"
    ss -tuln | grep -E ':(80|443|3000|8000|3306|6379) ' || echo "未找到相关端口"
    
    echo ""
    log_step "💾 存储诊断..."
    
    # 检查磁盘空间
    echo "磁盘使用情况:"
    df -h | grep -E '(/$|/var|/home)'
    
    # 检查Docker卷
    echo ""
    echo "Docker 卷:"
    docker volume ls
}



# 执行数据库备份
backup_database() {
    log_step "🗄️ 执行数据库备份..."
    
    # 检查备份脚本是否存在
    if [[ ! -f "oss_backup.sh" ]]; then
        log_error "备份脚本 oss_backup.sh 不存在"
        return 1
    fi
    
    # 检查MySQL容器状态
    if ! docker ps --filter "name=MirageMakers-mysql" --filter "status=running" | grep -q "MirageMakers-mysql"; then
        log_error "MySQL容器未运行，无法执行备份"
        return 1
    fi
    
    # 执行备份
    log_info "正在执行数据库备份..."
    if bash oss_backup.sh; then
        log_success "数据库备份完成"
        
        # 显示备份文件信息
        local backup_dir="database/mysql_data_backup"
        if [[ -d "$backup_dir" ]]; then
            local latest_backup=$(ls -t "$backup_dir"/mysql_backup_*.sql.gz 2>/dev/null | head -1)
            if [[ -n "$latest_backup" ]]; then
                local backup_size=$(du -h "$latest_backup" | cut -f1)
                log_info "最新备份: $(basename "$latest_backup") ($backup_size)"
            fi
        fi
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 恢复数据库数据
restore_database() {
    log_step "🔄 数据库恢复功能"
    
    local backup_dir="database/mysql_data_backup"
    local backup_file=""
    
    # 检查MySQL容器状态
    if ! docker ps --filter "name=MirageMakers-mysql" --filter "status=running" | grep -q "MirageMakers-mysql"; then
        log_error "MySQL容器未运行，无法执行恢复"
        return 1
    fi
    
    # 列出可用的备份文件
    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份目录不存在: $backup_dir"
        return 1
    fi
    
    local backup_files=($(ls -t "$backup_dir"/mysql_backup_*.sql.gz 2>/dev/null))
    if [[ ${#backup_files[@]} -eq 0 ]]; then
        log_error "未找到备份文件在目录: $backup_dir"
        log_info "请先执行备份: $0 backup"
        return 1
    fi
    
    echo ""
    log_info "可用的备份文件:"
    for i in "${!backup_files[@]}"; do
        local file="${backup_files[$i]}"
        local filename=$(basename "$file")
        local filesize=$(du -h "$file" | cut -f1)
        local timestamp=$(echo "$filename" | grep -o '[0-9]\{8\}_[0-9]\{6\}' | head -1)
        local formatted_time=""
        if [[ -n "$timestamp" ]]; then
            # 格式化时间戳为可读格式
            local date_part="${timestamp:0:8}"
            local time_part="${timestamp:9:6}"
            formatted_time="${date_part:0:4}-${date_part:4:2}-${date_part:6:2} ${time_part:0:2}:${time_part:2:2}:${time_part:4:2}"
        fi
        echo "  $((i+1)). $filename ($filesize) - $formatted_time"
    done
    
    echo ""
    echo -n "请选择要恢复的备份文件编号 (1-${#backup_files[@]}) 或按 Ctrl+C 取消: "
    read -r choice
    
    # 验证选择
    if ! [[ "$choice" =~ ^[0-9]+$ ]] || [[ "$choice" -lt 1 ]] || [[ "$choice" -gt ${#backup_files[@]} ]]; then
        log_error "无效的选择: $choice"
        return 1
    fi
    
    backup_file="${backup_files[$((choice-1))]}"
    local backup_name=$(basename "$backup_file")
    
    echo ""
    log_warn "⚠️  警告: 数据库恢复将会覆盖现有的所有数据！"
    log_warn "选择的备份文件: $backup_name"
    echo -n "确认要继续恢复吗？输入 'YES' 继续，其他任意键取消: "
    read -r confirm
    
    if [[ "$confirm" != "YES" ]]; then
        log_info "恢复操作已取消"
        return 0
    fi
    
    # 执行恢复
    log_step "开始恢复数据库..."
    
    # 1. 停止依赖数据库的服务
    log_info "停止依赖数据库的服务..."
    docker-compose stop django celery 2>/dev/null || true
    sleep 3
    
    # 2. 验证备份文件完整性
    log_info "验证备份文件完整性..."
    if ! gzip -t "$backup_file" 2>/dev/null; then
        log_error "备份文件损坏: $backup_file"
        log_info "重新启动服务..."
        docker-compose start django celery 2>/dev/null || true
        return 1
    fi
    
    # 3. 执行数据库恢复
    log_info "正在恢复数据库数据..."
    local restore_log="/tmp/restore_error.log"
    if gunzip < "$backup_file" | docker exec -i MirageMakers-mysql mysql -u root -p"$MYSQL_ROOT_PASSWORD" 2>"$restore_log"; then
        log_success "数据库恢复完成"
        rm -f "$restore_log"
    else
        log_error "数据库恢复失败"
        if [[ -f "$restore_log" ]]; then
            log_error "错误详情: $(cat "$restore_log")"
        fi
        log_info "重新启动服务..."
        docker-compose start django celery 2>/dev/null || true
        rm -f "$restore_log"
        return 1
    fi
    
    # 4. 重新启动服务
    log_info "重新启动服务..."
    docker-compose start django celery 2>/dev/null || true
    sleep 5
    
    # 5. 验证恢复结果
    log_info "验证恢复结果..."
    if docker exec MirageMakers-django python manage.py check --database default >/dev/null 2>&1; then
        log_success "数据库连接验证通过"
    else
        log_warn "数据库连接验证失败，请检查服务状态"
    fi
    
    log_success "数据库恢复操作完成！"
    log_info "已恢复备份: $backup_name"
    log_info "建议执行健康检查: $0 health"
}

# 清理系统
clean_system() {
    log_step "清理 $ENVIRONMENT 环境 Docker系统..."
    
    if $COMPOSE_CMD ps | grep -q "Up"; then
        log_warn "检测到 $ENVIRONMENT 环境运行中的服务，先停止服务..."
        $COMPOSE_CMD down
    fi
    
    $COMPOSE_CMD down -v
    docker system prune -f
    
    log_success "$ENVIRONMENT 环境系统清理完成"
}

# 修复CSRF/CORS和前后端对接问题 - 增强版本
fix_system() {
    log_step "执行全面的CSRF/CORS配置修复..."
    
    # 获取本机IP
    get_local_ip
    
    # 备份当前配置
    if [[ -f ".env" ]]; then
        cp .env .env.backup.manage.$(date +%Y%m%d_%H%M%S)
        log_info "已备份当前.env配置"
    fi
    
    # 备份nginx配置
    if [[ -f "nginx/nginx.conf" ]]; then
        cp nginx/nginx.conf nginx/nginx.conf.backup.manage.$(date +%Y%m%d_%H%M%S)
        log_info "已备份当前nginx配置"
    fi
    
    # 检测部署环境类型
    local deployment_env="local"
    if curl -s --connect-timeout 3 http://ipinfo.io/ip >/dev/null 2>&1; then
        local public_ip=$(curl -s --connect-timeout 3 http://ipinfo.io/ip 2>/dev/null)
        if [[ -n "$public_ip" && "$public_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            deployment_env="ecs"
            log_info "检测到ECS环境，公网IP: $public_ip"
        fi
    fi
    
    # 修复.env文件
    log_info "修复.env配置文件..."
    # 检查并替换可能的硬编码IP地址
    if grep -q "172\.16\.200\.125" .env; then
        log_warn "发现硬编码IP地址 **************，正在替换为 ${LOCAL_IP}"
        sed -i.bak "s|172\.16\.200\.125|${LOCAL_IP}|g" .env
    fi
    # 更新域名配置以支持miragemakers.ai
    if [[ "$deployment_env" == "ecs" ]]; then
        log_info "ECS环境：配置域名为miragemakers.ai"
        sed -i.bak "s|PRIMARY_DOMAIN=.*|PRIMARY_DOMAIN=miragemakers.ai|g" .env
        sed -i.bak "s|API_DOMAIN=.*|API_DOMAIN=miragemakers.ai|g" .env
        sed -i.bak "s|FRONTEND_DOMAIN=.*|FRONTEND_DOMAIN=miragemakers.ai|g" .env
    else
        log_info "本地环境：使用IP地址 ${LOCAL_IP}"
        sed -i.bak "s|PRIMARY_DOMAIN=.*|PRIMARY_DOMAIN=${LOCAL_IP}|g" .env
        sed -i.bak "s|API_DOMAIN=.*|API_DOMAIN=${LOCAL_IP}|g" .env
        sed -i.bak "s|FRONTEND_DOMAIN=.*|FRONTEND_DOMAIN=${LOCAL_IP}|g" .env
    fi
    # 确保使用HTTPS
    sed -i.bak "s|PROTOCOL=.*|PROTOCOL=https|g" .env
    # 清理临时文件
    rm -f .env.bak
    log_success ".env配置修复完成"
    
    # 修复nginx配置 - 解决geo指令语法错误和403问题
    log_info "修复nginx配置..."
    
    # 检查并修复nginx配置中的geo指令错误
    if grep -q "geo.*admin_allowed" nginx/nginx.conf; then
        # 如果存在语法错误的geo定义在location块中，创建简化的nginx配置
        log_warn "检测到nginx配置中有geo指令语法错误，应用修复..."
        
        # 使用与start.sh相同的修复后nginx配置
        cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 隐藏nginx版本信息
    server_tokens off;

    # 日志配置
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # 速率限制
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=20r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;

    # 连接数限制
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # 超时设置
    proxy_connect_timeout 30s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    client_max_body_size 50M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 安全头
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "0" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    upstream frontend {
        server frontend:3000;
        keepalive 32;
    }

    upstream backend {
        server django:8000;
        keepalive 32;
    }

    # HTTP Server - 重定向到HTTPS
    server {
        listen 80;
        server_name miragemakers.ai localhost _;

        # Health check
        location /health {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 重定向到HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    # HTTPS Server
    server {
        listen 443 ssl;
        http2 on;
        server_name miragemakers.ai localhost _;

        # 连接数限制
        limit_conn conn_limit_per_ip 20;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/miragemakers.ai.pem;
        ssl_certificate_key /etc/nginx/ssl/miragemakers.ai.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 1d;
        ssl_session_tickets off;
        ssl_dhparam /etc/nginx/ssl/dhparam.pem;

        # HSTS
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

        # Health Check
        location /health {
            limit_req zone=general burst=10 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API Routes - 关键修复：CORS配置
        location /api/ {
            limit_req zone=api burst=30 nodelay;

            # CORS配置 - 允许miragemakers.ai和localhost
            set $cors_origin "";
            if ($http_origin ~ "^https?://(miragemakers\.ai|localhost|127\.0\.0\.1|[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)(:[0-9]+)?$") {
                set $cors_origin $http_origin;
            }

            # 添加CORS头
            add_header Access-Control-Allow-Origin $cors_origin always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-CSRFToken" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Access-Control-Max-Age "86400" always;

            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin $cors_origin;
                add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
                add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-CSRFToken";
                add_header Access-Control-Allow-Credentials "true";
                add_header Access-Control-Max-Age 86400;
                add_header Content-Type 'text/plain; charset=utf-8';
                add_header Content-Length 0;
                return 204;
            }

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 60s;
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
        }

        # 社交登录路由
        location /accounts/ {
            limit_req zone=login burst=10 nodelay;

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Django Admin - 简化版本，无IP限制
        location /admin/ {
            limit_req zone=login burst=10 nodelay;

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Django Static Files
        location /static/ {
            limit_req zone=general burst=50 nodelay;

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            expires 30d;
            add_header Cache-Control "public, no-transform, immutable";
        }

        # Django Media Files
        location /media/ {
            limit_req zone=general burst=20 nodelay;

            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            expires 7d;
            add_header Cache-Control "public, no-transform";
        }

        # Frontend - 默认路由
        location / {
            limit_req zone=general burst=20 nodelay;

            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 60s;
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
        }
    }
}
EOF
        log_success "nginx配置已修复，移除了geo指令语法错误"
    else
        log_info "nginx配置无需修复"
    fi
    
    # 重启服务以应用修复
    log_info "重新构建并重启nginx以应用配置..."
    docker-compose build nginx
    docker-compose restart
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 20
    
    # 验证修复结果
    log_info "验证修复结果..."
    
    local fix_success=true
    
    # 检查容器状态
    for service in nginx frontend django mysql redis celery; do
        if docker ps --filter "name=MirageMakers-$service" --filter "status=running" | grep -q "MirageMakers-$service"; then
            record_test "$service容器" "PASS" "运行正常"
        else
            record_test "$service容器" "FAIL" "未运行"
            fix_success=false
        fi
    done
    
    # 测试nginx配置
    if docker exec MirageMakers-nginx nginx -t >/dev/null 2>&1; then
        record_test "nginx配置" "PASS" "语法正确"
    else
        record_test "nginx配置" "FAIL" "语法错误"
        fix_success=false
    fi
    
    if [[ "$fix_success" == "true" ]]; then
        log_success "系统修复完成，所有服务运行正常"
        echo ""
        echo "🌐 修复后访问地址:"
        if [[ "$deployment_env" == "ecs" ]]; then
            echo "  • 主要访问: https://miragemakers.ai/"
            echo "  • API接口: https://miragemakers.ai/api/"
            echo "  • 管理后台: https://miragemakers.ai/admin/"
        else
            echo "  • 主要访问: https://${LOCAL_IP}/"
            echo "  • API接口: https://${LOCAL_IP}/api/"
            echo "  • 管理后台: https://${LOCAL_IP}/admin/"
        fi
        echo ""
        echo "📋 修复内容:"
        echo "  ✅ 检查并修复硬编码IP地址配置"
        echo "  ✅ 更新域名配置支持miragemakers.ai"
        echo "  ✅ 修复nginx geo指令语法错误"
        echo "  ✅ 移除admin_allowed访问限制"
        echo "  ✅ 配置正确的CORS设置"
        echo "  ✅ 确保使用HTTPS协议"
        echo "  ✅ 重新构建并重启所有服务"
    else
        log_error "修复过程中发现问题，请检查上述错误信息"
        return 1
    fi
}

# 测试认证功能（注册、登录、验证）
test_authentication() {
    log_step "🔐 执行认证功能测试..."
    get_local_ip
    
    # 根据环境选择合适的访问URL
    local target_url
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        target_url="http://localhost:8000"  # 开发环境直接访问Django
    else
        target_url="${PROTOCOL}://${PRIMARY_DOMAIN}"
    fi
    local test_email="auth.test$(date +%s)@example.com"
    local test_name="Auth Test User"
    local test_password="test123456"
    
    # 重置测试计数器
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    echo ""
    log_step "🔍 1. 测试用户注册API..."
    
    # 测试注册API
    local register_response=$(curl -k -s -w "\n%{http_code}" -X POST "$target_url/api/auth/register" \
        -H "Content-Type: application/json" \
        -d "{\"email\": \"$test_email\", \"password\": \"$test_password\", \"name\": \"$test_name\", \"language\": \"zh\"}" \
        2>/dev/null || echo -e "\n000")
    
    local register_code=$(echo "$register_response" | tail -n1)
    local register_body=$(echo "$register_response" | head -n -1)
    
    if [[ "$register_code" == "201" ]]; then
        if echo "$register_body" | grep -q "requires_verification.*true" 2>/dev/null; then
            record_test "用户注册API" "PASS" "注册成功并返回验证要求 (HTTP $register_code)"
        else
            record_test "用户注册API" "WARN" "注册成功但响应格式异常"
        fi
    else
        record_test "用户注册API" "FAIL" "注册失败 (HTTP $register_code)"
    fi
    
    echo ""
    log_step "🔍 2. 检查用户创建状态..."
    
    # 获取验证码
    local verification_code=$(docker exec MirageMakers-django python manage.py shell -c "
from core.models import User, EmailVerification
try:
    user = User.objects.get(email='$test_email')
    verification = EmailVerification.objects.filter(user=user, is_used=False).first()
    if verification:
        print(verification.code)
    else:
        print('NO_CODE')
except:
    print('USER_NOT_FOUND')
" 2>/dev/null)
    
    if [[ "$verification_code" != "USER_NOT_FOUND" ]] && [[ "$verification_code" != "NO_CODE" ]] && [[ -n "$verification_code" ]]; then
        record_test "用户创建状态" "PASS" "用户已创建，验证码: ${verification_code:0:6}..."
    else
        record_test "用户创建状态" "FAIL" "用户创建失败或验证码缺失"
        verification_code=""
    fi
    
    echo ""
    log_step "🔍 3. 测试邮件验证API..."
    
    if [[ -n "$verification_code" ]]; then
        local verify_response=$(curl -k -s -w "\n%{http_code}" -X POST "$target_url/api/auth/verify-email" \
            -H "Content-Type: application/json" \
            -d "{\"email\": \"$test_email\", \"code\": \"$verification_code\"}" \
            2>/dev/null || echo -e "\n000")
        
        local verify_code=$(echo "$verify_response" | tail -n1)
        local verify_body=$(echo "$verify_response" | head -n -1)
        
        if [[ "$verify_code" == "200" ]]; then
            if echo "$verify_body" | grep -q "token" 2>/dev/null; then
                record_test "邮件验证API" "PASS" "验证成功并返回token (HTTP $verify_code)"
            else
                record_test "邮件验证API" "WARN" "验证成功但未返回token"
            fi
        else
            record_test "邮件验证API" "FAIL" "验证失败 (HTTP $verify_code)"
        fi
    else
        record_test "邮件验证API" "FAIL" "跳过验证测试（无验证码）"
    fi
    
    echo ""
    log_step "🔍 4. 测试用户登录API..."
    
    # 测试登录API
    local login_response=$(curl -k -s -w "\n%{http_code}" -X POST "$target_url/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\": \"$test_email\", \"password\": \"$test_password\"}" \
        2>/dev/null || echo -e "\n000")
    
    local login_code=$(echo "$login_response" | tail -n1)
    local login_body=$(echo "$login_response" | head -n -1)
    
    if [[ "$login_code" == "200" ]]; then
        if echo "$login_body" | grep -q "token" 2>/dev/null; then
            record_test "用户登录API" "PASS" "登录成功并返回token (HTTP $login_code)"
        else
            record_test "用户登录API" "WARN" "登录成功但响应异常"
        fi
    elif [[ "$login_code" == "403" ]]; then
        record_test "用户登录API" "WARN" "登录被拒绝（可能需要邮件验证）"
    else
        record_test "用户登录API" "FAIL" "登录失败 (HTTP $login_code)"
    fi
    
    echo ""
    log_step "🔍 5. 测试密码重置API..."
    
    # 测试忘记密码API
    local reset_response=$(curl -k -s -w "\n%{http_code}" -X POST "$target_url/api/auth/forgot-password" \
        -H "Content-Type: application/json" \
        -d "{\"email\": \"$test_email\"}" \
        2>/dev/null || echo -e "\n000")
    
    local reset_code=$(echo "$reset_response" | tail -n1)
    
    if [[ "$reset_code" == "200" ]]; then
        record_test "密码重置API" "PASS" "重置请求成功 (HTTP $reset_code)"
    else
        record_test "密码重置API" "FAIL" "重置请求失败 (HTTP $reset_code)"
    fi
    
    echo ""
    log_step "🔍 6. 测试重复注册检测..."
    
    # 测试重复注册
    local duplicate_response=$(curl -k -s -w "\n%{http_code}" -X POST "$target_url/api/auth/register" \
        -H "Content-Type: application/json" \
        -d "{\"email\": \"$test_email\", \"password\": \"$test_password\", \"name\": \"$test_name\", \"language\": \"zh\"}" \
        2>/dev/null || echo -e "\n000")
    
    local duplicate_code=$(echo "$duplicate_response" | tail -n1)
    
    if [[ "$duplicate_code" == "400" ]]; then
        record_test "重复注册检测" "PASS" "正确阻止重复注册 (HTTP $duplicate_code)"
    else
        record_test "重复注册检测" "FAIL" "重复注册检测失败 (HTTP $duplicate_code)"
    fi
    
    # 清理测试数据
    docker exec MirageMakers-django python manage.py shell -c "
from core.models import User, EmailVerification
try:
    user = User.objects.get(email='$test_email')
    EmailVerification.objects.filter(user=user).delete()
    user.delete()
except:
    pass
" 2>/dev/null
    
    # 生成认证测试报告
    echo ""
    echo "=========================================="
    log_info "🔐 认证功能测试报告"
    echo "=========================================="
    echo -e "🎯 测试目标: $target_url"
    echo -e "👤 测试邮箱: $test_email"
    echo -e "📊 总测试数: $TOTAL_TESTS"
    echo -e "✅ 通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "❌ 失败: ${RED}$FAILED_TESTS${NC}"
    
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        local pass_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
        echo -e "📈 通过率: $pass_rate%"
        
        if [[ $pass_rate -ge 90 ]]; then
            echo -e "\n${GREEN}🎉 认证功能: 优秀${NC}"
        elif [[ $pass_rate -ge 70 ]]; then
            echo -e "\n${YELLOW}⚠️  认证功能: 良好 (有小问题)${NC}"
        else
            echo -e "\n${RED}🚨 认证功能: 需要修复${NC}"
        fi
    fi
}

# 全面API端点测试
test_comprehensive_api_endpoints() {
    log_step "🔌 执行全面API端点测试..."
    get_local_ip
    
    # 根据环境选择合适的访问URL
    local target_url
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        target_url="http://localhost"  # 开发环境通过nginx访问
    else
        target_url="${PROTOCOL}://${PRIMARY_DOMAIN}"
    fi
    
    # 重置测试计数器
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    echo ""
    log_step "🔍 1. 系统基础API测试..."
    
    # 系统基础API
    local system_endpoints=(
        "/health/:GET:200:系统健康检查"
        "/api/:GET:200,404:API根路径"
        "/admin/:GET:200,302:Django管理后台"
        "/admin/login/:GET:200:管理后台登录页"
        "/media/:GET:200,403,404:媒体文件服务"
        "/static/:GET:200,403,404:静态文件服务"
    )
    
    for endpoint_info in "${system_endpoints[@]}"; do
        local endpoint=$(echo "$endpoint_info" | cut -d: -f1)
        local method=$(echo "$endpoint_info" | cut -d: -f2)
        local expected_codes=$(echo "$endpoint_info" | cut -d: -f3)
        local description=$(echo "$endpoint_info" | cut -d: -f4)
        
        local response_code=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url$endpoint" 2>/dev/null || echo "000")
        
        if [[ ",$expected_codes," == *",$response_code,"* ]]; then
            record_test "$description" "PASS" "$method $endpoint → HTTP $response_code"
        elif [[ "$response_code" == "000" ]]; then
            record_test "$description" "FAIL" "$method $endpoint → 连接超时"
        else
            record_test "$description" "WARN" "$method $endpoint → HTTP $response_code (期望 $expected_codes)"
        fi
    done
    
    echo ""
    log_step "🔍 2. 认证系统API测试..."
    
    # 认证相关API测试
    local auth_endpoints=(
        "/api/auth/login/:POST:400,405:用户登录接口"
        "/api/auth/register/:POST:400,405:用户注册接口"
        "/api/auth/logout/:POST:200,405:用户注销接口"
        "/api/auth/password/reset/:POST:400,404:密码重置接口"
        "/api/auth/change-password/:POST:400,401:密码修改接口"
        "/api/auth/verify-email/:POST:400,404:邮箱验证接口"
        "/api/auth/social-success/providers/:GET:200:社交登录配置"
        "/api/user/profile/:GET:401,403:用户资料接口"
    )
    
    for endpoint_info in "${auth_endpoints[@]}"; do
        local endpoint=$(echo "$endpoint_info" | cut -d: -f1)
        local method=$(echo "$endpoint_info" | cut -d: -f2)
        local expected_codes=$(echo "$endpoint_info" | cut -d: -f3)
        local description=$(echo "$endpoint_info" | cut -d: -f4)
        
        local response_code
        if [[ "$method" == "GET" ]]; then
            response_code=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url$endpoint" 2>/dev/null || echo "000")
        else
            response_code=$(curl -k -s -o /dev/null -w "%{http_code}" -X "$method" -H "Content-Type: application/json" --connect-timeout 5 "$target_url$endpoint" 2>/dev/null || echo "000")
        fi
        
        if [[ ",$expected_codes," == *",$response_code,"* ]]; then
            record_test "$description" "PASS" "$method $endpoint → HTTP $response_code"
        elif [[ "$response_code" == "000" ]]; then
            record_test "$description" "FAIL" "$method $endpoint → 连接超时"
        else
            record_test "$description" "WARN" "$method $endpoint → HTTP $response_code (期望 $expected_codes)"
        fi
    done
    
    echo ""
    log_step "🔍 3. 核心业务API测试..."
    
    # 核心业务API（根据实际业务调整）
    local business_endpoints=(
        "/api/core/features/:GET:200,404,401:核心功能接口"
        "/api/core/models/:GET:200,404,401:AI模型接口"
        "/api/core/generate/:POST:400,404,401:内容生成接口"
        "/api/core/upload/:POST:400,404,401:文件上传接口"
        "/api/core/history/:GET:200,404,401:历史记录接口"
        "/api/core/settings/:GET:200,404,401:系统设置接口"
    )
    
    for endpoint_info in "${business_endpoints[@]}"; do
        local endpoint=$(echo "$endpoint_info" | cut -d: -f1)
        local method=$(echo "$endpoint_info" | cut -d: -f2)
        local expected_codes=$(echo "$endpoint_info" | cut -d: -f3)
        local description=$(echo "$endpoint_info" | cut -d: -f4)
        
        local response_code
        if [[ "$method" == "GET" ]]; then
            response_code=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url$endpoint" 2>/dev/null || echo "000")
        else
            response_code=$(curl -k -s -o /dev/null -w "%{http_code}" -X "$method" -H "Content-Type: application/json" --connect-timeout 5 "$target_url$endpoint" 2>/dev/null || echo "000")
        fi
        
        if [[ ",$expected_codes," == *",$response_code,"* ]]; then
            record_test "$description" "PASS" "$method $endpoint → HTTP $response_code"
        elif [[ "$response_code" == "000" ]]; then
            record_test "$description" "FAIL" "$method $endpoint → 连接超时"
        else
            record_test "$description" "INFO" "$method $endpoint → HTTP $response_code (可能未实现)"
        fi
    done
    
    echo ""
    log_step "🔍 4. 邮件系统测试..."
    
    # 邮件功能专项测试
    local email_test_data='{"email":"<EMAIL>"}'
    local email_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 -X POST "$target_url/api/auth/password/reset/" -H "Content-Type: application/json" -d "$email_test_data" 2>/dev/null || echo "000")
    
    case "$email_response" in
        "200")
            record_test "邮件发送功能" "PASS" "邮件API正常响应"
            ;;
        "400"|"429")
            record_test "邮件发送功能" "PASS" "邮件API保护机制正常 (HTTP $email_response)"
            ;;
        "404")
            record_test "邮件发送功能" "WARN" "邮件API端点不存在"
            ;;
        "500"|"502"|"503")
            record_test "邮件发送功能" "FAIL" "邮件服务异常 (HTTP $email_response)"
            ;;
        "000")
            record_test "邮件发送功能" "FAIL" "邮件API连接失败"
            ;;
        *)
            record_test "邮件发送功能" "WARN" "邮件API异常响应 (HTTP $email_response)"
            ;;
    esac
    
    echo ""
    log_step "🔍 5. 前端页面路由测试..."
    
    # 前端页面测试（开发友好版）
    local core_pages=(
        "/:前端主页"
        "/profile:个人资料页面"
        "/api:API接口页面"
    )
    
    local optional_pages=(
        "/login:登录页面"
        "/register:注册页面"
        "/dashboard:仪表板页面"
        "/settings:设置页面"
    )
    
    # 测试核心页面（必须存在）
    for page_info in "${core_pages[@]}"; do
        local page=$(echo "$page_info" | cut -d: -f1)
        local description=$(echo "$page_info" | cut -d: -f2)
        
        local response_code=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url$page" 2>/dev/null || echo "000")
        
        case "$response_code" in
            "200")
                record_test "$description" "PASS" "页面正常访问"
                ;;
            "302"|"301")
                record_test "$description" "PASS" "页面重定向 (HTTP $response_code)"
                ;;
            "404")
                record_test "$description" "WARN" "核心页面不存在 (HTTP 404)"
                ;;
            "500"|"502"|"503")
                record_test "$description" "FAIL" "页面错误 (HTTP $response_code)"
                ;;
            "000")
                record_test "$description" "FAIL" "页面连接失败"
                ;;
            *)
                record_test "$description" "WARN" "页面异常响应 (HTTP $response_code)"
                ;;
        esac
    done
    
    # 测试可选页面（开发中正常）
    for page_info in "${optional_pages[@]}"; do
        local page=$(echo "$page_info" | cut -d: -f1)
        local description=$(echo "$page_info" | cut -d: -f2)
        
        local response_code=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$target_url$page" 2>/dev/null || echo "000")
        
        case "$response_code" in
            "200")
                record_test "$description" "PASS" "页面已实现"
                ;;
            "302"|"301")
                record_test "$description" "PASS" "页面重定向 (HTTP $response_code)"
                ;;
            "404")
                record_test "$description" "INFO" "页面开发中，未实现"
                ;;
            "500"|"502"|"503")
                record_test "$description" "FAIL" "页面错误 (HTTP $response_code)"
                ;;
            "000")
                record_test "$description" "FAIL" "页面连接失败"
                ;;
            *)
                record_test "$description" "INFO" "页面响应 (HTTP $response_code)"
                ;;
        esac
    done
    
    echo ""
    log_step "🔍 6. 文件服务测试..."
    
    # 文件上传测试（模拟）
    local upload_response=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 -X POST "$target_url/api/upload/" 2>/dev/null || echo "000")
    case "$upload_response" in
        "400"|"401"|"403"|"413")
            record_test "文件上传服务" "PASS" "上传接口响应正常 (HTTP $upload_response)"
            ;;
        "404")
            record_test "文件上传服务" "INFO" "上传接口未实现"
            ;;
        "500"|"502"|"503")
            record_test "文件上传服务" "FAIL" "上传服务异常 (HTTP $upload_response)"
            ;;
        "000")
            record_test "文件上传服务" "FAIL" "上传服务连接失败"
            ;;
        *)
            record_test "文件上传服务" "INFO" "上传服务响应 (HTTP $upload_response)"
            ;;
    esac
    
    # 生成综合API测试报告
    echo ""
    echo "=========================================="
    log_info "🔌 全面API端点测试报告"
    echo "=========================================="
    echo -e "🎯 测试目标: $target_url"
    echo -e "📊 总测试数: $TOTAL_TESTS"
    echo -e "✅ 通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "❌ 失败: ${RED}$FAILED_TESTS${NC}"
    echo -e "ℹ️  信息: $((TOTAL_TESTS - PASSED_TESTS - FAILED_TESTS))"
    
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        local pass_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
        echo -e "📈 成功率: $pass_rate%"
        
        if [[ $pass_rate -ge 90 ]]; then
            echo -e "\n${GREEN}🎉 API测试结果: 优秀${NC}"
        elif [[ $pass_rate -ge 70 ]]; then
            echo -e "\n${YELLOW}⚠️  API测试结果: 良好${NC}"
        else
            echo -e "\n${RED}🚨 API测试结果: 需要改进${NC}"
        fi
    fi
    
    echo -e "\n${CYAN}💡 提示:${NC}"
    echo -e "   • 404错误通常表示功能尚未实现，这是正常的"
    echo -e "   • 401/403错误表示需要认证，API保护机制正常"
    echo -e "   • 400错误表示请求格式问题，API验证机制正常"
}

# 兼容性别名（保持向后兼容）
test_api_endpoints() {
    test_comprehensive_api_endpoints
}

# 执行所有测试
test_all_comprehensive() {
    echo ""
    echo "=========================================="
    log_info "🎯 MirageMakers AI 综合测试套件"
    echo "=========================================="
    echo ""
    
    local start_time=$(date +%s)
    
    # 1. 健康检查
    log_step "第一阶段: 健康检查和诊断"
    intelligent_health_check
    local health_passed=$PASSED_TESTS
    local health_total=$TOTAL_TESTS
    
    echo ""
    echo ""
    
    # 2. 安全测试
    log_step "第二阶段: 安全配置测试"
    security_test
    local security_passed=$PASSED_TESTS
    local security_total=$TOTAL_TESTS
    
    echo ""
    echo ""
    
    # 3. 认证测试
    log_step "第三阶段: 认证功能测试"
    test_authentication
    local auth_passed=$PASSED_TESTS
    local auth_total=$TOTAL_TESTS
    
    echo ""
    echo ""
    
    # 4. API端点测试
    log_step "第四阶段: API端点测试"
    test_api_endpoints
    local api_passed=$PASSED_TESTS
    local api_total=$TOTAL_TESTS
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 综合报告
    echo ""
    echo ""
    echo "=================================================="
    log_info "🏆 MirageMakers AI 综合测试报告"
    echo "=================================================="
    echo ""
    echo -e "${BLUE}📋 测试阶段结果:${NC}"
    echo -e "   🏥 健康检查: ${GREEN}$health_passed${NC}/${BLUE}$health_total${NC} ($(( health_passed * 100 / health_total ))%)"
    echo -e "   🔒 安全测试: ${GREEN}$security_passed${NC}/${BLUE}$security_total${NC} ($(( security_passed * 100 / security_total ))%)"
    echo -e "   🔐 认证测试: ${GREEN}$auth_passed${NC}/${BLUE}$auth_total${NC} ($(( auth_passed * 100 / auth_total ))%)"
    echo -e "   🔌 API测试:  ${GREEN}$api_passed${NC}/${BLUE}$api_total${NC} ($(( api_passed * 100 / api_total ))%)"
    echo ""
    
    local total_passed=$((health_passed + security_passed + auth_passed + api_passed))
    local total_tests=$((health_total + security_total + auth_total + api_total))
    local overall_rate=$(( total_passed * 100 / total_tests ))
    
    echo -e "${PURPLE}🎯 综合评分: $overall_rate% (${total_passed}/${total_tests})${NC}"
    echo -e "${CYAN}⏱️  测试耗时: ${duration}秒${NC}"
    echo ""
    
    if [[ $overall_rate -ge 95 ]]; then
        echo -e "${GREEN}🏆 系统状态: 优秀 (A+)${NC}"
        echo -e "   ✨ 所有功能正常运行，系统完全可用"
    elif [[ $overall_rate -ge 85 ]]; then
        echo -e "${GREEN}🥇 系统状态: 良好 (A)${NC}"
        echo -e "   ✅ 主要功能正常，少量非关键问题"
    elif [[ $overall_rate -ge 70 ]]; then
        echo -e "${YELLOW}🥈 系统状态: 一般 (B)${NC}"
        echo -e "   ⚠️  部分功能异常，建议检查和修复"
    else
        echo -e "${RED}🚨 系统状态: 需要修复 (C)${NC}"
        echo -e "   ❌ 多个关键功能异常，需要立即处理"
    fi
    
    echo ""
    echo "=================================================="
}

# 主函数
main() {
    # 解析环境参数并获取剩余参数
    local args=("$@")
    local command=""
    local service=""
    
    # 解析环境参数
    while [[ ${#args[@]} -gt 0 ]]; do
        case "${args[0]}" in
            --dev)
                ENVIRONMENT="dev"
                COMPOSE_FILE="docker-compose.dev.yml"
                CONTAINER_PREFIX="MirageMakers"
                args=("${args[@]:1}")  # 移除已处理的参数
                ;;
            --prod)
                ENVIRONMENT="prod"
                COMPOSE_FILE="docker-compose.yml"
                CONTAINER_PREFIX="MirageMakers"
                args=("${args[@]:1}")  # 移除已处理的参数
                ;;
            *)
                # 不是环境参数，停止解析
                break
                ;;
        esac
    done
    
    # 设置Docker Compose命令
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose -f $COMPOSE_FILE"
    else
        COMPOSE_CMD="docker-compose -f $COMPOSE_FILE"
    fi
    
    # 获取命令和服务名
    command="${args[0]:-}"
    service="${args[1]:-}"
    
    # 加载环境变量
    load_env_variables
    
    get_local_ip
    
    log_info "当前环境: $ENVIRONMENT ($COMPOSE_FILE)"
    
    case "$command" in
        "status")
            check_status
            ;;
        "start")
            start_services "$service"
            ;;
        "stop")
            stop_services "$service"
            ;;
        "restart")
            restart_services "$service"
            ;;
        "logs")
            view_logs "$service"
            ;;
        "health")
            intelligent_health_check
            ;;
        "security")
            security_test
            ;;
        "diagnose")
            deep_diagnose
            ;;
        "test-auth")
            test_authentication
            ;;
        "test-api")
            test_comprehensive_api_endpoints
            ;;
        "test-all")
            test_all_comprehensive
            ;;
        "clean")
            clean_system
            ;;
        "fix")
            fix_system
            ;;
        "rebuild")
            rebuild_services "$service"
            ;;
        "backup")
            backup_database
            ;;
        "restore")
            restore_database
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 