#!/bin/bash

# ========================================
# MirageMakers AI Docker 安全停止脚本 v3.0
# 
# 系统架构：
# - 6个Docker容器的安全生命周期管理
# - 优雅停止机制
# - 服务状态监控
# - 日志查看功能
# 
# 更新 (January 2025):
# - 移除所有数据删除操作，确保数据安全
# - 优化服务停止流程
# - 改进状态检查和日志显示
# - 简化操作选项
# ========================================

# 脚本版本
SCRIPT_VERSION="3.0.0"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo ""
    echo "MirageMakers AI Docker 安全停止脚本 v${SCRIPT_VERSION}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --restart      重启所有服务"
    echo "  --status       显示服务状态"
    echo "  --logs         显示服务日志"
    echo "  --help, -h     显示帮助信息"
    echo ""
    echo "系统架构:"
    echo "  • Nginx: 反向代理和SSL终端 (Port 80/443)"
    echo "  • Frontend: Next.js前端应用 (Port 3000)"
    echo "  • Django: 后端API服务 (Port 8000)"
    echo "  • MySQL: 数据库服务 (Port 3306)"
    echo "  • Redis: 缓存和消息队列 (Port 6379)"
    echo "  • Celery: 异步任务处理 (Background)"
    echo ""
    echo "示例:"
    echo "  $0                停止所有服务"
    echo "  $0 --restart     重启所有服务"
    echo "  $0 --status      查看服务状态"
    echo "  $0 --logs        查看服务日志"
    echo ""
    echo "安全保证:"
    echo "  • 绝不删除数据卷和数据库数据"
    echo "  • 优雅停止所有服务"
    echo "  • 保留所有配置和用户数据"
    echo ""
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 确定使用的compose命令
if docker compose version >/dev/null 2>&1; then
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

# 检查服务状态
check_status() {
    log_step "检查MirageMakers AI服务状态..."
    echo ""
    
    # 显示容器状态
    echo "📋 容器状态:"
    $COMPOSE_CMD ps
    echo ""
    
    # 检查各服务健康状态
    local services=("nginx" "frontend" "django" "mysql" "redis" "celery")
    local running_count=0
    
    echo "🔍 服务健康检查:"
    for service in "${services[@]}"; do
        local container_name="MirageMakers-$service"
        
        if docker ps --filter "name=$container_name" --filter "status=running" | grep -q "$container_name"; then
            local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "no-health-check")
            
            if [[ "$health_status" == "healthy" ]]; then
                log_success "  $service: 运行正常 (健康)"
                running_count=$((running_count + 1))
            elif [[ "$health_status" == "unhealthy" ]]; then
                log_error "  $service: 运行异常 (不健康)"
            else
                log_info "  $service: 运行中 (无健康检查)"
                running_count=$((running_count + 1))
            fi
        else
            log_warn "  $service: 未运行"
        fi
    done
    
    echo ""
    log_info "运行状态: $running_count/${#services[@]} 服务正常运行"
    
    # 显示访问地址
    if [[ $running_count -gt 0 ]]; then
        echo ""
        echo "🌐 服务访问地址:"
        echo "  • 前端访问: https://miragemakers.ai/"
        echo "  • API接口: https://miragemakers.ai/api/"
        echo "  • 管理后台: https://miragemakers.ai/admin/"
    fi
    
    # 显示资源使用情况
    echo ""
    echo "📊 资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep MirageMakers || echo "无运行中的容器"
}

# 显示服务日志
show_logs() {
    log_step "显示服务日志 (最后30行)..."
    echo ""
    
    local services=("nginx" "frontend" "django" "mysql" "redis" "celery")
    
    for service in "${services[@]}"; do
        echo "================================================"
        echo "🔍 $service 服务日志:"
        echo "================================================"
        $COMPOSE_CMD logs --tail=30 $service 2>/dev/null || echo "服务 $service 无日志或未运行"
        echo ""
    done
}

# 重启服务
restart_services() {
    log_step "重启MirageMakers AI服务..."
    
    # 重启所有服务
    log_info "正在重启所有服务..."
    $COMPOSE_CMD restart
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    check_status
    
    log_success "服务重启完成"
}

# 安全停止服务
stop_services() {
    log_step "安全停止MirageMakers AI Docker系统..."
    
    # 显示将要停止的服务
    echo ""
    echo "📋 将停止以下服务:"
    echo "  • Nginx 反向代理 (SSL/HTTPS)"
    echo "  • Frontend (Next.js) - 端口 3000"
    echo "  • Django 后端 API - 端口 8000"
    echo "  • MySQL 数据库 - 端口 3306"
    echo "  • Redis 缓存 - 端口 6379"
    echo "  • Celery 任务队列"
    echo ""
    
    # 优雅停止所有服务
    log_info "优雅停止所有容器..."
    $COMPOSE_CMD stop
    
    # 移除容器但保留卷
    log_info "移除容器（保留数据卷）..."
    $COMPOSE_CMD down --remove-orphans
    
    # 显示保留的数据
    echo ""
    echo "💾 数据保护:"
    echo "  ✓ MySQL 数据库数据已保留"
    echo "  ✓ Redis 缓存数据已保留"
    echo "  ✓ Django 媒体文件已保留"
    echo "  ✓ Django 静态文件已保留"
    echo "  ✓ 所有配置文件已保留"
    echo ""
    
    # 显示剩余的Docker卷
    log_info "数据卷状态:"
    docker volume ls | grep -E "(mysql_data|redis_data|django_static|django_media)" || echo "未找到相关数据卷"
    
    log_success "MirageMakers AI Docker 系统已安全停止"
}

# 主函数
main() {
    echo "=== MirageMakers AI Docker 停止脚本 v${SCRIPT_VERSION} ==="
    echo ""
    
    # 解析参数
    case "${1:-}" in
        --restart)
            echo "🔄 重启 MirageMakers AI Docker 系统..."
            restart_services
            ;;
        --status)
            check_status
            ;;
        --logs)
            show_logs
            ;;
        --help|-h)
            show_help
            ;;
        "")
            echo "🛑 停止 MirageMakers AI Docker 系统..."
            stop_services
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    echo "💡 管理提示:"
    echo "  • 启动系统: ./start.sh"
    echo "  • 初始化环境: ./start.sh --init"
    echo "  • 更新部署: ./start.sh --update"
    echo "  • 查看状态: ./stop.sh --status"
    echo "  • 查看日志: ./stop.sh --logs"
    echo "  • 重启服务: ./stop.sh --restart"
    echo ""
    
    # 显示数据安全提醒
    echo "🔒 数据安全保证:"
    echo "  此脚本绝不会删除任何数据库数据、配置文件或用户数据"
    echo "  所有数据卷和重要文件都会被完整保留"
    echo ""
}

# 错误处理
set -euo pipefail
trap 'log_error "脚本执行出错，但数据安全未受影响"' ERR

# 执行主函数
main "$@" 