#!/bin/bash

# ==========================================
# MirageMakers AI 生产环境部署脚本 v4.0
# 专注于生产环境部署和管理
# 
# 系统架构：
# - Django 4.2.23 + Celery 5.5.3 后端
# - Next.js 14 前端 (生产模式优化)
# - MySQL 8.0 数据库
# - Redis 6.2.0 缓存和消息队列
# - Nginx 反向代理 (静态资源缓存优化)
# - Docker Compose 容器编排
# - 阿里云OSS自动备份
# 
# 优化特性 (January 2025 v4.0):
# - 专注生产环境，移除冗余功能
# - 简化部署流程，提高执行效率
# - 保留OSS备份和数据安全功能
# - 优化错误处理和日志输出
# ==========================================

# 脚本版本
SCRIPT_VERSION="4.0.0"

# 严格模式
set -euo pipefail

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 配置变量
MODE=""
BACKUP_OPTION=""
NETWORK_TYPE=""
COMPOSE_CMD=""

# 加载环境变量
load_env_variables() {
    if [[ -f ".env" ]]; then
        source .env
        log_success ".env 文件加载成功"
    else
        log_error ".env 文件不存在，请创建配置文件"
        exit 1
    fi
}

# 获取本机IP地址
get_local_ip() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
    else
        if [[ "$NETWORK_TYPE" == "wan" ]]; then
            log_info "检测公网IP..."
            if command -v curl >/dev/null; then
                PUBLIC_IP=$(curl -s --connect-timeout 3 http://ipinfo.io/ip 2>/dev/null || \
                           curl -s --connect-timeout 3 http://checkip.amazonaws.com 2>/dev/null)
                
                if [[ -n "$PUBLIC_IP" && "$PUBLIC_IP" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    LOCAL_IP="$PUBLIC_IP"
                    log_info "使用公网IP: $LOCAL_IP"
                else
                    LOCAL_IP=$(hostname -I | awk '{print $1}')
                    log_warn "无法获取公网IP，使用内网IP: $LOCAL_IP"
                fi
            else
                LOCAL_IP=$(hostname -I | awk '{print $1}')
                log_warn "curl不可用，使用内网IP: $LOCAL_IP"
            fi
        else
            LOCAL_IP=$(hostname -I | awk '{print $1}')
            log_info "使用内网IP: $LOCAL_IP"
        fi
    fi
    
    if [[ -z "$LOCAL_IP" ]]; then
        LOCAL_IP="127.0.0.1"
        log_warn "无法获取本机IP，使用默认地址: $LOCAL_IP"
    fi
}

# 更新网络配置
update_network_config() {
    if [[ -z "$NETWORK_TYPE" ]]; then
        return 0
    fi
    
    log_step "更新网络配置 ($NETWORK_TYPE)..."
    get_local_ip
    
    # 备份配置
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # 更新配置
    if [[ "$NETWORK_TYPE" == "wan" ]]; then
        log_info "配置广域网访问..."
        sed -i.bak "s|PRIMARY_DOMAIN=.*|PRIMARY_DOMAIN=$LOCAL_IP|g" .env
        sed -i.bak "s|API_DOMAIN=.*|API_DOMAIN=$LOCAL_IP|g" .env
        sed -i.bak "s|FRONTEND_DOMAIN=.*|FRONTEND_DOMAIN=$LOCAL_IP|g" .env
        sed -i.bak "s|CORS_ALLOWED_ORIGINS=.*|CORS_ALLOWED_ORIGINS=http://$LOCAL_IP:3000,https://$LOCAL_IP:3000|g" .env
        sed -i.bak "s|CSRF_TRUSTED_ORIGINS=.*|CSRF_TRUSTED_ORIGINS=http://$LOCAL_IP:3000,https://$LOCAL_IP:3000|g" .env
        sed -i.bak "s|ALLOWED_HOSTS=.*|ALLOWED_HOSTS=$LOCAL_IP,localhost,127.0.0.1|g" .env
    elif [[ "$NETWORK_TYPE" == "local" ]]; then
        log_info "配置局域网访问..."
        sed -i.bak "s|PRIMARY_DOMAIN=.*|PRIMARY_DOMAIN=$LOCAL_IP|g" .env
        sed -i.bak "s|API_DOMAIN=.*|API_DOMAIN=$LOCAL_IP|g" .env
        sed -i.bak "s|FRONTEND_DOMAIN=.*|FRONTEND_DOMAIN=$LOCAL_IP|g" .env
        sed -i.bak "s|PROTOCOL=.*|PROTOCOL=http|g" .env
        sed -i.bak "s|CORS_ALLOWED_ORIGINS=.*|CORS_ALLOWED_ORIGINS=http://$LOCAL_IP:3000,http://localhost:3000|g" .env
        sed -i.bak "s|CSRF_TRUSTED_ORIGINS=.*|CSRF_TRUSTED_ORIGINS=http://$LOCAL_IP:3000,http://localhost:3000|g" .env
        sed -i.bak "s|ALLOWED_HOSTS=.*|ALLOWED_HOSTS=$LOCAL_IP,localhost,127.0.0.1|g" .env
    fi
    
    rm -f .env.bak
    log_success "网络配置更新完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
MirageMakers AI 生产环境启动脚本 v${SCRIPT_VERSION}

用法: $0 [模式] [网络类型] [备份选项] [选项]

启动模式:
  --init          初始化生产环境 - 完整部署和初始化
  --update        更新生产环境 - 智能检测变更并更新
  --start         启动生产环境（默认）- 正常启动服务

网络类型 (仅--init模式支持):
  wan             广域网环境 - 配置公网IP访问
  local           局域网环境 - 配置本机IP访问

备份选项 (仅--update模式支持):
  backup          更新前执行OSS备份
  without-backup  跳过备份直接更新

选项:
  --help, -h      显示此帮助信息

生产环境特性:
  🚀 优化构建（多阶段Docker构建）
  🔒 安全配置（资源限制、权限控制）
  📦 OSS自动备份（每日21:30）
  🛡️ 数据安全（绝不删除现有数据）
  ⚡ 性能优化（针对生产环境调优）
  📡 WebSocket实时通信（高性能、低延迟）

示例:
  $0 --init wan           # 初始化广域网生产环境
  $0 --init local         # 初始化局域网生产环境
  $0 --update backup      # 更新前执行备份
  $0 --update without-backup  # 跳过备份直接更新
  $0                      # 正常启动生产环境

生产环境访问:
  前端: https://$\{PRIMARY_DOMAIN\}/
  后端: https://$\{PRIMARY_DOMAIN\}/api/
  WebSocket: wss://$\{PRIMARY_DOMAIN\}/ws/
  管理: https://$\{PRIMARY_DOMAIN\}/admin/
EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --init)
                MODE="init"
                shift
                if [[ $# -gt 0 && ($1 == "wan" || $1 == "local") ]]; then
                    NETWORK_TYPE="$1"
                    shift
                fi
                ;;
            --update)
                MODE="update"
                shift
                if [[ $# -gt 0 && ($1 == "backup" || $1 == "without-backup") ]]; then
                    BACKUP_OPTION="$1"
                    shift
                else
                    log_error "--update模式需要指定备份选项: backup 或 without-backup"
                    show_help
                    exit 1
                fi
                ;;
            --start)
                MODE="start"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 默认启动模式
    if [[ -z "$MODE" ]]; then
        MODE="start"
    fi
}

# 检查生产环境
check_production_environment() {
    log_step "检查生产环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 设置Docker Compose命令
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    # 检查必要文件
    local required_files=("docker-compose.yml" ".env")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "必要文件不存在: $file"
            exit 1
        fi
    done
    
    # 创建必要目录
    mkdir -p logs database/mysql_data_backup
    
    log_success "生产环境检查通过"
}

# 构建和启动服务
build_and_start_services() {
    log_step "构建和启动生产服务..."
    
    # 停止现有服务
    $COMPOSE_CMD down 2>/dev/null || true
    
    # 构建服务
    log_info "构建生产镜像..."
    $COMPOSE_CMD build --no-cache
    
    # 启动服务
    log_info "启动生产服务..."
    $COMPOSE_CMD up -d
    
    log_success "生产服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_step "等待服务就绪..."
    
    # 等待MySQL
    local mysql_wait=0
    local mysql_max_wait=60
    log_info "等待MySQL服务..."
    while [[ $mysql_wait -lt $mysql_max_wait ]]; do
        if docker exec MirageMakers-mysql mysqladmin ping -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" >/dev/null 2>&1; then
            log_success "MySQL服务就绪"
            break
        fi
        echo -n "."
        sleep 2
        mysql_wait=$((mysql_wait + 2))
    done
    
    if [[ $mysql_wait -ge $mysql_max_wait ]]; then
        log_error "MySQL服务启动超时"
        return 1
    fi
    
    # 等待Django
    local django_wait=0
    local django_max_wait=60
    log_info "等待Django服务..."
    while [[ $django_wait -lt $django_max_wait ]]; do
        if docker exec MirageMakers-django python manage.py check --database default >/dev/null 2>&1; then
            log_success "Django服务就绪"
            break
        fi
        echo -n "."
        sleep 2
        django_wait=$((django_wait + 2))
    done
    
    if [[ $django_wait -ge $django_max_wait ]]; then
        log_error "Django服务启动超时"
        return 1
    fi

    # 等待Daphne WebSocket服务
    local daphne_wait=0
    local daphne_max_wait=60
    log_info "等待Daphne WebSocket服务..."
    while [[ $daphne_wait -lt $daphne_max_wait ]]; do
        if docker ps --filter "name=MirageMakers-daphne" --filter "status=running" | grep -q "Up"; then
            # 检查WebSocket端口是否监听
            if docker exec MirageMakers-daphne netstat -tlnp 2>/dev/null | grep -q ":8001" 2>/dev/null || \
               curl -f --max-time 5 http://localhost:8001/health/ >/dev/null 2>&1; then
                log_success "Daphne WebSocket服务就绪"
                break
            fi
        fi
        echo -n "."
        sleep 2
        daphne_wait=$((daphne_wait + 2))
    done

    if [[ $daphne_wait -ge $daphne_max_wait ]]; then
        log_warn "Daphne WebSocket服务启动超时，但不影响基础功能"
    fi
    
    log_success "所有核心服务就绪"
}

# 执行OSS备份
perform_oss_backup() {
    log_step "执行OSS数据库备份..."
    
    if ! docker ps --filter "name=MirageMakers-mysql" --filter "status=running" | grep -q "MirageMakers-mysql"; then
        log_warn "MySQL容器未运行，跳过备份"
        return 0
    fi
    
    if [[ -f "oss_backup.sh" ]]; then
        log_info "执行OSS备份脚本..."
        ./oss_backup.sh
        log_success "OSS备份完成"
    else
        log_warn "OSS备份脚本不存在，跳过备份"
    fi
}

# 初始化数据库
initialize_database() {
    log_step "初始化生产数据库..."
    
    # 执行迁移
    log_info "执行数据库迁移..."
    docker exec MirageMakers-django python manage.py migrate
    
    # 创建管理员用户
    log_info "创建管理员用户..."
    docker exec MirageMakers-django python manage.py shell -c "
from core.models import User
from rest_framework.authtoken.models import Token

# 创建管理员用户
if not User.objects.filter(email='$ADMIN_EMAIL').exists():
    admin_user = User.objects.create_superuser(
        email='$ADMIN_EMAIL',
        username='$ADMIN_USERNAME',
        password='$ADMIN_PASSWORD'
    )
    Token.objects.get_or_create(user=admin_user)
    print('✓ 管理员用户创建成功')
else:
    print('✓ 管理员用户已存在')
"
    
    # 收集静态文件
    log_info "收集静态文件..."
    docker exec MirageMakers-django python manage.py collectstatic --noinput --clear
    
    log_success "数据库初始化完成"
}

# 更新数据库
update_database() {
    log_step "更新生产数据库..."
    
    # 根据备份选项执行备份
    if [[ "$BACKUP_OPTION" == "backup" ]]; then
        perform_oss_backup
    else
        log_info "跳过备份，直接更新"
    fi
    
    # 执行迁移
    log_info "执行数据库迁移..."
    docker exec MirageMakers-django python manage.py migrate
    
    # 更新静态文件
    log_info "更新静态文件..."
    docker exec MirageMakers-django python manage.py collectstatic --noinput --clear
    
    log_success "数据库更新完成"
}

# 安装ossutil
install_ossutil() {
    log_info "配置OSS备份工具..."
    
    if command -v ossutil &> /dev/null; then
        log_success "ossutil已安装"
        return 0
    fi
    
    # 检测系统架构
    local arch=$(uname -m)
    local os_type=$(uname -s | tr '[:upper:]' '[:lower:]')
    local ossutil_url=""
    
    case "$arch" in
        "x86_64")
            if [[ "$os_type" == "linux" ]]; then
                ossutil_url="https://gosspublic.alicdn.com/ossutil/1.7.19/ossutil-v1.7.19-linux-amd64.zip"
            elif [[ "$os_type" == "darwin" ]]; then
                ossutil_url="https://gosspublic.alicdn.com/ossutil/1.7.19/ossutil-v1.7.19-mac-amd64.zip"
            fi
            ;;
        "arm64"|"aarch64")
            if [[ "$os_type" == "linux" ]]; then
                ossutil_url="https://gosspublic.alicdn.com/ossutil/1.7.19/ossutil-v1.7.19-linux-arm64.zip"
            elif [[ "$os_type" == "darwin" ]]; then
                ossutil_url="https://gosspublic.alicdn.com/ossutil/1.7.19/ossutil-v1.7.19-mac-arm64.zip"
            fi
            ;;
        *)
            log_warn "不支持的系统架构: $arch，跳过ossutil安装"
            return 1
            ;;
    esac
    
    if [[ -z "$ossutil_url" ]]; then
        log_warn "无法确定ossutil下载链接"
        return 1
    fi
    
    # 安装ossutil
    local temp_dir="/tmp/ossutil_install"
    mkdir -p "$temp_dir"
    cd "$temp_dir"
    
    if command -v wget &> /dev/null; then
        wget -q "$ossutil_url" -O ossutil.zip
    elif command -v curl &> /dev/null; then
        curl -s -L "$ossutil_url" -o ossutil.zip
    else
        log_error "需要wget或curl来下载ossutil"
        return 1
    fi
    
    unzip -q ossutil.zip
    local ossutil_binary=$(find . -name "ossutil*" -type f -executable | head -1)
    
    if [[ -n "$ossutil_binary" ]]; then
        sudo cp "$ossutil_binary" /usr/local/bin/ossutil
        sudo chmod +x /usr/local/bin/ossutil
        log_success "ossutil安装成功"
        
        # 配置ossutil
        if [[ -n "${OSS_ENDPOINT:-}" && -n "${OSS_ACCESS_KEY_ID:-}" ]]; then
            ossutil config -e "$OSS_ENDPOINT" -i "$OSS_ACCESS_KEY_ID" -k "$OSS_ACCESS_KEY_SECRET" -L CH --output-dir=/tmp
            log_success "ossutil配置完成"
        fi
    else
        log_error "ossutil安装失败"
        return 1
    fi
    
    cd - > /dev/null
    rm -rf "$temp_dir"
}

# 设置OSS备份
setup_oss_backup() {
    log_step "配置OSS自动备份..."
    
    install_ossutil
    
    # 设置定时任务
    log_info "配置定时任务..."
    (crontab -l 2>/dev/null || true; echo "30 21 * * * cd $(pwd) && ./oss_backup.sh >> logs/backup.log 2>&1") | sort -u | crontab -
    
    # 执行首次备份
    perform_oss_backup
    
    log_success "OSS自动备份配置完成（每晚21:30执行）"
}

# 验证部署
verify_deployment() {
    log_step "验证生产环境部署..."
    
    # 检查服务状态
    log_info "检查服务状态..."
    $COMPOSE_CMD ps
    
    # 检查数据库连接
    if docker exec MirageMakers-django python manage.py check --database default >/dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    # 检查前端访问
    local frontend_url="http://localhost:3000"
    if curl -s "$frontend_url" >/dev/null 2>&1; then
        log_success "前端服务正常"
    else
        log_warn "前端服务可能需要时间启动"
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成！"
    echo ""
    echo "=== MirageMakers AI 生产环境 ==="
    echo "启动模式: $MODE"
    echo "脚本版本: $SCRIPT_VERSION"
    echo "部署时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo "=== 访问地址 ==="
    echo "前端应用: https://$PRIMARY_DOMAIN/"
    echo "Django Admin: https://$PRIMARY_DOMAIN/admin/"
    echo "WebSocket连接: wss://$PRIMARY_DOMAIN/ws/"
    echo "API接口: https://$PRIMARY_DOMAIN/api/"
    echo "健康检查: https://$PRIMARY_DOMAIN/health/"
    echo ""
    echo "=== 实时通信 ==="
    echo "WebSocket服务: ✅ Daphne (端口8001)"
    echo "实时推送: ✅ 任务进度、完成通知"
    echo "连接协议: wss:// (安全WebSocket)"
    echo ""
    echo "=== 管理员账户 ==="
    echo "Email: $ADMIN_EMAIL"
    echo "Username: $ADMIN_USERNAME"
    echo ""
    echo "=== 自动备份 ==="
    echo "OSS备份: 每晚21:30自动执行"
    echo "本地备份: database/mysql_data_backup/"
    echo ""
    echo "=== 服务管理 ==="
    echo "状态检查: ./manage_services.sh --prod status"
    echo "健康检查: ./manage_services.sh --prod health"
    echo "停止服务: ./stop.sh"
    echo ""
}

# 初始化环境变量
load_env_variables

# 主函数
main() {
    echo "=== MirageMakers AI 生产环境启动脚本 v${SCRIPT_VERSION} ==="
    echo ""
    
    # 解析参数
    parse_arguments "$@"
    
    log_info "启动模式: $MODE"
    if [[ -n "$NETWORK_TYPE" ]]; then
        log_info "网络类型: $NETWORK_TYPE"
    fi
    if [[ "$MODE" == "update" ]]; then
        log_info "备份选项: $BACKUP_OPTION"
    fi
    echo ""
    
    # 更新网络配置（仅init模式）
    if [[ "$MODE" == "init" ]]; then
        update_network_config
        # 重新加载环境变量
        if [[ -n "$NETWORK_TYPE" ]]; then
            load_env_variables
        fi
    fi
    
    # 执行主要流程
    check_production_environment
    build_and_start_services
    wait_for_services
    
    # 根据模式执行不同操作
    case "$MODE" in
        "init")
            initialize_database
            setup_oss_backup
            ;;
        "update")
            update_database
            ;;
        "start")
            log_info "正常启动模式，跳过初始化"
            ;;
    esac
    
    verify_deployment
    show_deployment_info
    
    log_success "生产环境启动完成！"
}

# 错误处理
trap 'log_error "脚本执行失败，请检查错误信息"' ERR

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
