# MirageMakers AI 容器服务管理

## 🎉 系统状态

✅ **MirageMakers AI 已完全部署并正常运行**

- 🚀 所有 6 个容器服务健康运行
- 📧 邮件系统完全配置并正常工作
- 🔄 服务管理功能已修复完成
- 🔗 WebSocket实时通信系统已部署
- 📊 智能健康检查和诊断系统已优化

> 📢 **最新更新**: 查看 [UPDATE_SUMMARY.md](UPDATE_SUMMARY.md) 了解v3.0重要功能更新

## 📋 服务列表

| 服务 | 容器名 | 端口 | 状态 |
|------|--------|------|------|
| 反向代理 | MirageMakers-nginx | 80/443 | ✅ 健康 |
| 前端 | MirageMakers-frontend | 3000 | ✅ 健康 |
| 后端 | MirageMakers-django | 8000 | ✅ 健康 |
| WebSocket | MirageMakers-daphne | 8001 | ✅ 健康 |
| 数据库 | MirageMakers-mysql | 3306 | ✅ 健康 |
| 缓存 | MirageMakers-redis | 6379 | ✅ 健康 |
| 任务队列 | MirageMakers-celery | - | ✅ 健康 |

## 🛠️ 服务管理

### 快速开始

#### 1. 获取代码
```bash
# 克隆项目
git clone https://github.com/qingshu8822/visual_gen_agent.git
cd visual_gen_agent/docker

# 检查系统依赖
./start.sh --help
```

#### 2. 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件 (重要)
nano .env
```

#### 3. SSL证书配置
```bash
# 将SSL证书文件放到指定目录
cp your_domain.pem docker/nginx/miragemakers.ai.pem
cp your_domain.key docker/nginx/miragemakers.ai.key

# 生成DH参数 (首次部署)
openssl dhparam -out docker/nginx/dhparam.pem 2048
```

#### 4. 智能部署
```bash
# 广域网环境初始化部署（自动配置公网IP访问）
./start.sh --init wan

# 局域网环境初始化部署（自动配置本机IP访问）
./start.sh --init local

# 更新部署（含数据库备份）
./start.sh --update backup

# 更新部署（跳过备份）
./start.sh --update without-backup

# 普通启动模式（使用现有配置）
./start.sh --start

# 兼容模式（使用现有.env配置）
./start.sh --init

# 显示详细输出
./start.sh --init wan --verbose

# 等待服务启动完成 (约2-3分钟)
# 完成后会显示访问地址
```

#### 5. 验证部署
```bash
# 查看所有服务状态
./manage_services.sh status

# 智能健康检查
./manage_services.sh health

# 安全配置测试
./manage_services.sh security

# 深度诊断
./manage_services.sh diagnose

# 修复系统配置问题
./manage_services.sh fix
```

### 🔧 高级管理

#### 核心脚本功能

##### start.sh - 智能部署脚本 v3.0
```bash
# 网络自动配置部署（仅--init模式支持）
./start.sh --init wan                    # 广域网初始化（自动配置公网IP访问）
./start.sh --init local                  # 局域网初始化（自动配置本机IP访问）

# 更新部署模式
./start.sh --update backup               # 更新（含数据库备份）
./start.sh --update without-backup       # 更新（跳过备份）

# 基础部署模式
./start.sh --init                        # 纯初始化（使用现有.env配置）
./start.sh --start                       # 普通启动（使用现有配置）

# 选项参数
./start.sh --verbose                     # 显示详细输出
./start.sh --help                        # 显示帮助信息

# 网络自动配置特性（仅--init模式）:
#   🔧 wan: 自动获取公网IP，更新域名和CORS配置
#   🔧 local: 使用本机局域网IP，适合本地开发
#   🔧 仅更新网络相关配置，保持其他.env设置不变
#   🔧 自动备份.env文件，安全可靠

# 备份选项说明（仅--update模式）:
#   🔧 backup: 执行数据库OSS备份后更新
#   🔧 without-backup: 跳过备份直接更新，适合开发环境

# 智能更新特性:
#   🔧 智能检测代码变更，仅重启必要的服务
#   🔧 Nginx配置热重载（nginx -s reload）
#   🔧 Redis配置在线重载（CONFIG REWRITE）
#   🔧 静态文件自动收集和更新
#   🔧 最小化业务中断，最大化可用性
#   🔧 自动配置数据库备份到阿里云OSS
#   🔧 设置定时任务每晚21:30备份数据库
#   🔧 绝不删除现有数据，确保数据安全
```

##### stop.sh - 安全停止脚本 v3.0
```bash
./stop.sh                    # 安全停止所有服务
./stop.sh --restart          # 重启所有服务
./stop.sh --status           # 查看服务状态
./stop.sh --logs             # 显示服务日志
./stop.sh --help             # 显示帮助信息

# 安全保证:
#   🔒 绝不删除数据卷和数据库数据
#   🔒 优雅停止所有服务
#   🔒 保留所有配置和用户数据
#   🔒 显示数据保护状态
```

##### manage_services.sh - 高级服务管理 v3.0
```bash
# 基础服务管理
./manage_services.sh status           # 查看服务状态
./manage_services.sh start [service]  # 启动服务
./manage_services.sh stop [service]   # 停止服务
./manage_services.sh restart [service] # 重启服务
./manage_services.sh logs [service]   # 查看日志

# 智能诊断和测试（v3.0 大幅优化）
./manage_services.sh health           # 智能健康检查和诊断（95%健康度）
./manage_services.sh security         # 安全配置测试  
./manage_services.sh diagnose         # 深度诊断和故障排查
./manage_services.sh test-auth        # 认证功能测试（注册、登录、验证）
./manage_services.sh test-api         # 全面API端点测试（29个端点）
./manage_services.sh test-all         # 综合测试套件（健康+安全+认证+API）

# 系统维护
./manage_services.sh rebuild [service] # 重新构建服务
./manage_services.sh clean            # 清理系统
./manage_services.sh fix              # 修复CSRF/CORS和前后端对接问题
./manage_services.sh backup          # 执行数据库备份（仅生产环境）
./manage_services.sh restore         # 恢复数据库数据（仅生产环境）

# v3.0 新增特性:
#   🔧 统一支持生产和开发环境（--prod和--dev参数）
#   🔧 智能健康检查：95%健康度，减少误报
#   🔧 全面API测试：29个端点，6大功能模块
#   🔧 四级结果分类：PASS/FAIL/WARN/INFO
#   🔧 智能容器名称匹配和状态解析
#   🔧 抑制Docker Compose WARNING信息干扰

# 支持的服务名:
#   nginx        - Nginx 反向代理/SSL终端
#   mysql        - MySQL 数据库
#   redis        - Redis 缓存
#   django       - Django 后端
#   daphne       - Daphne WebSocket服务
#   celery       - Celery 任务队列
#   frontend     - Next.js 前端
```

#### 服务管理命令详解
```bash
# 查看详细服务状态
./manage_services.sh status

# 重启特定服务
./manage_services.sh restart frontend
./manage_services.sh restart django

# 查看服务日志
./manage_services.sh logs django
./manage_services.sh logs frontend

# 重启所有服务
./manage_services.sh restart

# 深度诊断和故障排查
./manage_services.sh diagnose

# 安全配置测试
./manage_services.sh security

# 修复系统配置问题
./manage_services.sh fix

# 重新构建服务
./manage_services.sh rebuild frontend
```

## 🌐 访问地址

### 生产环境访问
- **前端页面**: https://miragemakers.ai/
- **Django API**: https://miragemakers.ai/api/
- **Django Admin**: https://miragemakers.ai/admin/
- **WebSocket连接**: wss://miragemakers.ai/ws/
- **健康检查**: https://miragemakers.ai/health/
  - 用户名: `admin`
  - 密码: `admin123`

### 本地开发访问
- **前端页面**: http://localhost:3000
- **Django API**: http://localhost:8000
- **Django Admin**: http://localhost:8000/admin/
- **WebSocket连接**: ws://localhost:8001/ws/
- **健康检查**: http://localhost:8000/health/

### 实时通信特性
- **WebSocket服务**: Daphne ASGI服务器
- **实时功能**: 任务进度推送、完成通知、错误提醒
- **连接协议**: 生产环境使用wss://，开发环境使用ws://
- **自动重连**: 支持断线自动重连机制
- **状态指示**: 前端界面显示连接状态

## 🏗️ 系统架构

### Docker Compose 服务配置

#### 核心服务
- **Nginx**: 反向代理和SSL终端 (端口 80/443)
- **MySQL 8.0**: 数据库服务 (端口 3306)
- **Redis 7**: 缓存和消息队列 (端口 6379)

#### 应用服务
- **Django 4.2.23**: 后端API服务 (端口 8000)
- **Daphne**: WebSocket ASGI服务 (端口 8001)
- **Celery 5.5.3**: 异步任务处理 (后台服务)
- **Next.js 14**: 前端应用 (端口 3000)

#### 数据卷
- `mysql_data`: MySQL数据库数据
- `redis_data`: Redis缓存数据
- `django_static`: Django静态文件
- `django_media`: Django媒体文件

#### 网络配置
- `MirageMakers_network`: 桥接网络，所有服务互联

#### WebSocket架构 (v3.0 优化)
- **Daphne服务**: 处理WebSocket连接和实时通信（端口8001）
- **Nginx代理**: 支持WebSocket协议升级和wss://加密
- **Redis Channels**: WebSocket消息的后端存储
- **Django Channels**: WebSocket消费者和路由管理
- **前端集成**: useWebSocket Hook和连接状态可视化
- **自动重连**: 支持断线自动重连（最多5次）
- **实时推送**: 任务进度、完成通知、错误提醒

## 📧 邮件系统

### 当前配置
- **邮件后端**: Django 直接使用 QQ企业邮箱 SMTP
- **发送者**: `MirageMakers AI <<EMAIL>>`
- **SMTP服务器**: 企业邮箱SMTP (SSL/TLS)

### 支持功能
- ✅ 用户注册验证邮件
- ✅ 密码重置邮件
- ✅ 系统通知邮件
- ✅ 多语言支持 (中文/英文)

### 测试邮件发送
```bash
docker compose exec django python manage.py shell -c "
from django.core.mail import send_mail
from django.conf import settings
send_mail(
    'MirageMakers AI 测试邮件',
    '邮件系统正常工作！',
    settings.DEFAULT_FROM_EMAIL,
    ['<EMAIL>'],
    fail_silently=False
)
"
```

## 💰 Token系统

### 系统架构
- **智能预检查**: 在任务执行前检查token余额和有效期
- **自动过期管理**: 过期token自动清零，防止滥用
- **精确消费控制**: 仅对成功执行的任务扣除token
- **用户友好提示**: 余额不足或过期时显示详细指导

### Token消费规则
| 服务类型 | Token消费 | 描述 |
|----------|-----------|------|
| Text to Image | 400 | 文本生成图片 |
| Image Editing | 400 | 图片编辑处理 |
| Text to Video | 800 | 文本生成视频 |
| Image to Video | 3000 | 图片转视频 |
| Multi-image Video | 3000 | 多图合成视频 |
| Chat API | 1 | 对话服务 |
| Video Keyframe | 0 | 视频关键帧提取（免费） |

### Token有效期管理
- **默认有效期**: 购买后30天内有效
- **自动检查**: 每次任务执行前检查是否过期
- **过期处理**: 过期token自动清零，避免误用
- **续费提醒**: 过期时提供购买指导

### 余额检查流程
1. **用户发起请求** → 系统接收任务
2. **计划生成阶段** → AI Agent分析任务类型和所需token
3. **预执行检查** → 检查token是否过期
4. **余额验证** → 检查token余额是否充足
5. **任务执行** → 仅在检查通过后执行
6. **精确扣费** → 仅对成功任务扣除相应token

### Token安全特性
- ✅ **执行前检查**: 防止余额不足时执行任务
- ✅ **过期自动清理**: 过期token自动归零
- ✅ **精确消费**: 失败任务不扣费
- ✅ **透明计费**: 详细的消费记录和统计
- ✅ **智能提醒**: 余额不足时提供购买指导

### 测试Token功能
```bash
# 检查用户token状态
docker compose exec django python manage.py shell -c "
from core.models import User
user = User.objects.filter(email='<EMAIL>').first()
if user:
    print(f'用户: {user.email}')
    print(f'Token余额: {user.tokens}')
    print(f'过期时间: {user.tokens_expires_at}')
    print(f'是否过期: {user.is_tokens_expired}')
else:
    print('用户不存在')
"

# 手动添加token（测试用）
docker compose exec django python manage.py shell -c "
from core.models import User
from django.utils import timezone
from datetime import timedelta
user = User.objects.filter(email='<EMAIL>').first()
if user:
    user.tokens = 1000
    user.tokens_expires_at = timezone.now() + timedelta(days=30)
    user.save()
    print(f'已为 {user.email} 添加 1000 tokens，30天后过期')
"
```

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
```bash
   # 检查日志
   ./manage_services.sh logs [service_name]

   # 深度诊断
   ./manage_services.sh diagnose

   # 重新构建
   ./manage_services.sh rebuild [service_name]
```

2. **WebSocket连接失败**
```bash
   # 检查Daphne服务状态
   docker ps --filter "name=MirageMakers-daphne"
   
   # 查看WebSocket服务日志
   ./manage_services.sh logs daphne
   
   # 检查Nginx WebSocket代理配置
   docker exec MirageMakers-nginx nginx -t
   
   # 测试WebSocket连接
   curl -f https://miragemakers.ai/health/
   
   # 重启WebSocket服务
   ./manage_services.sh restart daphne
```

3. **SSL WebSocket连接问题**
```bash
   # 检查SSL证书
   openssl s_client -connect miragemakers.ai:443 -servername miragemakers.ai
   
   # 检查Nginx WebSocket升级配置
   docker exec MirageMakers-nginx cat /etc/nginx/conf.d/default.conf | grep -A 10 "location /ws/"
   
   # 测试wss://连接
   wscat -c wss://miragemakers.ai/ws/user/
```

4. **实时推送延迟或失败**
```bash
   # 检查Redis Channels层
   docker exec MirageMakers-redis redis-cli
   # 在Redis CLI中执行: KEYS asgi:*
   
   # 检查Celery任务与WebSocket集成
   ./manage_services.sh logs celery | grep "WebSocket"
   
   # 手动测试WebSocket推送
   docker exec MirageMakers-django python manage.py shell -c "
   from core.websocket_utils import notify_task_completed
   notify_task_completed('test-user-id', 'test-task-id', 'Test completed')
   "
```

5. **邮件发送失败**
```bash
   # 检查邮件配置
   docker compose exec django python manage.py shell -c "
   from django.conf import settings
   print(f'EMAIL_HOST: {settings.EMAIL_HOST}')
   print(f'EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}')
   print(f'DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}')
   "
   ```

6. **前端无法访问**
```bash
   # 检查前端日志
   ./manage_services.sh logs frontend
   
   # 重启前端服务
   ./manage_services.sh restart frontend
   ```

7. **数据库连接失败**
```bash
   # 检查MySQL日志
   ./manage_services.sh logs mysql
   
   # 健康检查
   ./manage_services.sh health
   
   # 重启数据库
   ./manage_services.sh restart mysql
   ```

8. **CSRF/CORS问题**
```bash
   # 修复前后端对接问题
   ./manage_services.sh fix
   ```

### 系统测试和诊断
```bash
# 执行所有测试
./manage_services.sh test-all

# 认证功能测试
./manage_services.sh test-auth

# API端点测试
./manage_services.sh test-api

# 安全配置测试
./manage_services.sh security
```

### 完全重置
```bash
# 清理所有数据（⚠️ 会删除所有数据）
./manage_services.sh clean

# 重新初始化部署
./start.sh --init
```

## 📁 目录结构

```
docker/
├── start.sh              # 智能部署脚本 v4.0
├── stop.sh               # 安全停止脚本 v3.0
├── manage_services.sh    # 高级服务管理脚本 v3.0 (统一环境管理)
├── start-dev.sh          # 开发环境启动脚本 v3.0
├── stop-dev.sh           # 开发环境停止脚本 v2.0
├── docker-compose.yml    # 生产环境 Docker Compose 配置
├── docker-compose.dev.yml # 开发环境 Docker Compose 配置
├── .env                  # 环境变量配置
├── README.md             # 生产环境文档
├── README_dev.md         # 开发环境文档
├── django/               # Django 容器配置
├── frontend/             # 前端容器配置
├── nginx/                # Nginx 配置
├── mysql/                # 数据库配置
├── redis/                # 缓存配置
├── scripts/              # 辅助脚本
└── logs/                 # 日志目录
```

## 🚀 部署流程

### 1. 初始部署

#### 广域网部署（云服务器）
```bash
   # 自动配置公网IP访问
   ./start.sh --init wan
```

#### 局域网部署（本地开发）
```bash
   # 自动配置本机IP访问
   ./start.sh --init local
```

#### 使用现有配置部署
```bash
   # 使用现有.env文件配置
   ./start.sh --init
```

### 2. 日常管理
```bash
   # 查看服务状态
   ./manage_services.sh status
   
   # 健康检查
   ./manage_services.sh health
   
   # 安全测试
   ./manage_services.sh security
   ```

### 3. 生产环境更新
```bash
   # 拉取最新代码
   git pull
   
   # 生产环境更新（含数据库备份）
   ./start.sh --update backup
   ```

### 4. 开发环境更新
```bash
   # 拉取最新代码
   git pull
   
   # 开发环境快速更新（跳过备份）
   ./start.sh --update without-backup
   ```

### 5. 故障排除
```bash
   # 深度诊断
   ./manage_services.sh diagnose
   
   # 修复配置问题
   ./manage_services.sh fix
   
   # 重新构建服务
   ./manage_services.sh rebuild
   ```

## 🔐 安全特性

### 数据保护
- ✅ 绝不删除现有数据
- ✅ 自动数据库备份到阿里云OSS
- ✅ 定时任务每晚21:30备份
- ✅ 数据卷持久化存储

### 服务安全
- ✅ SSL/TLS加密通信
- ✅ 容器网络隔离
- ✅ 健康检查和自动重启
- ✅ 配置文件权限控制

### 访问控制
- ✅ Nginx反向代理保护
- ✅ CORS跨域配置
- ✅ CSRF防护机制
- ✅ API认证和授权

---

**MirageMakers AI** - 智能化部署，专业级服务管理 🎯