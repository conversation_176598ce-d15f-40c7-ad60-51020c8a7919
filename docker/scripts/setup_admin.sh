docker exec MirageMakers-django python manage.py shell -c "
from core.models import User
from django.contrib.auth.hashers import make_password
from rest_framework.authtoken.models import Token
import uuid
import os

admin_user, created = User.objects.get_or_create(
    email='<EMAIL>',
    defaults={
        'id': str(uuid.uuid4()).replace('-', ''),
        'name': 'Administrator',
        'username': 'admin',
        'password': make_password(os.getenv('ADMIN_PASSWORD', 'your_default_password')),
        'is_staff': True,
        'is_superuser': True,
        'is_active': True,
        'is_email_verified': True,
        'tokens': 0,
        'total_generations': 0,
        'current_plan': 'Premium Plan',
        'invitation_type': 'ADMIN_INVITATION'
    }
)

if created:
    print('✓ 管理员用户创建成功: <EMAIL>')
else:
    print('✓ 管理员用户已存在: <EMAIL>')

token, _ = Token.objects.get_or_create(user=admin_user)
print('✓ 管理员API Token:', token.key)
"