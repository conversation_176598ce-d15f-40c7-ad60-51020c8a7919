#!/bin/bash
# Setup cron jobs for token expiry management

echo "🔧 Setting up token expiry management cron jobs..."

# Create cron job entries
CRON_ENTRIES="
# Token Expiry Management - Run daily at 9:00 AM
0 9 * * * cd /home/<USER>/visual_gen_agent && python3 manage.py manage_token_expiry --send-reminders
30 9 * * * cd /home/<USER>/visual_gen_agent && python3 manage.py manage_token_expiry --clear-expired

# Annual Plan Token Renewal - Run daily at 10:00 AM
0 10 * * * cd /home/<USER>/visual_gen_agent && python3 manage.py manage_token_expiry --renew-annual

# Weekly cleanup check - Run every Sunday at 2:00 AM
0 2 * * 0 cd /home/<USER>/visual_gen_agent && python3 manage.py manage_token_expiry --send-reminders --clear-expired --renew-annual
"

# Add to crontab
echo "Adding cron jobs..."
(crontab -l 2>/dev/null; echo "$CRON_ENTRIES") | crontab -

echo "✅ Cron jobs added successfully!"
echo ""
echo "📋 Active cron jobs:"
crontab -l | grep -E "(token_expiry|Token Expiry)"

echo ""
echo "🧪 Test commands:"
echo "# Dry run - see what would happen without making changes"
echo "python3 manage.py manage_token_expiry --send-reminders --dry-run"
echo "python3 manage.py manage_token_expiry --clear-expired --dry-run"
echo "python3 manage.py manage_token_expiry --renew-annual --dry-run"
echo ""
echo "# Run all checks"
echo "python3 manage.py manage_token_expiry --send-reminders --clear-expired --renew-annual" 