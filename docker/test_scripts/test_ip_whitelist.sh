#!/bin/bash

# 🔐 IP白名单测试脚本
echo "🔐 测试管理后台IP白名单配置..."

# 获取当前服务器IP
SERVER_IP=$(curl -s ifconfig.me)
echo "当前服务器IP: $SERVER_IP"

echo ""
echo "📋 测试结果："

# 测试 /admin/ 路径
echo ""
echo "1. 测试 /admin/ 路径："
admin_status=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "https://${SERVER_IP}/admin/" || echo "000")
if [[ "$admin_status" == "302" ]] || [[ "$admin_status" == "200" ]]; then
    echo "   ✅ 从允许的IP访问: 正常 (状态码: $admin_status)"
else
    echo "   ❌ 从允许的IP访问: 异常 (状态码: $admin_status)"
fi

# 测试 /dashboard/ 路径
echo ""
echo "2. 测试 /dashboard/ 路径："
dashboard_status=$(curl -k -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "https://${SERVER_IP}/dashboard/" || echo "000")
if [[ "$dashboard_status" == "200" ]] || [[ "$dashboard_status" == "302" ]] || [[ "$dashboard_status" == "404" ]]; then
    echo "   ✅ 从允许的IP访问: 正常 (状态码: $dashboard_status)"
else
    echo "   ❌ 从允许的IP访问: 异常 (状态码: $dashboard_status)"
fi

echo ""
echo "📊 配置验证："

# 检查nginx配置中的IP白名单
echo ""
echo "3. 检查nginx配置中的IP白名单："
admin_allow_count=$(docker exec MirageMakers-nginx grep -A 10 "location /admin/" /etc/nginx/nginx.conf | grep -c "allow ")
dashboard_allow_count=$(docker exec MirageMakers-nginx grep -A 10 "location.*dashboard" /etc/nginx/nginx.conf | grep -c "allow ")

echo "   - /admin/ 路径配置的允许IP数量: $admin_allow_count"
echo "   - /dashboard/ 路径配置的允许IP数量: $dashboard_allow_count"

if [[ $admin_allow_count -gt 0 ]] && [[ $dashboard_allow_count -gt 0 ]]; then
    echo "   ✅ IP白名单配置已正确应用"
else
    echo "   ❌ IP白名单配置可能有问题"
fi

echo ""
echo "4. 当前配置的允许IP列表："
echo "   /admin/ 路径："
docker exec MirageMakers-nginx grep -A 15 "location /admin/" /etc/nginx/nginx.conf | grep "allow " | sed 's/^[[:space:]]*/   /'

echo ""
echo "   /dashboard/ 路径："
docker exec MirageMakers-nginx grep -A 15 "location.*dashboard" /etc/nginx/nginx.conf | grep "allow " | sed 's/^[[:space:]]*/   /'

echo ""
echo "🔒 安全状态总结："

if [[ $admin_allow_count -gt 0 ]] && [[ $dashboard_allow_count -gt 0 ]]; then
    echo "   ✅ 管理后台已启用IP白名单保护"
    echo "   ✅ 只有授权IP可以访问 /admin/ 和 /dashboard/"
    echo "   ✅ 安全配置正常生效"
else
    echo "   ⚠️  IP白名单配置可能需要检查"
fi

echo ""
echo "💡 提示："
echo "   - 如需添加新的管理员IP，请编辑 nginx/nginx.conf"
echo "   - 记得在两个location块中都添加新IP"
echo "   - 修改后需要运行: docker exec MirageMakers-nginx nginx -s reload"
echo "   - 详细说明请查看: docker/SECURITY_CONFIG.md"

echo ""
echo "🎉 IP白名单测试完成！" 