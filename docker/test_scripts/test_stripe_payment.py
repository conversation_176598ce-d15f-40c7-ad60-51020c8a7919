#!/usr/bin/env python3
"""
Stripe支付系统测试脚本
"""
import requests
import json

# 配置
BASE_URL = "https://miragemakers.ai"  # 或者你的域名
TOKEN = "your_auth_token_here"  # 请手动替换为真实的认证token

def test_create_checkout_session():
    """测试创建checkout session"""
    print("🧪 测试创建Stripe Checkout Session...")
    
    url = f"{BASE_URL}/api/stripe/create-checkout-session/"
    headers = {
        "Authorization": f"Token {TOKEN}",
        "Content-Type": "application/json"
    }
    data = {
        "plan_type": "TRIAL"  # 测试$1.99的试用计划
    }
    
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ Checkout session创建成功!")
            print(f"   Session ID: {result['session_id']}")
            print(f"   Payment URL: {result['url']}")
            print(f"   Order ID: {result['order_id']}")
            return result
        else:
            print(f"❌ 创建失败: {result['error']}")
    else:
        print(f"❌ 请求失败: {response.status_code} - {response.text}")
    
    return None

def test_subscription_status():
    """测试查询订阅状态"""
    print("\n🧪 测试查询订阅状态...")
    
    url = f"{BASE_URL}/api/stripe/subscription-status/"
    headers = {
        "Authorization": f"Token {TOKEN}"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 订阅状态查询成功!")
        print(f"   活跃订阅数: {len(result['subscriptions'])}")
        for sub in result['subscriptions']:
            print(f"   - {sub['plan_type']}: {sub['status']} (${sub['amount']})")
    else:
        print(f"❌ 查询失败: {response.status_code}")

def test_payment_history():
    """测试支付历史"""
    print("\n🧪 测试支付历史查询...")
    
    url = f"{BASE_URL}/api/stripe/payment-history/"
    headers = {
        "Authorization": f"Token {TOKEN}"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 支付历史查询成功!")
        print(f"   历史订单数: {len(result['orders'])}")
        for order in result['orders'][:3]:  # 显示最近3个
            print(f"   - {order['plan_type']}: ${order['amount']} ({order['status']})")
    else:
        print(f"❌ 查询失败: {response.status_code}")

def test_token_balance():
    """测试查询token余额"""
    print("\n🧪 测试token余额查询...")
    
    url = f"{BASE_URL}/api/user/token-usage/"
    headers = {
        "Authorization": f"Token {TOKEN}"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print(f"✅ Token余额查询成功!")
            print(f"   当前余额: {result['current_balance']:,} tokens")
            print(f"   总生成次数: {result['total_generations']:,}")
            if result.get('tokens_expiry'):
                print(f"   过期时间: {result['tokens_expiry']}")
            if result.get('current_plan'):
                print(f"   当前套餐: {result['current_plan']}")
        else:
            print(f"❌ 查询失败: {result.get('error', 'Unknown error')}")
    else:
        print(f"❌ 请求失败: {response.status_code}")

if __name__ == "__main__":
    print("🚀 开始Stripe支付系统测试\n")
    
    # 检查TOKEN是否设置
    if TOKEN == "your_auth_token_here":
        print("⚠️  需要设置认证TOKEN才能测试")
        print("   获取方法：")
        print("   1. 登录 https://miragemakers.ai")
        print("   2. 打开浏览器开发者工具 -> Application -> Local Storage")
        print("   3. 查找 'authToken' 或类似的key")
        print("   4. 将值复制并替换下面的TOKEN变量")
        print()
        
        # 提示用户输入token
        token_input = input("请输入你的认证token: ").strip()
        if token_input:
            TOKEN = token_input
        else:
            print("❌ 未提供TOKEN，退出测试")
            exit(1)
    
    # 先测试token余额
    test_token_balance()
    
    # 执行测试
    checkout_result = test_create_checkout_session()
    test_subscription_status()
    test_payment_history()
    
    if checkout_result:
        print(f"\n💡 下一步测试:")
        print(f"1. 访问支付链接完成支付: {checkout_result['url']}")
        print(f"2. 支付完成后检查用户token余额是否增加")
        print(f"3. 检查数据库中的PaymentOrder和TokenConsumption记录")
    
    print(f"\n🔍 其他测试建议:")
    print(f"- 测试不同计划类型: TRIAL, BASIC, PREMIUM, ANNUAL")
    print(f"- 测试支付失败场景")
    print(f"- 测试Webhook接收")
    print(f"- 检查数据库记录的完整性")
