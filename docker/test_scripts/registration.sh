#!/bin/bash

# ==========================================
# MirageMakers AI 注册流程测试脚本
# ==========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 测试邮箱
TEST_EMAIL="registration.test$(date +%s)@example.com"
TEST_NAME="Registration Test User"
TEST_PASSWORD="test123456"

echo ""
echo "=========================================="
echo "    MirageMakers AI 注册流程测试"
echo "=========================================="
echo ""

log_info "测试邮箱: $TEST_EMAIL"
log_info "测试用户: $TEST_NAME"
echo ""

# 1. 测试后端注册API
log_step "1. 测试后端注册API..."
REGISTER_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\", \"password\": \"$TEST_PASSWORD\", \"name\": \"$TEST_NAME\", \"language\": \"zh\"}")

HTTP_CODE=$(echo "$REGISTER_RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$REGISTER_RESPONSE" | head -n -1)

if [ "$HTTP_CODE" = "201" ]; then
    log_success "注册API响应正常 (HTTP $HTTP_CODE)"
    echo "响应内容: $RESPONSE_BODY"
    
    # 检查响应是否包含requires_verification
    if echo "$RESPONSE_BODY" | grep -q "requires_verification.*true"; then
        log_success "✅ 正确返回需要邮件验证标志"
    else
        log_warn "⚠️ 未找到requires_verification标志"
    fi
else
    log_error "注册API失败 (HTTP $HTTP_CODE)"
    echo "响应内容: $RESPONSE_BODY"
    exit 1
fi

echo ""

# 2. 检查用户是否创建但未验证
log_step "2. 检查用户状态..."
USER_CHECK=$(docker-compose exec -T django python manage.py shell -c "
from core.models import User, EmailVerification
try:
    user = User.objects.get(email='$TEST_EMAIL')
    verification = EmailVerification.objects.filter(user=user, is_used=False).first()
    print(f'用户已创建: {user.email}')
    print(f'邮箱已验证: {user.is_email_verified}')
    print(f'验证码存在: {verification is not None}')
    if verification:
        print(f'验证码: {verification.code}')
except User.DoesNotExist:
    print('用户未找到')
")

echo "$USER_CHECK"

if echo "$USER_CHECK" | grep -q "邮箱已验证: False"; then
    log_success "✅ 用户创建正确，邮箱未验证状态正确"
else
    log_error "❌ 用户状态异常"
fi

# 获取验证码
VERIFICATION_CODE=$(echo "$USER_CHECK" | grep "验证码:" | cut -d' ' -f2)
if [ -n "$VERIFICATION_CODE" ]; then
    log_success "✅ 验证码获取成功: $VERIFICATION_CODE"
else
    log_error "❌ 无法获取验证码"
    exit 1
fi

echo ""

# 3. 测试邮件验证API
log_step "3. 测试邮件验证API..."
VERIFY_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST http://localhost:8000/api/auth/verify-email/ \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\", \"code\": \"$VERIFICATION_CODE\"}")

VERIFY_HTTP_CODE=$(echo "$VERIFY_RESPONSE" | tail -n1)
VERIFY_BODY=$(echo "$VERIFY_RESPONSE" | head -n -1)

if [ "$VERIFY_HTTP_CODE" = "200" ]; then
    log_success "邮件验证API响应正常 (HTTP $VERIFY_HTTP_CODE)"
    echo "响应内容: $VERIFY_BODY"
    
    # 检查是否返回了token
    if echo "$VERIFY_BODY" | grep -q "token"; then
        log_success "✅ 验证成功，返回了认证token"
    else
        log_warn "⚠️ 验证成功但未返回token"
    fi
else
    log_error "邮件验证API失败 (HTTP $VERIFY_HTTP_CODE)"
    echo "响应内容: $VERIFY_BODY"
fi

echo ""

# 4. 检查用户最终状态
log_step "4. 检查用户最终状态..."
FINAL_CHECK=$(docker-compose exec -T django python manage.py shell -c "
from core.models import User
try:
    user = User.objects.get(email='$TEST_EMAIL')
    print(f'用户邮箱: {user.email}')
    print(f'用户姓名: {user.name}')
    print(f'邮箱已验证: {user.is_email_verified}')
    print(f'用户积分: {user.credits}')
except User.DoesNotExist:
    print('用户未找到')
")

echo "$FINAL_CHECK"

if echo "$FINAL_CHECK" | grep -q "邮箱已验证: True"; then
    log_success "✅ 用户邮箱验证成功"
else
    log_error "❌ 用户邮箱验证失败"
fi

echo ""

# 5. 测试重复注册
log_step "5. 测试重复注册检测..."
DUPLICATE_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\", \"password\": \"$TEST_PASSWORD\", \"name\": \"$TEST_NAME\", \"language\": \"zh\"}")

DUPLICATE_HTTP_CODE=$(echo "$DUPLICATE_RESPONSE" | tail -n1)
DUPLICATE_BODY=$(echo "$DUPLICATE_RESPONSE" | head -n -1)

if [ "$DUPLICATE_HTTP_CODE" = "400" ]; then
    log_success "重复注册检测正常 (HTTP $DUPLICATE_HTTP_CODE)"
    echo "响应内容: $DUPLICATE_BODY"
else
    log_warn "重复注册检测可能有问题 (HTTP $DUPLICATE_HTTP_CODE)"
    echo "响应内容: $DUPLICATE_BODY"
fi

echo ""

# 6. 清理测试数据
log_step "6. 清理测试数据..."
CLEANUP_RESULT=$(docker-compose exec -T django python manage.py shell -c "
from core.models import User, EmailVerification
try:
    user = User.objects.get(email='$TEST_EMAIL')
    EmailVerification.objects.filter(user=user).delete()
    user.delete()
    print('测试用户已删除')
except User.DoesNotExist:
    print('测试用户不存在，无需清理')
")

echo "$CLEANUP_RESULT"

echo ""
echo "=========================================="
log_success "🎉 注册流程测试完成！"
echo ""
log_info "测试结果总结:"
log_info "✅ 注册API工作正常"
log_info "✅ 邮件验证API工作正常"  
log_info "✅ 用户状态管理正确"
log_info "✅ 重复注册检测正常"
echo ""
log_info "前端应该能够："
log_info "1. 调用注册API并收到requires_verification=true"
log_info "2. 跳转到邮件验证页面"
log_info "3. 用户输入验证码后完成验证"
echo "==========================================" 