#!/bin/bash
# ==========================================
# MirageMakers AI MySQL 数据库备份脚本 v2.0
# 支持本地备份和OSS云备份
# 使用压缩格式节省存储空间
# ==========================================

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    [[ -n "${LOG_FILE:-}" ]] && echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1" >> "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    [[ -n "${LOG_FILE:-}" ]] && echo "[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $1" >> "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    [[ -n "${LOG_FILE:-}" ]] && echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1" >> "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    [[ -n "${LOG_FILE:-}" ]] && echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >> "$LOG_FILE"
}

# 配置变量（先定义，再加载环境变量）
LOCAL_BACKUP_DIR="database/mysql_data_backup"
LOG_FILE="logs/mysql_backup.log"
MAX_LOCAL_BACKUPS=${MAX_LOCAL_BACKUPS:-7}  # 保留最近7个本地备份

# 创建必要目录
mkdir -p "$LOCAL_BACKUP_DIR"
mkdir -p "logs"

# 确保日志文件存在
touch "$LOG_FILE"

# 加载环境变量
if [[ -f ".env" ]]; then
    source .env
    log_info "环境变量加载成功"
else
    log_error ".env 文件不存在，退出备份"
    exit 1
fi

# 检查MySQL容器状态
if ! docker ps --filter "name=MirageMakers-mysql" --filter "status=running" | grep -q "MirageMakers-mysql"; then
    log_error "MySQL容器未运行，无法执行备份"
    exit 1
fi

# 获取容器ID用于文件名标识
CONTAINER_ID=$(docker ps -qf "name=MirageMakers-mysql" | head -c 8)
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="mysql_backup_${TIMESTAMP}_${CONTAINER_ID}.sql.gz"
LOCAL_BACKUP_PATH="$LOCAL_BACKUP_DIR/$BACKUP_NAME"

log_info "开始数据库备份: $BACKUP_NAME"

# 执行数据库备份（使用压缩）
log_info "正在导出数据库数据..."

# 测试MySQL连接
if ! docker exec MirageMakers-mysql mysqladmin ping -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" >/dev/null 2>&1; then
    log_error "无法连接到MySQL数据库，请检查密码或服务状态"
    exit 1
fi

# 执行备份（排除系统数据库以避免恢复时的权限问题）
if docker exec MirageMakers-mysql mysqldump \
    -u root -p"$MYSQL_ROOT_PASSWORD" \
    --databases "$MYSQL_DATABASE" \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --add-drop-database \
    --add-drop-table \
    --create-options \
    --disable-keys \
    --extended-insert \
    --quick \
    --lock-tables=false \
    2>"$LOCAL_BACKUP_DIR/backup_error.log" | gzip > "$LOCAL_BACKUP_PATH"; then
    
    # 检查备份文件大小
    if [[ -f "$LOCAL_BACKUP_PATH" ]] && [[ -s "$LOCAL_BACKUP_PATH" ]]; then
        BACKUP_SIZE=$(du -h "$LOCAL_BACKUP_PATH" | cut -f1)
        log_success "本地备份完成: $LOCAL_BACKUP_PATH ($BACKUP_SIZE)"
        # 清理错误日志
        rm -f "$LOCAL_BACKUP_DIR/backup_error.log"
    else
        log_error "备份文件为空或未生成"
        if [[ -f "$LOCAL_BACKUP_DIR/backup_error.log" ]]; then
            log_error "错误详情: $(cat "$LOCAL_BACKUP_DIR/backup_error.log")"
        fi
        rm -f "$LOCAL_BACKUP_PATH"
        exit 1
    fi
else
    log_error "数据库备份失败"
    if [[ -f "$LOCAL_BACKUP_DIR/backup_error.log" ]]; then
        log_error "错误详情: $(cat "$LOCAL_BACKUP_DIR/backup_error.log")"
    fi
    rm -f "$LOCAL_BACKUP_PATH"
    exit 1
fi

# 上传到OSS（如果配置了）
if [[ -n "${OSS_BUCKET_NAME:-}" ]] && command -v ossutil &> /dev/null; then
    log_info "正在上传到OSS..."
    if ossutil cp "$LOCAL_BACKUP_PATH" "oss://$OSS_BUCKET_NAME/backups/$BACKUP_NAME" 2>/dev/null; then
        log_success "OSS备份上传成功: oss://$OSS_BUCKET_NAME/backups/$BACKUP_NAME"
    else
        log_warn "OSS上传失败，但本地备份已完成"
    fi
else
    if [[ -z "${OSS_BUCKET_NAME:-}" ]]; then
        log_warn "未配置OSS_BUCKET_NAME，跳过云备份"
    else
        log_warn "ossutil未安装，跳过OSS上传"
    fi
fi

# 清理旧的本地备份文件
log_info "清理旧备份文件（保留最近${MAX_LOCAL_BACKUPS}个）..."
pushd "$LOCAL_BACKUP_DIR" >/dev/null
OLD_BACKUPS=$(ls -t mysql_backup_*.sql.gz 2>/dev/null | tail -n +$((MAX_LOCAL_BACKUPS + 1)) || true)
if [[ -n "$OLD_BACKUPS" ]]; then
    echo "$OLD_BACKUPS" | xargs rm -f
    popd >/dev/null
    log_info "已清理 $(echo "$OLD_BACKUPS" | wc -l) 个旧备份文件"
else
    popd >/dev/null
    log_info "无需清理旧备份文件"
fi

# 生成备份报告
TOTAL_BACKUPS=$(ls -1 "$LOCAL_BACKUP_DIR"/mysql_backup_*.sql.gz 2>/dev/null | wc -l)
log_success "备份任务完成！"
log_info "备份文件: $BACKUP_NAME"
log_info "文件大小: $BACKUP_SIZE"
log_info "本地备份总数: $TOTAL_BACKUPS"
log_info "备份位置: $LOCAL_BACKUP_PATH"

# 验证备份文件完整性
if gzip -t "$LOCAL_BACKUP_PATH" 2>/dev/null; then
    log_success "备份文件完整性验证通过"
else
    log_error "备份文件损坏，请检查！"
    exit 1
fi

# 最终状态记录
if [[ -f "$LOG_FILE" ]]; then
    echo "========================================" >> "$LOG_FILE"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 备份任务完成" >> "$LOG_FILE"
    echo "备份文件: $BACKUP_NAME" >> "$LOG_FILE"
    echo "文件大小: $BACKUP_SIZE" >> "$LOG_FILE"
    echo "本地路径: $LOCAL_BACKUP_PATH" >> "$LOG_FILE"
    echo "========================================" >> "$LOG_FILE"
fi
