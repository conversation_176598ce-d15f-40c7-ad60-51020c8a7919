services:
  # Nginx反向代理 - 开发环境
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: MirageMakers-nginx-dev
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - django
    networks:
      - MirageMakers_dev_network
    # 开发环境去掉资源限制
    healthcheck:
      test: [ "CMD", "nginx", "-t" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # MySQL数据库 - 开发环境
  mysql:
    image: mysql:8.0
    container_name: MirageMakers-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max-connections=100
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      timeout: 20s
      retries: 10
      interval: 10s
    networks:
      - MirageMakers_dev_network

  # Redis缓存 - 开发环境
  redis:
    image: redis:7-alpine
    container_name: MirageMakers-redis-dev
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_dev_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - MirageMakers_dev_network

  # Django Backend - 开发环境
  django:
    build:
      context: ..
      dockerfile: docker/django/Dockerfile.dev
    container_name: MirageMakers-django-dev
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DJANGO_SETTINGS_MODULE=generator.settings
      - MYSQL_HOST=mysql
      - MYSQL_DATABASE=miragemakers
      - MYSQL_USER=miragemakers
      - MYSQL_PASSWORD=MirageMakers
      - MYSQL_PORT=3306
      - REDIS_HOST=redis
      - USE_DOCKER=true
      - DEBUG=true
      - DEVELOPMENT=true
      - DJANGO_LOG_LEVEL=DEBUG
      - ALLOWED_HOSTS=*
    ports:
      - "8000:8000"
      - "5679:5679"
    volumes:
      - ../:/app  # 开发环境挂载代码目录，支持热重载
      - django_dev_static:/app/staticfiles
      - django_dev_media:/app/media
      - paddle_models_cache:/root/.paddlex  # PaddleOCR默认模型缓存路径
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - MirageMakers_dev_network
    healthcheck:
      test: [ "CMD-SHELL", "curl -f --max-time 30 http://localhost:8000/health/ || exit 1" ]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 90s
    # 开发环境使用Django开发服务器，支持自动重载
    command: python manage.py runserver 0.0.0.0:8000

  # Daphne ASGI Server - WebSocket支持
  daphne:
    build:
      context: ..
      dockerfile: docker/django/Dockerfile.dev
    container_name: MirageMakers-daphne-dev
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DJANGO_SETTINGS_MODULE=generator.settings
      - MYSQL_HOST=mysql
      - MYSQL_DATABASE=miragemakers
      - MYSQL_USER=miragemakers
      - MYSQL_PASSWORD=MirageMakers
      - MYSQL_PORT=3306
      - REDIS_HOST=redis
      - USE_DOCKER=true
      - DEBUG=true
      - DEVELOPMENT=true
      - DJANGO_LOG_LEVEL=DEBUG
      - ALLOWED_HOSTS=*
    ports:
      - "8001:8001"  # WebSocket服务端口
    volumes:
      - ../:/app  # 开发环境挂载代码目录，支持热重载
      - django_dev_static:/app/staticfiles
      - django_dev_media:/app/media
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - MirageMakers_dev_network
    healthcheck:
      test: [ "CMD-SHELL", "curl -f --max-time 30 http://localhost:8001/ || exit 1" ]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 90s
    # 使用Daphne运行ASGI应用，支持WebSocket
    command: daphne -b 0.0.0.0 -p 8001 generator.asgi:application

  # Celery Worker - 开发环境
  celery:
    build:
      context: ..
      dockerfile: docker/django/Dockerfile.dev
    container_name: MirageMakers-celery-dev
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DJANGO_SETTINGS_MODULE=generator.settings
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - USE_DOCKER=true
      - DEBUG=true
      - DEVELOPMENT=true
    volumes:
      - ../:/app  # 开发环境挂载代码目录
      - django_dev_media:/app/media
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      django:
        condition: service_started
    networks:
      - MirageMakers_dev_network
    healthcheck:
      test: [ "CMD-SHELL", "celery -A app.celery_app inspect ping || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 开发环境使用适中日志级别
    command: celery -A app.celery_app worker --loglevel=info --queues=default --concurrency=2

  # Next.js Frontend - 开发环境
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/frontend/Dockerfile.dev
      args:
        - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}
    container_name: MirageMakers-frontend-dev
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - PORT=${FRONTEND_PORT:-3000}
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true  # 支持文件变更监听
    ports:
      - "3000:3000"
    volumes:
      - ../frontend:/app  # 开发环境挂载源码，支持热重载
      - frontend_node_modules:/app/node_modules  # 使用命名卷保护node_modules
      - /app/.next  # 排除.next
    depends_on:
      - django
    networks:
      - MirageMakers_dev_network
    healthcheck:
      test: [ "CMD-SHELL", "curl -f --max-time 30 http://localhost:3000/ || exit 1" ]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 90s

volumes:
  mysql_dev_data:
    driver: local
    name: mysql_dev_data
  redis_dev_data:
    driver: local
    name: redis_dev_data
  django_dev_static:
    driver: local
    name: django_dev_static
  django_dev_media:
    driver: local
    name: django_dev_media
  frontend_node_modules:
    driver: local
    name: frontend_node_modules
  paddle_models_cache:
    driver: local
    name: paddle_models_cache

networks:
  MirageMakers_dev_network:
    driver: bridge 