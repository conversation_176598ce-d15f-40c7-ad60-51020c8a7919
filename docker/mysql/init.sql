-- MySQL初始化脚本
-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS miragemakers_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE miragemakers_db;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'miragemakers'@'%' IDENTIFIED BY 'miragemakers_password';

-- 授予权限
GRANT ALL PRIVILEGES ON miragemakers_db.* TO 'miragemakers'@'%';
FLUSH PRIVILEGES;

-- 注释掉在初始化脚本中不能设置的全局变量
-- 这些设置应该在运行时通过 my.cnf 配置文件设置
-- SET GLOBAL innodb_buffer_pool_size = 128M;
-- SET GLOBAL max_connections = 200; 