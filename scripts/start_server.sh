#!/usr/bin/env bash
set -e

# -----------------------------------------------------------------------------
# 1. 启动 Redis（如果已经有在跑也不会报错）
# -----------------------------------------------------------------------------
if ! pgrep -x "redis-server" > /dev/null; then
  echo "🚀 启动 Redis..."
  # 如果你是 brew 安装的：
  # brew services start redis
  # 或者直接：
  redis-server &> /dev/null &
  sleep 1
else
  echo "✅ Redis 已在运行"
fi

# -----------------------------------------------------------------------------
# 2. 启动 Celery Worker
# -----------------------------------------------------------------------------
echo "🚀 启动 Celery Worker (concurrency=2)..."
mkdir -p logs

celery -A app.celery_app worker --concurrency=2 --loglevel=INFO &> logs/celery.log &
echo "   ↳ 日志输出到 logs/celery.log"

# -----------------------------------------------------------------------------
# 3. Django 迁移 & 启动
# -----------------------------------------------------------------------------

python manage.py makemigrations core

echo "🚀 Django migrate..."
python manage.py migrate

echo "🚀 启动 Django development server..."
python manage.py runserver:8000


########################################################
# 4. 关闭服务
########################################################
# # shutdown redis 
# redis-cli shutdown

# # shutdown celery
# celery -A app.celery_app control shutdown