#!/bin/bash

echo "🚀 快速启动本地Django服务"
echo "========================="

# 设置环境变量
export USE_DOCKER=false
export USE_LANGGRAPH_AGENT=true
export ENABLE_LEGACY_ROUTER=true
export DB_HOST=127.0.0.1
export DB_NAME=mvp
export DB_USER=root
export DB_PASSWORD=""
export EMAIL_BACKEND=console

echo "📋 使用本地配置启动Django..."
echo "  数据库: MySQL@127.0.0.1:3306/mvp"
echo "  用户: root (无密码)"
echo ""

# 跳过迁移，直接启动服务
echo "🌟 启动Django服务..."
python manage.py runserver 0.0.0.0:8000 