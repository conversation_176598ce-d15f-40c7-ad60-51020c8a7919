import os

# 定义项目结构
project_structure = {
    "generator": [
        "manage.py",
        ".env.example",
        "requirements.txt",
        os.path.join("generator", "__init__.py"),
        os.path.join("generator", "settings.py"),
        os.path.join("generator", "urls.py"),
        os.path.join("generator", "wsgi.py"),
        os.path.join("core", "__init__.py"),
        os.path.join("core", "forms.py"),
        os.path.join("core", "views.py"),
        os.path.join("core", "urls.py"),
        os.path.join("core", "adapters", "__init__.py"),
        os.path.join("core", "adapters", "image_gen.py"),
        os.path.join("core", "adapters", "img_edit.py"),
        os.path.join("core", "adapters", "video_keyframe.py"),
        os.path.join("core", "adapters", "img2video.py"),
        os.path.join("core", "adapters", "video_gen.py"),
        os.path.join("templates", "index.html"),
        os.path.join("templates", "result.html"),
    ]
}

def create_structure(base_path, structure):
    for root, files in structure.items():
        root_path = os.path.join(base_path, root)
        os.makedirs(root_path, exist_ok=True)
        for file in files:
            file_path = os.path.join(base_path, file)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w') as f:
                f.write('')  # 创建空文件

if __name__ == "__main__":
    base_dir = os.getcwd()  # 当前目录
    create_structure(base_dir, project_structure)
    print("✅ 项目结构已创建在：", os.path.join(base_dir, "generator"))