#!/bin/bash

# 测试配置
BASE_URL="http://localhost:8000"
API_ENDPOINT="/api/generate/"

echo "🚀 Starting Graph API Test Suite (Fixed Version - First 3 Tests Only)"
echo "===================================================================="
echo "Base URL: $BASE_URL"
echo "API Endpoint: $API_ENDPOINT"
echo ""

# 创建输出目录
mkdir -p data/curl_data

# 获取CSRF Token和Session Cookie
echo "🔄 Getting CSRF token..."
CSRF_RESPONSE=$(curl -s -c cookies.txt -b cookies.txt "${BASE_URL}/")
CSRF_TOKEN=$(echo "$CSRF_RESPONSE" | grep -o "X-CSRFToken.*'[^']*'" | sed "s/.*'\([^']*\)'.*/\1/")

# 如果第一种方法失败，尝试从cookies中获取
if [ -z "$CSRF_TOKEN" ]; then
    CSRF_TOKEN=$(grep csrftoken cookies.txt | cut -f7)
fi

# 如果还是失败，使用固定的token（从HTML中找到的）
if [ -z "$CSRF_TOKEN" ]; then
    CSRF_TOKEN="FCWTtJ9u6ETic1uCldOxgenGce60uK0PEbMaamFyfbio5NLcOTwaRr3VCFXTHyGA"
    echo "⚠️  Using hardcoded CSRF token from HTML"
fi

if [ -z "$CSRF_TOKEN" ]; then
    echo "❌ Failed to get CSRF token"
    echo "Response length: ${#CSRF_RESPONSE}"
    exit 1
fi

echo "✅ CSRF Token: $CSRF_TOKEN"
echo ""

# 格式化JSON输出的函数
format_json() {
    local file=$1
    echo "📄 Response saved to $file"
    if command -v python3 &> /dev/null; then
        echo "📋 Response content (formatted):"
        python3 -c "import json; data=json.load(open('$file')); print(json.dumps(data, ensure_ascii=False, indent=2))" 2>/dev/null || {
            echo "📋 Response content (raw):"
            head -20 "$file"
        }
    else
        echo "📋 Response content (raw):"
        head -20 "$file"
    fi
}

# 测试用例1：简单聊天对话（只发送prompt）
echo "🧪 测试用例1: 简单聊天对话"
echo "========================"
curl -X POST \
  -H "X-CSRFToken: $CSRF_TOKEN" \
  -b cookies.txt \
  -d "prompt=你好" \
  "${BASE_URL}${API_ENDPOINT}" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -o data/curl_data/response1_fixed.json

echo ""
format_json "data/curl_data/response1_fixed.json"
echo ""

# 测试用例2：带有有效media_url的请求
echo "🧪 测试用例2: 带有有效图片URL"
echo "========================"
curl -X POST \
  -H "X-CSRFToken: $CSRF_TOKEN" \
  -b cookies.txt \
  -d "prompt=分析这张图片&media_url=https://example.com/image.jpg" \
  "${BASE_URL}${API_ENDPOINT}" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -o data/curl_data/response2_fixed.json

echo ""
format_json "data/curl_data/response2_fixed.json"
echo ""

# 测试用例3：空prompt测试（应该返回400错误）
echo "🧪 测试用例3: 空prompt验证"
echo "========================"
curl -X POST \
  -H "X-CSRFToken: $CSRF_TOKEN" \
  -b cookies.txt \
  -d "prompt=" \
  "${BASE_URL}${API_ENDPOINT}" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -o data/curl_data/response3_fixed.json

echo ""
format_json "data/curl_data/response3_fixed.json"
echo ""

# 注释掉测试用例4和5，专注于前3个核心测试
# # 测试用例4：长文本prompt
# echo "🧪 测试用例4: 长文本prompt"
# echo "========================"
# LONG_PROMPT="请帮我生成一个关于人工智能发展历史的详细介绍，包括从图灵测试到现代深度学习的发展历程"
# curl -X POST \
#   -H "X-CSRFToken: $CSRF_TOKEN" \
#   -b cookies.txt \
#   --data-urlencode "prompt=$LONG_PROMPT" \
#   "${BASE_URL}${API_ENDPOINT}" \
#   -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
#   -o data/curl_data/response4_fixed.json
# 
# echo ""
# format_json "data/curl_data/response4_fixed.json"
# echo ""
# 
# # 测试用例5：带session_id的请求
# echo "🧪 测试用例5: 带session_id的对话"
# echo "========================"
# curl -X POST \
#   -H "X-CSRFToken: $CSRF_TOKEN" \
#   -b cookies.txt \
#   -d "prompt=继续我们的对话&session_id=test-session-123" \
#   "${BASE_URL}${API_ENDPOINT}" \
#   -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
#   -o data/curl_data/response5_fixed.json
# 
# echo ""
# format_json "data/curl_data/response5_fixed.json"
# echo ""

# 清理临时文件
echo "🧹 Cleaning up..."
rm -f cookies.txt

# 测试结果摘要
echo ""
echo "✅ Core Test Suite Completed!"
echo "============================="
echo "Generated files:"
echo "  - data/curl_data/response1_fixed.json (简单聊天)"
echo "  - data/curl_data/response2_fixed.json (带图片URL)"
echo "  - data/curl_data/response3_fixed.json (空prompt验证)"
echo ""
echo "📊 测试重点:"
echo "- ✅ 基础聊天功能测试"
echo "- ✅ 媒体处理功能测试"
echo "- ✅ 表单验证功能测试"
echo "- ✅ 修复了中文显示问题"
echo "- ✅ 专注于核心功能验证"
echo ""
echo "如需查看详细响应，请检查生成的JSON文件。" 