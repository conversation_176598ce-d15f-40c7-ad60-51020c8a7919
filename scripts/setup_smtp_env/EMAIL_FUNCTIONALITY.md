### 环境配置
python3 setup_msmtp_mac.py 

### msmtp配置
```bash
# ~/.msmtprc
defaults
auth           on
tls            on
tls_trust_file /etc/ssl/cert.pem
logfile        ~/.msmtp.log

account        gmail
host           smtp.gmail.com
port           587
from           <EMAIL>
user           <EMAIL>
password       ojej knfw mucu hfuw

account default : gmail
```

### Django邮件设置
```python
# generator/settings.py
USE_MSMTP = True
EMAIL_BACKEND = 'core.msmtp_backend.MsmtpEmailBackend'
EMAIL_MSMTP_PATH = '/opt/homebrew/bin/msmtp'
EMAIL_MSMTP_ACCOUNT = 'default'
DEFAULT_FROM_EMAIL = 'miragemakers AI <<EMAIL>>'
```

## 验证步骤

运行以下命令验证邮件功能：

```bash
# 1. 测试密码重置
curl -X POST http://dev.miragemakers.ai:8000/api/auth/forgot-password/ \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","language":"zh"}'

# 2. 测试用户注册
curl -X POST http://dev.miragemakers.ai:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{"name":"测试用户","email":"<EMAIL>","password":"123456","language":"zh"}'

# 3. 检查msmtp日志
tail -5 ~/.msmtp.log

# 4. 运行综合测试
python test_email_functionality.py
```

## 邮件模板

系统支持中英文邮件模板：

### 密码重置邮件 (中文)
- 主题: "重置您的miragemakers AI密码"
- 包含6位数字重置码
- 10分钟有效期
- HTML和纯文本版本

### 邮箱验证邮件 (中文)  
- 主题: "验证您的miragemakers AI邮箱"
- 包含6位数字验证码
- 10分钟有效期
- HTML和纯文本版本

## 注意事项

1. **邮件发送状态**: 检查msmtp日志中的`exitcode=EX_OK`确认发送成功
2. **垃圾邮件**: 提醒用户检查垃圾邮件文件夹
3. **有效期**: 验证码和重置码都有10分钟有效期
4. **安全性**: 即使用户不存在，也返回成功消息（安全考虑）

## 相关文件

- `core/models.py` - 用户模型定义
- `core/auth_views.py` - 认证API视图
- `core/email_utils.py` - 邮件发送工具
- `core/msmtp_backend.py` - msmtp邮件后端
- `generator/settings.py` - Django配置
- `test_email_functionality.py` - 综合测试脚本
