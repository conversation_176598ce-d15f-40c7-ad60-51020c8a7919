#!/usr/bin/env python3
"""
miragemakers AI - Ubuntu msmtp邮件自动配置工具
适用于Ubuntu/Debian系统，自动安装和配置msmtp邮件服务
"""

import os
import sys
import subprocess
import django
from pathlib import Path
import getpass

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
sys.path.append(str(Path(__file__).parent))
django.setup()

from django.core.mail import send_mail
from django.conf import settings


class UbuntuMsmtpSetup:
    def __init__(self):
        self.msmtp_path = '/usr/bin/msmtp'
        self.config_path = Path.home() / '.msmtprc'
        self.log_path = Path.home() / '.msmtp.log'
        
    def check_system(self):
        """检查系统信息"""
        print("🐧 检查Ubuntu系统...")
        
        try:
            # 检查是否为Ubuntu/Debian系统
            with open('/etc/os-release', 'r') as f:
                os_info = f.read()
                
            if 'ubuntu' in os_info.lower() or 'debian' in os_info.lower():
                print("✅ 系统兼容：Ubuntu/Debian")
                return True
            else:
                print("⚠️  警告：非Ubuntu/Debian系统，可能存在兼容性问题")
                return True  # 仍然尝试继续
                
        except Exception as e:
            print(f"❌ 无法检测系统信息: {e}")
            return True  # 仍然尝试继续
    
    def update_package_list(self):
        """更新软件包列表"""
        print("📦 更新软件包列表...")
        
        try:
            result = subprocess.run(['sudo', 'apt', 'update'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 软件包列表更新成功")
                return True
            else:
                print(f"❌ 更新软件包列表失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 更新软件包列表时出错: {e}")
            return False
    
    def install_msmtp(self):
        """安装msmtp和相关依赖"""
        print("📦 检查并安装msmtp...")
        
        # 检查是否已安装
        try:
            result = subprocess.run(['which', 'msmtp'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.msmtp_path = result.stdout.strip()
                print(f"✅ msmtp已安装: {self.msmtp_path}")
                return True
        except Exception:
            pass
        
        try:
            print("🔄 正在安装msmtp和依赖...")
            
            # 安装msmtp和ca-certificates
            packages = ['msmtp', 'msmtp-mta', 'ca-certificates']
            cmd = ['sudo', 'apt', 'install', '-y'] + packages
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ msmtp安装成功")
                
                # 重新获取msmtp路径
                result = subprocess.run(['which', 'msmtp'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.msmtp_path = result.stdout.strip()
                    print(f"✅ msmtp路径: {self.msmtp_path}")
                
                return True
            else:
                print(f"❌ msmtp安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 安装msmtp时出错: {e}")
            return False
    
    def get_email_config(self):
        """获取邮件配置信息"""
        print("\n📧 配置邮件信息")
        print("=" * 40)
        
        # 获取Gmail信息
        print("请输入Gmail配置信息:")
        email = input("📮 Gmail地址: ").strip()
        
        if not email or '@gmail.com' not in email:
            print("❌ 请输入有效的Gmail地址")
            return None
            
        print("\n🔐 Gmail应用专用密码配置:")
        print("1. 登录 https://myaccount.google.com/")
        print("2. 选择「安全性」→「两步验证」")
        print("3. 选择「应用专用密码」")
        print("4. 生成应用专用密码")
        print("5. 复制16位密码（格式: xxxx xxxx xxxx xxxx）")
        
        password = getpass.getpass("🔑 Gmail应用专用密码: ").strip()
        
        if not password or len(password.replace(' ', '')) != 16:
            print("❌ 密码格式不正确，应为16位字符")
            return None
        
        return {
            'email': email,
            'password': password.replace(' ', '')  # 移除空格
        }
    
    def create_msmtp_config(self, email_config):
        """创建msmtp配置文件"""
        print(f"\n⚙️  创建msmtp配置文件: {self.config_path}")
        
        config_content = f"""# msmtp configuration for miragemakers AI
defaults
auth           on
tls            on
tls_trust_file /etc/ssl/certs/ca-certificates.crt
logfile        {self.log_path}

# Gmail configuration
account        gmail
host           smtp.gmail.com
port           587
from           {email_config['email']}
user           {email_config['email']}
password       {email_config['password']}

# Set default account
account default : gmail
"""
        
        try:
            # 写入配置文件
            with open(self.config_path, 'w') as f:
                f.write(config_content)
            
            # 设置正确的权限 (只有用户可读写)
            os.chmod(self.config_path, 0o600)
            
            print("✅ msmtp配置文件创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    
    def test_msmtp(self, test_email):
        """测试msmtp发送"""
        print(f"\n🧪 测试msmtp发送邮件到: {test_email}")
        
        email_content = f"""From: miragemakers AI <{test_email}>
To: {test_email}
Subject: msmtp配置测试 - Ubuntu

您好！

这是来自miragemakers AI的msmtp配置测试邮件。

如果您收到这封邮件，说明msmtp在Ubuntu上配置成功！

✅ 系统: Ubuntu/Debian
✅ 工具: msmtp + apt
✅ 状态: 配置成功

miragemakers AI团队
{'-' * 40}
此邮件由自动配置脚本发送
"""
        
        try:
            cmd = [self.msmtp_path, '--account', 'default', test_email]
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(input=email_content)
            
            if process.returncode == 0:
                print("✅ msmtp测试发送成功！")
                return True
            else:
                print(f"❌ msmtp测试失败: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 测试msmtp时出错: {e}")
            return False
    
    def update_django_settings(self):
        """更新Django设置以使用msmtp"""
        print("\n⚙️  检查Django邮件配置...")
        
        # 检查当前配置
        if 'msmtp' in settings.EMAIL_BACKEND:
            print("✅ Django已配置使用msmtp后端")
            return True
        
        print("📝 需要更新Django设置文件...")
        settings_file = Path(__file__).parent / 'generator' / 'settings.py'
        
        if not settings_file.exists():
            print(f"❌ 找不到Django设置文件: {settings_file}")
            return False
        
        print("ℹ️  请手动确认generator/settings.py中包含以下配置:")
        print(f"""
# Email settings for msmtp (Ubuntu)
USE_MSMTP = True
EMAIL_BACKEND = 'core.msmtp_backend.MsmtpEmailBackend'
EMAIL_MSMTP_PATH = '{self.msmtp_path}'
EMAIL_MSMTP_ACCOUNT = 'default'
DEFAULT_FROM_EMAIL = 'miragemakers AI <<EMAIL>>'
""")
        
        return True
    
    def test_django_email(self, test_email):
        """测试Django邮件发送"""
        print(f"\n🔧 测试Django集成邮件发送到: {test_email}")
        
        try:
            send_mail(
                subject='Django msmtp集成测试 - Ubuntu',
                message='这是通过Django msmtp后端发送的测试邮件。',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[test_email],
                html_message="""
                <h2>🎉 Django msmtp集成测试成功！</h2>
                <p>恭喜！您的Ubuntu系统已成功配置Django + msmtp邮件发送。</p>
                <ul>
                    <li>✅ msmtp已安装和配置</li>
                    <li>✅ Django邮件后端已集成</li>
                    <li>✅ 邮件发送功能正常</li>
                </ul>
                <p><strong>miragemakers AI团队</strong></p>
                <hr>
                <small>此邮件由Django msmtp后端自动发送</small>
                """,
                fail_silently=False,
            )
            
            print("✅ Django邮件发送成功！")
            return True
            
        except Exception as e:
            print(f"❌ Django邮件发送失败: {e}")
            return False
    
    def setup(self):
        """执行完整的安装配置流程"""
        print("🚀 开始Ubuntu msmtp邮件配置")
        print("=" * 50)
        
        # 1. 检查系统
        if not self.check_system():
            print("❌ 系统检查失败，无法继续")
            return False
        
        # 2. 更新软件包列表
        if not self.update_package_list():
            print("❌ 软件包列表更新失败，无法继续")
            return False
        
        # 3. 安装msmtp
        if not self.install_msmtp():
            print("❌ msmtp安装失败，无法继续")
            return False
        
        # 4. 获取邮件配置
        email_config = self.get_email_config()
        if not email_config:
            print("❌ 邮件配置获取失败，无法继续")
            return False
        
        # 5. 创建msmtp配置
        if not self.create_msmtp_config(email_config):
            print("❌ msmtp配置文件创建失败，无法继续")
            return False
        
        # 6. 测试msmtp
        test_email = email_config['email']
        if not self.test_msmtp(test_email):
            print("❌ msmtp测试失败")
            return False
        
        # 7. 更新Django设置
        self.update_django_settings()
        
        # 8. 测试Django集成
        if not self.test_django_email(test_email):
            print("❌ Django邮件集成测试失败")
            return False
        
        print("\n🎉 Ubuntu msmtp邮件配置完成！")
        print("=" * 50)
        print("✅ 系统: Ubuntu/Debian兼容")
        print("✅ msmtp: 已安装和配置")
        print("✅ Django: 邮件后端已集成")
        print("✅ 测试: 邮件发送正常")
        print(f"\n📧 测试邮件已发送到: {test_email}")
        print("📝 请检查您的Gmail收件箱确认收到邮件")
        print(f"\n📋 配置文件位置:")
        print(f"   - msmtp配置: {self.config_path}")
        print(f"   - msmtp日志: {self.log_path}")
        print(f"   - msmtp路径: {self.msmtp_path}")
        
        return True


def main():
    """主函数"""
    print("🐧 miragemakers AI - Ubuntu msmtp邮件自动配置工具")
    print("适用于Ubuntu/Debian系统的一键配置")
    print("=" * 60)
    
    # 检查操作系统
    if sys.platform != 'linux':
        print("❌ 此脚本仅适用于Linux系统")
        print("💡 请使用对应的macOS版本脚本")
        return
    
    # 检查sudo权限
    print("🔐 此脚本需要sudo权限来安装软件包")
    try:
        result = subprocess.run(['sudo', '-n', 'true'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("⚠️  请确保您有sudo权限，或者先运行 'sudo -v'")
    except Exception:
        pass
    
    # 确认开始配置
    print("\n📋 此脚本将执行以下操作:")
    print("1. 检查Ubuntu/Debian系统兼容性")
    print("2. 更新apt软件包列表")
    print("3. 安装msmtp邮件传输代理")
    print("4. 配置Gmail SMTP设置")
    print("5. 创建msmtp配置文件")
    print("6. 测试邮件发送功能")
    print("7. 集成Django邮件后端")
    
    confirm = input("\n是否继续？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("👋 配置已取消")
        return
    
    # 执行配置
    setup = UbuntuMsmtpSetup()
    success = setup.setup()
    
    if success:
        print("\n🎊 恭喜！msmtp邮件配置成功完成！")
        print("现在您可以使用miragemakers AI的邮件功能了。")
    else:
        print("\n😞 配置过程中遇到问题，请检查错误信息并重试。")


if __name__ == '__main__':
    main() 
