#!/usr/bin/env python3
"""
miragemakers AI - macOS msmtp邮件自动配置工具
适用于全新的Mac环境，自动安装和配置msmtp邮件服务
"""

import os
import sys
import subprocess
import django
from pathlib import Path
import getpass

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'generator.settings')
sys.path.append(str(Path(__file__).parent))
django.setup()

from django.core.mail import send_mail
from django.conf import settings
from core.models import User
from core.email_utils import send_verification_email


class MacMsmtpSetup:
    def __init__(self):
        self.msmtp_path = '/opt/homebrew/bin/msmtp'
        self.config_path = Path.home() / '.msmtprc'
        self.log_path = Path.home() / '.msmtp.log'
        
    def check_homebrew(self):
        """检查并安装Homebrew"""
        print("🍺 检查Homebrew...")
        
        try:
            result = subprocess.run(['which', 'brew'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Homebrew已安装")
                return True
        except Exception:
            pass
            
        print("❌ Homebrew未安装，正在安装...")
        print("📝 这可能需要几分钟时间...")
        
        try:
            # 安装Homebrew
            install_cmd = '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
            result = subprocess.run(install_cmd, shell=True)
            
            if result.returncode == 0:
                print("✅ Homebrew安装成功")
                return True
            else:
                print("❌ Homebrew安装失败")
                return False
                
        except Exception as e:
            print(f"❌ 安装Homebrew时出错: {e}")
            return False
    
    def install_msmtp(self):
        """安装msmtp"""
        print("📦 检查并安装msmtp...")
        
        # 检查是否已安装
        if Path(self.msmtp_path).exists():
            print("✅ msmtp已安装")
            return True
        
        try:
            print("🔄 正在安装msmtp...")
            result = subprocess.run(['brew', 'install', 'msmtp'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ msmtp安装成功")
                return True
            else:
                print(f"❌ msmtp安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 安装msmtp时出错: {e}")
            return False
    
    def get_email_config(self):
        """获取邮件配置信息"""
        print("\n📧 配置邮件信息")
        print("=" * 40)
        
        # 获取Gmail信息
        print("请输入Gmail配置信息:")
        email = input("📮 Gmail地址: ").strip()
        
        if not email or '@gmail.com' not in email:
            print("❌ 请输入有效的Gmail地址")
            return None
            
        print("\n🔐 Gmail应用专用密码配置:")
        print("1. 登录 https://myaccount.google.com/")
        print("2. 选择「安全性」→「两步验证」")
        print("3. 选择「应用专用密码」")
        print("4. 生成应用专用密码")
        print("5. 复制16位密码（格式: xxxx xxxx xxxx xxxx）")
        
        password = getpass.getpass("🔑 Gmail应用专用密码: ").strip()
        
        if not password or len(password.replace(' ', '')) != 16:
            print("❌ 密码格式不正确，应为16位字符")
            return None
        
        return {
            'email': email,
            'password': password.replace(' ', '')  # 移除空格
        }
    
    def create_msmtp_config(self, email_config):
        """创建msmtp配置文件"""
        print(f"\n⚙️  创建msmtp配置文件: {self.config_path}")
        
        config_content = f"""# msmtp configuration for miragemakers AI
defaults
auth           on
tls            on
tls_trust_file /etc/ssl/cert.pem
logfile        {self.log_path}

# Gmail configuration
account        gmail
host           smtp.gmail.com
port           587
from           {email_config['email']}
user           {email_config['email']}
password       {email_config['password']}

# Set default account
account default : gmail
"""
        
        try:
            # 写入配置文件
            with open(self.config_path, 'w') as f:
                f.write(config_content)
            
            # 设置正确的权限 (只有用户可读写)
            os.chmod(self.config_path, 0o600)
            
            print("✅ msmtp配置文件创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    
    def test_msmtp(self, test_email):
        """测试msmtp发送"""
        print(f"\n🧪 测试msmtp发送邮件到: {test_email}")
        
        email_content = f"""From: miragemakers AI <{test_email}>
To: {test_email}
Subject: msmtp配置测试 - macOS

您好！

这是来自miragemakers AI的msmtp配置测试邮件。

如果您收到这封邮件，说明msmtp在macOS上配置成功！

✅ 系统: macOS
✅ 工具: msmtp + Homebrew
✅ 状态: 配置成功

miragemakers AI团队
{'-' * 40}
此邮件由自动配置脚本发送
"""
        
        try:
            cmd = [self.msmtp_path, '--account', 'default', test_email]
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(input=email_content)
            
            if process.returncode == 0:
                print("✅ msmtp测试发送成功！")
                return True
            else:
                print(f"❌ msmtp测试失败: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 测试msmtp时出错: {e}")
            return False
    
    def update_django_settings(self):
        """更新Django设置以使用msmtp"""
        print("\n⚙️  检查Django邮件配置...")
        
        # 检查当前配置
        if 'msmtp' in settings.EMAIL_BACKEND:
            print("✅ Django已配置使用msmtp后端")
            return True
        
        print("📝 需要更新Django设置文件...")
        settings_file = Path(__file__).parent / 'generator' / 'settings.py'
        
        if not settings_file.exists():
            print(f"❌ 找不到Django设置文件: {settings_file}")
            return False
        
        print("ℹ️  请手动确认generator/settings.py中包含以下配置:")
        print("""
# Email settings for msmtp
USE_MSMTP = True
EMAIL_BACKEND = 'core.msmtp_backend.MsmtpEmailBackend'
EMAIL_MSMTP_PATH = '/opt/homebrew/bin/msmtp'
EMAIL_MSMTP_ACCOUNT = 'default'
DEFAULT_FROM_EMAIL = 'miragemakers AI <<EMAIL>>'
""")
        
        return True
    
    def test_django_email(self, test_email):
        """测试Django邮件发送"""
        print(f"\n🔧 测试Django集成邮件发送到: {test_email}")
        
        try:
            send_mail(
                subject='Django msmtp集成测试 - macOS',
                message='这是通过Django msmtp后端发送的测试邮件。',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[test_email],
                html_message="""
                <h2>🎉 Django msmtp集成测试成功！</h2>
                <p>恭喜！您的macOS系统已成功配置Django + msmtp邮件发送。</p>
                <ul>
                    <li>✅ msmtp已安装和配置</li>
                    <li>✅ Django邮件后端已集成</li>
                    <li>✅ 邮件发送功能正常</li>
                </ul>
                <p><strong>miragemakers AI团队</strong></p>
                <hr>
                <small>此邮件由Django msmtp后端自动发送</small>
                """,
                fail_silently=False,
            )
            
            print("✅ Django邮件发送成功！")
            return True
            
        except Exception as e:
            print(f"❌ Django邮件发送失败: {e}")
            return False
    
    def setup(self):
        """执行完整的安装配置流程"""
        print("🚀 开始macOS msmtp邮件配置")
        print("=" * 50)
        
        # 1. 检查/安装Homebrew
        if not self.check_homebrew():
            print("❌ Homebrew配置失败，无法继续")
            return False
        
        # 2. 安装msmtp
        if not self.install_msmtp():
            print("❌ msmtp安装失败，无法继续")
            return False
        
        # 3. 获取邮件配置
        email_config = self.get_email_config()
        if not email_config:
            print("❌ 邮件配置获取失败，无法继续")
            return False
        
        # 4. 创建msmtp配置
        if not self.create_msmtp_config(email_config):
            print("❌ msmtp配置文件创建失败，无法继续")
            return False
        
        # 5. 测试msmtp
        test_email = email_config['email']
        if not self.test_msmtp(test_email):
            print("❌ msmtp测试失败")
            return False
        
        # 6. 更新Django设置
        self.update_django_settings()
        
        # 7. 测试Django集成
        if not self.test_django_email(test_email):
            print("❌ Django邮件集成测试失败")
            return False
        
        print("\n🎉 macOS msmtp邮件配置完成！")
        print("=" * 50)
        print("✅ Homebrew: 已安装")
        print("✅ msmtp: 已安装和配置")
        print("✅ Django: 邮件后端已集成")
        print("✅ 测试: 邮件发送正常")
        print(f"\n📧 测试邮件已发送到: {test_email}")
        print("📝 请检查您的Gmail收件箱确认收到邮件")
        print(f"\n📋 配置文件位置:")
        print(f"   - msmtp配置: {self.config_path}")
        print(f"   - msmtp日志: {self.log_path}")
        
        return True


def main():
    """主函数"""
    print("🍎 miragemakers AI - macOS msmtp邮件自动配置工具")
    print("适用于全新Mac环境的一键配置")
    print("=" * 60)
    
    # 检查操作系统
    if sys.platform != 'darwin':
        print("❌ 此脚本仅适用于macOS系统")
        print("💡 请使用对应的Ubuntu版本脚本")
        return
    
    # 确认开始配置
    print("\n📋 此脚本将执行以下操作:")
    print("1. 检查并安装Homebrew（如果需要）")
    print("2. 安装msmtp邮件传输代理")
    print("3. 配置Gmail SMTP设置")
    print("4. 创建msmtp配置文件")
    print("5. 测试邮件发送功能")
    print("6. 集成Django邮件后端")
    
    confirm = input("\n是否继续？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("👋 配置已取消")
        return
    
    # 执行配置
    setup = MacMsmtpSetup()
    success = setup.setup()
    
    if success:
        print("\n🎊 恭喜！msmtp邮件配置成功完成！")
        print("现在您可以使用miragemakers AI的邮件功能了。")
    else:
        print("\n😞 配置过程中遇到问题，请检查错误信息并重试。")


if __name__ == '__main__':
    main() 
