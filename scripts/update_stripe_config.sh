#!/bin/bash

# Stripe配置更新脚本
# 在完成Stripe Dashboard配置后，运行此脚本更新环境变量

echo "🔧 更新Stripe配置..."

# 提示用户输入从Stripe Dashboard获取的价格ID
echo ""
echo "请在Stripe Dashboard中创建产品后，输入对应的价格ID："
echo ""

read -p "输入 TRIAL 价格ID (price_xxxxx): " TRIAL_PRICE_ID
read -p "输入 BASIC 价格ID (price_xxxxx): " BASIC_PRICE_ID  
read -p "输入 PREMIUM 价格ID (price_xxxxx): " PREMIUM_PRICE_ID
read -p "输入 ANNUAL 价格ID (price_xxxxx): " ANNUAL_PRICE_ID

echo ""
read -p "输入 Webhook Secret (whsec_xxxxx): " WEBHOOK_SECRET

# 备份当前.env文件
cp .env .env.backup.stripe.$(date +%Y%m%d_%H%M%S)
echo "✅ 已备份当前.env文件"

# 更新价格ID
sed -i.bak "s/STRIPE_TRIAL_PRICE_ID=.*/STRIPE_TRIAL_PRICE_ID=${TRIAL_PRICE_ID}/" .env
sed -i.bak "s/STRIPE_BASIC_PRICE_ID=.*/STRIPE_BASIC_PRICE_ID=${BASIC_PRICE_ID}/" .env
sed -i.bak "s/STRIPE_PREMIUM_PRICE_ID=.*/STRIPE_PREMIUM_PRICE_ID=${PREMIUM_PRICE_ID}/" .env
sed -i.bak "s/STRIPE_ANNUAL_PRICE_ID=.*/STRIPE_ANNUAL_PRICE_ID=${ANNUAL_PRICE_ID}/" .env

# 更新Webhook Secret
sed -i.bak "s/STRIPE_WEBHOOK_SECRET=.*/STRIPE_WEBHOOK_SECRET=${WEBHOOK_SECRET}/" .env

# 清理临时文件
rm -f .env.bak

echo ""
echo "✅ Stripe配置更新完成！"
echo ""
echo "更新的配置："
echo "  TRIAL:   ${TRIAL_PRICE_ID}"
echo "  BASIC:   ${BASIC_PRICE_ID}"
echo "  PREMIUM: ${PREMIUM_PRICE_ID}"
echo "  ANNUAL:  ${ANNUAL_PRICE_ID}"
echo "  WEBHOOK: ${WEBHOOK_SECRET:0:10}..."
echo ""
echo "🔄 重启Django服务以应用配置："
echo "  docker-compose restart django"
echo ""
echo "🧪 测试支付功能："
echo "  curl -k https://miragemakers.ai/api/stripe/create-checkout-session/ \\"
echo "    -X POST -H 'Content-Type: application/json' \\"
echo "    -H 'Authorization: Token YOUR_USER_TOKEN' \\"
echo "    -d '{\"plan_type\": \"TRIAL\"}'" 