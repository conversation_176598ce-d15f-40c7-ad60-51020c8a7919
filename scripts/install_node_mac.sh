#!/bin/bash

set -e

echo "🚀 Step 1: 从 Gitee 安装 nvm..."
mkdir -p $HOME/.nvm
curl -fsSL https://gitee.com/mirrors/nvm/raw/master/install.sh -o /tmp/nvm_install.sh
bash /tmp/nvm_install.sh

# 加载 nvm
export NVM_DIR="$HOME/.nvm"
source "$NVM_DIR/nvm.sh"

# 写入 shell 启动项
if ! grep -q 'nvm.sh' ~/.zshrc; then
  echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.zshrc
  echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> ~/.zshrc
fi

echo "✅ nvm 安装完成"

echo "🚀 Step 2: 设置 Node.js 镜像为淘宝源..."
export NVM_NODEJS_ORG_MIRROR=https://npmmirror.com/mirrors/node

echo "🚀 Step 3: 安装 Node.js 最新版..."
nvm install node
nvm use node
nvm alias default node

echo "🚀 Step 4: 设置 npm 镜像为淘宝源..."
npm config set registry https://registry.npmmirror.com

echo "🎉 全部配置完成"
echo "👉 Node 版本: $(node -v)"
echo "👉 npm 版本:  $(npm -v)"