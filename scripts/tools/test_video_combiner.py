#!/usr/bin/env python3
"""
测试视频随机组合工具
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from video_random_combiner import VideoRandomComb<PERSON>

def test_video_combiner():
    """测试视频组合工具"""
    print("🧪 测试视频随机组合工具")
    
    # 检查是否有测试视频
    test_dirs = [
        "/Users/<USER>/Desktop/personal/gen_project/workspace/visual_gen_agent/visual_gen_agent/downloads",
        "/Users/<USER>/Desktop/personal/gen_project/workspace/visual_gen_agent/visual_gen_agent/test_downloads"
    ]
    
    available_dirs = []
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            # 检查是否有视频文件
            video_files = []
            for ext in ['.mp4', '.avi', '.mov', '.mkv']:
                video_files.extend(Path(test_dir).glob(f'*{ext}'))
                video_files.extend(Path(test_dir).glob(f'**/*{ext}'))
            
            if video_files:
                available_dirs.append(test_dir)
                print(f"✅ 找到测试目录: {test_dir} (包含 {len(video_files)} 个视频)")
            else:
                print(f"⚠️ 测试目录存在但无视频: {test_dir}")
        else:
            print(f"❌ 测试目录不存在: {test_dir}")
    
    if not available_dirs:
        print("❌ 没有找到包含视频的测试目录")
        return False
    
    # 创建临时输出目录
    with tempfile.TemporaryDirectory() as temp_output:
        print(f"\n📁 临时输出目录: {temp_output}")
        
        try:
            # 测试1: 创建1个30秒的视频
            print("\n🧪 测试1: 创建1个30秒的视频")
            combiner1 = VideoRandomCombiner(target_duration=30.0, output_count=1)
            result1 = combiner1.combine_videos(available_dirs, temp_output)
            
            if result1:
                print(f"✅ 测试1成功: 创建了 {len(result1)} 个视频")
                for video in result1:
                    size = os.path.getsize(video) / (1024 * 1024)  # MB
                    print(f"   📹 {os.path.basename(video)} ({size:.1f} MB)")
            else:
                print("❌ 测试1失败")
                return False
            
            # 测试2: 创建2个15秒的视频
            print("\n🧪 测试2: 创建2个15秒的视频")
            combiner2 = VideoRandomCombiner(target_duration=15.0, output_count=2)
            result2 = combiner2.combine_videos(available_dirs, temp_output)
            
            if result2:
                print(f"✅ 测试2成功: 创建了 {len(result2)} 个视频")
                for video in result2:
                    size = os.path.getsize(video) / (1024 * 1024)  # MB
                    print(f"   📹 {os.path.basename(video)} ({size:.1f} MB)")
            else:
                print("❌ 测试2失败")
                return False
            
            print(f"\n🎉 所有测试通过! 临时文件将在函数结束时自动清理")
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("视频随机组合工具 - 测试程序")
    print("=" * 60)
    
    # 检查ffmpeg
    try:
        import subprocess
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✅ ffmpeg 检查通过")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ ffmpeg 未安装或不在PATH中")
        print("请先安装ffmpeg: https://ffmpeg.org/download.html")
        return
    
    # 运行测试
    if test_video_combiner():
        print("\n🎉 所有测试完成!")
    else:
        print("\n❌ 测试失败!")

if __name__ == "__main__":
    main() 