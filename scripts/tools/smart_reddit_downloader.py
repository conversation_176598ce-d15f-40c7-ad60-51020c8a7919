#!/usr/bin/env python3
"""
智能Reddit视频下载器
自动检测可用格式并选择最佳格式进行下载
"""

import subprocess
import json
import argparse
import sys
import time
import logging
import re
from pathlib import Path
from typing import List, Optional, Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartRedditDownloader:
    def __init__(self, output_dir: str = "./downloads"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查必要的依赖"""
        try:
            subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True)
            logger.info("✅ yt-dlp 已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ yt-dlp 未安装，请先安装: pip install yt-dlp")
            sys.exit(1)
        
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
            logger.info("✅ ffmpeg 已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ ffmpeg 未安装，请先安装 ffmpeg")
            sys.exit(1)
    
    def get_available_formats(self, url: str, auth_options: List[str] = None) -> Optional[Dict]:
        """获取视频的可用格式"""
        try:
            cmd = ['yt-dlp', '--dump-json', '--no-warnings']
            if auth_options:
                cmd.extend(auth_options)
            cmd.append(url)
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return data.get('formats', [])
            else:
                logger.error(f"获取格式失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"获取格式异常: {e}")
            return None
    
    def select_best_format(self, formats: List[Dict]) -> str:
        """智能选择最佳格式组合"""
        if not formats:
            return "best"
        
        # 按类型分类格式
        video_formats = []
        audio_formats = []
        
        for fmt in formats:
            format_id = fmt.get('format_id', '')
            vcodec = fmt.get('vcodec', 'none')
            acodec = fmt.get('acodec', 'none')
            filesize = fmt.get('filesize', 0) or 0
            
            if vcodec != 'none' and acodec == 'none':
                # 纯视频格式，添加文件大小信息
                fmt['_filesize'] = filesize
                video_formats.append(fmt)
            elif acodec != 'none' and vcodec == 'none':
                # 纯音频格式
                audio_formats.append(fmt)
        
        # 选择最佳视频格式 - 优先高质量
        best_video = None
        max_size = 500 * 1024 * 1024  # 提高到500MB限制，允许更高质量
        
        # 首先尝试fallback格式（通常是最高质量）
        for fmt in video_formats:
            format_id = fmt.get('format_id', '')
            filesize = fmt.get('_filesize', 0)
            height = fmt.get('height', 0) or 0
            
            if 'fallback' in format_id:
                # 如果文件大小在限制内，优先选择fallback
                if filesize <= max_size:
                    best_video = fmt
                    logger.info(f"🎯 选择fallback格式，分辨率: {fmt.get('width', '?')}x{height}")
                    break
        
        # 如果fallback太大或不存在，选择最高质量的dash格式
        if not best_video:
            dash_formats = [fmt for fmt in video_formats if fmt.get('format_id', '').startswith('dash-')]
            # 按分辨率和格式ID排序，选择最高质量的
            dash_formats.sort(key=lambda x: (x.get('height', 0), x.get('format_id', '')), reverse=True)
            
            for fmt in dash_formats:
                filesize = fmt.get('_filesize', 0)
                if filesize <= max_size or not best_video:
                    best_video = fmt
                    height = fmt.get('height', 0) or 0
                    logger.info(f"🎯 选择dash格式: {fmt.get('format_id', '')}, 分辨率: {fmt.get('width', '?')}x{height}")
                    break
        
        # 如果还没找到，尝试最高质量的hls格式
        if not best_video:
            hls_formats = [fmt for fmt in video_formats if fmt.get('format_id', '').startswith('hls-')]
            # 按比特率排序，选择最高质量的
            hls_formats.sort(key=lambda x: int(x.get('format_id', 'hls-0').split('-')[1]) if x.get('format_id', '').startswith('hls-') else 0, reverse=True)
            
            for fmt in hls_formats:
                filesize = fmt.get('_filesize', 0)
                if filesize <= max_size or not best_video:
                    best_video = fmt
                    height = fmt.get('height', 0) or 0
                    logger.info(f"🎯 选择hls格式: {fmt.get('format_id', '')}, 分辨率: {fmt.get('width', '?')}x{height}")
                    break
        
        # 选择最佳音频格式 - 优先高质量
        best_audio = None
        
        # 首先选择最高质量的dash音频格式
        dash_audio_formats = [fmt for fmt in audio_formats if fmt.get('format_id', '').startswith('dash-')]
        if dash_audio_formats:
            # 按格式ID排序，选择最高编号的（通常质量更高）
            dash_audio_formats.sort(key=lambda x: x.get('format_id', ''), reverse=True)
            best_audio = dash_audio_formats[0]
            logger.info(f"🎵 选择音频格式: {best_audio.get('format_id', '')}")
        
        # 如果没有dash音频，选择hls音频
        if not best_audio:
            hls_audio_formats = [fmt for fmt in audio_formats if 'audio' in fmt.get('format_id', '')]
            if hls_audio_formats:
                # 选择high质量的音频
                for fmt in hls_audio_formats:
                    if 'high' in fmt.get('format_id', '') or 'audio_0' in fmt.get('format_id', ''):
                        best_audio = fmt
                        logger.info(f"🎵 选择音频格式: {best_audio.get('format_id', '')}")
                        break
                
                # 如果没有找到特定的，选择第一个
                if not best_audio:
                    best_audio = hls_audio_formats[0]
                    logger.info(f"🎵 选择音频格式: {best_audio.get('format_id', '')}")
        
        # 组合格式
        if best_video and best_audio:
            video_id = best_video.get('format_id', '')
            audio_id = best_audio.get('format_id', '')
            format_str = f"{video_id}+{audio_id}"
            
            # 显示文件大小信息
            video_size = best_video.get('_filesize', 0)
            if video_size > 0:
                size_mb = video_size / (1024 * 1024)
                logger.info(f"选择格式: {format_str} (视频大小: {size_mb:.1f}MB)")
            else:
                logger.info(f"选择格式: {format_str}")
            return format_str
        elif best_video:
            video_id = best_video.get('format_id', '')
            video_size = best_video.get('_filesize', 0)
            if video_size > 0:
                size_mb = video_size / (1024 * 1024)
                logger.info(f"选择视频格式: {video_id} (大小: {size_mb:.1f}MB)")
            else:
                logger.info(f"选择视频格式: {video_id}")
            return video_id
        else:
            logger.info("使用默认格式: best")
            return "best"
    
    def get_video_info(self, url: str, auth_options: List[str] = None) -> Optional[Dict]:
        """获取视频信息（包括标题等）"""
        try:
            cmd = ['yt-dlp', '--dump-json', '--no-warnings', '--no-download']
            if auth_options:
                cmd.extend(auth_options)
            cmd.append(url)
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                logger.error(f"获取视频信息失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"获取视频信息异常: {e}")
            return None
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除特殊字符，与yt-dlp的--restrict-filenames保持一致"""
        # 这个函数应该模拟yt-dlp的--restrict-filenames行为
        # 替换特殊字符为下划线
        filename = re.sub(r'[^\w\s\-_\.]', '_', filename)
        # 替换多个空格为单个下划线
        filename = re.sub(r'\s+', '_', filename)
        # 替换多个连续下划线为单个下划线
        filename = re.sub(r'_+', '_', filename)
        # 移除首尾的下划线和点
        filename = filename.strip('_.')
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100].rstrip('_.')
        return filename
    
    def check_existing_video(self, video_info: Dict) -> Optional[Path]:
        """检查视频是否已经存在"""
        if not video_info:
            return None
            
        title = video_info.get('title', 'unknown')
        
        # 使用与yt-dlp相同的文件名清理逻辑
        sanitized_title = self.sanitize_filename(title)
        
        logger.info(f"🔍 检查文件: 原标题='{title}', 清理后='{sanitized_title}'")
        
        # 可能的文件名格式
        possible_names = [
            f"{sanitized_title}.mp4",
            f"{title}.mp4",  # 原始标题（如果没有特殊字符）
        ]
        
        # 检查是否存在
        for name in possible_names:
            file_path = self.output_dir / name
            logger.info(f"🔍 检查路径: {file_path}")
            if file_path.exists() and file_path.stat().st_size > 0:
                logger.info(f"📁 发现已存在文件: {file_path.name}")
                return file_path
        
        # 列出目录中的所有mp4文件进行调试
        existing_files = list(self.output_dir.glob("*.mp4"))
        if existing_files:
            logger.info(f"🔍 目录中现有的mp4文件:")
            for f in existing_files:
                logger.info(f"   - {f.name}")
        
        # 模糊匹配 - 检查包含关键词的文件
        title_words = set(re.findall(r'\w+', sanitized_title.lower()))
        if title_words and len(title_words) >= 2:  # 至少要有2个词才进行模糊匹配
            for existing_file in existing_files:
                existing_words = set(re.findall(r'\w+', existing_file.stem.lower()))
                # 如果有足够多的共同词汇，认为是同一个视频
                common_words = title_words.intersection(existing_words)
                match_ratio = len(common_words) / len(title_words) if title_words else 0
                logger.info(f"🔍 匹配检查: {existing_file.name}")
                logger.info(f"   标题词汇: {sorted(title_words)}")
                logger.info(f"   文件词汇: {sorted(existing_words)}")
                logger.info(f"   共同词汇: {sorted(common_words)} - {len(common_words)}/{len(title_words)} ({match_ratio:.2f})")
                
                # 降低匹配阈值，并且对于短标题更宽松
                min_matches = max(2, min(3, len(title_words) * 0.5))
                if len(common_words) >= min_matches or match_ratio >= 0.6:
                    logger.info(f"📁 发现匹配的文件: {existing_file.name} (匹配度: {match_ratio:.2f})")
                    return existing_file
        
        return None

    def download_video(self, url: str, auth_options: List[str] = None) -> Dict[str, any]:
        """下载单个视频，返回结果状态"""
        try:
            logger.info(f"🎬 开始处理: {url}")
            
            # 首先获取视频信息
            video_info = self.get_video_info(url, auth_options)
            if not video_info:
                logger.error(f"无法获取视频信息: {url}")
                return {'status': 'failed', 'reason': '无法获取视频信息'}
            
            # 检查是否已经存在
            existing_file = self.check_existing_video(video_info)
            if existing_file:
                file_size = existing_file.stat().st_size / (1024 * 1024)  # MB
                logger.info(f"⏭️  视频已存在，跳过下载: {existing_file.name} ({file_size:.1f}MB)")
                return {'status': 'skipped', 'file': existing_file.name, 'size': file_size}
            
            logger.info(f"🎬 开始下载: {video_info.get('title', 'unknown')}")
            
            # 获取可用格式
            formats = video_info.get('formats', [])
            if not formats:
                logger.error(f"无法获取视频格式: {url}")
                return {'status': 'failed', 'reason': '无法获取视频格式'}
            
            # 选择最佳格式
            best_format = self.select_best_format(formats)
            
            # 构建下载命令 - 使用更安全的文件名模板
            cmd = [
                'yt-dlp',
                '-f', best_format,
                '-o', str(self.output_dir / '%(title).100s.%(ext)s'),  # 限制标题长度
                '--merge-output-format', 'mp4',
                '--write-info-json',
                '--write-thumbnail',
                '--embed-metadata',
                '--no-warnings',
                '--restrict-filenames',  # 限制文件名字符，避免特殊字符
            ]
            
            # 添加认证选项
            if auth_options:
                cmd.extend(auth_options)
            
            # 添加URL
            cmd.append(url)
            
            logger.info(f"🔧 执行命令: {' '.join(cmd)}")
            
            # 执行下载 - 实时显示进度
            logger.info(f"⏳ 开始执行下载...")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 实时输出下载进度
            output_lines = []
            while True:
                line = process.stdout.readline()
                if not line and process.poll() is not None:
                    break
                if line:
                    line = line.strip()
                    output_lines.append(line)
                    
                    # 显示下载进度
                    if '[download]' in line:
                        if '% of' in line or 'ETA' in line:
                            logger.info(f"📥 {line}")
                    elif '[Merger]' in line:
                        logger.info(f"🔄 {line}")
                    elif 'ERROR:' in line:
                        logger.error(f"❌ {line}")
                    elif line.startswith('['):
                        logger.info(f"ℹ️  {line}")
            
            # 等待进程完成
            return_code = process.wait()
            result_output = '\n'.join(output_lines)
            
            if return_code == 0:
                logger.info(f"✅ 下载成功: {url}")
                return {'status': 'downloaded', 'title': video_info.get('title', 'unknown')}
            else:
                logger.error(f"❌ 下载失败: {url}")
                # 只显示最后几行错误信息，避免输出过多
                error_lines = result_output.split('\n')[-5:]
                for line in error_lines:
                    if line.strip():
                        logger.error(f"   {line}")
                return {'status': 'failed', 'reason': '下载命令执行失败'}
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 下载超时: {url}")
            return {'status': 'failed', 'reason': '下载超时'}
        except Exception as e:
            logger.error(f"💥 下载异常: {url} - {e}")
            return {'status': 'failed', 'reason': f'下载异常: {e}'}
    
    def read_urls_from_file(self, file_path: str) -> List[str]:
        """从文件读取URL列表"""
        urls = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    # 跳过空行和注释
                    if line and not line.startswith('#'):
                        urls.append(line)
                        logger.info(f"📝 第{line_num}行: {line}")
            
            logger.info(f"📋 总共读取到 {len(urls)} 个视频URL")
            return urls
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return []
    
    def download_from_file(self, file_path: str, auth_options: List[str] = None, delay: float = 3.0) -> Dict:
        """从文件批量下载视频"""
        urls = self.read_urls_from_file(file_path)
        
        results = {
            'total': len(urls),
            'success': 0,
            'skipped': 0,
            'downloaded': 0,
            'failed': 0,
            'failed_urls': []
        }
        
        logger.info(f"🚀 开始批量下载，总共 {len(urls)} 个视频")
        logger.info(f"📁 输出目录: {self.output_dir.absolute()}")
        
        for i, url in enumerate(urls, 1):
            logger.info(f"\n📺 [{i}/{len(urls)}] 处理: {url}")
            logger.info(f"📊 当前进度: {i}/{len(urls)} ({(i/len(urls)*100):.1f}%)")
            
            result = self.download_video(url, auth_options)
            
            if result['status'] == 'downloaded':
                results['downloaded'] += 1
                results['success'] += 1
                logger.info(f"🎉 第{i}个视频下载成功！")
            elif result['status'] == 'skipped':
                results['skipped'] += 1
                results['success'] += 1
                logger.info(f"⏭️  第{i}个视频已存在，跳过！")
            else:  # failed
                results['failed'] += 1
                results['failed_urls'].append(url)
                logger.error(f"💔 第{i}个视频处理失败！")
            
            # 显示当前统计
            logger.info(f"📈 当前统计: 成功 {results['success']} (下载 {results['downloaded']} + 跳过 {results['skipped']}) | 失败 {results['failed']} | 剩余 {len(urls)-i}")
            
            # 添加延迟，避免被限制
            if i < len(urls) and delay > 0:
                logger.info(f"⏱️ 等待 {delay} 秒后继续...")
                time.sleep(delay)
        
        # 输出最终统计结果
        logger.info(f"\n🏁 下载完成统计:")
        logger.info(f"   总数: {results['total']}")
        logger.info(f"   成功: {results['success']} ({(results['success']/results['total']*100):.1f}%)")
        logger.info(f"   - 新下载: {results['downloaded']}")
        logger.info(f"   - 已跳过: {results['skipped']}")
        logger.info(f"   失败: {results['failed']}")
        
        if results['failed_urls']:
            logger.info(f"\n❌ 失败的URL:")
            for i, url in enumerate(results['failed_urls'], 1):
                logger.info(f"   {i}. {url}")
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="智能Reddit视频下载器，自动选择最佳格式",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--input', '-i',
        required=True,
        help="包含视频URL的输入文件路径（每行一个URL）"
    )
    
    parser.add_argument(
        '--output', '-o',
        default="./downloads",
        help="下载文件的输出目录"
    )
    
    parser.add_argument(
        '--delay', '-d',
        type=float,
        default=3.0,
        help="下载间隔时间（秒），避免被限制"
    )
    
    parser.add_argument(
        '--cookies-from-browser',
        choices=['chrome', 'firefox', 'safari', 'edge'],
        help="从指定浏览器自动获取cookies"
    )
    
    args = parser.parse_args()
    
    # 创建下载器
    downloader = SmartRedditDownloader(args.output)
    
    # 构建认证选项
    auth_options = []
    if args.cookies_from_browser:
        auth_options.extend(['--cookies-from-browser', args.cookies_from_browser])
        logger.info(f"🌐 从浏览器获取cookies: {args.cookies_from_browser}")
    
    # 执行下载
    results = downloader.download_from_file(
        args.input, 
        auth_options, 
        args.delay
    )
    
    # 退出码
    sys.exit(0 if results['failed'] == 0 else 1)

if __name__ == "__main__":
    main() 