#!/usr/bin/env python3
"""
YouTube视频下载器
专门用于下载YouTube视频，支持最佳格式选择
"""

import subprocess
import json
import argparse
import sys
import time
import logging
import re
from pathlib import Path
from typing import List, Optional, Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YouTubeDownloader:
    def __init__(self, output_dir: str = "./downloads"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查必要的依赖"""
        try:
            subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True)
            logger.info("✅ yt-dlp 已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ yt-dlp 未安装，请先安装: pip install yt-dlp")
            sys.exit(1)
        
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
            logger.info("✅ ffmpeg 已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ ffmpeg 未安装，请先安装 ffmpeg")
            sys.exit(1)
    
    def get_video_info(self, url: str) -> Optional[Dict]:
        """获取YouTube视频信息"""
        try:
            cmd = ['yt-dlp', '--dump-json', '--no-warnings', '--no-download', url]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                logger.error(f"获取视频信息失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"获取视频信息异常: {e}")
            return None
    
    def select_best_format(self, video_info: Dict) -> str:
        """为YouTube视频选择最佳格式"""
        formats = video_info.get('formats', [])
        if not formats:
            return "best[height<=1080]"
        
        # YouTube格式优先级策略
        # 1. 优先720p-1080p分辨率
        # 2. 优先mp4格式
        # 3. 优先较小文件大小
        
        video_formats = []
        for fmt in formats:
            if fmt.get('vcodec', 'none') != 'none':
                height = fmt.get('height', 0) or 0
                ext = fmt.get('ext', '')
                filesize = fmt.get('filesize', 0) or 0
                format_id = fmt.get('format_id', '')
                
                # 只考虑合理分辨率的视频
                if 360 <= height <= 1080:
                    video_formats.append({
                        'format_id': format_id,
                        'height': height,
                        'ext': ext,
                        'filesize': filesize,
                        'fps': fmt.get('fps', 30) or 30,
                        'vcodec': fmt.get('vcodec', ''),
                        'acodec': fmt.get('acodec', '')
                    })
        
        if not video_formats:
            # 如果没有找到合适的格式，使用默认策略
            logger.info("🎯 使用默认格式策略: best[height<=1080]")
            return "best[height<=1080]"
        
        # 按照优先级排序：720p > 1080p > 480p > 其他
        def format_priority(fmt):
            height = fmt['height']
            ext = fmt['ext']
            filesize = fmt['filesize']
            has_audio = fmt['acodec'] != 'none'
            
            # 分辨率优先级
            if height == 720:
                res_score = 100
            elif height == 1080:
                res_score = 90
            elif height == 480:
                res_score = 80
            else:
                res_score = max(0, 70 - abs(height - 720) / 10)
            
            # 格式优先级
            ext_score = 20 if ext == 'mp4' else 10 if ext == 'webm' else 0
            
            # 有音频的格式优先
            audio_score = 30 if has_audio else 0
            
            # 文件大小惩罚（太大的文件降低优先级）
            max_size = 500 * 1024 * 1024  # 500MB
            size_score = 0 if filesize > max_size else 10
            
            return res_score + ext_score + audio_score + size_score
        
        # 选择最佳格式
        best_format = max(video_formats, key=format_priority)
        format_id = best_format['format_id']
        
        height = best_format['height']
        ext = best_format['ext']
        has_audio = best_format['acodec'] != 'none'
        filesize = best_format['filesize']
        
        size_info = f", 大小: {filesize/(1024*1024):.1f}MB" if filesize > 0 else ""
        audio_info = " (含音频)" if has_audio else " (仅视频)"
        
        logger.info(f"🎯 选择格式: {format_id} - {height}p {ext}{audio_info}{size_info}")
        
        return format_id
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除特殊字符"""
        # 替换特殊字符为下划线
        filename = re.sub(r'[^\w\s\-_\.]', '_', filename)
        # 替换多个空格为单个下划线
        filename = re.sub(r'\s+', '_', filename)
        # 替换多个连续下划线为单个下划线
        filename = re.sub(r'_+', '_', filename)
        # 移除首尾的下划线和点
        filename = filename.strip('_.')
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100].rstrip('_.')
        return filename
    
    def check_existing_video(self, video_info: Dict) -> Optional[Path]:
        """检查视频是否已经存在"""
        if not video_info:
            return None
            
        title = video_info.get('title', 'unknown')
        video_id = video_info.get('id', '')
        
        # 使用标题和视频ID检查
        sanitized_title = self.sanitize_filename(title)
        
        logger.info(f"🔍 检查文件: 标题='{title}', ID={video_id}")
        
        # 可能的文件名格式
        possible_names = [
            f"{sanitized_title}.mp4",
            f"{sanitized_title}.webm",
            f"{title}.mp4",
            f"{video_id}.mp4",
            f"{sanitized_title} [{video_id}].mp4",
        ]
        
        # 检查是否存在
        for name in possible_names:
            file_path = self.output_dir / name
            if file_path.exists() and file_path.stat().st_size > 0:
                logger.info(f"📁 发现已存在文件: {file_path.name}")
                return file_path
        
        # 模糊匹配 - 检查包含视频ID的文件
        existing_files = list(self.output_dir.glob("*.mp4")) + list(self.output_dir.glob("*.webm"))
        
        for existing_file in existing_files:
            # 检查文件名是否包含视频ID
            if video_id in existing_file.name:
                logger.info(f"📁 发现匹配的文件（通过ID）: {existing_file.name}")
                return existing_file
            
            # 检查文件名是否匹配标题
            file_words = set(re.findall(r'\w+', existing_file.stem.lower()))
            title_words = set(re.findall(r'\w+', sanitized_title.lower()))
            
            if title_words and len(title_words) >= 2:
                common_words = title_words.intersection(file_words)
                match_ratio = len(common_words) / len(title_words) if title_words else 0
                
                if match_ratio >= 0.7:  # 70%的词汇匹配
                    logger.info(f"📁 发现匹配的文件（通过标题）: {existing_file.name} (匹配度: {match_ratio:.2f})")
                    return existing_file
        
        return None

    def download_video(self, url: str) -> Dict[str, any]:
        """下载单个YouTube视频"""
        try:
            logger.info(f"🎬 开始处理: {url}")
            
            # 首先获取视频信息
            video_info = self.get_video_info(url)
            if not video_info:
                logger.error(f"无法获取视频信息: {url}")
                return {'status': 'failed', 'reason': '无法获取视频信息'}
            
            title = video_info.get('title', 'unknown')
            duration = video_info.get('duration', 0)
            video_id = video_info.get('id', '')
            
            logger.info(f"📺 视频: {title}")
            logger.info(f"⏱️ 时长: {duration}秒 ({duration//60}:{duration%60:02d})")
            logger.info(f"🆔 ID: {video_id}")
            
            # 检查是否已经存在
            existing_file = self.check_existing_video(video_info)
            if existing_file:
                file_size = existing_file.stat().st_size / (1024 * 1024)  # MB
                logger.info(f"⏭️  视频已存在，跳过下载: {existing_file.name} ({file_size:.1f}MB)")
                return {'status': 'skipped', 'file': existing_file.name, 'size': file_size}
            
            # 选择最佳格式
            best_format = self.select_best_format(video_info)
            
            # 构建下载命令
            output_template = str(self.output_dir / '%(title).100s [%(id)s].%(ext)s')
            
            cmd = [
                'yt-dlp',
                '-f', best_format,
                '-o', output_template,
                '--merge-output-format', 'mp4',
                '--write-info-json',
                '--write-thumbnail',
                '--embed-metadata',
                '--embed-subs',  # 嵌入字幕
                '--write-auto-subs',  # 下载自动生成的字幕
                '--sub-langs', 'zh,en',  # 优先中文和英文字幕
                '--no-warnings',
                '--restrict-filenames',
                url
            ]
            
            logger.info(f"⏳ 开始下载: {title}")
            
            # 执行下载
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 实时输出下载进度
            output_lines = []
            while True:
                line = process.stdout.readline()
                if not line and process.poll() is not None:
                    break
                if line:
                    line = line.strip()
                    output_lines.append(line)
                    
                    # 显示关键信息
                    if '[download]' in line:
                        if '% of' in line or 'ETA' in line:
                            logger.info(f"📥 {line}")
                    elif '[Merger]' in line:
                        logger.info(f"🔄 {line}")
                    elif '[ExtractAudio]' in line:
                        logger.info(f"🎵 {line}")
                    elif 'ERROR:' in line:
                        logger.error(f"❌ {line}")
                    elif line.startswith('[info]'):
                        logger.info(f"ℹ️  {line}")
            
            # 等待进程完成
            return_code = process.wait()
            
            if return_code == 0:
                logger.info(f"✅ 下载成功: {title}")
                return {'status': 'downloaded', 'title': title, 'id': video_id}
            else:
                logger.error(f"❌ 下载失败: {title}")
                # 显示错误信息
                error_lines = [line for line in output_lines if 'ERROR:' in line]
                for line in error_lines[-3:]:  # 只显示最后3个错误
                    logger.error(f"   {line}")
                return {'status': 'failed', 'reason': '下载命令执行失败'}
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 下载超时: {url}")
            return {'status': 'failed', 'reason': '下载超时'}
        except Exception as e:
            logger.error(f"💥 下载异常: {url} - {e}")
            return {'status': 'failed', 'reason': f'下载异常: {e}'}
    
    def read_urls_from_file(self, file_path: str) -> List[str]:
        """从文件读取YouTube URL列表"""
        urls = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    # 跳过空行和注释
                    if line and not line.startswith('#'):
                        # 验证是否为YouTube URL
                        if 'youtube.com' in line or 'youtu.be' in line:
                            urls.append(line)
                            logger.info(f"📝 第{line_num}行: {line}")
                        else:
                            logger.warning(f"⚠️ 第{line_num}行不是YouTube URL，跳过: {line}")
            
            logger.info(f"📋 总共读取到 {len(urls)} 个YouTube视频URL")
            return urls
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return []
    
    def download_from_file(self, file_path: str, delay: float = 2.0) -> Dict:
        """从文件批量下载YouTube视频"""
        urls = self.read_urls_from_file(file_path)
        
        results = {
            'total': len(urls),
            'success': 0,
            'skipped': 0,
            'downloaded': 0,
            'failed': 0,
            'failed_urls': []
        }
        
        logger.info(f"🚀 开始批量下载YouTube视频，总共 {len(urls)} 个")
        logger.info(f"📁 输出目录: {self.output_dir.absolute()}")
        
        for i, url in enumerate(urls, 1):
            logger.info(f"\n📺 [{i}/{len(urls)}] 处理: {url}")
            logger.info(f"📊 当前进度: {i}/{len(urls)} ({(i/len(urls)*100):.1f}%)")
            
            result = self.download_video(url)
            
            if result['status'] == 'downloaded':
                results['downloaded'] += 1
                results['success'] += 1
                logger.info(f"🎉 第{i}个视频下载成功！")
            elif result['status'] == 'skipped':
                results['skipped'] += 1
                results['success'] += 1
                logger.info(f"⏭️  第{i}个视频已存在，跳过！")
            else:  # failed
                results['failed'] += 1
                results['failed_urls'].append(url)
                logger.error(f"💔 第{i}个视频处理失败！")
            
            # 显示当前统计
            logger.info(f"📈 当前统计: 成功 {results['success']} (下载 {results['downloaded']} + 跳过 {results['skipped']}) | 失败 {results['failed']} | 剩余 {len(urls)-i}")
            
            # 添加延迟，避免被YouTube限制
            if i < len(urls) and delay > 0:
                logger.info(f"⏱️ 等待 {delay} 秒后继续...")
                time.sleep(delay)
        
        # 输出最终统计结果
        logger.info(f"\n🏁 YouTube下载完成统计:")
        logger.info(f"   总数: {results['total']}")
        logger.info(f"   成功: {results['success']} ({(results['success']/results['total']*100):.1f}%)")
        logger.info(f"   - 新下载: {results['downloaded']}")
        logger.info(f"   - 已跳过: {results['skipped']}")
        logger.info(f"   失败: {results['failed']}")
        
        if results['failed_urls']:
            logger.info(f"\n❌ 失败的URL:")
            for i, url in enumerate(results['failed_urls'], 1):
                logger.info(f"   {i}. {url}")
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YouTube视频下载器，自动选择最佳格式",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--input', '-i',
        required=True,
        help="包含YouTube视频URL的输入文件路径（每行一个URL）"
    )
    
    parser.add_argument(
        '--output', '-o',
        default="./youtube_downloads",
        help="下载文件的输出目录"
    )
    
    parser.add_argument(
        '--delay', '-d',
        type=float,
        default=2.0,
        help="下载间隔时间（秒），避免被限制"
    )
    
    args = parser.parse_args()
    
    # 创建下载器
    downloader = YouTubeDownloader(args.output)
    
    # 执行下载
    results = downloader.download_from_file(args.input, args.delay)
    
    # 退出码
    sys.exit(0 if results['failed'] == 0 else 1)

if __name__ == "__main__":
    main() 