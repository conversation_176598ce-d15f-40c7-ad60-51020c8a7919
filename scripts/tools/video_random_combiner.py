#!/usr/bin/env python3
"""
视频随机组合工具
从指定文件夹中的视频进行随机组合，生成指定时长和数量的新视频
"""

import os
import random
import argparse
import tempfile
import shutil
from pathlib import Path
from typing import List, Tuple, Optional
import subprocess
import json

class VideoRandomCombiner:
    """视频随机组合器"""
    
    def __init__(self, target_duration: float = 45.0, output_count: int = 1):
        """
        初始化视频组合器
        
        Args:
            target_duration: 目标视频时长（秒），默认45秒
            output_count: 输出视频数量，默认1个
        """
        self.target_duration = target_duration
        self.output_count = output_count
        self.supported_formats = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm', '.m4v'}
        
    def get_video_duration(self, video_path: str) -> float:
        """
        获取视频时长
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频时长（秒）
        """
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            data = json.loads(result.stdout)
            
            # 从format信息中获取时长
            if 'format' in data and 'duration' in data['format']:
                return float(data['format']['duration'])
            
            # 从视频流中获取时长
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video' and 'duration' in stream:
                    return float(stream['duration'])
                    
            print(f"⚠️ 无法获取视频时长: {video_path}")
            return 0.0
            
        except (subprocess.CalledProcessError, json.JSONDecodeError, KeyError) as e:
            print(f"❌ 获取视频时长失败 {video_path}: {e}")
            return 0.0
    
    def find_videos(self, folder_paths: List[str]) -> List[Tuple[str, float]]:
        """
        从指定文件夹中查找所有视频文件
        
        Args:
            folder_paths: 文件夹路径列表
            
        Returns:
            视频文件路径和时长的列表 [(path, duration), ...]
        """
        videos = []
        
        for folder_path in folder_paths:
            folder = Path(folder_path)
            if not folder.exists():
                print(f"⚠️ 文件夹不存在: {folder_path}")
                continue
                
            print(f"🔍 扫描文件夹: {folder_path}")
            
            for file_path in folder.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    duration = self.get_video_duration(str(file_path))
                    if duration > 0:
                        videos.append((str(file_path), duration))
                        print(f"   ✅ 发现视频: {file_path.name} ({duration:.1f}s)")
                    else:
                        print(f"   ❌ 跳过无效视频: {file_path.name}")
        
        print(f"\n📊 总共发现 {len(videos)} 个有效视频文件")
        return videos
    
    def cut_video_segment(self, video_path: str, start_time: float, duration: float, output_path: str) -> bool:
        """
        裁剪视频片段
        
        Args:
            video_path: 源视频路径
            start_time: 开始时间（秒）
            duration: 片段时长（秒）
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            cmd = [
                'ffmpeg', '-i', video_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c', 'copy',  # 使用流复制，速度更快
                '-avoid_negative_ts', 'make_zero',
                '-y', output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return os.path.exists(output_path)
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 裁剪视频失败: {e}")
            return False
    
    def concat_videos(self, video_segments: List[str], output_path: str) -> bool:
        """
        拼接多个视频片段
        
        Args:
            video_segments: 视频片段路径列表
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        if not video_segments:
            return False
            
        # 创建临时文件列表
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            concat_file = f.name
            for segment in video_segments:
                f.write(f"file '{segment}'\n")
        
        try:
            cmd = [
                'ffmpeg', '-f', 'concat', '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',
                '-y', output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            success = os.path.exists(output_path)
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 拼接视频失败: {e}")
            success = False
        
        finally:
            # 清理临时文件
            if os.path.exists(concat_file):
                os.unlink(concat_file)
        
        return success
    
    def generate_random_segments(self, videos: List[Tuple[str, float]]) -> List[Tuple[str, float, float]]:
        """
        生成随机视频片段
        
        Args:
            videos: 视频文件列表 [(path, duration), ...]
            
        Returns:
            视频片段列表 [(path, start_time, duration), ...]
        """
        segments = []
        current_duration = 0.0
        used_videos = set()
        
        while current_duration < self.target_duration:
            remaining_duration = self.target_duration - current_duration
            
            # 随机选择一个视频（避免重复使用同一个视频）
            available_videos = [v for i, v in enumerate(videos) if i not in used_videos]
            if not available_videos:
                # 如果所有视频都用过了，重置可用视频列表
                available_videos = videos
                used_videos.clear()
            
            video_path, video_duration = random.choice(available_videos)
            video_index = videos.index((video_path, video_duration))
            
            if video_duration <= remaining_duration:
                # 整个视频都可以用
                segments.append((video_path, 0.0, video_duration))
                current_duration += video_duration
                used_videos.add(video_index)
            else:
                # 只用视频的一部分
                if video_duration > remaining_duration:
                    # 随机选择起始位置
                    max_start = video_duration - remaining_duration
                    start_time = random.uniform(0, max_start) if max_start > 0 else 0
                    segments.append((video_path, start_time, remaining_duration))
                    current_duration += remaining_duration
                    used_videos.add(video_index)
        
        return segments
    
    def create_combined_video(self, videos: List[Tuple[str, float]], output_path: str, video_index: int) -> bool:
        """
        创建一个组合视频
        
        Args:
            videos: 视频文件列表
            output_path: 输出文件路径
            video_index: 视频索引（用于显示）
            
        Returns:
            是否成功
        """
        print(f"\n🎬 开始创建视频 {video_index + 1}/{self.output_count}")
        
        # 生成随机片段
        segments = self.generate_random_segments(videos)
        
        print(f"📝 计划使用 {len(segments)} 个视频片段:")
        total_planned_duration = 0
        for i, (path, start, duration) in enumerate(segments, 1):
            total_planned_duration += duration
            print(f"   {i}. {Path(path).name} ({start:.1f}s - {start + duration:.1f}s, 时长: {duration:.1f}s)")
        print(f"   📊 计划总时长: {total_planned_duration:.1f}s")
        
        # 创建临时目录存放片段
        with tempfile.TemporaryDirectory() as temp_dir:
            segment_files = []
            
            # 提取各个片段
            for i, (video_path, start_time, duration) in enumerate(segments):
                segment_file = os.path.join(temp_dir, f"segment_{i:03d}.mp4")
                
                print(f"   🔧 提取片段 {i + 1}/{len(segments)}: {Path(video_path).name}")
                if self.cut_video_segment(video_path, start_time, duration, segment_file):
                    segment_files.append(segment_file)
                else:
                    print(f"   ❌ 片段提取失败: {i + 1}")
                    return False
            
            # 拼接所有片段
            print(f"   🔗 拼接 {len(segment_files)} 个片段...")
            if self.concat_videos(segment_files, output_path):
                # 验证输出文件
                final_duration = self.get_video_duration(output_path)
                print(f"   ✅ 视频创建成功: {Path(output_path).name} (实际时长: {final_duration:.1f}s)")
                return True
            else:
                print(f"   ❌ 视频拼接失败")
                return False
    
    def combine_videos(self, folder_paths: List[str], output_dir: str) -> List[str]:
        """
        主要功能：组合视频
        
        Args:
            folder_paths: 输入文件夹路径列表
            output_dir: 输出目录路径
            
        Returns:
            成功创建的视频文件路径列表
        """
        print(f"🚀 开始视频随机组合任务")
        print(f"   📁 输入文件夹: {folder_paths}")
        print(f"   📁 输出目录: {output_dir}")
        print(f"   ⏱️ 目标时长: {self.target_duration}秒")
        print(f"   🎞️ 输出数量: {self.output_count}个")
        
        # 检查ffmpeg是否可用
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError("❌ ffmpeg 未安装或不在PATH中，请先安装ffmpeg")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 查找所有视频文件
        videos = self.find_videos(folder_paths)
        
        if not videos:
            raise ValueError("❌ 未找到任何有效的视频文件")
        
        if len(videos) < 2:
            print("⚠️ 只有1个视频文件，建议添加更多视频以增加随机性")
        
        # 创建组合视频
        successful_videos = []
        
        for i in range(self.output_count):
            output_filename = f"combined_video_{i + 1:03d}_{self.target_duration:.0f}s.mp4"
            output_path = os.path.join(output_dir, output_filename)
            
            if self.create_combined_video(videos, output_path, i):
                successful_videos.append(output_path)
            else:
                print(f"❌ 创建视频 {i + 1} 失败")
        
        print(f"\n🎉 任务完成! 成功创建 {len(successful_videos)}/{self.output_count} 个视频")
        for video_path in successful_videos:
            print(f"   ✅ {video_path}")
        
        return successful_videos


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description="视频随机组合工具 - 从指定文件夹中随机组合视频",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法
  python video_random_combiner.py /path/to/videos /path/to/output
  
  # 指定时长和数量
  python video_random_combiner.py /path/to/videos /path/to/output --duration 30 --count 5
  
  # 多个输入文件夹
  python video_random_combiner.py /path/to/videos1 /path/to/videos2 /path/to/output --duration 60 --count 3
        """
    )
    
    parser.add_argument(
        'paths',
        nargs='+',
        help='输入文件夹路径（可指定多个）+ 输出目录路径（最后一个参数）'
    )
    
    parser.add_argument(
        '--duration', '-d',
        type=float,
        default=45.0,
        help='目标视频时长（秒），默认45秒'
    )
    
    parser.add_argument(
        '--count', '-c',
        type=int,
        default=1,
        help='输出视频数量，默认1个'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出'
    )
    
    args = parser.parse_args()
    
    # 验证输入 - 最后一个参数是输出目录，前面的都是输入文件夹
    if len(args.paths) < 2:
        parser.error("至少需要一个输入文件夹和一个输出目录")
    
    input_folders = args.paths[:-1]  # 除了最后一个参数都是输入文件夹
    output_dir = args.paths[-1]      # 最后一个参数是输出目录
    
    if args.duration <= 0:
        parser.error("视频时长必须大于0")
    
    if args.count <= 0:
        parser.error("输出数量必须大于0")
    
    # 检查输入文件夹
    for folder in input_folders:
        if not os.path.exists(folder):
            parser.error(f"输入文件夹不存在: {folder}")
        if not os.path.isdir(folder):
            parser.error(f"不是有效的文件夹: {folder}")
    
    try:
        # 创建组合器并执行
        combiner = VideoRandomCombiner(
            target_duration=args.duration,
            output_count=args.count
        )
        
        successful_videos = combiner.combine_videos(input_folders, output_dir)
        
        if successful_videos:
            print(f"\n✨ 全部完成! 输出文件:")
            for video in successful_videos:
                print(f"   🎬 {video}")
        else:
            print("\n❌ 没有成功创建任何视频")
            exit(1)
    
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        exit(1)


if __name__ == "__main__":
    main() 