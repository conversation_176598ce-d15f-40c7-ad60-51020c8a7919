# 基础框架和核心依赖
Django>=4.2.7           # Web框架
django-allauth           # 社交认证
django-cors-headers      # CORS处理
django-extensions        # Django扩展
djangorestframework      # REST API框架
PyJWT                    # JWT令牌处理

# 数据库和缓存
redis==4.5.4            # 缓存和会话存储
mysqlclient>=2.2.0      # MySQL数据库连接器
django-redis             # Django Redis缓存后端

# 异步任务处理
celery>=5.3.0           # 分布式任务队列
kombu>=5.3.0            # 消息代理抽象层

# HTTP和API处理
requests>=2.31.0        # HTTP请求库
httpx>=0.24.0           # 异步HTTP客户端
uvicorn>=0.20.0         # ASGI服务器
gunicorn>=21.2.0        # WSGI HTTP服务器
channels>=4.0.0         # Django WebSocket支持
channels-redis>=4.1.0   # WebSocket Redis后端
asgiref                  # async_to_sync

# 核心数据处理 - 固定版本避免冲突
numpy==1.24.4           # 数值计算库（固定版本解决multiarray问题）
opencv-python>=4.5.0,<=4.6.0.66  # 计算机视觉库（兼容numpy 1.24.3）
Pillow==10.0.1          # 图像处理库（稳定版本）
pillow-avif-plugin      # AVIF格式支持

# AI和机器学习
openai>=1.0.0           # OpenAI API客户端
langchain>=0.1.0        # LLM应用框架
langchain-openai>=0.1.0 # LangChain OpenAI集成
langchain-community>=0.0.20  # LangChain社区组件
langgraph>=0.0.55       # 工作流图构建

# OCR文本识别 - 修复版本
paddlepaddle==2.5.2     # PaddleOCR基础依赖（CPU版本，固定稳定版本）
paddleocr==*******      # 高精度OCR引擎（固定稳定版本）

# 其他工具依赖
beautifulsoup4>=4.12.2  # HTML解析
lxml>=4.9.3             # XML/HTML处理
python-dateutil>=2.8.2  # 日期时间处理
pytz                    # 时区处理

# OCR和高级图像处理依赖
shapely>=1.8.0          # 几何计算（OCR文本框处理）
pyclipper>=1.3.0        # 多边形裁剪（OCR后处理）

# 云存储和OSS
oss2>=2.18.1            # 阿里云OSS存储
aliyun-python-sdk-core>=2.14.0  # 阿里云SDK核心
crcmod>=1.7             # CRC校验

# 支付处理
stripe>=8.0.0           # Stripe支付集成

# 配置和环境管理
python-dotenv>=1.0.0    # 环境变量管理
pyyaml>=6.0             # YAML配置文件支持

# 系统监控
psutil                  # 系统监控

# 其他云厂商和模型SDK
dashscope>=1.14.0       # 阿里云模型服务
google-genai            # Google Gemini API

# 异步和工具
aiofiles>=23.0.0        # 异步文件处理
aiohttp                 # 异步HTTP
tenacity                # 重试机制
shortuuid               # 短UUID

# 日志和调试
loguru                  # 日志库

# 火山方舟SDK (用于视频生成)
volcengine-python-sdk[ark]

# 开发工具
ruff
mypy
pre-commit
